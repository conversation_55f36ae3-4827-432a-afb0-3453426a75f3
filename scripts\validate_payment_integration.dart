#!/usr/bin/env dart

import 'dart:io';
import 'dart:convert';

/// Quick validation script for payment integration compliance
/// 
/// Usage: dart scripts/validate_payment_integration.dart
/// 
/// This script performs automated checks for:
/// - Code structure compliance
/// - Configuration validation
/// - Documentation requirements
/// - Basic functionality verification

void main() async {
  print('🔍 Payment Integration Validation');
  print('⏰ Started at: ${DateTime.now()}');
  print('=' * 60);
  
  final validator = PaymentIntegrationValidator();
  await validator.runValidation();
}

class PaymentIntegrationValidator {
  int _totalChecks = 0;
  int _passedChecks = 0;
  int _failedChecks = 0;
  int _warningChecks = 0;
  
  final List<String> _criticalIssues = [];
  final List<String> _warnings = [];
  
  Future<void> runValidation() async {
    try {
      await _validateCodeStructure();
      await _validateConfiguration();
      await _validateDocumentation();
      await _validateTestCoverage();
      await _validateSecurity();
      
      _printSummary();
    } catch (e) {
      _printError('Validation failed: $e');
      exit(1);
    }
  }
  
  Future<void> _validateCodeStructure() async {
    _printSection('🏗️ Code Structure Validation');
    
    // Check required files exist
    await _checkFile('lib/core/services/zoho_payment_service.dart', 'ZohoPaymentService implementation');
    await _checkFile('lib/presentation/widgets/zoho_payment_button.dart', 'ZohoPaymentButton widget');
    await _checkFile('lib/presentation/cubit/payments/payment_cubit.dart', 'PaymentCubit state management');
    await _checkFile('lib/presentation/screens/zoho_payment_web_view.dart', 'ZohoPaymentWebView screen');
    await _checkFile('lib/domain/entities/payments/payment_request.dart', 'PaymentRequest entity');
    await _checkFile('lib/domain/entities/payments/payment_session.dart', 'PaymentSession entity');
    await _checkFile('lib/data/datasources/remote/payment_remote_datasource.dart', 'Payment data source');
    
    // Check architecture compliance
    await _validateArchitecture();
    
    // Check for legacy code
    await _checkLegacyCode();
  }
  
  Future<void> _validateArchitecture() async {
    _check('Clean Architecture Layers', () async {
      final hasPresentation = await _fileExists('lib/presentation/');
      final hasDomain = await _fileExists('lib/domain/');
      final hasData = await _fileExists('lib/data/');
      
      if (hasPresentation && hasDomain && hasData) {
        return CheckResult.pass('All architecture layers present');
      } else {
        return CheckResult.fail('Missing architecture layers');
      }
    });
    
    _check('Dependency Injection Setup', () async {
      final diFile = File('lib/injection/payment_di.dart');
      if (await diFile.exists()) {
        final content = await diFile.readAsString();
        if (content.contains('PaymentCubit') && content.contains('ZohoPaymentService')) {
          return CheckResult.pass('Dependency injection configured');
        } else {
          return CheckResult.fail('Incomplete dependency injection setup');
        }
      } else {
        return CheckResult.fail('Payment dependency injection file missing');
      }
    });
  }
  
  Future<void> _checkLegacyCode() async {
    _check('Legacy PaymentService Removed', () async {
      final legacyFile = File('lib/core/services/payment_service.dart');
      if (await legacyFile.exists()) {
        return CheckResult.fail('Legacy PaymentService still exists - should be removed');
      } else {
        return CheckResult.pass('Legacy PaymentService properly removed');
      }
    });
  }
  
  Future<void> _validateConfiguration() async {
    _printSection('⚙️ Configuration Validation');
    
    // Check base URL configuration
    await _checkBaseUrl();
    
    // Check endpoint configuration
    await _checkEndpoints();
    
    // Check constants
    await _checkConstants();
  }
  
  Future<void> _checkBaseUrl() async {
    _check('Base URL Configuration', () async {
      final constantsFile = File('lib/core/constants/app_constants.dart');
      if (await constantsFile.exists()) {
        final content = await constantsFile.readAsString();
        if (content.contains('aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/api')) {
          return CheckResult.pass('Correct base URL configured');
        } else {
          return CheckResult.fail('Incorrect or missing base URL');
        }
      } else {
        return CheckResult.fail('App constants file missing');
      }
    });
  }
  
  Future<void> _checkEndpoints() async {
    _check('Endpoint Trailing Slashes', () async {
      final dataSourceFile = File('lib/data/datasources/remote/payment_remote_datasource.dart');
      if (await dataSourceFile.exists()) {
        final content = await dataSourceFile.readAsString();
        final hasTrailingSlashes = content.contains('create-session/') && 
                                  content.contains('status/\$sessionId/');
        
        if (hasTrailingSlashes) {
          return CheckResult.pass('Endpoints have trailing slashes');
        } else {
          return CheckResult.fail('Endpoints missing trailing slashes');
        }
      } else {
        return CheckResult.fail('Payment data source file missing');
      }
    });
  }
  
  Future<void> _checkConstants() async {
    _check('Payment Constants', () async {
      final constantsFile = File('lib/core/constants/app_constants.dart');
      if (await constantsFile.exists()) {
        final content = await constantsFile.readAsString();
        final hasPaymentConstants = content.contains('payment') || content.contains('zoho');
        
        if (hasPaymentConstants) {
          return CheckResult.pass('Payment constants defined');
        } else {
          return CheckResult.warn('Payment constants may be missing');
        }
      } else {
        return CheckResult.fail('App constants file missing');
      }
    });
  }
  
  Future<void> _validateDocumentation() async {
    _printSection('📚 Documentation Validation');
    
    await _checkFile('ZOHO_PAYMENT_INTEGRATION_README.md', 'Integration documentation');
    await _checkFile('docs/MANUAL_PAYMENT_TESTING_GUIDE.md', 'Manual testing guide');
    await _checkFile('docs/PAYMENT_TESTING_CHECKLIST.md', 'Testing checklist');
    await _checkFile('docs/PAYMENT_VALIDATION_CHECKLIST.md', 'Validation checklist');
    
    _check('Documentation Completeness', () async {
      final readmeFile = File('ZOHO_PAYMENT_INTEGRATION_README.md');
      if (await readmeFile.exists()) {
        final content = await readmeFile.readAsString();
        final hasRequiredSections = content.contains('## Architecture') &&
                                   content.contains('## API Integration') &&
                                   content.contains('## Payment Flow');
        
        if (hasRequiredSections) {
          return CheckResult.pass('Documentation has required sections');
        } else {
          return CheckResult.warn('Documentation may be incomplete');
        }
      } else {
        return CheckResult.fail('Main documentation file missing');
      }
    });
  }
  
  Future<void> _validateTestCoverage() async {
    _printSection('🧪 Test Coverage Validation');
    
    // Check test files exist
    await _checkFile('test/core/services/zoho_payment_service_test.dart', 'ZohoPaymentService tests');
    await _checkFile('test/presentation/cubit/payments/payment_cubit_test.dart', 'PaymentCubit tests');
    await _checkFile('test/presentation/widgets/zoho_payment_button_test.dart', 'ZohoPaymentButton tests');
    
    // Check test helpers
    await _checkFile('test/helpers/payment_test_config.dart', 'Test configuration');
    await _checkFile('test/helpers/mock_payment_server.dart', 'Mock server');
    
    // Check integration tests
    await _checkFile('test/integration/payment_flow_integration_test.dart', 'Integration tests');
    
    _check('Test Scripts Available', () async {
      final apiTestScript = File('scripts/test_payment_api.dart');
      final setupScript = File('scripts/setup_test_environment.dart');
      
      if (await apiTestScript.exists() && await setupScript.exists()) {
        return CheckResult.pass('Test scripts available');
      } else {
        return CheckResult.warn('Some test scripts missing');
      }
    });
  }
  
  Future<void> _validateSecurity() async {
    _printSection('🔒 Security Validation');
    
    _check('HTTPS Configuration', () async {
      final constantsFile = File('lib/core/constants/app_constants.dart');
      if (await constantsFile.exists()) {
        final content = await constantsFile.readAsString();
        if (content.contains('https://')) {
          return CheckResult.pass('HTTPS configured');
        } else {
          return CheckResult.fail('HTTPS not configured');
        }
      } else {
        return CheckResult.fail('Cannot verify HTTPS configuration');
      }
    });
    
    _check('Input Validation', () async {
      final serviceFile = File('lib/core/services/zoho_payment_service.dart');
      if (await serviceFile.exists()) {
        final content = await serviceFile.readAsString();
        final hasValidation = content.contains('validation') || content.contains('validate');
        
        if (hasValidation) {
          return CheckResult.pass('Input validation implemented');
        } else {
          return CheckResult.warn('Input validation may be missing');
        }
      } else {
        return CheckResult.fail('Cannot verify input validation');
      }
    });
    
    _check('Error Handling', () async {
      final serviceFile = File('lib/core/services/zoho_payment_service.dart');
      if (await serviceFile.exists()) {
        final content = await serviceFile.readAsString();
        final hasErrorHandling = content.contains('PaymentException') || content.contains('try') || content.contains('catch');
        
        if (hasErrorHandling) {
          return CheckResult.pass('Error handling implemented');
        } else {
          return CheckResult.fail('Error handling missing');
        }
      } else {
        return CheckResult.fail('Cannot verify error handling');
      }
    });
  }
  
  Future<void> _checkFile(String path, String description) async {
    _check(description, () async {
      final file = File(path);
      if (await file.exists()) {
        return CheckResult.pass('File exists: $path');
      } else {
        return CheckResult.fail('File missing: $path');
      }
    });
  }
  
  Future<bool> _fileExists(String path) async {
    if (path.endsWith('/')) {
      return Directory(path).exists();
    } else {
      return File(path).exists();
    }
  }
  
  Future<void> _check(String name, Future<CheckResult> Function() checkFunction) async {
    _totalChecks++;
    stdout.write('  $name... ');
    
    try {
      final result = await checkFunction();
      
      switch (result.status) {
        case CheckStatus.pass:
          _passedChecks++;
          print('✅ ${result.message}');
          break;
        case CheckStatus.warn:
          _warningChecks++;
          _warnings.add('$name: ${result.message}');
          print('⚠️  ${result.message}');
          break;
        case CheckStatus.fail:
          _failedChecks++;
          _criticalIssues.add('$name: ${result.message}');
          print('❌ ${result.message}');
          break;
      }
    } catch (e) {
      _failedChecks++;
      _criticalIssues.add('$name: Error during check - $e');
      print('💥 Error: $e');
    }
  }
  
  void _printSection(String title) {
    print('\n$title');
    print('-' * title.length);
  }
  
  void _printSummary() {
    print('\n' + '=' * 60);
    print('📊 Validation Summary');
    print('=' * 60);
    print('Total Checks: $_totalChecks');
    print('Passed: $_passedChecks ✅');
    print('Warnings: $_warningChecks ⚠️');
    print('Failed: $_failedChecks ❌');
    
    final successRate = (_passedChecks / _totalChecks * 100).toStringAsFixed(1);
    print('Success Rate: $successRate%');
    
    if (_criticalIssues.isNotEmpty) {
      print('\n🚨 Critical Issues:');
      for (final issue in _criticalIssues) {
        print('  ❌ $issue');
      }
    }
    
    if (_warnings.isNotEmpty) {
      print('\n⚠️  Warnings:');
      for (final warning in _warnings) {
        print('  ⚠️  $warning');
      }
    }
    
    print('\n🎯 Recommendation:');
    if (_failedChecks == 0) {
      if (_warningChecks == 0) {
        print('✅ Payment integration is fully compliant and ready for production!');
      } else {
        print('⚠️  Payment integration is mostly compliant. Address warnings before production.');
      }
    } else {
      print('❌ Payment integration has critical issues. Must be resolved before production.');
    }
    
    print('\n⏰ Completed at: ${DateTime.now()}');
  }
  
  void _printError(String error) {
    print('\n❌ $error');
  }
}

enum CheckStatus { pass, warn, fail }

class CheckResult {
  final CheckStatus status;
  final String message;
  
  CheckResult._(this.status, this.message);
  
  factory CheckResult.pass(String message) => CheckResult._(CheckStatus.pass, message);
  factory CheckResult.warn(String message) => CheckResult._(CheckStatus.warn, message);
  factory CheckResult.fail(String message) => CheckResult._(CheckStatus.fail, message);
}
