import 'package:equatable/equatable.dart';
import '../../../domain/entities/smr_report.dart';

abstract class SMRReportState extends Equatable {
  const SMRReportState();

  @override
  List<Object?> get props => [];
}

class SMRReportInitial extends SMRReportState {}

class SMRReportLoading extends SMRReportState {}

class SMRReportLoaded extends SMRReportState {
  final List<SMRReport> reports;
  final bool isFromCache;

  const SMRReportLoaded({required this.reports, this.isFromCache = false});

  @override
  List<Object?> get props => [reports, isFromCache];
}

class SMRReportError extends SMRReportState {
  final String message;

  const SMRReportError(this.message);

  @override
  List<Object?> get props => [message];
}
