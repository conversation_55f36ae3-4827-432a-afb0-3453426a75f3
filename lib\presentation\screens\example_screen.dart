import 'package:flutter/material.dart';
import '../../core/mixins/analytics_mixin.dart';

class ExampleScreen extends StatefulWidget {
  const ExampleScreen({Key? key}) : super(key: key);

  @override
  State<ExampleScreen> createState() => _ExampleScreenState();
}

class _ExampleScreenState extends State<ExampleScreen> with AnalyticsMixin {
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
  }

  // Implement the required screenName getter
  @override
  String get screenName => 'example_screen';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Example')),
      body: SingleChildScrollView(
        // Track scroll position using NotificationListener
        child: NotificationListener<ScrollNotification>(
          onNotification: (notification) {
            if (notification is ScrollUpdateNotification) {
              trackScrollPosition(
                notification.metrics.pixels,
                notification.metrics.maxScrollExtent,
              );
            }
            return false;
          },
          child: Column(
            children: [
              ElevatedButton(
                onPressed: () {
                  // Track user interaction
                  trackUserInteraction(
                    'tap',
                    'button',
                    elementId: 'submit_button',
                  );

                  // Track a custom event
                  trackEvent(
                    'button_pressed',
                    params: {'button_type': 'submit'},
                  );

                  // Perform the action
                  _submitForm();
                },
                child: const Text('Submit'),
              ),

              // More widgets...
            ],
          ),
        ),
      ),
    );
  }

  void _submitForm() {
    try {
      // Form submission logic

      // Track successful form submission
      trackUserFlow(
        flowName: 'form_submission',
        stepName: 'submit',
        status: 'success',
      );
    } catch (e) {
      // Track error
      trackError(
        'form_submission_error',
        e.toString(),
        additionalParams: {'form_type': 'example_form'},
      );
    }
  }
}
