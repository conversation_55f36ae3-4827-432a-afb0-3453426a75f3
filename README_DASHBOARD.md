# Dashboard Implementation Guide

This document provides instructions on how to integrate the dashboard feature into your Flutter application.

## Overview

The dashboard feature provides a comprehensive view of sales, payments, dues, liquidation, and farmer data. It follows the clean architecture pattern with an offline-first approach, ensuring data is available even without an internet connection.

## Files Structure

The implementation consists of the following components:

### Domain Layer

- **Entities**: Represent the business objects
- **Repositories**: Define interfaces for data operations
- **Use Cases**: Implement business logic

### Data Layer

- **Models**: Map to MongoDB data structures
- **Data Sources**: Local (ObjectBox) and remote (MongoDB) data sources
- **Repository Implementations**: Implement repository interfaces

### Presentation Layer

- **Cubit**: Manages dashboard state
- **States**: Represent different UI states
- **Screen**: Displays dashboard data

## Integration Steps

1. **Copy Files**: Copy all the provided files to their respective directories in your project.

2. **Update Dependency Injection**:

   - Replace `lib/injection_container.dart` with `lib/injection_container_updated.dart`
   - This registers all dashboard-related dependencies

3. **Update App Providers**:

   - Replace `lib/presentation/providers/app_providers.dart` with `lib/presentation/providers/app_providers_updated.dart`
   - This adds the DashboardCubit to the provider list

4. **Update App Router**:
   - Replace `lib/core/routes/app_router.dart` with `lib/core/routes/app_router_updated.dart`
   - This adds the route to the dashboard screen

## Usage

To use the dashboard feature:

```dart
// Navigate to dashboard
AppRouter.navigateToDashboard(customerId: 'customer_id');

// Access dashboard data directly
final dashboardCubit = context.read<DashboardCubit>();
dashboardCubit.loadDashboard('customer_id');

// Sync dashboard data
dashboardCubit.syncDashboard('customer_id');
```

## Offline-First Approach

The implementation follows an offline-first approach:

1. Data is first loaded from local storage (ObjectBox)
2. If data is available locally, it's displayed immediately
3. In the background, a sync operation is performed to update local data with the latest from the server
4. If no data is available locally, it's fetched from the server and stored locally

## Error Handling

The implementation includes comprehensive error handling:

- Network errors are caught and displayed with appropriate messages
- Local storage errors are handled gracefully
- Sync errors are reported but don't block the UI

## Customization

You can customize the dashboard UI by modifying the `DashboardScreen` class. The screen is built using standard Flutter widgets and can be adapted to match your app's design.

## Dependencies

This implementation relies on the following packages:

- `flutter_bloc` for state management
- `get_it` for dependency injection
- `objectbox` for local storage
- `intl` for date and number formatting

Make sure these dependencies are added to your `pubspec.yaml` file.

## Troubleshooting

If you encounter issues:

1. Ensure all dependencies are correctly registered in the dependency injection container
2. Check that the ObjectBox model has been generated correctly
3. Verify MongoDB connection settings
4. Check logs for any error messages

For more help, refer to the code documentation or contact the development team.
