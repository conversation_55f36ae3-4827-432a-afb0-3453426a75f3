# Firebase Analytics Debug Guide for AquaPartner

This guide explains how to monitor Firebase Analytics events in debug mode for the AquaPartner app.

## Current Implementation

Firebase Analytics is implemented in the app with the following features:

- Analytics initialization in `main.dart`
- Debug mode enabled in debug builds
- Console logging for all analytics events in debug mode
- Comprehensive event tracking through the `AnalyticsService` class
- User properties tracking for user analytics

## Monitoring Firebase Analytics Events

### 1. Console <PERSON>gs (Easiest Method)

The app now prints detailed logs for all analytics events in debug mode. When running the app in debug mode, you'll see logs in the console with the 🔥 emoji prefix:

```
🔥 Screen View: HomeScreen (HomePage)
🔥 Feature Used: price_list_view
🔥 User Login:
🔥 - User ID: 12345
🔥 - User Type: partner
```

### 2. Firebase Console DebugView

To view events in the Firebase Console:

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project: `aquaconnect-partner-prod`
3. Navigate to Analytics > DebugView
4. Events from debug devices will appear in real-time

### 3. Enable Debug Mode on Your Device

#### For Android:

```bash
adb shell setprop debug.firebase.analytics.app blue.aquaconnect.aquapartner_self_service
```

#### For iOS:

Add the following argument to your Xcode run configuration:

```
-FIRDebugEnabled
```

Or run this command in terminal:

```bash
defaults write com.google.AnalyticsDebugMode YES
```

### 4. Using Firebase CLI

For more detailed debugging:

1. Install Firebase CLI:

   ```bash
   npm install -g firebase-tools
   ```

2. Log in to Firebase:

   ```bash
   firebase login
   ```

3. Enable Analytics debug mode:

   ```bash
   firebase analytics:debug
   ```

4. Connect your device and run the app

### 5. Chrome DevTools (WebView Debugging)

For Android:

1. Enable USB debugging on your device
2. Connect your device to your computer
3. Open Chrome and navigate to: `chrome://inspect/#devices`
4. Find your connected device and open "inspect"
5. In the console, you can see Firebase Analytics debug logs

## Verifying Events

To verify that events are being properly tracked:

1. Run the app in debug mode
2. Perform actions that should trigger analytics events (login, view screens, etc.)
3. Check the console logs for the 🔥 emoji prefix logs
4. Verify events appear in Firebase Console DebugView

## Common Issues and Troubleshooting

### Events Not Appearing in DebugView

- Make sure debug mode is properly enabled
- Check that the device has an internet connection
- Verify the correct Firebase project is selected in the console
- Events may take a few minutes to appear in the console

### Debug Mode Not Working on Android

Try resetting the debug property:

```bash
adb shell setprop debug.firebase.analytics.app .none.
adb shell setprop debug.firebase.analytics.app blue.aquaconnect.aquapartner_self_service
```

### Debug Mode Not Working on iOS

Reset the debug settings:

```bash
defaults delete com.google.AnalyticsDebugMode
defaults write com.google.AnalyticsDebugMode YES
```

## Additional Resources

- [Firebase Analytics Debug Mode Documentation](https://firebase.google.com/docs/analytics/debugview)
- [Firebase Analytics for Flutter](https://firebase.google.com/docs/analytics/flutter/start)
