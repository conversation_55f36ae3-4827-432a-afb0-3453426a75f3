// lib/presentation/bloc/customer_payments/customer_payments_cubit.dart
import 'package:aquapartner/domain/services/auth_service.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../domain/entities/payments/payments_summary.dart';
import '../../../domain/usecases/customer_payments_usecases.dart';
import 'customer_payments_state.dart';

class CustomerPaymentsCubit extends Cubit<CustomerPaymentsState> {
  final GetCustomerPayments getCustomerPayments;
  final SyncCustomerPayments syncCustomerPayments;
  final GetLocalCustomerPayments getLocalCustomerPayments;
  final AuthService authService;

  bool _isBackgroundSyncing = false;
  String? _currentCustomerId;

  CustomerPaymentsCubit({
    required this.getCustomerPayments,
    required this.syncCustomerPayments,
    required this.getLocalCustomerPayments,
    required this.authService,
  }) : super(CustomerPaymentsInitial());

  /// Load customer payments with a caching-first approach
  /// Always returns cached data immediately if available
  /// and triggers a background sync if needed
  Future<void> loadCustomerPayments() async {
    emit(CustomerPaymentsLoading());
    final customerResult = await authService.getCurrentCustomer();

    customerResult.fold(
      (failure) => emit(
        CustomerPaymentsError("Unable to fetch customer details, try again"),
      ),
      (customer) async {
        if (customer == null) {
          emit(CustomerPaymentsError("Customer information not found"));
          return;
        }

        _currentCustomerId = customer.customerId;

        // CACHING-FIRST APPROACH:
        // 1. Always try to get data from local cache first
        await _loadFromCache(customer.customerId);

        // 2. Check if sync is needed in the background
        await _checkAndSyncIfNeeded(customer.customerId);
      },
    );
  }

  /// Load payments from local cache
  Future<void> _loadFromCache(String customerId) async {
    final result = await getLocalCustomerPayments(customerId);

    result.fold(
      (failure) {
        // If local data not available, try remote
        emit(CustomerPaymentsError("Unable to Load Payments, try again later"));
        syncPayments(customerId);
      },
      (payments) {
        if (payments.isEmpty) {
          // If no local data, try remote
          syncPayments(customerId);
          return;
        }

        // Calculate total sum
        final totalSum = payments.fold(0.0, (sum, payment) {
          final amountStr = payment.amount
              .replaceAll('INR ', '')
              .replaceAll(',', '');
          return sum + (double.tryParse(amountStr) ?? 0.0);
        });

        // Emit loaded state with cached data
        emit(
          CustomerPaymentsLoaded(
            PaymentsSummary(totalSum: totalSum, payments: payments),
            isFromCache: true,
            isCacheStale: false, // We don't know yet, will update if needed
            isBackgroundSyncInProgress: false,
          ),
        );
      },
    );
  }

  /// Check if sync is needed and perform background sync if necessary
  Future<void> _checkAndSyncIfNeeded(String customerId) async {
    if (_isBackgroundSyncing) {
      return; // Don't start another sync if one is already in progress
    }

    // For now, we'll always sync in the background since we don't have a specific
    // check for payments sync needed. In a real implementation, you would check
    // if sync is needed based on last sync time.

    // Update state to indicate background sync is in progress
    if (state is CustomerPaymentsLoaded) {
      emit(
        (state as CustomerPaymentsLoaded).copyWith(
          isCacheStale: true,
          isBackgroundSyncInProgress: true,
        ),
      );
    }

    // Perform background sync
    _isBackgroundSyncing = true;
    await _backgroundSync(customerId);
    _isBackgroundSyncing = false;

    // Update state to indicate background sync is complete
    if (state is CustomerPaymentsLoaded) {
      emit(
        (state as CustomerPaymentsLoaded).copyWith(
          isBackgroundSyncInProgress: false,
        ),
      );
    }
  }

  /// Perform a background sync without showing loading indicators
  Future<void> _backgroundSync(String customerId) async {
    try {
      // Sync payments
      final result = await syncCustomerPayments(customerId);

      result.fold(
        (failure) {
          // Don't emit error state as this is a background operation
        },
        (_) async {
          // After successful sync, load the updated local data
          final localResult = await getLocalCustomerPayments(customerId);

          localResult.fold(
            (failure) {
              // Don't emit error state as this is a background operation
            },
            (payments) {
              // Calculate total sum
              final totalSum = payments.fold(0.0, (sum, payment) {
                final amountStr = payment.amount
                    .replaceAll('INR ', '')
                    .replaceAll(',', '');
                return sum + (double.tryParse(amountStr) ?? 0.0);
              });

              // Update the state with new data
              emit(
                CustomerPaymentsLoaded(
                  PaymentsSummary(totalSum: totalSum, payments: payments),
                  isFromCache: false,
                  isCacheStale: false,
                  isBackgroundSyncInProgress: false,
                ),
              );
            },
          );
        },
      );
    } catch (e) {
      // Don't emit error state as this is a background operation
      _isBackgroundSyncing = false;
    }
  }

  /// Manual sync that shows loading indicators
  Future<void> syncPayments(String customerId) async {
    emit(CustomerPaymentsSyncing());
    final result = await syncCustomerPayments(customerId);

    result.fold(
      (failure) => emit(CustomerPaymentsError('No Sync, try again')),
      (_) async {
        final localResult = await getLocalCustomerPayments(customerId);

        localResult.fold(
          (failure) =>
              emit(CustomerPaymentsError('No customer payments found')),
          (payments) {
            final totalSum = payments.fold(0.0, (sum, payment) {
              final amountStr = payment.amount
                  .replaceAll('INR ', '')
                  .replaceAll(',', '');
              return sum + (double.tryParse(amountStr) ?? 0.0);
            });

            emit(
              CustomerPaymentsLoaded(
                PaymentsSummary(totalSum: totalSum, payments: payments),
                isFromCache: false,
                isCacheStale: false,
                isBackgroundSyncInProgress: false,
              ),
            );
          },
        );
      },
    );
  }

  /// Force invalidate the cache and trigger a sync
  Future<void> invalidateAndSync() async {
    if (_currentCustomerId == null) {
      emit(CustomerPaymentsError('Customer information not available'));
      return;
    }

    // Mark cache as stale if we have loaded data
    if (state is CustomerPaymentsLoaded) {
      emit((state as CustomerPaymentsLoaded).copyWith(isCacheStale: true));
    }

    // Perform a full sync
    await syncPayments(_currentCustomerId!);
  }
}
