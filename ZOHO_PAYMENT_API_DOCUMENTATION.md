# Zoho Payment Integration API Documentation

## Overview

This documentation provides comprehensive details for integrating with the Zoho Payment system. The API supports payment session creation, status tracking, refunds, webhooks, and health monitoring.

**Base URL:** `https://partner.aquaconnect.blue`

## Authentication

All API endpoints use OAuth 2.0 authentication with Zoho. The authentication is handled server-side, so client applications don't need to manage tokens directly.

## Environment Variables Required

```env
# Domain Configuration
NEXT_PUBLIC_DOMAIN=https://partner.aquaconnect.blue
NEXT_PUBLIC_API_DOMAIN=/api

# Database
MONGODB_URI=mongodb+srv://admin:<EMAIL>/aquapartner

# Zoho OAuth Configuration
ZOHO_OAUTH_CLIENT_ID=1000.OYY8H71ELT0DNW5CFFBE124H7GILMP
ZOHO_OAUTH_CLIENT_SECRET=67ca50629a18ba97eb9868be9c8faacf6645edf13d
ZOHO_OAUTH_REFRESH_TOKEN=**********************************************************************

# Zoho Payment Configuration
ZOHO_PAYMENT_SESSION_URL=https://payments.zoho.in/api/v1/paymentsessions
ZOHO_PAY_ACCOUNT_ID=***********
ZOHO_PAY_API_KEY=1003.f35b9411653295bb03db1e8490dc6cdd.0f625d54ec97f4eba7bedd0dc6fc23b8

# Webhook Configuration
ZOHO_WEBHOOK_SECRET=21155a0f5fd70801737195ba85492b7f622fe3a39b1da0a13a76635bfe3b0c1c56c0d7a833170831ac446bcf4ec801288a0e3c4ecf1347d5ceac9f6c54faa43ad9f40e40d9cc77733fe0afed6018bdee
```

## API Endpoints

### 1. Create Payment Session

**Endpoint:** `POST /api/zoho/payments/create-session`

**Description:** Creates a new payment session for processing payments.

**Request Headers:**

```
Content-Type: application/json
```

**Request Body:**

```json
{
  "amount": 100.5,
  "currency": "INR",
  "description": "Payment for Order #12345",
  "invoice_number": "INV-12345",
  "customer_id": "CUST-001",
  "customer_name": "John Doe",
  "customer_email": "<EMAIL>",
  "customer_phone": "+************",
  "redirect_url": "https://yourapp.com/payment/success",
  "reference_id": "REF-12345",
  "meta_data": [
    { "key": "order_id", "value": "ORD-123" },
    { "key": "product_type", "value": "aquaculture" }
  ]
}
```

**Required Fields:**

- `amount` (number): Payment amount
- `description` (string): Payment description
- `invoice_number` (string): Unique invoice number
- `customer_id` (string): Customer identifier

**Optional Fields:**

- `currency` (string): Currency code (default: "INR")
- `customer_name` (string): Customer name
- `customer_email` (string): Customer email
- `customer_phone` (string): Customer phone number
- `redirect_url` (string): URL to redirect after payment
- `reference_id` (string): Reference identifier
- `meta_data` (array): Additional metadata (max 5 items)

**Success Response (200):**

```json
{
  "success": true,
  "message": "Payment session created successfully",
  "data": {
    "payment_session": {
      "payments_session_id": "PS_123456789",
      "amount": 100.5,
      "currency": "INR",
      "status": "created",
      "created_time": 1640995200,
      "expires_at": 1640996100,
      "payment_url": "https://payments.zoho.in/checkout/PS_123456789"
    },
    "transaction_id": "64a1b2c3d4e5f6789012345",
    "expires_in": "15 minutes"
  }
}
```

**Error Response (400):**

```json
{
  "error": "Missing required fields",
  "message": "amount, description, invoice_number, customer_id are required",
  "required_fields": ["amount", "description", "invoice_number", "customer_id"]
}
```

### 2. Legacy Payment Initiation (Backward Compatibility)

**Endpoint:** `POST /api/initiatePayment`

**Description:** Legacy endpoint that redirects to the new Zoho Payment API while maintaining backward compatibility.

**Request Body:**

```json
{
  "amount": 100.5,
  "invoiceNo": "INV-12345",
  "redirectUrl": "https://yourapp.com/payment/success",
  "referenceId": "REF-12345",
  "customerId": "CUST-001",
  "customerName": "John Doe",
  "customerEmail": "<EMAIL>",
  "customerPhone": "+************"
}
```

**Success Response (200):**

```json
{
  "result": "success",
  "paymentSession": {
    "payments_session_id": "PS_123456789",
    "amount": 100.5,
    "currency": "INR",
    "status": "created",
    "payment_url": "https://payments.zoho.in/checkout/PS_123456789"
  },
  "transaction_id": "64a1b2c3d4e5f6789012345",
  "payment_session_id": "PS_123456789",
  "expires_in": "15 minutes",
  "paymentSessionId": "PS_123456789"
}
```

### 3. Get Payment Status

**Endpoint:** `GET /api/zoho/payments/status/{sessionId}`

**Description:** Retrieves the current status of a payment session.

**Path Parameters:**

- `sessionId` (string): Payment session ID

**Success Response (200):**

```json
{
  "success": true,
  "message": "Payment status retrieved successfully",
  "data": {
    "payment_session": {
      "payments_session_id": "PS_123456789",
      "amount": 100.5,
      "currency": "INR",
      "status": "succeeded",
      "payment_id": "PAY_987654321",
      "payment_method": "card",
      "created_time": 1640995200,
      "completed_time": 1640995800
    },
    "transaction": {
      "id": "64a1b2c3d4e5f6789012345",
      "customer_id": "CUST-001",
      "customer_name": "John Doe",
      "reference_id": "REF-12345",
      "created_at": "2024-01-01T10:00:00.000Z",
      "updated_at": "2024-01-01T10:10:00.000Z"
    }
  }
}
```

**Error Response (404):**

```json
{
  "error": "Zoho Payment API Error",
  "message": "Payment session not found",
  "details": "Payment session may not exist or may have expired"
}
```

### 4. Update Payment Status (Manual)

**Endpoint:** `PUT /api/zoho/payments/status/{sessionId}`

**Description:** Manually update payment status (for webhook simulation or manual updates).

**Request Body:**

```json
{
  "status": "succeeded",
  "payment_id": "PAY_987654321",
  "payment_method": "card",
  "error_code": null,
  "error_message": null
}
```

**Valid Status Values:**

- `created`
- `pending`
- `succeeded`
- `failed`
- `cancelled`
- `expired`

**Success Response (200):**

```json
{
  "success": true,
  "message": "Payment status updated successfully",
  "data": {
    "transaction_id": "64a1b2c3d4e5f6789012345",
    "payment_session_id": "PS_123456789",
    "status": "succeeded",
    "updated_at": "2024-01-01T10:10:00.000Z"
  }
}
```

### 5. List Customer Payments

**Endpoint:** `GET /api/zoho/payments/list`

**Description:** List payments for a customer with pagination and filtering.

**Query Parameters:**

- `customer_id` (string, required): Customer ID
- `page` (number, optional): Page number (default: 1)
- `limit` (number, optional): Items per page (default: 10, max: 100)
- `status` (string, optional): Filter by payment status
- `from_date` (string, optional): Start date (YYYY-MM-DD)
- `to_date` (string, optional): End date (YYYY-MM-DD)

**Example Request:**

```
GET /api/zoho/payments/list?customer_id=CUST-001&page=1&limit=10&status=succeeded
```

**Success Response (200):**

```json
{
  "success": true,
  "message": "Payments retrieved successfully",
  "data": {
    "transactions": [
      {
        "_id": "64a1b2c3d4e5f6789012345",
        "payments_session_id": "PS_123456789",
        "amount": 100.5,
        "currency": "INR",
        "status": "succeeded",
        "invoice_number": "INV-12345",
        "customer_id": "CUST-001",
        "created_at": "2024-01-01T10:00:00.000Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25,
      "pages": 3
    },
    "filters": {
      "customer_id": "CUST-001",
      "status": "succeeded"
    }
  }
}
```

### 6. Advanced Payment Search

**Endpoint:** `POST /api/zoho/payments/list`

**Description:** Advanced search for payments with complex filters.

**Request Body:**

```json
{
  "customer_ids": ["CUST-001", "CUST-002"],
  "statuses": ["succeeded", "pending"],
  "amount_range": {
    "min": 50.0,
    "max": 500.0
  },
  "date_range": {
    "from": "2024-01-01",
    "to": "2024-01-31"
  },
  "invoice_numbers": ["INV-12345", "INV-67890"],
  "page": 1,
  "limit": 20,
  "sort_by": "created_at",
  "sort_order": "desc"
}
```

### 7. Create Refund

**Endpoint:** `POST /api/zoho/refunds/create`

**Description:** Create a refund for a successful payment.

**Request Body:**

```json
{
  "payment_id": "PAY_987654321",
  "amount": 50.0,
  "reason": "Customer request",
  "reference_id": "REF-REFUND-001"
}
```

**Required Fields:**

- `payment_id` (string): Payment ID to refund
- `amount` (number): Refund amount

**Success Response (200):**

```json
{
  "success": true,
  "message": "Refund created successfully",
  "data": {
    "refund": {
      "refund_id": "REF_123456789",
      "payment_id": "PAY_987654321",
      "amount": 50.0,
      "status": "pending",
      "created_time": 1640995200,
      "reason": "Customer request"
    },
    "transaction_updated": true
  }
}
```

**Error Response (400):**

```json
{
  "error": "Payment not refundable",
  "message": "Only succeeded payments can be refunded",
  "payment_status": "failed"
}
```

## Payment Status Codes

| Status      | Description                                  |
| ----------- | -------------------------------------------- |
| `created`   | Payment session created, awaiting payment    |
| `pending`   | Payment is being processed                   |
| `succeeded` | Payment completed successfully               |
| `failed`    | Payment failed                               |
| `cancelled` | Payment was cancelled by user                |
| `expired`   | Payment session expired (15 minutes timeout) |

## Error Codes and Messages

### Common Error Responses

**400 Bad Request:**

```json
{
  "error": "Missing required fields",
  "message": "amount and invoiceNo are required",
  "required_fields": ["amount", "invoiceNo"]
}
```

**401 Unauthorized:**

```json
{
  "error": "Authentication failed",
  "message": "No Zoho Payment token found. Please check token management service."
}
```

**404 Not Found:**

```json
{
  "error": "Transaction not found",
  "message": "No transaction found for the provided payment session ID"
}
```

**500 Internal Server Error:**

```json
{
  "error": "Payment session creation failed",
  "message": "Zoho API Error: Invalid account configuration",
  "details": "Please check your Zoho Payment account settings"
}
```

## Webhook Configuration

### Payment Webhook Handler

**Endpoint:** `POST /api/zoho/webhooks/payment`

**Description:** Handles webhook events from Zoho Payments automatically. This endpoint processes payment status updates in real-time.

**Webhook Configuration in Zoho Dashboard:**

- **URL:** `https://partner.aquaconnect.blue/api/zoho/webhooks/payment`
- **Method:** POST
- **Content-Type:** application/json
- **Signature Header:** `x-zoho-webhook-signature` (optional, for verification)

### Supported Webhook Events

| Event Type                | Description                    | Triggered When             |
| ------------------------- | ------------------------------ | -------------------------- |
| `payment.succeeded`       | Payment completed successfully | Customer completes payment |
| `payment.failed`          | Payment failed                 | Payment processing fails   |
| `payment.pending`         | Payment is pending             | Payment is being processed |
| `payment.cancelled`       | Payment was cancelled          | Customer cancels payment   |
| `payment_session.expired` | Payment session expired        | 15-minute timeout reached  |

### Webhook Payload Format

**Example Webhook Payload:**

```json
{
  "event_type": "payment.succeeded",
  "payment_session_id": "PS_123456789",
  "payment_id": "PAY_987654321",
  "status": "succeeded",
  "amount": 100.5,
  "currency": "INR",
  "payment_method": "card",
  "created_time": 1640995200,
  "error_code": null,
  "error_message": null,
  "customer_details": {
    "customer_id": "CUST-001",
    "customer_email": "<EMAIL>"
  },
  "meta_data": [
    { "key": "invoice_number", "value": "INV-12345" },
    { "key": "customer_id", "value": "CUST-001" }
  ]
}
```

### Webhook Response

**Success Response (200):**

```json
{
  "success": true,
  "message": "Webhook processed successfully",
  "data": {
    "event_type": "payment.succeeded",
    "payment_session_id": "PS_123456789",
    "status": "succeeded",
    "processed_at": "2024-01-01T10:10:00.000Z"
  }
}
```

**Error Response (400):**

```json
{
  "error": "Invalid webhook data",
  "message": "event_type and payment_session_id are required"
}
```

### Webhook Information Endpoint

**Endpoint:** `GET /api/zoho/webhooks/payment`

**Description:** Get webhook configuration information and setup instructions.

**Response:**

```json
{
  "message": "Zoho Payment Webhook Endpoint",
  "endpoint": "/api/zoho/webhooks/payment",
  "method": "POST",
  "supported_events": [
    {
      "event": "payment.succeeded",
      "description": "Payment completed successfully"
    },
    {
      "event": "payment.failed",
      "description": "Payment failed"
    },
    {
      "event": "payment.pending",
      "description": "Payment is pending"
    },
    {
      "event": "payment.cancelled",
      "description": "Payment was cancelled"
    },
    {
      "event": "payment_session.expired",
      "description": "Payment session expired"
    }
  ],
  "configuration": {
    "webhook_url": "https://partner.aquaconnect.blue/api/zoho/webhooks/payment",
    "signature_verification": true,
    "content_type": "application/json"
  },
  "setup_instructions": [
    {
      "step": 1,
      "title": "Configure Webhook URL in Zoho Payments",
      "description": "Add the webhook URL in your Zoho Payments dashboard"
    },
    {
      "step": 2,
      "title": "Set Webhook Secret (Optional)",
      "description": "Set ZOHO_WEBHOOK_SECRET environment variable for signature verification"
    },
    {
      "step": 3,
      "title": "Test Webhook",
      "description": "Make a test payment to verify webhook is working"
    }
  ]
}
```

## Authentication Endpoints

### Check Token Status

**Endpoint:** `GET /api/zoho/auth/refresh`

**Description:** Check if the Zoho Payment token is valid.

**Response:**

```json
{
  "success": true,
  "message": "Token is valid",
  "data": {
    "token_valid": true,
    "has_access_token": true
  }
}
```

### Refresh Token

**Endpoint:** `POST /api/zoho/auth/refresh`

**Description:** Manually refresh the access token.

**Response:**

```json
{
  "success": true,
  "message": "Access token refreshed successfully",
  "data": {
    "token_refreshed": true,
    "expires_at": "2024-01-01T11:00:00.000Z"
  }
}
```

### Setup Instructions

**Endpoint:** `GET /api/zoho/auth/setup`

**Description:** Get OAuth setup instructions for Zoho Payments.

**Response:**

```json
{
  "message": "Zoho Payment OAuth Setup Instructions",
  "steps": [
    {
      "step": 1,
      "title": "Visit Zoho Developer Console",
      "url": "https://accounts.zoho.in/developerconsole",
      "description": "Go to Zoho Developer Console and create a Self Client"
    },
    {
      "step": 2,
      "title": "Generate Authorization Code",
      "description": "In the Generate Code tab, enter the required scopes and generate an authorization code",
      "scopes": ["ZohoPay.payments.CREATE", "ZohoPay.payments.READ", "ZohoPay.refunds.CREATE", "ZohoPay.refunds.READ"]
    }
  ]
}
```

## Health Check Endpoint

**Endpoint:** `GET /api/zoho/health`

**Description:** System health check and diagnostics for the Zoho Payment integration.

**Response:**

```json
{
  "timestamp": "2024-01-01T10:00:00.000Z",
  "service": "Zoho Payment Integration",
  "version": "1.0.0",
  "status": "healthy",
  "checks": {
    "database": {
      "status": "healthy",
      "message": "Database connection successful"
    },
    "zoho_token": {
      "status": "healthy",
      "message": "Valid access token found",
      "expires_at": "2024-01-01T11:00:00.000Z"
    },
    "zoho_api": {
      "status": "healthy",
      "message": "Zoho API is accessible",
      "response_time": "245ms"
    }
  },
  "configuration": {
    "account_id": "configured",
    "webhook_secret": "configured",
    "domain": "https://partner.aquaconnect.blue"
  }
}
```

**Unhealthy Response (503):**

```json
{
  "timestamp": "2024-01-01T10:00:00.000Z",
  "service": "Zoho Payment Integration",
  "version": "1.0.0",
  "status": "unhealthy",
  "checks": {
    "database": {
      "status": "unhealthy",
      "message": "Database connection failed",
      "error": "Connection timeout"
    },
    "zoho_token": {
      "status": "unhealthy",
      "message": "No valid access token found"
    }
  }
}
```

## Complete Payment Flow

### 1. Payment Initiation Flow

```mermaid
sequenceDiagram
    participant Flutter as Flutter App
    participant API as Payment API
    participant Zoho as Zoho Payments
    participant Webhook as Webhook Handler

    Flutter->>API: POST /api/zoho/payments/create-session
    API->>Zoho: Create payment session
    Zoho-->>API: Payment session details
    API-->>Flutter: Payment session + URL
    Flutter->>Zoho: Redirect to payment_url
    Zoho-->>Flutter: Payment completion
    Zoho->>Webhook: Send webhook event
    Webhook->>API: Update transaction status
    Flutter->>API: GET /api/zoho/payments/status/{sessionId}
    API-->>Flutter: Final payment status
```

### 2. Step-by-Step Implementation Guide

#### Step 1: Create Payment Session

```dart
// Flutter implementation example
Future<Map<String, dynamic>> createPaymentSession({
  required double amount,
  required String description,
  required String invoiceNumber,
  required String customerId,
  String? customerName,
  String? customerEmail,
  String? customerPhone,
  String? redirectUrl,
  String? referenceId,
}) async {
  final response = await http.post(
    Uri.parse('https://partner.aquaconnect.blue/api/zoho/payments/create-session'),
    headers: {'Content-Type': 'application/json'},
    body: jsonEncode({
      'amount': amount,
      'description': description,
      'invoice_number': invoiceNumber,
      'customer_id': customerId,
      'customer_name': customerName,
      'customer_email': customerEmail,
      'customer_phone': customerPhone,
      'redirect_url': redirectUrl,
      'reference_id': referenceId,
      'currency': 'INR',
    }),
  );

  if (response.statusCode == 200) {
    return jsonDecode(response.body);
  } else {
    throw Exception('Failed to create payment session');
  }
}
```

#### Step 2: Launch Payment URL

```dart
// Launch payment URL in browser or WebView
Future<void> launchPayment(String paymentUrl) async {
  if (await canLaunch(paymentUrl)) {
    await launch(paymentUrl);
  } else {
    throw Exception('Could not launch payment URL');
  }
}
```

#### Step 3: Handle Payment Completion

```dart
// Check payment status after user returns
Future<Map<String, dynamic>> checkPaymentStatus(String sessionId) async {
  final response = await http.get(
    Uri.parse('https://partner.aquaconnect.blue/api/zoho/payments/status/$sessionId'),
  );

  if (response.statusCode == 200) {
    return jsonDecode(response.body);
  } else {
    throw Exception('Failed to get payment status');
  }
}
```

### 3. Payment Status Polling

For real-time status updates, implement polling:

```dart
Future<String> pollPaymentStatus(String sessionId, {
  Duration interval = const Duration(seconds: 3),
  Duration timeout = const Duration(minutes: 5),
}) async {
  final startTime = DateTime.now();

  while (DateTime.now().difference(startTime) < timeout) {
    try {
      final statusResponse = await checkPaymentStatus(sessionId);
      final status = statusResponse['data']['payment_session']['status'];

      if (status == 'succeeded' || status == 'failed' ||
          status == 'cancelled' || status == 'expired') {
        return status;
      }

      await Future.delayed(interval);
    } catch (e) {
      print('Error polling payment status: $e');
      await Future.delayed(interval);
    }
  }

  return 'timeout';
}
```

## Flutter Integration Examples

### 1. Payment Service Class

```dart
class ZohoPaymentService {
  static const String baseUrl = 'https://partner.aquaconnect.blue/api';

  // Create payment session
  Future<PaymentSession> createPaymentSession(PaymentRequest request) async {
    final response = await http.post(
      Uri.parse('$baseUrl/zoho/payments/create-session'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode(request.toJson()),
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return PaymentSession.fromJson(data['data']);
    } else {
      throw PaymentException.fromResponse(response);
    }
  }

  // Get payment status
  Future<PaymentStatus> getPaymentStatus(String sessionId) async {
    final response = await http.get(
      Uri.parse('$baseUrl/zoho/payments/status/$sessionId'),
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return PaymentStatus.fromJson(data['data']);
    } else {
      throw PaymentException.fromResponse(response);
    }
  }

  // List customer payments
  Future<PaymentList> getCustomerPayments({
    required String customerId,
    int page = 1,
    int limit = 10,
    String? status,
  }) async {
    final queryParams = {
      'customer_id': customerId,
      'page': page.toString(),
      'limit': limit.toString(),
      if (status != null) 'status': status,
    };

    final uri = Uri.parse('$baseUrl/zoho/payments/list')
        .replace(queryParameters: queryParams);

    final response = await http.get(uri);

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return PaymentList.fromJson(data['data']);
    } else {
      throw PaymentException.fromResponse(response);
    }
  }

  // Create refund
  Future<RefundResponse> createRefund({
    required String paymentId,
    required double amount,
    String reason = 'Customer request',
    String? referenceId,
  }) async {
    final response = await http.post(
      Uri.parse('$baseUrl/zoho/refunds/create'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({
        'payment_id': paymentId,
        'amount': amount,
        'reason': reason,
        'reference_id': referenceId,
      }),
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return RefundResponse.fromJson(data['data']);
    } else {
      throw PaymentException.fromResponse(response);
    }
  }
}
```

### 2. Data Models

```dart
class PaymentRequest {
  final double amount;
  final String description;
  final String invoiceNumber;
  final String customerId;
  final String? customerName;
  final String? customerEmail;
  final String? customerPhone;
  final String? redirectUrl;
  final String? referenceId;
  final String currency;
  final List<MetaData>? metaData;

  PaymentRequest({
    required this.amount,
    required this.description,
    required this.invoiceNumber,
    required this.customerId,
    this.customerName,
    this.customerEmail,
    this.customerPhone,
    this.redirectUrl,
    this.referenceId,
    this.currency = 'INR',
    this.metaData,
  });

  Map<String, dynamic> toJson() => {
    'amount': amount,
    'description': description,
    'invoice_number': invoiceNumber,
    'customer_id': customerId,
    'customer_name': customerName,
    'customer_email': customerEmail,
    'customer_phone': customerPhone,
    'redirect_url': redirectUrl,
    'reference_id': referenceId,
    'currency': currency,
    'meta_data': metaData?.map((e) => e.toJson()).toList(),
  };
}

class PaymentSession {
  final String paymentSessionId;
  final double amount;
  final String currency;
  final String status;
  final String paymentUrl;
  final int createdTime;
  final int expiresAt;
  final String transactionId;

  PaymentSession({
    required this.paymentSessionId,
    required this.amount,
    required this.currency,
    required this.status,
    required this.paymentUrl,
    required this.createdTime,
    required this.expiresAt,
    required this.transactionId,
  });

  factory PaymentSession.fromJson(Map<String, dynamic> json) => PaymentSession(
    paymentSessionId: json['payment_session']['payments_session_id'],
    amount: json['payment_session']['amount'].toDouble(),
    currency: json['payment_session']['currency'],
    status: json['payment_session']['status'],
    paymentUrl: json['payment_session']['payment_url'],
    createdTime: json['payment_session']['created_time'],
    expiresAt: json['payment_session']['expires_at'],
    transactionId: json['transaction_id'],
  );
}

class PaymentStatus {
  final String paymentSessionId;
  final String status;
  final double amount;
  final String currency;
  final String? paymentId;
  final String? paymentMethod;
  final int? completedTime;
  final String? errorCode;
  final String? errorMessage;

  PaymentStatus({
    required this.paymentSessionId,
    required this.status,
    required this.amount,
    required this.currency,
    this.paymentId,
    this.paymentMethod,
    this.completedTime,
    this.errorCode,
    this.errorMessage,
  });

  factory PaymentStatus.fromJson(Map<String, dynamic> json) => PaymentStatus(
    paymentSessionId: json['payment_session']['payments_session_id'],
    status: json['payment_session']['status'],
    amount: json['payment_session']['amount'].toDouble(),
    currency: json['payment_session']['currency'],
    paymentId: json['payment_session']['payment_id'],
    paymentMethod: json['payment_session']['payment_method'],
    completedTime: json['payment_session']['completed_time'],
    errorCode: json['payment_session']['error_code'],
    errorMessage: json['payment_session']['error_message'],
  );

  bool get isCompleted => status == 'succeeded';
  bool get isFailed => status == 'failed';
  bool get isPending => status == 'pending';
  bool get isCancelled => status == 'cancelled';
  bool get isExpired => status == 'expired';
}

class PaymentException implements Exception {
  final String message;
  final int? statusCode;
  final Map<String, dynamic>? details;

  PaymentException(this.message, {this.statusCode, this.details});

  factory PaymentException.fromResponse(http.Response response) {
    final data = jsonDecode(response.body);
    return PaymentException(
      data['message'] ?? 'Payment operation failed',
      statusCode: response.statusCode,
      details: data,
    );
  }

  @override
  String toString() => 'PaymentException: $message';
}

class MetaData {
  final String key;
  final String value;

  MetaData({required this.key, required this.value});

  Map<String, dynamic> toJson() => {'key': key, 'value': value};
}
```

### 3. Complete Flutter Widget Example

```dart
class PaymentScreen extends StatefulWidget {
  final PaymentRequest paymentRequest;

  const PaymentScreen({Key? key, required this.paymentRequest}) : super(key: key);

  @override
  _PaymentScreenState createState() => _PaymentScreenState();
}

class _PaymentScreenState extends State<PaymentScreen> {
  final ZohoPaymentService _paymentService = ZohoPaymentService();
  PaymentSession? _paymentSession;
  PaymentStatus? _paymentStatus;
  bool _isLoading = false;
  String? _error;

  @override
  void initState() {
    super.initState();
    _createPaymentSession();
  }

  Future<void> _createPaymentSession() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final session = await _paymentService.createPaymentSession(widget.paymentRequest);
      setState(() {
        _paymentSession = session;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _launchPayment() async {
    if (_paymentSession == null) return;

    try {
      await launch(_paymentSession!.paymentUrl);
      _startStatusPolling();
    } catch (e) {
      setState(() {
        _error = 'Failed to launch payment: $e';
      });
    }
  }

  void _startStatusPolling() {
    Timer.periodic(Duration(seconds: 3), (timer) async {
      try {
        final status = await _paymentService.getPaymentStatus(_paymentSession!.paymentSessionId);
        setState(() {
          _paymentStatus = status;
        });

        if (status.isCompleted || status.isFailed || status.isCancelled || status.isExpired) {
          timer.cancel();
          _handlePaymentCompletion(status);
        }
      } catch (e) {
        print('Error polling payment status: $e');
      }
    });
  }

  void _handlePaymentCompletion(PaymentStatus status) {
    if (status.isCompleted) {
      // Payment successful
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (context) => PaymentSuccessScreen(paymentStatus: status),
        ),
      );
    } else {
      // Payment failed, cancelled, or expired
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (context) => PaymentFailureScreen(paymentStatus: status),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Payment')),
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            if (_isLoading)
              Center(child: CircularProgressIndicator()),
            if (_error != null)
              Card(
                color: Colors.red[100],
                child: Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Text(_error!, style: TextStyle(color: Colors.red[800])),
                ),
              ),
            if (_paymentSession != null) ...[
              Card(
                child: Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Payment Details', style: Theme.of(context).textTheme.headline6),
                      SizedBox(height: 8),
                      Text('Amount: ₹${_paymentSession!.amount}'),
                      Text('Currency: ${_paymentSession!.currency}'),
                      Text('Status: ${_paymentSession!.status}'),
                      Text('Session ID: ${_paymentSession!.paymentSessionId}'),
                    ],
                  ),
                ),
              ),
              SizedBox(height: 16),
              ElevatedButton(
                onPressed: _launchPayment,
                child: Text('Proceed to Payment'),
              ),
            ],
            if (_paymentStatus != null) ...[
              SizedBox(height: 16),
              Card(
                child: Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Payment Status', style: Theme.of(context).textTheme.headline6),
                      SizedBox(height: 8),
                      Text('Status: ${_paymentStatus!.status}'),
                      if (_paymentStatus!.paymentId != null)
                        Text('Payment ID: ${_paymentStatus!.paymentId}'),
                      if (_paymentStatus!.paymentMethod != null)
                        Text('Method: ${_paymentStatus!.paymentMethod}'),
                      if (_paymentStatus!.errorMessage != null)
                        Text('Error: ${_paymentStatus!.errorMessage}',
                             style: TextStyle(color: Colors.red)),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
```

## Testing and Validation

### 1. API Testing with cURL

#### Test Payment Session Creation

```bash
curl -X POST https://partner.aquaconnect.blue/api/zoho/payments/create-session \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 100.50,
    "description": "Test Payment",
    "invoice_number": "TEST-INV-001",
    "customer_id": "TEST-CUST-001",
    "customer_name": "Test Customer",
    "customer_email": "<EMAIL>",
    "customer_phone": "+************"
  }'
```

#### Test Payment Status Check

```bash
curl -X GET https://partner.aquaconnect.blue/api/zoho/payments/status/PS_123456789
```

#### Test Health Check

```bash
curl -X GET https://partner.aquaconnect.blue/api/zoho/health
```

### 2. Flutter Testing

```dart
void main() {
  group('ZohoPaymentService Tests', () {
    late ZohoPaymentService paymentService;

    setUp(() {
      paymentService = ZohoPaymentService();
    });

    test('should create payment session successfully', () async {
      final request = PaymentRequest(
        amount: 100.50,
        description: 'Test Payment',
        invoiceNumber: 'TEST-INV-001',
        customerId: 'TEST-CUST-001',
      );

      final session = await paymentService.createPaymentSession(request);

      expect(session.amount, equals(100.50));
      expect(session.status, equals('created'));
      expect(session.paymentUrl, isNotEmpty);
    });

    test('should handle payment session creation failure', () async {
      final request = PaymentRequest(
        amount: -100.50, // Invalid amount
        description: 'Test Payment',
        invoiceNumber: 'TEST-INV-001',
        customerId: 'TEST-CUST-001',
      );

      expect(
        () => paymentService.createPaymentSession(request),
        throwsA(isA<PaymentException>()),
      );
    });
  });
}
```

## Security Considerations

### 1. API Security

- **OAuth 2.0 Authentication**: All API calls use secure OAuth tokens
- **HTTPS Only**: All communications are encrypted
- **Webhook Signature Verification**: Optional webhook signature validation
- **Input Validation**: All inputs are validated server-side
- **Rate Limiting**: Implement rate limiting for API endpoints

### 2. Client-Side Security

- **No Sensitive Data Storage**: Never store payment credentials on client
- **Secure Communication**: Always use HTTPS for API calls
- **Token Management**: Handle authentication tokens securely
- **Error Handling**: Don't expose sensitive information in error messages

### 3. Best Practices

- **Validate Amounts**: Always validate payment amounts on both client and server
- **Timeout Handling**: Implement proper timeout handling for payment sessions
- **Status Verification**: Always verify payment status through API, not just webhooks
- **Logging**: Log payment events for audit trails (without sensitive data)

## Implementation Checklist

### Backend Setup

- [ ] Environment variables configured
- [ ] Database connection established
- [ ] Zoho OAuth tokens configured
- [ ] Webhook endpoint configured
- [ ] Health check endpoint working

### Flutter Integration

- [ ] HTTP client configured
- [ ] Payment service class implemented
- [ ] Data models created
- [ ] Error handling implemented
- [ ] UI components created
- [ ] Status polling implemented
- [ ] Deep linking configured (for payment returns)

### Testing

- [ ] Unit tests for payment service
- [ ] Integration tests for API endpoints
- [ ] UI tests for payment flow
- [ ] Error scenario testing
- [ ] Performance testing

### Production Deployment

- [ ] SSL certificates configured
- [ ] Environment variables secured
- [ ] Monitoring and logging setup
- [ ] Backup and recovery procedures
- [ ] Documentation updated

## Troubleshooting

### Common Issues

1. **Token Expired Error**

   - Check token refresh mechanism
   - Verify OAuth configuration
   - Contact system administrator

2. **Payment Session Creation Fails**

   - Validate required fields
   - Check Zoho account configuration
   - Verify API connectivity

3. **Webhook Not Received**

   - Check webhook URL configuration
   - Verify firewall settings
   - Test webhook endpoint manually

4. **Payment Status Not Updating**
   - Check webhook processing
   - Verify database connectivity
   - Implement status polling as fallback

### Support Contacts

- **Technical Support**: Contact system administrator
- **Zoho Support**: https://help.zoho.com/portal/en/community/topic/zoho-payments
- **API Documentation**: https://www.zoho.com/payments/help/api/

---

**Last Updated**: January 2024
**API Version**: 1.0.0
**Documentation Version**: 1.0.0
