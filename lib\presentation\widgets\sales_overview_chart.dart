import 'package:aqua_ui/aqua_ui.dart';
import 'package:aquapartner/core/utils/currency_formatter.dart';
import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:intl/intl.dart';
import 'dart:math' show max;

import '../../domain/entities/dashboard/dashboard_entity.dart';
import '../../core/services/analytics_service.dart';
import '../../injection_container.dart' as di;

class SalesOverviewChart extends StatefulWidget {
  final SalesEntity salesData;
  final PaymentsEntity paymentsData;

  const SalesOverviewChart({
    super.key,
    required this.salesData,
    required this.paymentsData,
  });

  @override
  State<SalesOverviewChart> createState() => _SalesOverviewChartState();
}

class _SalesOverviewChartState extends State<SalesOverviewChart> {
  int _selectedFilterIndex = 0; // 0: All, 1: Current Month, 2: Last Month
  late final AnalyticsService _analyticsService;

  @override
  void initState() {
    super.initState();
    _analyticsService = di.sl<AnalyticsService>();
    // Track chart initialization - Convert boolean values to strings
    _trackEvent('chart_initialized', {
      'has_sales_data':
          widget.salesData.yearlyData.isNotEmpty ? 'true' : 'false',
      'has_payments_data':
          widget.paymentsData.yearlyData.isNotEmpty ? 'true' : 'false',
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Color(0xFFF9FAFC),
        borderRadius: BorderRadius.circular(0),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Wrap(
            alignment: WrapAlignment.start,
            spacing: 4, // Reduced spacing
            children: [
              _buildFilterOption(0, 'All'),
              _buildFilterOption(1, 'Current Month'),
              _buildFilterOption(2, 'Last Month'),
            ],
          ),
          SizedBox(height: 8),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: EdgeInsets.all(16),
                decoration: BoxDecoration(
                  border: Border.all(color: Color(0xFFE5E7EB), width: 1.0),
                  color: acWhiteColor,
                  borderRadius: BorderRadius.all(Radius.circular(8)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    AquaText.subheadline(
                      'Purchases',
                      weight: AquaFontWeight.bold,
                    ),
                    const SizedBox(height: 16),
                    SizedBox(height: 200, child: _buildSalesChart()),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              Container(
                padding: EdgeInsets.all(16),
                decoration: BoxDecoration(
                  border: Border.all(color: Color(0xFFE5E7EB), width: 1.0),
                  color: acWhiteColor,
                  borderRadius: BorderRadius.all(Radius.circular(8)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    AquaText.subheadline(
                      'Payments',
                      weight: AquaFontWeight.bold,
                    ),
                    const SizedBox(height: 16),
                    SizedBox(height: 200, child: _buildPaymentsChart()),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFilterOption(int index, String label) {
    return IntrinsicWidth(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Radio<int>(
            value: index,
            groupValue: _selectedFilterIndex,
            activeColor: acPrimaryBlue,
            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
            visualDensity: VisualDensity.compact,
            onChanged: (value) {
              // Track filter change
              _trackEvent('filter_changed', {
                'previous_filter': _getFilterName(_selectedFilterIndex),
                'new_filter': _getFilterName(value!),
              });

              // Also log as feature usage for the analytics dashboard
              _analyticsService.logFeatureUsage(
                'chart_filter_${_getFilterName(value)}',
              );

              setState(() => _selectedFilterIndex = value);
            },
          ),
          Flexible(
            child: AquaText.body(label, overflow: TextOverflow.ellipsis),
          ),
        ],
      ),
    );
  }

  Widget _buildSalesChart() {
    // Track which chart type is being viewed
    final chartType = _selectedFilterIndex == 0 ? 'monthly' : 'weekly';
    _trackEvent('sales_chart_viewed', {
      'chart_type': chartType,
      'filter': _getFilterName(_selectedFilterIndex),
    });

    if (_selectedFilterIndex == 0) {
      return _buildMonthlyBarChart(_getAllMonthlySalesData(widget.salesData));
    } else if (_selectedFilterIndex == 1) {
      return _buildWeeklyBarChart(
        periods: _getSalesMonthYearPeriods(widget.salesData),
        isCurrentMonth: true,
        entity: widget.salesData,
        isPayment: false,
      );
    } else {
      return _buildWeeklyBarChart(
        periods: _getSalesMonthYearPeriods(widget.salesData),
        isCurrentMonth: false,
        entity: widget.salesData,
        isPayment: false,
      );
    }
  }

  Widget _buildPaymentsChart() {
    // Track which chart type is being viewed
    final chartType = _selectedFilterIndex == 0 ? 'monthly' : 'weekly';
    _trackEvent('payments_chart_viewed', {
      'chart_type': chartType,
      'filter': _getFilterName(_selectedFilterIndex),
    });

    if (_selectedFilterIndex == 0) {
      return _buildMonthlyBarChart(
        _getAllMonthlyPaymentsData(widget.paymentsData),
      );
    } else if (_selectedFilterIndex == 1) {
      return _buildWeeklyBarChart(
        periods: _getPaymentsMonthYearPeriods(widget.paymentsData),
        isCurrentMonth: true,
        entity: widget.paymentsData,
        isPayment: true,
      );
    } else {
      return _buildWeeklyBarChart(
        periods: _getPaymentsMonthYearPeriods(widget.paymentsData),
        isCurrentMonth: false,
        entity: widget.paymentsData,
        isPayment: true,
      );
    }
  }

  Widget _buildMonthlyBarChart(List<ChartMonthData> data) {
    final maxValue = _getMaxValue(data.map((e) => e.amount).toList());

    // Track monthly chart data metrics
    _trackEvent('monthly_chart_rendered', {
      'data_points': data.length,
      'max_value': maxValue,
      'chart_type': _selectedFilterIndex == 0 ? 'sales' : 'payments',
    });

    return RotatedBox(
      quarterTurns: 1, // Rotate the entire chart 90 degrees
      child: BarChart(
        BarChartData(
          alignment: BarChartAlignment.spaceAround,
          maxY: maxValue * 1.2,
          groupsSpace: 20,
          barTouchData: BarTouchData(
            enabled: false,
            touchTooltipData: BarTouchTooltipData(
              getTooltipColor: (group) => Colors.transparent,
              tooltipPadding: EdgeInsets.zero,
              tooltipMargin: 8,
              fitInsideHorizontally: true,
              fitInsideVertically: true,
              rotateAngle: 270.0,
              getTooltipItem: (group, groupIndex, rod, rodIndex) {
                return BarTooltipItem(
                  _formatLargeAmount(rod.toY),
                  AquaText.caption("").style,
                );
              },
            ),
          ),
          titlesData: FlTitlesData(
            show: true,
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                getTitlesWidget: (value, meta) {
                  final index = value.toInt();
                  if (index >= 0 && index < data.length) {
                    return RotatedBox(
                      quarterTurns: -1,
                      child: Padding(
                        padding: const EdgeInsets.only(bottom: 8.0),
                        child: AquaText.caption(data[index].label),
                      ),
                    );
                  }
                  return AquaText.body('');
                },
                reservedSize: 60,
              ),
            ),
            leftTitles: const AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
            rightTitles: const AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
            topTitles: const AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
          ),
          borderData: FlBorderData(show: false),
          gridData: const FlGridData(show: false),
          barGroups:
              data.asMap().entries.map((entry) {
                final index = entry.key;
                final item = entry.value;
                return BarChartGroupData(
                  x: index,
                  barRods: [
                    BarChartRodData(
                      toY: item.amount,
                      color: acPrimaryBlue,
                      width: 12,
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(2),
                        topRight: Radius.circular(2),
                      ),
                    ),
                  ],
                  showingTooltipIndicators: [0],
                );
              }).toList(),
        ),
      ),
    );
  }

  Widget _buildWeeklyBarChart({
    required bool isCurrentMonth,
    required List<ChartPeriod> periods,
    required dynamic entity,
    required bool isPayment,
  }) {
    final today = DateTime.now();
    final currentYear = DateFormat("yyyy").format(today);
    final selectedMonth =
        isCurrentMonth
            ? DateFormat("MM").format(today)
            : DateFormat('MM').format(DateTime(today.year, today.month - 1));

    final newperiodToUse =
        periods.isNotEmpty
            ? periods.firstWhere(
              (period) =>
                  period.yearKey == currentYear &&
                  period.monthKey == selectedMonth,
              orElse:
                  () => ChartPeriod(
                    yearKey: currentYear,
                    monthKey: selectedMonth,
                  ),
            )
            : ChartPeriod(yearKey: currentYear, monthKey: selectedMonth);

    final weeklyData =
        isPayment
            ? _getPaymentsWeeklyDataForPeriod(entity, newperiodToUse)
            : _getSalesWeeklyDataForPeriod(entity, newperiodToUse);

    // Track weekly chart data metrics
    _trackEvent('weekly_chart_rendered', {
      'is_current_month': isCurrentMonth ? 'true' : 'false',
      'year': currentYear,
      'month': selectedMonth,
      'data_points': weeklyData.length.toString(),
      'chart_type': isPayment ? 'payments' : 'sales',
    });

    // Find the maximum week number in the data, but limit to 3 weeks
    int maxWeeks = 5; // Default to 3 weeks (safer than 4 or 5)
    if (weeklyData.isNotEmpty) {
      try {
        final weekNumbers = weeklyData.map((w) => int.parse(w.weekLabel));
        if (weekNumbers.isNotEmpty) {
          maxWeeks = weekNumbers.reduce(max);
          // Ensure we don't exceed 3 weeks to avoid index errors
          maxWeeks = maxWeeks > 5 ? 5 : maxWeeks;
        }
      } catch (e) {
        // If parsing fails, default to 3 weeks
        maxWeeks = 5;
      }
    }

    final completeWeeklyData = List.generate(maxWeeks, (index) {
      final weekNumber = (index + 1).toString();
      final existingWeek = weeklyData.firstWhere(
        (item) => item.weekLabel == weekNumber,
        orElse: () => ChartWeekData(weekLabel: weekNumber, amount: 0),
      );
      return existingWeek;
    });

    final maxValue = _getMaxValue(
      completeWeeklyData.map((e) => e.amount).toList(),
    );

    return RotatedBox(
      quarterTurns: 1,
      child: BarChart(
        BarChartData(
          alignment: BarChartAlignment.spaceAround,
          maxY: maxValue * 1.2,
          groupsSpace: 20,
          barTouchData: BarTouchData(
            enabled: false,
            touchTooltipData: BarTouchTooltipData(
              getTooltipColor: (group) => Colors.transparent,
              tooltipPadding: EdgeInsets.zero,
              tooltipMargin: 8,
              fitInsideHorizontally: true,
              fitInsideVertically: true,
              rotateAngle: 270.0,
              getTooltipItem: (group, groupIndex, rod, rodIndex) {
                return BarTooltipItem(
                  _formatLargeAmount(rod.toY),
                  AquaText.caption('', color: acBlackColor).style,
                );
              },
            ),
          ),
          titlesData: FlTitlesData(
            show: true,
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                getTitlesWidget: (value, meta) {
                  final index = value.toInt();
                  if (index >= 0 && index < completeWeeklyData.length) {
                    return RotatedBox(
                      quarterTurns: -1,
                      child: Padding(
                        padding: const EdgeInsets.only(bottom: 8.0),
                        child: AquaText.body(
                          'Week ${completeWeeklyData[index].weekLabel}',
                        ),
                      ),
                    );
                  }
                  return const Text('');
                },
                reservedSize: 60,
              ),
            ),
            leftTitles: const AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
            rightTitles: const AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
            topTitles: const AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
          ),
          borderData: FlBorderData(show: false),
          gridData: const FlGridData(show: false),
          barGroups:
              completeWeeklyData.asMap().entries.map((entry) {
                final index = entry.key;
                final item = entry.value;
                return BarChartGroupData(
                  x: index,
                  barRods: [
                    BarChartRodData(
                      toY: item.amount,
                      color: acPrimaryBlue,
                      width: 12,
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(2),
                        topRight: Radius.circular(2),
                      ),
                    ),
                  ],
                  showingTooltipIndicators: [0],
                );
              }).toList(),
        ),
      ),
    );
  }

  List<ChartMonthData> _getAllMonthlySalesData(SalesEntity salesData) {
    List<ChartMonthData> result = [];

    // Process all years in the data
    for (var yearKey in salesData.yearlyData.keys) {
      for (var monthData in salesData.yearlyData[yearKey]!) {
        // Create label like "May 24" by parsing month and year
        final monthNum = int.tryParse(monthData.month) ?? 0;
        final monthName = _getMonthAbbr(monthNum);
        final yearSuffix = yearKey.substring(
          yearKey.length - 2,
        ); // Get last 2 digits

        result.add(
          ChartMonthData(
            yearKey: yearKey,
            monthKey: monthData.month,
            label: '$monthName $yearSuffix',
            amount: monthData.totalSales,
          ),
        );
      }
    }

    // Sort by year and month numerically
    result.sort((a, b) {
      int yearA = int.tryParse(a.yearKey) ?? 0;
      int yearB = int.tryParse(b.yearKey) ?? 0;
      if (yearA != yearB) return yearA.compareTo(yearB);

      int monthA = int.tryParse(a.monthKey) ?? 0;
      int monthB = int.tryParse(b.monthKey) ?? 0;
      return monthA.compareTo(monthB);
    });

    return result;
  }

  List<ChartMonthData> _getAllMonthlyPaymentsData(PaymentsEntity paymentsData) {
    List<ChartMonthData> result = [];

    // Process all years in the data
    for (var yearKey in paymentsData.yearlyData.keys) {
      for (var monthData in paymentsData.yearlyData[yearKey]!) {
        // Create label like "May 24" by parsing month and year
        final monthNum = int.tryParse(monthData.month) ?? 0;
        final monthName = _getMonthAbbr(monthNum);
        final yearSuffix = yearKey.substring(
          yearKey.length - 2,
        ); // Get last 2 digits

        result.add(
          ChartMonthData(
            yearKey: yearKey,
            monthKey: monthData.month,
            label: '$monthName $yearSuffix',
            amount: monthData.totalPayment,
          ),
        );
      }
    }

    // Sort by year and month numerically
    result.sort((a, b) {
      int yearA = int.tryParse(a.yearKey) ?? 0;
      int yearB = int.tryParse(b.yearKey) ?? 0;
      if (yearA != yearB) return yearA.compareTo(yearB);

      int monthA = int.tryParse(a.monthKey) ?? 0;
      int monthB = int.tryParse(b.monthKey) ?? 0;
      return monthA.compareTo(monthB);
    });

    return result;
  }

  // Get sorted list of all year-month periods in the data
  List<ChartPeriod> _getSalesMonthYearPeriods(SalesEntity salesData) {
    List<ChartPeriod> periods = [];

    for (var yearKey in salesData.yearlyData.keys) {
      for (var monthData in salesData.yearlyData[yearKey]!) {
        periods.add(ChartPeriod(yearKey: yearKey, monthKey: monthData.month));
      }
    }

    // Sort by year and month
    periods.sort((a, b) {
      int yearA = int.tryParse(a.yearKey) ?? 0;
      int yearB = int.tryParse(b.yearKey) ?? 0;
      if (yearA != yearB) return yearA.compareTo(yearB);

      int monthA = int.tryParse(a.monthKey) ?? 0;
      int monthB = int.tryParse(b.monthKey) ?? 0;
      return monthA.compareTo(monthB);
    });

    return periods;
  }

  // Get sorted list of all year-month periods in the data
  List<ChartPeriod> _getPaymentsMonthYearPeriods(PaymentsEntity paymentData) {
    List<ChartPeriod> periods = [];

    for (var yearKey in paymentData.yearlyData.keys) {
      for (var monthData in paymentData.yearlyData[yearKey]!) {
        periods.add(ChartPeriod(yearKey: yearKey, monthKey: monthData.month));
      }
    }

    // Sort by year and month
    periods.sort((a, b) {
      int yearA = int.tryParse(a.yearKey) ?? 0;
      int yearB = int.tryParse(b.yearKey) ?? 0;
      if (yearA != yearB) return yearA.compareTo(yearB);

      int monthA = int.tryParse(a.monthKey) ?? 0;
      int monthB = int.tryParse(b.monthKey) ?? 0;
      return monthA.compareTo(monthB);
    });

    return periods;
  }

  List<ChartWeekData> _getSalesWeeklyDataForPeriod(
    SalesEntity salesData,
    ChartPeriod period,
  ) {
    if (salesData.yearlyData.containsKey(period.yearKey)) {
      final monthList =
          salesData.yearlyData[period.yearKey]!
              .where((m) => m.month == period.monthKey)
              .toList();

      if (monthList.isNotEmpty) {
        return monthList.first.weeks
            .map((w) => ChartWeekData(weekLabel: w.week, amount: w.amount))
            .toList();
      }
    }
    return [];
  }

  List<ChartWeekData> _getPaymentsWeeklyDataForPeriod(
    PaymentsEntity paymentData,
    ChartPeriod period,
  ) {
    if (paymentData.yearlyData.containsKey(period.yearKey)) {
      final monthList =
          paymentData.yearlyData[period.yearKey]!
              .where((m) => m.month == period.monthKey)
              .toList();

      if (monthList.isNotEmpty) {
        return monthList.first.weeks
            .map((w) => ChartWeekData(weekLabel: w.week, amount: w.amount))
            .toList();
      }
    }
    return [];
  }

  double _getMaxValue(List<double> values) {
    if (values.isEmpty) return 100000;
    return values.reduce((max, value) => max > value ? max : value);
  }

  String _getMonthAbbr(int month) {
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];
    if (month < 1 || month > 12) return '';
    return months[month - 1];
  }

  // Helper method to track analytics events
  void _trackEvent(
    String eventName, [
    Map<String, Object> parameters = const {},
  ]) {
    // Add common parameters
    final eventParams = {
      'screen': 'DashboardScreen',
      'component': 'SalesOverviewChart',
      'timestamp': DateTime.now().toIso8601String(),
      ...parameters,
    };

    _analyticsService.logEvent(
      name: 'sales_chart_$eventName',
      parameters: eventParams,
    );
  }

  // Helper to get filter name from index
  String _getFilterName(int index) {
    switch (index) {
      case 0:
        return 'all';
      case 1:
        return 'current_month';
      case 2:
        return 'last_month';
      default:
        return 'unknown';
    }
  }

  // Helper method to format large amounts in a more compact way
  String _formatLargeAmount(double amount) {
    if (amount >= 100000) {
      // Format as lakhs (divide by 100000 and show with L suffix)
      final lakhs = amount / 100000;
      return '${CurrencyFormatter.formatAsINR(lakhs, decimalPlaces: 2)}L';
    } else if (amount >= 1000) {
      // Format as thousands (divide by 1000 and show with K suffix)
      final thousands = amount / 1000;
      return '${CurrencyFormatter.formatAsINR(thousands, decimalPlaces: 2)}K';
    } else {
      // Use regular formatting for smaller amounts
      return CurrencyFormatter.formatAsINR(amount, decimalPlaces: 0);
    }
  }
}

// Helper classes for chart data
class ChartMonthData {
  final String yearKey;
  final String monthKey;
  final String label;
  final double amount;

  ChartMonthData({
    required this.yearKey,
    required this.monthKey,
    required this.label,
    required this.amount,
  });
}

class ChartWeekData {
  final String weekLabel;
  final double amount;

  ChartWeekData({required this.weekLabel, required this.amount});
}

class ChartPeriod {
  final String yearKey;
  final String monthKey;

  ChartPeriod({required this.yearKey, required this.monthKey});
}
