#!/usr/bin/env dart

import 'dart:io';
import 'manual_testing/payment_test_utils.dart';

/// Quick test runner for Zoho Payment API
/// 
/// Usage:
/// dart test/run_payment_tests.dart [options]
/// 
/// Options:
/// --env=<environment>    Set environment (dev, staging, production)
/// --token=<auth_token>   Set authentication token
/// --quick               Run quick tests only
/// --comprehensive       Run comprehensive test suite
/// --performance         Run performance tests
/// --help                Show this help message

void main(List<String> arguments) async {
  print('🚀 Zoho Payment API Test Runner');
  print('================================');

  // Parse command line arguments
  final args = _parseArguments(arguments);

  if (args['help'] == true) {
    _showHelp();
    return;
  }

  // Initialize test environment
  final environment = args['env'] ?? 'dev';
  final authToken = args['token'];
  
  print('Environment: $environment');
  if (authToken != null) {
    print('Using provided auth token');
  } else {
    print('⚠️  No auth token provided. Some tests may fail.');
  }
  print('');

  try {
    // Initialize test utilities
    PaymentTestUtils.initialize(
      authToken: authToken,
    );

    // Print test configuration
    PaymentTestUtils.printTestConfiguration();
    print('');

    // Run tests based on arguments
    if (args['quick'] == true) {
      await _runQuickTests();
    } else if (args['comprehensive'] == true) {
      await _runComprehensiveTests();
    } else if (args['performance'] == true) {
      await _runPerformanceTests();
    } else {
      await _runDefaultTests();
    }

    print('');
    print('✅ All tests completed successfully!');
    print('📊 Check test_reports/ directory for detailed reports');

  } catch (e) {
    print('');
    print('❌ Tests failed with error: $e');
    exit(1);
  }
}

/// Parse command line arguments
Map<String, dynamic> _parseArguments(List<String> arguments) {
  final args = <String, dynamic>{};

  for (final arg in arguments) {
    if (arg == '--help') {
      args['help'] = true;
    } else if (arg == '--quick') {
      args['quick'] = true;
    } else if (arg == '--comprehensive') {
      args['comprehensive'] = true;
    } else if (arg == '--performance') {
      args['performance'] = true;
    } else if (arg.startsWith('--env=')) {
      args['env'] = arg.substring(6);
    } else if (arg.startsWith('--token=')) {
      args['token'] = arg.substring(8);
    }
  }

  return args;
}

/// Show help message
void _showHelp() {
  print('''
Zoho Payment API Test Runner

Usage:
  dart test/run_payment_tests.dart [options]

Options:
  --env=<environment>    Set environment (dev, staging, production)
                        Default: dev
  
  --token=<auth_token>   Set authentication token for API calls
                        Required for most tests
  
  --quick               Run quick tests only (basic functionality)
                        Duration: ~30 seconds
  
  --comprehensive       Run comprehensive test suite (all scenarios)
                        Duration: ~5 minutes
  
  --performance         Run performance tests only
                        Duration: ~2 minutes
  
  --help                Show this help message

Examples:
  # Quick test with staging environment
  dart test/run_payment_tests.dart --env=staging --token=your_token --quick
  
  # Comprehensive test suite
  dart test/run_payment_tests.dart --comprehensive --token=your_token
  
  # Performance testing only
  dart test/run_payment_tests.dart --performance --token=your_token

Environment Variables:
  You can also set environment variables:
  export PAYMENT_API_TOKEN=your_token
  export PAYMENT_ENV=staging
  dart test/run_payment_tests.dart

Notes:
  - Ensure your backend server is running
  - Use test/sandbox credentials only
  - Check network connectivity before running tests
  - Review generated reports in test_reports/ directory
''');
}

/// Run quick tests (basic functionality)
Future<void> _runQuickTests() async {
  print('🏃‍♂️ Running quick tests...');
  print('');

  try {
    // Test 1: Create payment session
    print('1️⃣ Testing payment session creation...');
    final sessionResponse = await PaymentTestUtils.testCreatePaymentSession();
    final sessionId = sessionResponse['data']['payment_session_id'] as String;
    print('   ✅ Session created: $sessionId');

    // Test 2: Check payment status
    print('2️⃣ Testing payment status check...');
    await PaymentTestUtils.testCheckPaymentStatus(sessionId);
    print('   ✅ Status check successful');

    // Test 3: Basic error handling
    print('3️⃣ Testing basic error handling...');
    try {
      await PaymentTestUtils.testCheckPaymentStatus('INVALID_SESSION');
    } catch (e) {
      print('   ✅ Error handling working correctly');
    }

    print('');
    print('🎉 Quick tests completed successfully!');

  } catch (e) {
    print('❌ Quick tests failed: $e');
    rethrow;
  }
}

/// Run comprehensive test suite
Future<void> _runComprehensiveTests() async {
  print('🔬 Running comprehensive test suite...');
  print('');

  await PaymentTestUtils.runComprehensiveTests();
  await PaymentTestUtils.generateTestReport();
}

/// Run performance tests only
Future<void> _runPerformanceTests() async {
  print('⚡ Running performance tests...');
  print('');

  await PaymentTestUtils.testPerformance();
}

/// Run default tests (moderate coverage)
Future<void> _runDefaultTests() async {
  print('🧪 Running default test suite...');
  print('');

  try {
    // Test 1: Payment session creation
    print('1️⃣ Testing payment session creation...');
    final sessionResponse = await PaymentTestUtils.testCreatePaymentSession();
    final sessionId = sessionResponse['data']['payment_session_id'] as String;
    print('   ✅ Session created: $sessionId');

    // Test 2: Payment status checking
    print('2️⃣ Testing payment status check...');
    await PaymentTestUtils.testCheckPaymentStatus(sessionId);
    print('   ✅ Status check successful');

    // Test 3: Payment verification
    print('3️⃣ Testing payment verification...');
    await PaymentTestUtils.testVerifyPayment(sessionId);
    print('   ✅ Verification successful');

    // Test 4: Error scenarios
    print('4️⃣ Testing error scenarios...');
    await PaymentTestUtils.testErrorScenarios();
    print('   ✅ Error handling verified');

    // Test 5: Response validation
    print('5️⃣ Testing response validation...');
    final isValid = PaymentTestUtils.validatePaymentSessionResponse(sessionResponse);
    if (isValid) {
      print('   ✅ Response validation passed');
    } else {
      throw Exception('Response validation failed');
    }

    print('');
    print('🎉 Default tests completed successfully!');

  } catch (e) {
    print('❌ Default tests failed: $e');
    rethrow;
  }
}

/// Check if required environment variables are set
bool _checkEnvironment() {
  final token = Platform.environment['PAYMENT_API_TOKEN'];
  final env = Platform.environment['PAYMENT_ENV'];

  if (token == null) {
    print('⚠️  PAYMENT_API_TOKEN environment variable not set');
    return false;
  }

  print('Environment: ${env ?? 'default'}');
  print('Token: ${token.substring(0, 10)}...');
  
  return true;
}

/// Create test reports directory if it doesn't exist
void _ensureReportsDirectory() {
  final reportsDir = Directory('test_reports');
  if (!reportsDir.existsSync()) {
    reportsDir.createSync(recursive: true);
    print('📁 Created test_reports directory');
  }
}
