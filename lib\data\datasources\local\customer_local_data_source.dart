import 'package:aquapartner/core/error/exceptions.dart';
import 'package:aquapartner/data/models/customer_model.dart';
import 'package:aquapartner/data/models/objectbox_customer_model.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../objectbox.g.dart';

abstract class CustomerLocalDataSource {
  Future<CustomerModel?> getCustomerByMobileNumber(String mobileNumber);
  Future<CustomerModel?> getCustomerByCustomerId(String customerId);

  Future<void> saveCustomer(CustomerModel customer);
  Future<DateTime?> getLastSyncTime();
  Future<void> updateLastSyncTime(DateTime time);
  Future<void> deleteCustomer();
}

class CustomerLocalDataSourceImpl implements CustomerLocalDataSource {
  final Box<ObjectBoxCustomerModel> customerBox;
  final SharedPreferences sharedPreferences;
  final String lastSyncTimeKey = 'last_customer_sync_time';

  CustomerLocalDataSourceImpl({
    required this.customerBox,
    required this.sharedPreferences,
  });

  @override
  Future<CustomerModel?> getCustomerByMobileNumber(String mobileNumber) async {
    try {
      final query =
          customerBox
              .query(ObjectBoxCustomerModel_.mobileNumber.equals(mobileNumber))
              .build();

      final result = query.findFirst();
      query.close();

      if (result != null) {
        return result.toCustomerModel();
      }
      return null;
    } catch (e) {
      throw CacheException();
    }
  }

  @override
  Future<CustomerModel?> getCustomerByCustomerId(String customerId) async {
    try {
      final query =
          customerBox
              .query(ObjectBoxCustomerModel_.customerId.equals(customerId))
              .build();

      final result = query.findFirst();
      query.close();

      if (result != null) {
        return result.toCustomerModel();
      }
      return null;
    } catch (e) {
      throw CacheException();
    }
  }

  @override
  Future<void> saveCustomer(CustomerModel customer) async {
    try {
      // Check if customer already exists
      final query =
          customerBox
              .query(
                ObjectBoxCustomerModel_.mobileNumber.equals(
                  customer.mobileNumber,
                ),
              )
              .build();

      final existingCustomer = query.findFirst();
      query.close();

      if (existingCustomer != null) {
        // Update existing customer
        final updatedCustomer = existingCustomer.copyWith(
          mongoId: customer.id,
          customerId: customer.customerId,
          customerName: customer.customerName,
          email: customer.email,
          companyName: customer.companyName,
          gstNo: customer.gstNo,
          businessVertical: customer.businessVertical,
          customerCode: customer.customerCode,
          billingAddress: customer.billingAddress,
          isSynced: true,
        );
        customerBox.put(updatedCustomer);
      } else {
        // Add new customer
        final newCustomer = ObjectBoxCustomerModel.fromCustomerModel(
          customer.copyWith(isSynced: true),
        );
        customerBox.put(newCustomer);
      }
    } catch (e) {
      throw CacheException();
    }
  }

  @override
  Future<DateTime?> getLastSyncTime() async {
    try {
      final timeString = sharedPreferences.getString(lastSyncTimeKey);
      if (timeString != null) {
        return DateTime.parse(timeString);
      }
      return null;
    } catch (e) {
      throw CacheException();
    }
  }

  @override
  Future<void> updateLastSyncTime(DateTime time) async {
    try {
      await sharedPreferences.setString(
        lastSyncTimeKey,
        time.toIso8601String(),
      );
    } catch (e) {
      throw CacheException();
    }
  }

  @override
  Future<void> deleteCustomer() async {
    customerBox.removeAll();
  }
}
