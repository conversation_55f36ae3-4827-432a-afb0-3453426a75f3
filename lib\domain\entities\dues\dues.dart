import 'package:equatable/equatable.dart';

class DuesInvoice extends Equatable {
  final String id;
  final String invoiceId;
  final String invoiceNumber;
  final String invoiceDate;
  final String customerId;
  final String customerCode;
  final String customerName;
  final String customerDistrict;
  final String customerState;
  final String customerType;
  final String onBoardedTime;
  final String categoryType;
  final String invoiceType;
  final String retailType;
  final String invoiceStatus;
  final String invoiceRaisedBy;
  final String mode;
  final String businessVertical;
  final String feedCreditLimit;
  final String nonFeedCreditLimit;
  final String harvestCreditLimit;
  final double totalSalesInclTax;
  final double totalSalesExclTax;
  final double tcsAmount;
  final double tdsAmount;
  final double amountAfterTcs;
  final double shippingCharges;
  final String feedAmountAfterTcs;
  final double nonFeedAmountAfterTcs;
  final double creditNoteAmountWithTcs;
  final double payableAmount;
  final double paidAmount;
  final double due;
  final String dueDate;
  final int dueDays;
  final String aging;
  final String aging1;
  final String paymentCredibility;
  final double? totalPurchase;
  final double? totalSales;
  final String? salesTier;
  final double? healthcareSales;
  final double? feedSales;
  final double? chemicalSales;
  final double? equipmentSales;
  final double? harvestSales;
  final double? grossMargin;
  final String? lastPaidDate;

  const DuesInvoice({
    required this.id,
    required this.invoiceId,
    required this.invoiceNumber,
    required this.invoiceDate,
    required this.customerId,
    required this.customerCode,
    required this.customerName,
    required this.customerDistrict,
    required this.customerState,
    required this.customerType,
    required this.onBoardedTime,
    required this.categoryType,
    required this.invoiceType,
    required this.retailType,
    required this.invoiceStatus,
    required this.invoiceRaisedBy,
    required this.mode,
    required this.businessVertical,
    required this.feedCreditLimit,
    required this.nonFeedCreditLimit,
    required this.harvestCreditLimit,
    required this.totalSalesInclTax,
    required this.totalSalesExclTax,
    required this.tcsAmount,
    required this.tdsAmount,
    required this.amountAfterTcs,
    required this.shippingCharges,
    required this.feedAmountAfterTcs,
    required this.nonFeedAmountAfterTcs,
    required this.creditNoteAmountWithTcs,
    required this.payableAmount,
    required this.paidAmount,
    required this.due,
    required this.dueDate,
    required this.dueDays,
    required this.aging,
    required this.aging1,
    required this.paymentCredibility,
    this.totalPurchase,
    this.totalSales,
    this.salesTier,
    this.healthcareSales,
    this.feedSales,
    this.chemicalSales,
    this.equipmentSales,
    this.harvestSales,
    this.grossMargin,
    this.lastPaidDate,
  });

  @override
  List<Object?> get props => [
    id,
    invoiceId,
    invoiceNumber,
    invoiceDate,
    customerId,
    due,
    dueDays,
  ];
}

class DuesAgingGroup extends Equatable {
  final List<DuesInvoice> invoices;
  final double totalPayableAmount;
  final String aging;
  final int dueDays;

  const DuesAgingGroup({
    required this.invoices,
    required this.totalPayableAmount,
    required this.aging,
    required this.dueDays,
  });

  @override
  List<Object?> get props => [invoices, totalPayableAmount, aging, dueDays];
}

class DuesSummary extends Equatable {
  final List<DuesAgingGroup> agingGroups;
  final String customerId;
  final double totalDue;

  const DuesSummary({
    required this.agingGroups,
    required this.customerId,
    required this.totalDue,
  });

  @override
  List<Object?> get props => [agingGroups, customerId, totalDue];
}
