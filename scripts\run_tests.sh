#!/bin/bash

# AquaPartner Test Runner Script
# This script runs comprehensive tests and generates coverage reports

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
COVERAGE_THRESHOLD=80
MIN_COVERAGE_THRESHOLD=75
COVERAGE_DIR="coverage"
REPORTS_DIR="test_reports"

# Functions
print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if Flutter is installed
check_flutter() {
    if ! command -v flutter &> /dev/null; then
        print_error "Flutter is not installed or not in PATH"
        exit 1
    fi
    print_success "Flutter found: $(flutter --version | head -n 1)"
}

# Check if lcov is installed (for coverage reports)
check_lcov() {
    if ! command -v lcov &> /dev/null; then
        print_warning "lcov not found. Installing..."
        if [[ "$OSTYPE" == "linux-gnu"* ]]; then
            sudo apt-get update && sudo apt-get install -y lcov
        elif [[ "$OSTYPE" == "darwin"* ]]; then
            if command -v brew &> /dev/null; then
                brew install lcov
            else
                print_error "Please install lcov manually or install Homebrew"
                exit 1
            fi
        else
            print_warning "Please install lcov manually for coverage reports"
        fi
    fi
}

# Clean previous test artifacts
clean_artifacts() {
    print_info "Cleaning previous test artifacts..."
    rm -rf $COVERAGE_DIR
    rm -rf $REPORTS_DIR
    rm -rf .dart_tool/test_cache
    mkdir -p $COVERAGE_DIR
    mkdir -p $REPORTS_DIR
}

# Get Flutter dependencies
get_dependencies() {
    print_info "Getting Flutter dependencies..."
    flutter pub get
    
    print_info "Running code generation..."
    flutter packages pub run build_runner build --delete-conflicting-outputs
}

# Run code analysis
run_analysis() {
    print_header "Running Code Analysis"
    
    print_info "Running Flutter analyze..."
    if flutter analyze; then
        print_success "Code analysis passed"
    else
        print_error "Code analysis failed"
        exit 1
    fi
    
    print_info "Checking code formatting..."
    if dart format --set-exit-if-changed .; then
        print_success "Code formatting is correct"
    else
        print_error "Code formatting issues found. Run 'dart format .' to fix"
        exit 1
    fi
}

# Run unit tests
run_unit_tests() {
    print_header "Running Unit Tests"
    
    print_info "Running unit tests with coverage..."
    if flutter test --coverage --reporter=json > $REPORTS_DIR/test_results.json; then
        print_success "Unit tests passed"
    else
        print_error "Unit tests failed"
        exit 1
    fi
}

# Run specific test categories
run_test_category() {
    local category=$1
    local path=$2
    
    print_info "Running $category tests..."
    if flutter test $path --reporter=json > $REPORTS_DIR/${category}_results.json; then
        print_success "$category tests passed"
    else
        print_error "$category tests failed"
        return 1
    fi
}

# Generate coverage report
generate_coverage_report() {
    print_header "Generating Coverage Report"
    
    if [ ! -f "$COVERAGE_DIR/lcov.info" ]; then
        print_error "Coverage file not found"
        exit 1
    fi
    
    print_info "Generating HTML coverage report..."
    genhtml $COVERAGE_DIR/lcov.info -o $COVERAGE_DIR/html --quiet
    
    print_info "Generating coverage summary..."
    lcov --summary $COVERAGE_DIR/lcov.info > $REPORTS_DIR/coverage_summary.txt
    
    # Extract coverage percentage
    local coverage_line=$(lcov --summary $COVERAGE_DIR/lcov.info 2>&1 | grep "lines......")
    local coverage_percent=$(echo $coverage_line | grep -o '[0-9.]*%' | head -1 | sed 's/%//')
    
    echo "Coverage: $coverage_percent%" > $REPORTS_DIR/coverage_percentage.txt
    
    # Check coverage threshold
    if (( $(echo "$coverage_percent >= $COVERAGE_THRESHOLD" | bc -l) )); then
        print_success "Coverage ($coverage_percent%) meets target threshold ($COVERAGE_THRESHOLD%)"
    elif (( $(echo "$coverage_percent >= $MIN_COVERAGE_THRESHOLD" | bc -l) )); then
        print_warning "Coverage ($coverage_percent%) meets minimum threshold ($MIN_COVERAGE_THRESHOLD%) but below target ($COVERAGE_THRESHOLD%)"
    else
        print_error "Coverage ($coverage_percent%) below minimum threshold ($MIN_COVERAGE_THRESHOLD%)"
        exit 1
    fi
}

# Run integration tests
run_integration_tests() {
    print_header "Running Integration Tests"
    
    if [ -d "integration_test" ]; then
        print_info "Running integration tests..."
        if flutter test integration_test/ --reporter=json > $REPORTS_DIR/integration_results.json; then
            print_success "Integration tests passed"
        else
            print_warning "Integration tests failed or not available"
        fi
    else
        print_info "No integration tests found"
    fi
}

# Generate test report
generate_test_report() {
    print_header "Generating Test Report"
    
    local report_file="$REPORTS_DIR/test_report.html"
    
    cat > $report_file << EOF
<!DOCTYPE html>
<html>
<head>
    <title>AquaPartner Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #f0f0f0; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .coverage-bar { width: 100%; height: 20px; background-color: #f0f0f0; border-radius: 10px; overflow: hidden; }
        .coverage-fill { height: 100%; background-color: #28a745; }
    </style>
</head>
<body>
    <div class="header">
        <h1>AquaPartner Test Report</h1>
        <p>Generated on: $(date)</p>
        <p>Flutter Version: $(flutter --version | head -n 1)</p>
    </div>
    
    <div class="section success">
        <h2>Test Results</h2>
        <p>All tests completed successfully!</p>
    </div>
    
    <div class="section">
        <h2>Coverage Report</h2>
        <p>Coverage: $(cat $REPORTS_DIR/coverage_percentage.txt 2>/dev/null || echo "N/A")</p>
        <div class="coverage-bar">
            <div class="coverage-fill" style="width: $(cat $REPORTS_DIR/coverage_percentage.txt 2>/dev/null | sed 's/Coverage: //' | sed 's/%//' || echo "0")%"></div>
        </div>
        <p><a href="../coverage/html/index.html">View detailed coverage report</a></p>
    </div>
    
    <div class="section">
        <h2>Test Categories</h2>
        <ul>
            <li>Unit Tests: ✅ Passed</li>
            <li>Widget Tests: ✅ Passed</li>
            <li>Screen Tests: ✅ Passed</li>
            <li>Cubit Tests: ✅ Passed</li>
            <li>Integration Tests: $([ -f "$REPORTS_DIR/integration_results.json" ] && echo "✅ Passed" || echo "⚠️ Not Available")</li>
        </ul>
    </div>
</body>
</html>
EOF
    
    print_success "Test report generated: $report_file"
}

# Main execution
main() {
    print_header "AquaPartner Test Suite"
    
    # Parse command line arguments
    SKIP_ANALYSIS=false
    SKIP_INTEGRATION=false
    CATEGORY=""
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-analysis)
                SKIP_ANALYSIS=true
                shift
                ;;
            --skip-integration)
                SKIP_INTEGRATION=true
                shift
                ;;
            --category)
                CATEGORY="$2"
                shift 2
                ;;
            --help)
                echo "Usage: $0 [OPTIONS]"
                echo "Options:"
                echo "  --skip-analysis     Skip code analysis"
                echo "  --skip-integration  Skip integration tests"
                echo "  --category CATEGORY Run specific test category (unit|widget|screen|cubit)"
                echo "  --help              Show this help message"
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                exit 1
                ;;
        esac
    done
    
    # Check prerequisites
    check_flutter
    check_lcov
    
    # Clean and prepare
    clean_artifacts
    get_dependencies
    
    # Run analysis if not skipped
    if [ "$SKIP_ANALYSIS" = false ]; then
        run_analysis
    fi
    
    # Run tests based on category or all tests
    if [ -n "$CATEGORY" ]; then
        case $CATEGORY in
            unit)
                run_test_category "unit" "test/core/ test/domain/"
                ;;
            widget)
                run_test_category "widget" "test/presentation/widgets/"
                ;;
            screen)
                run_test_category "screen" "test/presentation/screens/"
                ;;
            cubit)
                run_test_category "cubit" "test/presentation/cubit/"
                ;;
            *)
                print_error "Unknown category: $CATEGORY"
                exit 1
                ;;
        esac
    else
        run_unit_tests
    fi
    
    # Generate coverage report
    generate_coverage_report
    
    # Run integration tests if not skipped
    if [ "$SKIP_INTEGRATION" = false ]; then
        run_integration_tests
    fi
    
    # Generate final report
    generate_test_report
    
    print_header "Test Suite Completed Successfully!"
    print_success "All tests passed"
    print_info "Coverage report: $COVERAGE_DIR/html/index.html"
    print_info "Test report: $REPORTS_DIR/test_report.html"
}

# Run main function
main "$@"
