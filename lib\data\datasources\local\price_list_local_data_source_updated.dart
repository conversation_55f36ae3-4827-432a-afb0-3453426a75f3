import '../../../core/error/exceptions.dart';
import '../../../core/utils/logger.dart';
import '../../../objectbox.g.dart';
import '../../models/objectbox_price_list_model.dart';
import '../../models/price_list_model.dart';

abstract class PriceListLocalDataSource {
  /// Gets all price lists from local storage
  Future<List<PriceListModel>> getPriceLists();

  /// Gets a specific price list by state
  Future<PriceListModel?> getPriceListByState(String state);

  /// Saves price lists to local storage
  Future<void> savePriceLists(List<PriceListModel> priceLists);

  /// Checks if there are any price lists in local storage
  Future<bool> hasPriceLists();
}

class PriceListLocalDataSourceImpl implements PriceListLocalDataSource {
  final Box<ObjectBoxPriceListModel> priceListBox;
  final AppLogger logger;

  PriceListLocalDataSourceImpl({
    required this.priceListBox,
    required this.logger,
  });

  @override
  Future<bool> hasPriceLists() async {
    try {
      logger.i("Checking if price lists exist in local storage");
      final count = priceListBox.count();
      logger.i("Price list count: $count");
      return count > 0;
    } catch (e) {
      logger.e("Error checking if price lists exist in local storage", e);
      throw CacheException();
    }
  }

  @override
  Future<List<PriceListModel>> getPriceLists() async {
    try {
      final objectBoxModels = priceListBox.getAll();

      if (objectBoxModels.isEmpty) {
        logger.w("No price lists found in local storage");
        return [];
      }

      final priceLists =
          objectBoxModels.map((model) => model.toPriceListModel()).toList();

      logger.i("Found ${priceLists.length} price lists in local storage");
      return priceLists;
    } catch (e) {
      logger.e("Error getting price lists from local storage", e);
      throw CacheException();
    }
  }

  @override
  Future<PriceListModel?> getPriceListByState(String state) async {
    try {
      logger.i("Getting price list by state: $state from local storage");

      final query =
          priceListBox
              .query(ObjectBoxPriceListModel_.state.equals(state))
              .build();

      final results = query.find();
      query.close();

      if (results.isEmpty) {
        logger.w("No price list found with state: $state in local storage");
        return null;
      }

      logger.i("Found price list with state: $state in local storage");
      return results.first.toPriceListModel();
    } catch (e) {
      logger.e("Error getting price list by state from local storage", e);
      throw CacheException();
    }
  }

  @override
  Future<void> savePriceLists(List<PriceListModel> priceLists) async {
    try {
      logger.i("Saving ${priceLists.length} price lists to local storage");

      // Create a map of states to entities for quick lookup
      final priceListMap = {for (var pl in priceLists) pl.state: pl};

      // Get all existing price lists
      final query = priceListBox.query().build();
      final existingModels = query.find();
      query.close();

      // Create maps for updates and inserts
      final modelsToUpdate = <ObjectBoxPriceListModel>[];
      final modelsToInsert = <ObjectBoxPriceListModel>[];

      // Process existing models - update if in the new data
      final processedStates = <String>{};

      for (var existingModel in existingModels) {
        if (priceListMap.containsKey(existingModel.state)) {
          // Update existing model
          final entity = priceListMap[existingModel.state]!;
          final objectBoxModel = ObjectBoxPriceListModel.fromPriceListModel(
            entity,
          );

          // Check if the data has actually changed before updating
          if (existingModel.date != objectBoxModel.date ||
              existingModel.pricesJson != objectBoxModel.pricesJson) {
            logger.i("Updating price list for state: ${existingModel.state}");

            final updatedModel = existingModel.copyWith(
              date: objectBoxModel.date,
              pricesJson: objectBoxModel.pricesJson,
            );

            modelsToUpdate.add(updatedModel);
          } else {
            logger.i(
              "No changes detected for state: ${existingModel.state}, skipping update",
            );
          }

          processedStates.add(existingModel.state);
        }
      }

      // Process new models - insert if not already processed
      for (var entity in priceLists) {
        if (!processedStates.contains(entity.state)) {
          logger.i("Inserting new price list for state: ${entity.state}");
          modelsToInsert.add(
            ObjectBoxPriceListModel.fromPriceListModel(entity),
          );
        }
      }

      // Batch update and insert
      if (modelsToUpdate.isNotEmpty) {
        logger.i("Batch updating ${modelsToUpdate.length} price lists");
        priceListBox.putMany(modelsToUpdate);
      }

      if (modelsToInsert.isNotEmpty) {
        logger.i("Batch inserting ${modelsToInsert.length} price lists");
        priceListBox.putMany(modelsToInsert);
      }

      logger.i("Successfully saved price lists to local storage");
    } catch (e) {
      logger.e("Error saving price lists to local storage", e);
      throw CacheException();
    }
  }
}
