import 'package:objectbox/objectbox.dart';

import '../../domain/entities/scheme/customer_scheme.dart';
import 'support_person_model.dart';

@Entity()
class CustomerSchemeModel {
  @Id()
  int id = 0;

  String customerId;
  String customerName;
  double sales;
  double payment;

  @Backlink('customerScheme')
  final supportPersons = ToMany<SupportPersonModel>();

  CustomerSchemeModel({
    this.id = 0,
    required this.customerId,
    required this.customerName,
    required this.sales,
    required this.payment,
  });

  // Convert from domain entity to model
  factory CustomerSchemeModel.fromEntity(CustomerScheme entity) {
    final model = CustomerSchemeModel(
      customerId: entity.customerId,
      customerName: entity.customerName,
      sales: entity.sales,
      payment: entity.payment,
    );

    // Add support persons
    for (var person in entity.supportPersons) {
      final personModel = SupportPersonModel.fromEntity(person);
      personModel.customerScheme.target = model;
      model.supportPersons.add(personModel);
    }

    return model;
  }

  // Convert from model to domain entity
  CustomerScheme toEntity() {
    return CustomerScheme(
      customerId: customerId,
      customerName: customerName,
      sales: sales,
      payment: payment,
      supportPersons:
          supportPersons.map((personModel) => personModel.toEntity()).toList(),
    );
  }

  // Convert from JSON to model
  factory CustomerSchemeModel.fromJson(Map<String, dynamic> json) {
    final model = CustomerSchemeModel(
      customerId: json['customerId'] ?? '',
      customerName: json['customerName'] ?? '',
      sales: (json['sales'] ?? 0.0).toDouble(),
      payment: (json['payment'] ?? 0.0).toDouble(),
    );

    // Process support persons
    if (json['supportPersons'] != null) {
      final supportPersonsList = json['supportPersons'] as List;
      for (var personJson in supportPersonsList) {
        final supportPerson = SupportPersonModel.fromJson(personJson);
        supportPerson.customerScheme.target = model;
        model.supportPersons.add(supportPerson);
      }
    }

    return model;
  }

  // Convert from model to JSON
  Map<String, dynamic> toJson() {
    return {
      'customerId': customerId,
      'customerName': customerName,
      'sales': sales,
      'payment': payment,
      'supportPersons':
          supportPersons.map((person) => person.toJson()).toList(),
    };
  }
}