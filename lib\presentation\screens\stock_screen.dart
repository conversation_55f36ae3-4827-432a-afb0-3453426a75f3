import 'package:aqua_ui/aqua_ui.dart';
import 'package:aquapartner/core/utils/logger.dart';
import 'package:aquapartner/presentation/widgets/loading_widget.dart';
import 'package:aquapartner/presentation/widgets/styled_generic_table.dart';
import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/entities/smr_report.dart';
import '../cubit/smr_report/smr_report_cubit.dart';
import '../cubit/smr_report/smr_report_state.dart';
import '../../core/mixins/analytics_mixin.dart';

class StockScreen extends StatefulWidget {
  final AppLogger logger = AppLogger();
  StockScreen({super.key});

  @override
  State<StockScreen> createState() => _StockScreenState();
}

class _StockScreenState extends State<StockScreen> with AnalyticsMixin {
  String selectedFilter = 'All';
  List<String> filterOptions = ['All'];

  // Track screen view time
  DateTime? _screenViewStartTime;
  DateTime? _lastInteractionTime;

  // Track user interactions
  int _filterChangeCount = 0;
  int _scrollCount = 0;
  int _productViewCount = 0;

  // Track scroll position
  final ScrollController _scrollController = ScrollController();

  @override
  String get screenName => 'StockScreen';

  @override
  void initState() {
    super.initState();
    _screenViewStartTime = DateTime.now();
    _lastInteractionTime = _screenViewStartTime;

    // Add scroll listener for analytics
    _scrollController.addListener(_onScroll);

    // Track screen view
    trackEvent('stocks_screen_viewed');

    // Load reports with caching enabled
    context.read<SMRReportCubit>().loadSMRReports(forceRefresh: false);

    // Initialize filter options from current state if already loaded
    final currentState = context.read<SMRReportCubit>().state;
    if (currentState is SMRReportLoaded) {
      final statusSet =
          currentState.reports.map((report) => report.status).toSet();
      filterOptions = ['All', ...statusSet];
    }
  }

  @override
  void dispose() {
    // Track total time spent on screen
    _trackScreenDuration();

    // Track final engagement metrics
    _trackEngagementMetrics();

    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _trackScreenDuration() {
    if (_screenViewStartTime != null) {
      final duration = DateTime.now().difference(_screenViewStartTime!);
      trackEvent(
        'stocks_screen_duration',
        params: {
          'duration_ms': duration.inMilliseconds.toString(),
          'duration_seconds': duration.inSeconds.toString(),
        },
      );
    }
  }

  void _trackEngagementMetrics() {
    trackEvent(
      'stocks_screen_engagement',
      params: {
        'filter_change_count': _filterChangeCount.toString(),
        'scroll_count': _scrollCount.toString(),
        'product_view_count': _productViewCount.toString(),
        'time_since_last_interaction':
            _lastInteractionTime != null
                ? DateTime.now()
                    .difference(_lastInteractionTime!)
                    .inSeconds
                    .toString()
                : '0',
        'final_filter': selectedFilter,
      },
    );
  }

  void _onScroll() {
    // Don't track every tiny scroll event - only significant ones
    if (!_scrollController.hasClients) return;

    // Track scroll position as percentage
    final scrollPosition = _scrollController.position.pixels;
    final maxScroll = _scrollController.position.maxScrollExtent;

    if (maxScroll <= 0) return;

    final scrollPercentage = (scrollPosition / maxScroll * 100).round();

    // Only track at 25%, 50%, 75%, and 100% scroll positions
    if (scrollPercentage == 25 ||
        scrollPercentage == 50 ||
        scrollPercentage == 75 ||
        scrollPercentage == 100) {
      _scrollCount++;
      _lastInteractionTime = DateTime.now();

      trackEvent(
        'stocks_scrolled',
        params: {
          'scroll_percentage': scrollPercentage.toString(),
          'scroll_position': scrollPosition.toString(),
          'selected_filter': selectedFilter,
        },
      );
    }
  }

  void _trackFilterChange(String newFilter) {
    _filterChangeCount++;
    _lastInteractionTime = DateTime.now();

    trackUserInteraction(
      'filter_changed',
      'dropdown',
      elementId: newFilter,
      additionalParams: {
        'previous_filter': selectedFilter,
        'new_filter': newFilter,
        'filter_change_count': _filterChangeCount.toString(),
        'time_on_screen_before_change':
            _screenViewStartTime != null
                ? DateTime.now()
                    .difference(_screenViewStartTime!)
                    .inSeconds
                    .toString()
                : '0',
      },
    );
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Ensure filter options are updated when dependencies change
    final currentState = context.read<SMRReportCubit>().state;
    if (currentState is SMRReportLoaded) {
      final statusSet =
          currentState.reports.map((report) => report.status).toSet();
      setState(() {
        filterOptions = ['All', ...statusSet];
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<SMRReportCubit, SMRReportState>(
      listener: (context, state) {
        // Track state changes for analytics
        if (state is SMRReportLoading) {
          trackEvent('stocks_loading');
        } else if (state is SMRReportLoaded) {
          _lastInteractionTime = DateTime.now();

          // Update filter options when data is loaded
          widget.logger.d("updating the filter");
          final statusSet =
              state.reports.map((report) => report.status).toSet();
          setState(() {
            filterOptions = ['All', ...statusSet];
          });

          trackEvent(
            'stocks_loaded',
            params: {
              'report_count': state.reports.length.toString(),
              'status_count': statusSet.length.toString(),
              'is_from_cache': state.isFromCache ? 'true' : 'false',
              'time_to_load':
                  _screenViewStartTime != null
                      ? DateTime.now()
                          .difference(_screenViewStartTime!)
                          .inMilliseconds
                          .toString()
                      : '0',
            },
          );
        } else if (state is SMRReportError) {
          trackEvent('stocks_error', params: {'error_message': state.message});
        }
      },
      builder: (context, state) {
        if (state is SMRReportLoaded) {
          // Filter reports based on selected filter
          final filteredReports =
              selectedFilter == 'All'
                  ? state.reports
                  : state.reports
                      .where((report) => report.status == selectedFilter)
                      .toList();

          return Scaffold(
            backgroundColor: Colors.white,
            body: Padding(
              padding: const EdgeInsets.only(left: 16.0, right: 16, top: 32),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  AquaText.headline(
                    "Stocks",
                    weight: AquaFontWeight.semibold,
                    color: acTextSecondaryColor,
                  ),
                  // Date display with cache indicator
                  Row(
                    children: [
                      AquaText.body(
                        'As of ${_formatDate(state.reports.first.lastDate)}',
                      ),
                      if (state.isFromCache)
                        Padding(
                          padding: const EdgeInsets.only(left: 8.0),
                          child: AquaText.caption(
                            '(cached)',
                            color: Colors.grey,
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: acGrey200),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: DropdownButtonHideUnderline(
                      child: DropdownButton2<String>(
                        value: selectedFilter,
                        isExpanded: true,
                        buttonStyleData: ButtonStyleData(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          padding: EdgeInsets.only(right: 16),
                        ),
                        dropdownStyleData: DropdownStyleData(
                          padding: EdgeInsets.only(right: 0),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            color: Colors.white,
                            border: Border.all(color: acGrey200, width: 1.0),
                          ),
                          elevation: 0,
                          offset: const Offset(0, -5),
                          direction: DropdownDirection.left,
                          maxHeight: 300,
                        ),
                        items:
                            filterOptions.map((String page) {
                              return DropdownMenuItem<String>(
                                value: page,
                                child: Container(
                                  padding: const EdgeInsets.only(
                                    right: 0,
                                    left: 0,
                                  ),
                                  child: AquaText.body(page),
                                ),
                              );
                            }).toList(),
                        onChanged: (String? newValue) {
                          if (newValue != null && newValue != selectedFilter) {
                            _trackFilterChange(newValue);
                            setState(() {
                              selectedFilter = newValue;
                            });
                          }
                        },
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),
                  filteredReports.isNotEmpty
                      ? Expanded(
                        child: SingleChildScrollView(
                          controller: _scrollController,
                          child: Padding(
                            padding: const EdgeInsets.all(0.0),
                            child: StyledGenericTable<SMRReport>(
                              items: filteredReports,
                              showDividers: true,
                              columns: [
                                ColumnConfig<SMRReport>(
                                  title: 'Product Name',
                                  width: 220,
                                  cellBuilder:
                                      (report) => AquaText.body(
                                        report.productName,
                                        weight: AquaFontWeight.semibold,
                                      ),
                                ),
                                ColumnConfig<SMRReport>(
                                  title: 'Quantity',
                                  titleAlignment: Alignment.center,
                                  bodyAlignment: Alignment.centerRight,
                                  width: 90,
                                  cellBuilder:
                                      (report) => AquaText.body(
                                        report.closingBalance.toString(),
                                        weight: AquaFontWeight.semibold,
                                      ),
                                ),
                                ColumnConfig<SMRReport>(
                                  title: 'Sales',
                                  width: 70,
                                  titleAlignment: Alignment.center,
                                  bodyAlignment: Alignment.centerRight,
                                  cellBuilder:
                                      (report) => AquaText.body(
                                        report.sales.toString(),
                                        color: _getSalesColor(report.sales),
                                        weight: AquaFontWeight.medium,
                                      ),
                                ),
                              ],
                              onRowTap: (report) {
                                _productViewCount++;
                                _lastInteractionTime = DateTime.now();

                                trackUserInteraction(
                                  'product_viewed',
                                  'table_row',
                                  elementId: report.productName,
                                  additionalParams: {
                                    'product_name': report.productName,
                                    'product_status': report.status,
                                    'product_quantity':
                                        report.closingBalance.toString(),
                                    'product_sales': report.sales.toString(),
                                    'product_view_count':
                                        _productViewCount.toString(),
                                    'selected_filter': selectedFilter,
                                    'time_on_screen_before_view':
                                        _screenViewStartTime != null
                                            ? DateTime.now()
                                                .difference(
                                                  _screenViewStartTime!,
                                                )
                                                .inSeconds
                                                .toString()
                                            : '0',
                                  },
                                );
                              },
                            ),
                          ),
                        ),
                      )
                      : Center(
                        child: AquaText.body(
                          "No stock for the selected filters",
                        ),
                      ),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          );
        } else if (state is SMRReportError) {
          return Scaffold(
            backgroundColor: Colors.white,
            body: Padding(
              padding: const EdgeInsets.only(left: 16.0, right: 16, top: 32),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  AquaText.headline(
                    "Stocks",
                    weight: AquaFontWeight.semibold,
                    color: acTextSecondaryColor,
                  ),
                  const SizedBox(height: 24),
                  Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.error_outline, size: 64, color: Colors.red),
                        const SizedBox(height: 16),
                        AquaText.body(
                          state.message,
                          textAlign: TextAlign.center,
                          color: Colors.red,
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () {
                            context.read<SMRReportCubit>().loadSMRReports(
                              forceRefresh: true,
                            );
                          },
                          child: AquaText.body('Retry'),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        }
        return const LoadingWidget(message: "Loading Your Stocks...");
      },
    );
  }

  // Helper method for formatting dates
  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}-${date.month.toString().padLeft(2, '0')}-${date.year}';
  }

  Color _getSalesColor(int sales) {
    return sales > 0 ? acGreenColor : acRedColor;
  }
}
