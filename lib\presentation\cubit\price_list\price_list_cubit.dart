import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/error/failures.dart';
import '../../../core/utils/logger.dart';
import '../../../domain/entities/price_list.dart';
import '../../../domain/usecases/price_list_usecases.dart';
import 'price_list_state.dart';

class PriceListCubit extends Cubit<PriceListState> {
  final GetPriceListsUseCase getPriceListsUseCase;
  final SyncPriceListsUseCase syncPriceListsUseCase;
  final AppLogger logger;

  bool _isBackgroundSyncing = false;
  static const Duration _cacheStaleThreshold = Duration(hours: 1);

  PriceListCubit({
    required this.getPriceListsUseCase,
    required this.syncPriceListsUseCase,
    required this.logger,
  }) : super(const PriceListState());

  /// Load price lists with a caching-first approach
  /// Always returns cached data immediately if available
  /// and triggers a background sync if needed
  Future<void> loadPriceLists() async {
    if (state.isLoading) return;

    emit(
      state.copyWith(
        status: PriceListStatus.loading,
        errorMessage: null,
        isBackgroundSyncInProgress: false,
      ),
    );

    final result = await getPriceListsUseCase();

    result.fold(
      (failure) {
        logger.e("Failed to load price lists", failure);
        String errorMessage = _getReadableErrorMessage(failure);

        emit(
          state.copyWith(
            status: PriceListStatus.error,
            errorMessage: errorMessage,
          ),
        );
      },
      (priceLists) {
        logger.i("Successfully loaded ${priceLists.length} price lists");

        // Check if list is not empty before accessing first element
        final selectedState = priceLists.isNotEmpty ? priceLists.first : null;

        // Determine if data is from cache and if it's stale
        final isFromCache =
            true; // The repository always returns cached data first
        final isCacheStale = _isCacheStale();

        // Emit a single state update with all changes
        emit(
          state.copyWith(
            status: PriceListStatus.success,
            priceLists: priceLists,
            selectedState: selectedState,
            isFromCache: isFromCache,
            isCacheStale: isCacheStale,
          ),
        );

        // If cache is stale, trigger a background sync
        if (isCacheStale) {
          _checkAndSyncIfNeeded();
        }
      },
    );
  }

  /// Check if the cache is stale based on the last sync time
  bool _isCacheStale() {
    final lastSyncTime = state.lastSyncTime;
    if (lastSyncTime == null) {
      return true; // No sync time means cache is stale
    }

    final now = DateTime.now();
    final difference = now.difference(lastSyncTime);
    return difference > _cacheStaleThreshold;
  }

  /// Check if sync is needed and perform background sync if necessary
  Future<void> _checkAndSyncIfNeeded() async {
    if (_isBackgroundSyncing) {
      return; // Don't start another sync if one is already in progress
    }

    // Update state to indicate background sync is in progress
    emit(state.copyWith(isBackgroundSyncInProgress: true));

    // Perform background sync
    _isBackgroundSyncing = true;
    await _backgroundSync();
    _isBackgroundSyncing = false;

    // Update state to indicate background sync is complete
    emit(state.copyWith(isBackgroundSyncInProgress: false));
  }

  /// Perform a background sync without changing the UI state
  Future<void> _backgroundSync() async {
    try {
      logger.i('Performing background sync of price lists');

      final result = await syncPriceListsUseCase();

      result.fold(
        (failure) {
          logger.e('Error in background sync: $failure');
          // Don't emit error state as this is a background operation
        },
        (priceLists) {
          logger.i('Background sync completed successfully');

          // Check if list is not empty before accessing first element
          final selectedState = state.selectedState;

          // If we have a selected state, try to find it in the new list
          PriceList? newSelectedState;
          if (selectedState != null) {
            newSelectedState = priceLists.firstWhere(
              (priceList) => priceList.state == selectedState.state,
              orElse:
                  () =>
                      priceLists.isNotEmpty ? priceLists.first : selectedState,
            );
          } else if (priceLists.isNotEmpty) {
            newSelectedState = priceLists.first;
          }

          // Update the state with new data
          emit(
            state.copyWith(
              status: PriceListStatus.success,
              priceLists: priceLists,
              selectedState: newSelectedState,
              isFromCache: false,
              isCacheStale: false,
              lastSyncTime: DateTime.now(),
            ),
          );
        },
      );
    } catch (e) {
      logger.e('Error in background sync: $e');
      // Don't emit error state as this is a background operation
      _isBackgroundSyncing = false;
    }
  }

  Future<void> changeState(PriceList newPriceList) async {
    emit(
      state.copyWith(
        selectedState: newPriceList,
        status: PriceListStatus.stateChange,
      ),
    );
  }

  /// Manual sync that shows loading indicators
  Future<void> syncPriceLists() async {
    if (state.isSyncing) return;

    // Store the current selected state to restore it after sync
    final currentSelectedState = state.selectedState;
    final currentSelectedStateName = currentSelectedState?.state;

    emit(
      state.copyWith(
        status: PriceListStatus.syncing,
        errorMessage: null,
        isBackgroundSyncInProgress: true,
      ),
    );

    final result = await syncPriceListsUseCase();

    result.fold(
      (failure) {
        logger.e("Failed to sync price lists", failure);
        String errorMessage = _getReadableErrorMessage(failure);

        // Consistent error handling approach
        final newStatus =
            state.priceLists.isNotEmpty
                ? PriceListStatus.success
                : PriceListStatus.error;

        emit(
          state.copyWith(
            status: newStatus,
            errorMessage: errorMessage,
            isBackgroundSyncInProgress: false,
            isCacheStale: true, // Mark as stale since sync failed
          ),
        );
      },
      (priceLists) {
        logger.i("Successfully synced ${priceLists.length} price lists");

        // Try to preserve the previously selected state if possible
        PriceList? newSelectedState;

        if (currentSelectedStateName != null && priceLists.isNotEmpty) {
          // Try to find the same state in the updated list
          newSelectedState = priceLists.firstWhere(
            (priceList) => priceList.state == currentSelectedStateName,
            orElse: () => priceLists.first,
          );
          logger.i("Restored selected state: ${newSelectedState.state}");
        }
        // If we didn't have a selected state or couldn't find it, select the first one if available
        else if (priceLists.isNotEmpty) {
          newSelectedState = priceLists.first;
          logger.i("Selected first state: ${newSelectedState.state}");
        }

        emit(
          state.copyWith(
            status: PriceListStatus.success,
            priceLists: priceLists,
            selectedState: newSelectedState,
            isFromCache: false,
            isCacheStale: false,
            isBackgroundSyncInProgress: false,
            lastSyncTime: DateTime.now(),
          ),
        );
      },
    );
  }

  /// Force refresh price lists with pull-to-refresh
  Future<void> refreshPriceLists() async {
    if (state.isRefreshing || state.isSyncing) return;

    // Emit refreshing state but keep current data visible
    emit(
      state.copyWith(
        status: PriceListStatus.refreshing,
        isBackgroundSyncInProgress: true,
      ),
    );

    // Force sync
    await syncPriceLists();
  }

  /// Force invalidate the cache and trigger a sync
  Future<void> invalidateAndSync() async {
    // Mark cache as stale
    emit(state.copyWith(isCacheStale: true));

    // Perform a full sync
    await syncPriceLists();
  }

  String _getReadableErrorMessage(Failure failure) {
    if (failure is NetworkFailure) {
      return "No internet connection. Please check your connection and try again.";
    } else if (failure is ServerFailure) {
      return "Check your internet connection, Please try again later.";
    } else if (failure is CacheFailure) {
      return "No data available. Please connect to the internet to download price lists.";
    } else {
      return "An unexpected error occurred. Please try again.";
    }
  }
}
