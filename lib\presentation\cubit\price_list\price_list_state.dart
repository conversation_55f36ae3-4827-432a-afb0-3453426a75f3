import 'package:equatable/equatable.dart';

import '../../../domain/entities/price_list.dart';

enum PriceListStatus {
  initial,
  loading,
  success,
  error,
  syncing,
  stateChange,
  refreshing,
}

class PriceListState extends Equatable {
  final PriceListStatus status;
  final List<PriceList> priceLists;
  final PriceList? selectedState;
  final String? errorMessage;
  final List<String>? _cachedStateNames;
  final bool isFromCache;
  final bool isCacheStale;
  final bool isBackgroundSyncInProgress;
  final DateTime? lastSyncTime;

  const PriceListState({
    this.status = PriceListStatus.initial,
    this.priceLists = const [],
    this.selectedState,
    this.errorMessage,
    List<String>? cachedStateNames,
    this.isFromCache = false,
    this.isCacheStale = false,
    this.isBackgroundSyncInProgress = false,
    this.lastSyncTime,
  }) : _cachedStateNames = cachedStateNames;

  PriceListState copyWith({
    PriceListStatus? status,
    List<PriceList>? priceLists,
    PriceList? selectedState,
    String? errorMessage,
    bool? isFromCache,
    bool? isCacheStale,
    bool? isBackgroundSyncInProgress,
    DateTime? lastSyncTime,
  }) {
    final newPriceLists = priceLists ?? this.priceLists;
    // Only recalculate state names if price lists changed
    final newStateNames =
        (priceLists != null)
            ? newPriceLists.map((e) => e.state).toList()
            : _cachedStateNames;

    return PriceListState(
      status: status ?? this.status,
      priceLists: newPriceLists,
      selectedState: selectedState ?? this.selectedState,
      errorMessage: errorMessage,
      cachedStateNames: newStateNames,
      isFromCache: isFromCache ?? this.isFromCache,
      isCacheStale: isCacheStale ?? this.isCacheStale,
      isBackgroundSyncInProgress:
          isBackgroundSyncInProgress ?? this.isBackgroundSyncInProgress,
      lastSyncTime: lastSyncTime ?? this.lastSyncTime,
    );
  }

  bool get isLoading => status == PriceListStatus.loading;
  bool get isSyncing => status == PriceListStatus.syncing;
  bool get isSuccess => status == PriceListStatus.success;
  bool get isError => status == PriceListStatus.error;
  bool get isRefreshing => status == PriceListStatus.refreshing;
  // Renamed for consistency
  bool get isStateChange => status == PriceListStatus.stateChange;

  // Helper to determine if we should show a loading indicator
  bool get isLoadingOrSyncing => isLoading || isSyncing || isRefreshing;

  List<String> get stateNames =>
      _cachedStateNames ?? priceLists.map((e) => e.state).toList();

  List<PriceList> get allPriceList => priceLists;

  @override
  List<Object?> get props => [
    status,
    priceLists,
    selectedState,
    errorMessage,
    isFromCache,
    isCacheStale,
    isBackgroundSyncInProgress,
    lastSyncTime,
  ];
}
