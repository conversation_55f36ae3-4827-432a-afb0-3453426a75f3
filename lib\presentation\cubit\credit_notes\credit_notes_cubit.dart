// lib/presentation/cubit/credit_notes/credit_notes_cubit.dart
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../domain/services/auth_service.dart';
import '../../../domain/usecases/credit_notes_usecases.dart';
import 'credit_notes_state.dart';

class CreditNotesCubit extends Cubit<CreditNotesState> {
  final GetCreditNotes getCreditNotes;
  final SyncCreditNotes syncCreditNotes;
  final GetLocalCreditNotes getLocalCreditNotes;
  final AuthService authService;

  // Store the last loaded customer ID to refresh data after sync
  String? _currentCustomerId;
  bool _isBackgroundSyncing = false;

  CreditNotesCubit({
    required this.getCreditNotes,
    required this.syncCreditNotes,
    required this.getLocalCreditNotes,
    required this.authService,
  }) : super(CreditNotesInitial());

  /// Load credit notes with a caching-first approach
  /// Always returns cached data immediately if available
  /// and triggers a background sync if needed
  Future<void> loadCreditNotes() async {
    emit(CreditNotesLoading());
    final customerResult = await authService.getCurrentCustomer();

    customerResult.fold(
      (failure) => emit(
        CreditNotesError('Unable to load credit notes, try again later'),
      ),
      (customer) async {
        if (customer == null) {
          emit(CreditNotesError('Customer information not found'));
          return;
        }

        _currentCustomerId = customer.customerId;

        // CACHING-FIRST APPROACH:
        // 1. Always try to get data from local cache first
        await _loadFromCache(customer.customerId);

        // 2. Check if sync is needed in the background
        await _checkAndSyncIfNeeded(customer.customerId);
      },
    );
  }

  /// Load credit notes from local cache
  Future<void> _loadFromCache(String customerId) async {
    final result = await getLocalCreditNotes(customerId);

    result.fold(
      (failure) {
        // If local data not available, try remote
        emit(CreditNotesError('Unable to load credit notes, try again later'));
        syncNotes();
      },
      (notes) {
        if (notes.isEmpty) {
          // If no local data, try remote
          syncNotes();
          return;
        }

        // Emit loaded state with cached data
        emit(
          CreditNotesLoaded(
            notes,
            isFromCache: true,
            isCacheStale: false, // We don't know yet, will update if needed
            isBackgroundSyncInProgress: false,
          ),
        );
      },
    );
  }

  /// Check if sync is needed and perform background sync if necessary
  Future<void> _checkAndSyncIfNeeded(String customerId) async {
    if (_isBackgroundSyncing) {
      return; // Don't start another sync if one is already in progress
    }

    // For now, we'll always sync in the background since we don't have a specific
    // check for credit notes sync needed. In a real implementation, you would check
    // if sync is needed based on last sync time.

    // Update state to indicate background sync is in progress
    if (state is CreditNotesLoaded) {
      emit(
        (state as CreditNotesLoaded).copyWith(
          isCacheStale: true,
          isBackgroundSyncInProgress: true,
        ),
      );
    }

    // Perform background sync
    _isBackgroundSyncing = true;
    await _backgroundSync(customerId);
    _isBackgroundSyncing = false;

    // Update state to indicate background sync is complete
    if (state is CreditNotesLoaded) {
      emit(
        (state as CreditNotesLoaded).copyWith(
          isBackgroundSyncInProgress: false,
        ),
      );
    }
  }

  /// Perform a background sync without showing loading indicators
  Future<void> _backgroundSync(String customerId) async {
    try {
      // Sync credit notes
      final syncResult = await syncCreditNotes(customerId);

      syncResult.fold(
        (failure) {
          // Don't emit error state as this is a background operation
        },
        (_) async {
          // After successful sync, load the updated local data
          final localResult = await getLocalCreditNotes(customerId);

          localResult.fold(
            (failure) {
              // Don't emit error state as this is a background operation
            },
            (notes) {
              // Update the state with new data
              emit(
                CreditNotesLoaded(
                  notes,
                  isFromCache: false,
                  isCacheStale: false,
                  isBackgroundSyncInProgress: false,
                ),
              );
            },
          );
        },
      );
    } catch (e) {
      // Don't emit error state as this is a background operation
      _isBackgroundSyncing = false;
    }
  }

  /// Manual sync that shows loading indicators
  Future<void> syncNotes() async {
    if (_currentCustomerId == null) {
      emit(const CreditNotesError("Cannot sync without a customer context."));
      return;
    }

    emit(CreditNotesSyncing()); // Indicate sync is starting
    final syncResult = await syncCreditNotes(_currentCustomerId!);

    // After sync attempt (success or failure), reload data from local source
    await syncResult.fold(
      (failure) async {
        // Even if sync fails, load local data to show what's available
        final localResult = await getLocalCreditNotes(_currentCustomerId!);

        localResult.fold(
          (localFailure) =>
              emit(CreditNotesError("Sync failed, Error loading local data.")),
          (notes) => emit(
            CreditNotesLoaded(
              notes,
              isFromCache: true,
              isCacheStale: true, // Mark as stale since sync failed
              isBackgroundSyncInProgress: false,
            ),
          ),
        );
      },
      (_) async {
        // Sync successful, now load the updated local data
        final localResult = await getLocalCreditNotes(_currentCustomerId!);

        localResult.fold(
          (failure) => emit(
            CreditNotesError(
              "Sync successful, but failed to reload local data",
            ),
          ),
          (notes) => emit(
            CreditNotesLoaded(
              notes,
              isFromCache: false,
              isCacheStale: false,
              isBackgroundSyncInProgress: false,
            ),
          ),
        );
      },
    );
  }

  /// Force invalidate the cache and trigger a sync
  Future<void> invalidateAndSync() async {
    if (_currentCustomerId == null) {
      emit(CreditNotesError('Customer information not available'));
      return;
    }

    // Mark cache as stale if we have loaded data
    if (state is CreditNotesLoaded) {
      emit((state as CreditNotesLoaded).copyWith(isCacheStale: true));
    }

    // Perform a full sync
    await syncNotes();
  }
}
