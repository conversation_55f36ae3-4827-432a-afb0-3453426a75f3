import 'package:dio/dio.dart';
import '../services/app_check_service.dart';
import '../utils/logger.dart';
import 'package:flutter/foundation.dart';

/// An interceptor for Dio HTTP client that adds Firebase App Check tokens
/// to outgoing requests for enhanced security.
class AppCheckInterceptor extends Interceptor {
  final AppCheckService _appCheckService;
  final AppLogger _logger;

  AppCheckInterceptor(this._appCheckService, this._logger);

  @override
  Future<void> onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    try {
      // Skip App Check for payment endpoints to avoid blocking critical payment flows
      if (_isPaymentEndpoint(options.path)) {
        _logger.i('Skipping App Check for payment endpoint: ${options.path}');
        handler.next(options);
        return;
      }

      // Get App Check token for non-payment endpoints
      final token = await _appCheckService.getToken();

      if (token != null) {
        // Add token to request headers
        options.headers['X-Firebase-AppCheck'] = token;
        _logger.d('Added App Check token to request: ${options.uri}');
      } else {
        _logger.w('No App Check token available for request: ${options.uri}');

        // In debug mode, we can add a fake header to avoid backend rejections
        if (kDebugMode) {
          options.headers['X-Firebase-AppCheck-Debug'] = 'debug-mode';
          _logger.d('Added debug App Check header');
        }
      }
    } catch (e) {
      // Log error but don't block the request
      _logger.e('Error adding App Check token to request: ${e.toString()}');

      // In debug mode, add debug header as fallback
      if (kDebugMode) {
        options.headers['X-Firebase-AppCheck-Debug'] = 'debug-fallback';
      }
    }

    // Continue with the request
    handler.next(options);
  }

  /// Check if the request path is a payment-related endpoint
  bool _isPaymentEndpoint(String path) {
    final paymentPaths = [
      '/zoho/payments/',
      '/zoho/webhooks/payment',
      '/payments/',
      '/payment-session',
    ];

    return paymentPaths.any((paymentPath) => path.contains(paymentPath));
  }
}
