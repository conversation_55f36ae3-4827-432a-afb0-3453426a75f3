class SalesOrderItem {
  final int id;
  final String itemId;
  final String salesOrderId;
  final String productId;
  final DateTime createdTime;
  final String entityDiscountPercent;
  final String hsnSac;
  final String invoicedQuantityCancelled;
  final String itemName;
  final double itemPrice;
  final DateTime lastModifiedTime;
  final String manuallyFulfilledQuantity;
  final String nonPackageQuantity;
  final String placeOfSupply;
  final String quantityDelivered;
  final String quantityDropshipped;
  final String quantityPacked;
  final String salesVertices;
  final String sno;
  final double total;

  SalesOrderItem({
    this.id = 0,
    required this.itemId,
    required this.salesOrderId,
    required this.productId,
    required this.createdTime,
    required this.entityDiscountPercent,
    required this.hsnSac,
    required this.invoicedQuantityCancelled,
    required this.itemName,
    required this.itemPrice,
    required this.lastModifiedTime,
    required this.manuallyFulfilledQuantity,
    required this.nonPackageQuantity,
    required this.placeOfSupply,
    required this.quantityDelivered,
    required this.quantityDropshipped,
    required this.quantityPacked,
    required this.salesVertices,
    required this.sno,
    required this.total,
  });

  // Create a copy with updated fields
  SalesOrderItem copyWith({
    int? id,
    String? itemId,
    String? salesOrderId,
    String? productId,
    DateTime? createdTime,
    String? entityDiscountPercent,
    String? hsnSac,
    String? invoicedQuantityCancelled,
    String? itemName,
    double? itemPrice,
    DateTime? lastModifiedTime,
    String? manuallyFulfilledQuantity,
    String? nonPackageQuantity,
    String? placeOfSupply,
    String? quantityDelivered,
    String? quantityDropshipped,
    String? quantityPacked,
    String? salesVertices,
    String? sno,
    double? total,
  }) {
    return SalesOrderItem(
      id: id ?? this.id,
      itemId: itemId ?? this.itemId,
      salesOrderId: salesOrderId ?? this.salesOrderId,
      productId: productId ?? this.productId,
      createdTime: createdTime ?? this.createdTime,
      entityDiscountPercent:
          entityDiscountPercent ?? this.entityDiscountPercent,
      hsnSac: hsnSac ?? this.hsnSac,
      invoicedQuantityCancelled:
          invoicedQuantityCancelled ?? this.invoicedQuantityCancelled,
      itemName: itemName ?? this.itemName,
      itemPrice: itemPrice ?? this.itemPrice,
      lastModifiedTime: lastModifiedTime ?? this.lastModifiedTime,
      manuallyFulfilledQuantity:
          manuallyFulfilledQuantity ?? this.manuallyFulfilledQuantity,
      nonPackageQuantity: nonPackageQuantity ?? this.nonPackageQuantity,
      placeOfSupply: placeOfSupply ?? this.placeOfSupply,
      quantityDelivered: quantityDelivered ?? this.quantityDelivered,
      quantityDropshipped: quantityDropshipped ?? this.quantityDropshipped,
      quantityPacked: quantityPacked ?? this.quantityPacked,
      salesVertices: salesVertices ?? this.salesVertices,
      sno: sno ?? this.sno,
      total: total ?? this.total,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SalesOrderItem &&
          runtimeType == other.runtimeType &&
          itemId == other.itemId;

  @override
  int get hashCode => itemId.hashCode;

  @override
  String toString() {
    return 'SalesOrderItem{id: $id, itemId: $itemId, itemName: $itemName, itemPrice: $itemPrice}';
  }
}
