#!/bin/bash

echo "Installing Flutter dependencies..."
flutter pub get

echo "Building iOS app..."
cd ios
pod install
cd ..

echo "Opening the project in Xcode..."
open ios/Runner.xcworkspace

echo "==========================================="
echo "PROJECT OPENED IN XCODE"
echo "Please follow these steps to run on your connected iPhone:"
echo "1. In Xcode, select your connected iPhone from the device dropdown at the top"
echo "2. Select the Runner target"
echo "3. Go to Signing & Capabilities tab and ensure:"
echo "   - Your team is selected"
echo "   - Bundle ID is set to blue.aquaconnect.partnerselfservice"
echo "   - Ensure the entitlements file is properly linked"
echo "4. Click the Play button to build and run on your device"
echo "==========================================="
