import 'dart:convert';

import 'package:aquapartner/core/network/api_client.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/error/exceptions.dart';
import '../../../core/utils/logger.dart';
import '../../models/dashboard/dashboard_model.dart';

abstract class DashboardRemoteDataSource {
  Future<DashboardModel?> getDashboard(String customerId);
}

class DashboardRemoteDataSourceImpl implements DashboardRemoteDataSource {
  final ApiClient apiClient;
  final AppLogger logger;

  // Add a timeout for operations
  final Duration operationTimeout = const Duration(seconds: 15);

  DashboardRemoteDataSourceImpl({
    required this.apiClient,
    required this.logger,
  });

  @override
  Future<DashboardModel?> getDashboard(String customerId) async {
    try {
      logger.i("Fetching dashboard data from API for customer: $customerId");
      final response = await apiClient.get(
        '${AppConstants.baseUrl}/dashboard/$customerId',
      );

      if (response.statusCode == 200) {
        // The response data is already a string, so we don't need to decode it first
        // Check if the response data is a string or already parsed JSON
        final dynamic responseData = response.data;
        Map<String, dynamic> data;

        if (responseData is String) {
          data = jsonDecode(responseData);
        } else if (responseData is Map<String, dynamic>) {
          data = responseData;
        } else {
          logger.e("Unexpected response format: ${responseData.runtimeType}");
          throw ServerException();
        }

        // Check if result is null or empty
        if (data.isEmpty) {
          return null;
        }

        // Create dashboard model from response with sync status
        final DashboardModel dashboardModel = DashboardModel.fromJson(data);

        return dashboardModel;
      }
      return null;
    } catch (e) {
      logger.i(e.toString());
      throw ServerException();
    }
  }
}
