// ignore_for_file: unused_field

import 'package:aquapartner/core/network/performance_interceptor.dart';
import 'package:aquapartner/core/services/performance_monitoring_service.dart';
import 'package:aquapartner/core/utils/logger.dart';
import 'package:dio/dio.dart';
import 'package:firebase_performance/firebase_performance.dart';
import 'package:flutter_test/flutter_test.dart';

// Custom logger mock that captures error calls
class TestAppLogger implements AppLogger {
  List<String> errorMessages = [];
  List<Object> errorObjects = [];
  List<String> infoMessages = [];
  List<String> debugMessages = [];
  List<String> warningMessages = [];

  @override
  void e(String message, [dynamic error, StackTrace? stackTrace]) {
    errorMessages.add(message);
    if (error != null) errorObjects.add(error);
  }

  @override
  void i(String message) {
    infoMessages.add(message);
  }

  @override
  void d(String message) {
    debugMessages.add(message);
  }

  @override
  void w(String message) {
    warningMessages.add(message);
  }

  @override
  void enableFirebaseVerboseLogging() {
    // No-op for testing
  }
}

// Custom HttpMetric implementation for testing
class TestHttpMetric implements HttpMetric {
  bool _started = false;
  bool _stopped = false;
  int? _requestPayloadSize;
  int? _responsePayloadSize;
  String? _responseContentType;

  bool get isStarted => _started;
  bool get isStopped => _stopped;

  @override
  Future<void> start() async {
    _started = true;
  }

  @override
  Future<void> stop() async {
    _stopped = true;
  }

  @override
  set httpResponseCode(int? value) {}

  @override
  set requestPayloadSize(int? value) {
    _requestPayloadSize = value;
  }

  @override
  set responsePayloadSize(int? value) {
    _responsePayloadSize = value;
  }

  @override
  set responseContentType(String? value) {
    _responseContentType = value;
  }

  // Required to implement the interface
  @override
  dynamic noSuchMethod(Invocation invocation) => super.noSuchMethod(invocation);
}

// Custom PerformanceMonitoringService implementation for testing
class TestPerformanceService implements PerformanceMonitoringService {
  Map<String, TestHttpMetric> metrics = {};
  Exception? exceptionToThrow;

  @override
  HttpMetric? startHttpMetric(String url, HttpMethod httpMethod) {
    if (exceptionToThrow != null) {
      throw exceptionToThrow!;
    }

    final metric = TestHttpMetric();
    metrics[url] = metric;
    metric.start();
    return metric;
  }

  @override
  Future<void> stopHttpMetric(
    HttpMetric? metric, {
    int? responseCode,
    int? requestSize,
    int? responseSize,
    String? contentType,
  }) async {
    if (metric == null) return;

    final testMetric = metric as TestHttpMetric;
    if (responseCode != null) {
      testMetric.httpResponseCode = responseCode;
    }
    if (requestSize != null) {
      testMetric.requestPayloadSize = requestSize;
    }
    if (responseSize != null) {
      testMetric.responsePayloadSize = responseSize;
    }
    if (contentType != null) {
      testMetric.responseContentType = contentType;
    }

    await testMetric.stop();
  }

  // Implement other required methods with minimal functionality
  @override
  bool startTrace(String name) => true;

  @override
  Future<int?> stopTrace(
    String name, {
    Map<String, String>? attributes,
  }) async => 0;

  @override
  bool incrementMetric(
    String traceName,
    String counterName, [
    int incrementBy = 1,
  ]) => true;

  @override
  Future<void> stopAllTraces() async {}
}

// Custom handler for testing
class TestRequestHandler implements RequestInterceptorHandler {
  RequestOptions? passedOptions;
  bool nextCalled = false;

  @override
  void next(RequestOptions options) {
    passedOptions = options;
    nextCalled = true;
  }

  @override
  void reject(DioException error, [bool callFollowingReject = true]) {
    // Not used in this test
  }

  @override
  void resolve(Response response, [bool callFollowingResolve = true]) {
    // Not used in this test
  }

  // These methods are required by the interface but not used in our tests
  @override
  dynamic noSuchMethod(Invocation invocation) => super.noSuchMethod(invocation);
}

// Custom response handler for testing
class TestResponseHandler implements ResponseInterceptorHandler {
  Response? passedResponse;
  bool nextCalled = false;

  @override
  void next(Response response) {
    passedResponse = response;
    nextCalled = true;
  }

  @override
  void reject(DioException error, [bool callFollowingReject = true]) {
    // Not used in this test
  }

  @override
  void resolve(Response response, [bool callFollowingResolve = true]) {
    // Not used in this test
  }

  // These methods are required by the interface but not used in our tests
  @override
  dynamic noSuchMethod(Invocation invocation) => super.noSuchMethod(invocation);
}

// Custom error handler for testing
class TestErrorHandler implements ErrorInterceptorHandler {
  DioException? passedError;
  bool nextCalled = false;

  @override
  void next(DioException error) {
    passedError = error;
    nextCalled = true;
  }

  @override
  void reject(DioException error, [bool callFollowingReject = true]) {
    // Not used in this test
  }

  @override
  void resolve(Response response, [bool callFollowingResolve = true]) {
    // Not used in this test
  }

  // These methods are required by the interface but not used in our tests
  @override
  dynamic noSuchMethod(Invocation invocation) => super.noSuchMethod(invocation);
}

void main() {
  late TestPerformanceService testPerformanceService;
  late TestAppLogger testLogger;
  late PerformanceInterceptor interceptor;
  late TestRequestHandler testRequestHandler;
  late TestResponseHandler testResponseHandler;
  late TestErrorHandler testErrorHandler;

  setUp(() {
    testPerformanceService = TestPerformanceService();
    testLogger = TestAppLogger();
    testRequestHandler = TestRequestHandler();
    testResponseHandler = TestResponseHandler();
    testErrorHandler = TestErrorHandler();
    interceptor = PerformanceInterceptor(testPerformanceService, testLogger);
  });

  group('PerformanceInterceptor', () {
    test('onRequest should start HTTP metric', () {
      // Arrange
      final options = RequestOptions(
        path: '/test',
        method: 'GET',
        baseUrl: 'https://example.com',
        data: '{"test": "data"}',
      );

      // Act
      interceptor.onRequest(options, testRequestHandler);

      // Assert
      expect(
        testPerformanceService.metrics.containsKey('https://example.com/test'),
        isTrue,
      );
      expect(
        testPerformanceService.metrics['https://example.com/test']!.isStarted,
        isTrue,
      );
      expect(testRequestHandler.nextCalled, isTrue);
      expect(testRequestHandler.passedOptions, equals(options));
    });

    test('onResponse should stop HTTP metric', () {
      // Arrange
      final options = RequestOptions(
        path: '/test',
        method: 'GET',
        baseUrl: 'https://example.com',
      );

      final response = Response(
        requestOptions: options,
        statusCode: 200,
        data: '{"result": "success"}',
        headers: Headers.fromMap({
          'content-type': ['application/json'],
        }),
      );

      // First call onRequest to store the metric
      interceptor.onRequest(options, testRequestHandler);

      // Act
      interceptor.onResponse(response, testResponseHandler);

      // Assert
      final metric =
          testPerformanceService.metrics['https://example.com/test']!;
      expect(metric.isStopped, isTrue);
      expect(testResponseHandler.nextCalled, isTrue);
      expect(testResponseHandler.passedResponse, equals(response));
    });

    test('onError should stop HTTP metric with error information', () {
      // Arrange
      final options = RequestOptions(
        path: '/test',
        method: 'GET',
        baseUrl: 'https://example.com',
      );

      final error = DioException(
        requestOptions: options,
        response: Response(requestOptions: options, statusCode: 404),
        type: DioExceptionType.badResponse,
      );

      // First call onRequest to store the metric
      interceptor.onRequest(options, testRequestHandler);

      // Act
      interceptor.onError(error, testErrorHandler);

      // Assert
      final metric =
          testPerformanceService.metrics['https://example.com/test']!;
      expect(metric.isStopped, isTrue);
      expect(testErrorHandler.nextCalled, isTrue);
      expect(testErrorHandler.passedError, equals(error));
    });

    test('onRequest should handle errors gracefully', () {
      // Arrange
      final options = RequestOptions(
        path: '/test',
        method: 'GET',
        baseUrl: 'https://example.com',
      );

      testPerformanceService.exceptionToThrow = Exception('Test error');

      // Act
      interceptor.onRequest(options, testRequestHandler);

      // Assert
      expect(testLogger.errorMessages, isNotEmpty);
      expect(testLogger.errorObjects, isNotEmpty);
      expect(testRequestHandler.nextCalled, isTrue);
    });
  });
}
