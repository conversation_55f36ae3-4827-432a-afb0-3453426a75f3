import 'package:aqua_ui/aqua_ui.dart';
import 'package:flutter/material.dart';

import '../../domain/entities/product.dart';
import '../../domain/entities/product_catalogue.dart';
import 'product_card_widget.dart';

class CategoryProductsListViewWidget extends StatelessWidget {
  final List<ProductCatalogue> categories;
  final bool isScrollable;
  final Function(Product)? onProductTap;
  final double? paddingLeft;
  final double? paddingRight;
  final ScrollController? scrollController;

  const CategoryProductsListViewWidget({
    required this.categories,
    this.isScrollable = true,
    this.onProductTap,
    super.key,
    this.paddingLeft,
    this.paddingRight,
    this.scrollController,
  });

  @override
  Widget build(BuildContext context) {
    return CustomScrollView(
      controller: scrollController,
      physics:
          isScrollable
              ? const AlwaysScrollableScrollPhysics()
              : const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      slivers: _buildCategorySlivers(context),
    );
  }

  List<Widget> _buildCategorySlivers(BuildContext context) {
    List<Widget> slivers = [];

    for (var category in categories) {
      // Add products for this category
      if (category.products.isNotEmpty) {
        slivers.add(
          SliverPadding(
            padding: EdgeInsets.only(
              left: paddingLeft ?? 0,
              right: paddingRight ?? 0,
            ),
            sliver: SliverList(
              delegate: SliverChildBuilderDelegate((context, index) {
                return _buildProductItem(
                  context,
                  category.products[index],
                  category.name,
                );
              }, childCount: category.products.length),
            ),
          ),
        );
      } else {
        // Show empty state for category with no products
        slivers.add(
          SliverToBoxAdapter(
            child: Padding(
              padding: EdgeInsets.only(
                left: paddingLeft ?? 0,
                right: paddingRight ?? 0,
              ),
              child: Center(
                child: AquaText.body(
                  'No products in ${category.name}',
                  color: acTextSecondaryColor,
                ),
              ),
            ),
          ),
        );
      }

      // Add spacing between categories
      slivers.add(
        const SliverToBoxAdapter(child: SizedBox(height: 0, width: 0)),
      );
    }

    return slivers;
  }

  Widget _buildProductItem(
    BuildContext context,
    Product product,
    String categoryName,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: ProductCardWidget(
        brand: categoryName,
        product: product.productName,
        image: product.productImage,
        description: product.tagLine,
        badge: product.productTag,
        onTap: () {
          if (onProductTap != null) {
            onProductTap!(product);
          }
        },
      ),
    );
  }
}
