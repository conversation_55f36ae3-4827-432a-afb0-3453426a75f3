import 'product.dart';

class ProductCatalogue {
  final String? id;
  final String name;
  final String image;
  final String sortOrder;
  final String status;
  final List<Product> products;

  ProductCatalogue({
    this.id,
    required this.name,
    required this.image,
    required this.sortOrder,
    required this.status,
    required this.products,
  });

  const ProductCatalogue.empty()
    : id = null,
      name = '',
      image = '',
      sortOrder = '',
      status = '',
      products = const [];
}
