import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio/dio.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../core/utils/logger.dart';
import '../../../domain/entities/account_statement/account_statement.dart';
import '../../../domain/entities/account_statement/account_statement_entity.dart';
import '../../../domain/entities/customer.dart';
import '../../../domain/services/auth_service.dart';
import '../../../domain/usecases/account_statement/check_if_sync_needed_usecase.dart';
import '../../../domain/usecases/account_statement/get_account_statement_usecase.dart';
import '../../../domain/usecases/account_statement/invalidate_account_statement_cache_usecase.dart';
import '../../../domain/usecases/account_statement/sync_account_statement_usecase.dart';
import 'account_statement_state.dart';
// Assuming DateTimeRange is defined in account_statement_state.dart, otherwise import from flutter/material
// import 'package:flutter/material.dart';

/// Generates a list of dynamic period strings for account statement filtering.
/// Periods include the current Financial Year, Current Month, This Quarter,
/// and Last Month (if applicable based on FY start).
List<String> generateAccountStatementPeriods(DateTime now) {
  final int currentYear = now.year;
  final int currentMonth = now.month; // 1 = Jan, 12 = Dec

  final List<String> periods = [];

  // --- Add Financial Year ---
  // Indian Financial Year starts from April (month 4) to March (month 3 of next year).
  final int fyStartYear = currentMonth >= 4 ? currentYear : currentYear - 1;
  final int fyEndYear = fyStartYear + 1;
  // Format YY-YY
  final String fyPeriod = 'FY (${fyStartYear % 100}-${fyEndYear % 100})';
  periods.add(fyPeriod);

  // --- Add Current Month ---
  periods.add('Current Month');

  // --- Add Last Month ---
  // 'Last Month' is only relevant/visible from May onwards in the current FY.
  // If the current month is April (4), there is no "last month" *within* the current FY context.
  if (currentMonth > 4) {
    // May (5) onwards
    periods.add('Last Month');
  }

  // --- Add This Quarter ---
  // Determine the current financial quarter based on calendar month
  // Q1: April, May, June
  // Q2: July, August, September
  // Q3: October, November, December
  // Q4: January, February, March
  // No need to calculate the specific quarter number here, just add the generic option
  periods.add('This Quarter');

  // You can add other standard periods if needed, or sort them
  // periods.sort(); // Optional: Sort alphabetically or in a specific order

  return periods;
}

class AccountStatementCubit extends Cubit<AccountStatementState> {
  final GetAccountStatementUseCase getAccountStatementUseCase;
  final SyncAccountStatementUseCase syncAccountStatementUseCase;
  final CheckIfAccountStatementSyncNeededUseCase checkIfSyncNeededUseCase;
  final InvalidateAccountStatementCacheUseCase invalidateCacheUseCase;
  final AuthService authService;
  final AppLogger logger;
  final Connectivity connectivity;

  // Storage for current customer ID and timestamps
  String? _currentCustomerId;
  Customer? _customer;
  DateTime? _lastSyncTime;
  bool _isLoadingData = false;
  bool _isSyncing = false;
  bool _isBackgroundSyncing = false;

  // Default period, now dynamically generated
  late String _defaultPeriod;
  late _DateRange
  _currentDateRange; // Use _currentDateRange to track the range of the *currently filtered* data

  // Subscription for connectivity changes
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;

  // Store the full statement after initial load/sync
  AccountStatement? _fullStatement;

  AccountStatementCubit({
    required this.getAccountStatementUseCase,
    required this.syncAccountStatementUseCase,
    required this.checkIfSyncNeededUseCase,
    required this.invalidateCacheUseCase,
    required this.authService,
    required this.logger,
    required this.connectivity,
  }) : super(AccountStatementInitial()) {
    // Generate initial periods and set the default filter and date range
    final initialPeriods = generateAccountStatementPeriods(DateTime.now());
    _defaultPeriod =
        initialPeriods.isNotEmpty
            ? initialPeriods.first
            : 'FY'; // Use the first generated period as default if list is not empty
    _currentDateRange = _getDateRangeForPeriod(_defaultPeriod);
    _setupConnectivityListener();
  }

  // Setup connectivity listener to detect network changes
  void _setupConnectivityListener() {
    _connectivitySubscription = connectivity.onConnectivityChanged.listen((
      result,
    ) {
      final isConnected =
          result.contains(ConnectivityResult.mobile) ||
          result.contains(ConnectivityResult.wifi) ||
          result.contains(ConnectivityResult.ethernet);

      logger.i('Connectivity changed: $isConnected');

      // If we're back online and have data loaded, check if sync is needed
      // Only trigger if not currently loading or syncing to prevent race conditions
      if (isConnected &&
          state is AccountStatementLoaded &&
          !_isLoadingData &&
          !_isSyncing) {
        _checkAndSyncIfNeeded();
      }
    });
  }

  // Check if sync is needed and perform sync if necessary
  Future<void> _checkAndSyncIfNeeded() async {
    if (_currentCustomerId == null || _isSyncing || _isBackgroundSyncing) {
      logger.i(
        'Skipping sync check: customerId=${_currentCustomerId != null}, '
        'isSyncing=$_isSyncing, isBackgroundSyncing=$_isBackgroundSyncing',
      );
      return;
    }

    // First check connectivity
    final isConnected = await _checkConnectivity();
    if (!isConnected) {
      logger.w('Cannot check if sync needed: No internet connection');
      return;
    }

    logger.i('Checking if sync is needed for customer: $_currentCustomerId');

    try {
      final syncNeededResult = await checkIfSyncNeededUseCase(
        _currentCustomerId!,
      ).timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          logger.w('Sync check timed out after 10 seconds');
          throw TimeoutException('Sync check timed out');
        },
      );

      syncNeededResult.fold(
        (failure) {
          logger.w('Failed to check if sync needed: ${failure.toString()}');
          // Optionally emit a subtle error state or log only
        },
        (syncNeeded) async {
          if (syncNeeded) {
            logger.i('Auto-sync needed, performing background sync');
            // Perform background sync, don't wait for it to complete here
            // Note: Background sync should typically not block or show full screen loading
            syncAccountStatement(isManualSync: false);
          } else {
            logger.i('Auto-sync not needed, data is up to date');

            // Update UI to show data is fresh if we're currently showing stale data
            if (state is AccountStatementLoaded) {
              final currentState = state as AccountStatementLoaded;
              if (currentState.isCacheStale) {
                emit(
                  currentState.copyWith(
                    isCacheStale: false,
                    isBackgroundSyncInProgress: false,
                  ),
                );
              }
            }
          }
        },
      );
    } on TimeoutException catch (e) {
      logger.w('Sync check timed out: ${e.toString()}');
      // Don't update UI for background operations timing out
    } catch (e) {
      logger.e('Error checking if sync needed: ${e.toString()}');
      // Don't update UI for background errors
    }
  }

  // Get date range for a specific period string
  _DateRange _getDateRangeForPeriod(String period) {
    final now = DateTime.now();
    DateTime fromDate;
    DateTime toDate;

    // Example: Parse 'FY (25-26)' -> 25-26
    final fyMatch = RegExp(r'FY \((\d{2})-(\d{2})\)').firstMatch(period);

    if (fyMatch != null) {
      // This is a Financial Year period
      try {
        final int startYearYY = int.parse(fyMatch.group(1)!);
        final int endYearYY = int.parse(fyMatch.group(2)!);

        // Reconstruct full years, assuming 20xx. Handle wrap-around (e.g., 99-00)
        // This is a simplified approach assuming recent years.
        int startYear = 2000 + startYearYY;
        int endYear = 2000 + endYearYY;

        // Adjust for century roll-over if necessary (e.g., 99-00 means 2099-2100)
        if (endYear < startYear) {
          endYear += 100;
        }

        // FY starts April 1st and ends March 31st of the next year
        fromDate = DateTime(startYear, 4, 1);
        toDate = DateTime(endYear, 3, 31);

        logger.i('Parsed FY period "$period": $fromDate to $toDate');
      } catch (e) {
        logger.e('Error parsing FY period string "$period": $e');
        // Fallback to a default range or handle error appropriately
        fromDate = DateTime(
          now.year,
          now.month,
          1,
        ); // Fallback to current month start
        toDate = DateTime(
          now.year,
          now.month + 1,
          0,
        ); // Fallback to current month end
      }
    } else {
      // Handle other predefined periods
      switch (period) {
        case 'Current Month':
          fromDate = DateTime(now.year, now.month, 1);
          toDate = DateTime(
            now.year,
            now.month + 1,
            0,
          ); // Last day of current month
          break;
        case 'Last Month':
          // Go back one month
          final lastMonthDate = DateTime(now.year, now.month - 1, now.day);
          fromDate = DateTime(lastMonthDate.year, lastMonthDate.month, 1);
          toDate = DateTime(
            lastMonthDate.year,
            lastMonthDate.month + 1,
            0,
          ); // Last day of previous month
          break;
        case 'This Quarter':
          // Calculate quarter start month based on current month relative to FY start (April)
          int financialMonth =
              (now.month >= 4)
                  ? now.month - 3
                  : now.month + 9; // Month relative to FY start (April = 1)
          int quarter =
              (financialMonth - 1) ~/ 3 + 1; // Financial Quarter (1-4)
          int quarterStartMonth =
              (quarter - 1) * 3 +
              4; // Calendar month of quarter start (April=4, July=7, Oct=10, Jan=1)
          int quarterStartYear = now.year;
          if (quarterStartMonth > 12) {
            quarterStartMonth -= 12;
            quarterStartYear++;
          }
          if (quarterStartMonth < 4) {
            // Quarters Q4 (Jan-Mar) are in the next calendar year of the FY
            quarterStartYear = now.year - 1;
            quarterStartMonth =
                (quarter - 1) * 3 + 4 - 12; // Adjust month for previous year
          }

          fromDate = DateTime(quarterStartYear, quarterStartMonth, 1);

          int quarterEndMonth =
              quarterStartMonth + 2; // Last month of the quarter
          int quarterEndYear = quarterStartYear;
          if (quarterEndMonth > 12) {
            quarterEndMonth -= 12;
            quarterEndYear++;
          }

          toDate = DateTime(
            quarterEndYear,
            quarterEndMonth + 1,
            0,
          ); // Last day of quarter end month

          break;
        default:
          // Default to the default period range if period is unrecognized
          logger.w(
            'Unrecognized period string: "$period". Defaulting to $_defaultPeriod.',
          );
          final defaultRange = _getDateRangeForPeriod(_defaultPeriod);
          fromDate = defaultRange.fromDate;
          toDate = defaultRange.toDate;
          break;
      }
      logger.i('Date range for "$period": $fromDate to $toDate');
    }

    // Ensure toDate is not in the future relative to now
    if (toDate.isAfter(now)) {
      toDate = now;
    }

    return _DateRange(fromDate, toDate);
  }

  /// Load account statement data
  /// Implements a caching-first approach - returns cached data immediately if available
  /// and triggers a background sync if needed
  Future<void> loadAccountStatement() async {
    if (_isLoadingData) {
      logger.i('Already loading data, ignoring duplicate call');
      return;
    }

    _isLoadingData = true;

    // Check if we have previous data to show during loading
    AccountStatement? previousStatement;
    String currentSelectedFilter = _defaultPeriod;
    DateTimeRange? currentDateRangeState;

    if (state is AccountStatementLoaded) {
      final currentState = state as AccountStatementLoaded;
      previousStatement = currentState.statement;
      // Keep existing periods if available
      currentSelectedFilter = currentState.selectedFilter;
      currentDateRangeState = currentState.dateRange;

      // Emit loading with previous data
      emit(
        AccountStatementLoading(
          previousStatement: previousStatement,
          hasPreviousData: true,
          lastSyncTime: currentState.lastSyncTime,
          isLoadingFromCache: true,
        ),
      );
    } else {
      // Emit initial loading state without previous data
      emit(
        AccountStatementLoading(
          hasPreviousData: false,
          isLoadingFromCache: true,
        ),
      );
    }

    try {
      // Get customer ID only if we don't have one yet
      if (_currentCustomerId == null) {
        await _getCustomerId();
        if (_currentCustomerId == null) {
          emit(
            const AccountStatementError(
              'User not authenticated or customer ID not available',
            ),
          );
          _isLoadingData = false;
          return;
        }
      }

      logger.i('Loading account statement for customer: $_currentCustomerId');

      // CACHING-FIRST APPROACH:
      // Use the repository's getAccountStatement which now implements caching-first
      final result = await getAccountStatementUseCase(_currentCustomerId!);

      await result.fold(
        (failure) async {
          logger.e('Failed to load account statement: ${failure.toString()}');
          // If there was previous data, keep it but show error
          if (previousStatement != null) {
            // Generate periods dynamically even on load failure, in case the date has changed
            final dynamicPeriods = generateAccountStatementPeriods(
              DateTime.now(),
            );

            emit(
              AccountStatementLoaded(
                customer: _customer!,
                statement: previousStatement,
                isSyncing: false,
                lastSyncTime: _lastSyncTime,
                hasError: true,
                errorMessage: 'Failed to load data: ${failure.toString()}',
                // Re-filter previous data based on the most recent filter and date range
                filteredEntries: _filterEntries(
                  previousStatement.entries,
                  _currentDateRange.fromDate,
                  _currentDateRange.toDate,
                ),
                selectedFilter: currentSelectedFilter,
                dateRange:
                    currentDateRangeState ??
                    DateTimeRange(
                      startDate: _currentDateRange.fromDate,
                      endDate: _currentDateRange.toDate,
                    ),
                periods: dynamicPeriods,
                isFromCache: true,
                isCacheStale: true,
              ),
            );
          } else {
            // No previous data, emit error state
            emit(
              AccountStatementError(
                'Failed to load account statement: ${failure.toString()}',
              ),
            );
          }
        },
        (statement) async {
          // Data loaded successfully (either from cache or sync)
          _fullStatement = statement; // Store the full statement
          _lastSyncTime = statement?.lastSyncTime;

          // Generate periods dynamically based on the current date
          final dynamicPeriods = generateAccountStatementPeriods(
            DateTime.now(),
          );

          // Determine the effective filter to apply initially
          // Use the previous selected filter if available and still valid, otherwise use default
          String effectiveSelectedFilter = currentSelectedFilter;
          if (!dynamicPeriods.contains(effectiveSelectedFilter) &&
              !effectiveSelectedFilter.startsWith('Custom')) {
            effectiveSelectedFilter = _defaultPeriod;
          }

          // Calculate the date range for the effective filter
          final effectiveDateRange = _getDateRangeForPeriod(
            effectiveSelectedFilter,
          );
          _currentDateRange =
              effectiveDateRange; // Update current date range state

          // Filter entries based on the selected period
          final filteredEntries = _filterEntries(
            statement?.entries ?? [],
            effectiveDateRange.fromDate,
            effectiveDateRange.toDate,
          );

          // Check if a background sync is in progress
          final isBackgroundSyncInProgress = _isBackgroundSyncing;

          // Emit loaded state with cache status
          emit(
            AccountStatementLoaded(
              customer: _customer!,
              statement:
                  statement ??
                  AccountStatement(entries: [], lastSyncTime: DateTime.now()),
              isSyncing: false,
              lastSyncTime: _lastSyncTime,
              hasError: false,
              errorMessage: null,
              filteredEntries: filteredEntries,
              selectedFilter: effectiveSelectedFilter,
              dateRange: DateTimeRange(
                startDate: effectiveDateRange.fromDate,
                endDate: effectiveDateRange.toDate,
              ),
              hasEmptyStatement: statement?.entries.isEmpty ?? true,
              periods: dynamicPeriods,
              isFromCache: true,
              isCacheStale: false, // We don't know yet, will update if needed
              isBackgroundSyncInProgress: isBackgroundSyncInProgress,
            ),
          );

          logger.i(
            'Account statement loaded successfully. Entries: ${statement?.entries.length ?? 0}',
          );

          // After loading from cache, if online, check if sync is needed
          if (await _checkConnectivity()) {
            logger.i('Connected to internet, checking if sync is needed');

            try {
              final syncNeededResult = await checkIfSyncNeededUseCase(
                _currentCustomerId!,
              ).timeout(
                const Duration(seconds: 10),
                onTimeout: () {
                  logger.w('Sync check timed out after 10 seconds');
                  throw TimeoutException('Sync check timed out');
                },
              );

              syncNeededResult.fold(
                (failure) {
                  logger.w(
                    'Failed to check sync needed after load: ${failure.toString()}',
                  );
                },
                (syncNeeded) {
                  if (syncNeeded && !_isSyncing && !_isBackgroundSyncing) {
                    logger.i('Cache is stale, performing background sync');

                    // Update state to indicate cache is stale
                    if (state is AccountStatementLoaded) {
                      emit(
                        (state as AccountStatementLoaded).copyWith(
                          isCacheStale: true,
                          isBackgroundSyncInProgress: true,
                        ),
                      );
                    }

                    // Trigger background sync
                    _isBackgroundSyncing = true;
                    syncAccountStatement(isManualSync: false)
                        .then((_) {
                          logger.i('Background sync completed');
                        })
                        .catchError((error) {
                          logger.e('Background sync failed: $error');
                        })
                        .whenComplete(() {
                          _isBackgroundSyncing = false;
                          // Update state to indicate background sync is complete
                          if (state is AccountStatementLoaded) {
                            emit(
                              (state as AccountStatementLoaded).copyWith(
                                isBackgroundSyncInProgress: false,
                              ),
                            );
                          }
                        });
                  } else {
                    logger.i(
                      'Cache is fresh or sync already in progress. '
                      'syncNeeded=$syncNeeded, isSyncing=$_isSyncing, '
                      'isBackgroundSyncing=$_isBackgroundSyncing',
                    );
                  }
                },
              );
            } on TimeoutException catch (e) {
              logger.w('Sync check timed out: ${e.toString()}');
              // Don't update UI for background operations timing out
            } catch (e) {
              logger.e('Error checking if sync needed: ${e.toString()}');
              // Don't update UI for background errors
            }
          } else {
            logger.w('Not connected to internet, skipping sync check');
          }
        },
      );
    } catch (e) {
      logger.e('Unexpected error loading account statement: ${e.toString()}');
      // If there was previous data, keep it but show error
      if (previousStatement != null) {
        // Generate periods dynamically even on unexpected error, in case the date has changed
        final dynamicPeriods = generateAccountStatementPeriods(DateTime.now());

        emit(
          AccountStatementLoaded(
            customer: _customer!,
            statement: previousStatement,
            isSyncing: false,
            lastSyncTime: _lastSyncTime,
            hasError: true,
            errorMessage: 'Error loading data: ${e.toString()}',
            // Re-filter previous data based on the most recent filter and date range
            filteredEntries: _filterEntries(
              previousStatement.entries,
              _currentDateRange.fromDate,
              _currentDateRange.toDate,
            ),
            selectedFilter: currentSelectedFilter,
            dateRange:
                currentDateRangeState ??
                DateTimeRange(
                  startDate: _currentDateRange.fromDate,
                  endDate: _currentDateRange.toDate,
                ),
            periods: dynamicPeriods,
            isFromCache: true,
            isCacheStale: true,
          ),
        );
      } else {
        // No previous data, emit error state
        emit(
          AccountStatementError(
            'Error loading account statement: ${e.toString()}',
          ),
        );
      }
    } finally {
      _isLoadingData = false;
    }
  }

  /// Manually sync account statement data
  /// [isManualSync] determines if this was user-initiated (shows loading overlay)
  /// or a background sync (more subtle).
  Future<void> syncAccountStatement({bool isManualSync = true}) async {
    if (isManualSync) {
      logger.i('Manual sync requested');
    } else {
      logger.i('Background sync requested');
    }

    if (_isSyncing) {
      logger.i('Already syncing, ignoring duplicate call');
      return;
    }

    // Get customer ID if needed
    if (_currentCustomerId == null) {
      await _getCustomerId();
      if (_currentCustomerId == null) {
        if (state is! AccountStatementLoaded || isManualSync) {
          // Only show error if no data or it's a manual sync attempt without data
          emit(
            const AccountStatementError(
              'User not authenticated or customer ID not available',
            ),
          );
        }
        return;
      }
    }

    // Check network connectivity with enhanced check
    final isConnected = await _checkConnectivity();
    if (!isConnected) {
      logger.w('Cannot sync: No internet connection');
      if (state is AccountStatementLoaded) {
        // Keep existing data but show a temporary message for manual sync
        final currentState = state as AccountStatementLoaded;
        emit(
          currentState.copyWith(
            hasError: isManualSync, // Only show error flag for manual sync
            errorMessage:
                isManualSync
                    ? 'No internet connection. Please check your network settings and try again.'
                    : null, // Only show message for manual sync
            isSyncing: false, // Syncing couldn't start
            isBackgroundSyncInProgress: false,
          ),
        );
      } else if (isManualSync) {
        // Only show error state if no data and it's a manual sync
        emit(
          const AccountStatementError(
            'No internet connection. Please check your network settings and try again.',
          ),
        );
      }
      _isSyncing = false; // Ensure flag is reset
      _isBackgroundSyncing = false;
      return;
    }

    // Set sync flags
    _isSyncing = true;
    if (!isManualSync) {
      _isBackgroundSyncing = true;
    }

    // Update UI to show syncing
    if (state is AccountStatementLoaded) {
      final currentState = state as AccountStatementLoaded;
      emit(
        currentState.copyWith(
          isSyncing:
              isManualSync, // Only show loading indicator for manual sync
          isBackgroundSyncInProgress:
              !isManualSync, // Show background sync indicator for background sync
          hasError: false,
          errorMessage: null,
          isCacheStale:
              currentState.isCacheStale, // Preserve cache stale status
        ),
      ); // Clear previous errors on sync start
    } else if (isManualSync) {
      // If no data and manual sync, show loading state
      emit(
        AccountStatementLoading(
          hasPreviousData: false,
          isLoadingFromCache: false,
        ),
      );
    }

    try {
      logger.i('Starting sync for customer: $_currentCustomerId');

      // Perform sync with timeout
      final result = await syncAccountStatementUseCase(
        _currentCustomerId!,
      ).timeout(
        const Duration(seconds: 30),
        onTimeout: () {
          logger.e('Sync operation timed out after 30 seconds');
          throw TimeoutException('Sync operation timed out');
        },
      );

      result.fold(
        (failure) {
          logger.e('Sync failed: ${failure.toString()}');
          if (state is AccountStatementLoaded) {
            // Keep existing data but show error message
            final currentState = state as AccountStatementLoaded;
            emit(
              currentState.copyWith(
                isSyncing: false,
                isBackgroundSyncInProgress: false,
                hasError: true,
                errorMessage: 'Failed to sync: ${failure.toString()}',
              ),
            );
          } else {
            // If no data yet, show error state
            emit(
              AccountStatementError('Failed to sync: ${failure.toString()}'),
            );
          }
        },
        (statement) {
          // Sync successful
          _fullStatement = statement; // Store the full statement
          _lastSyncTime = statement?.lastSyncTime ?? DateTime.now();

          logger.i(
            'Sync successful. Statement: ${statement != null ? "not null" : "null"}, '
            'Entries: ${statement?.entries.length ?? 0}, '
            'Last sync time: $_lastSyncTime',
          );

          // Generate periods dynamically based on the current date
          final dynamicPeriods = generateAccountStatementPeriods(
            DateTime.now(),
          );

          // Determine the filter to apply after sync
          String filterToApply;
          _DateRange dateRangeToApply;

          // Attempt to keep the current filter after sync if it's still a valid generated period or custom
          if (state is AccountStatementLoaded) {
            final currentState = state as AccountStatementLoaded;
            String previousFilter = currentState.selectedFilter;

            if (dynamicPeriods.contains(previousFilter) ||
                previousFilter.startsWith('Custom')) {
              filterToApply = previousFilter;
            } else {
              // Previous filter is no longer valid (e.g., last month from previous FY)
              // Default to the first generated period (current FY)
              filterToApply =
                  dynamicPeriods.isNotEmpty ? dynamicPeriods.first : 'FY';
            }
          } else {
            // If starting from loading/initial, apply the default filter (current FY)
            filterToApply =
                dynamicPeriods.isNotEmpty ? dynamicPeriods.first : 'FY';
          }

          dateRangeToApply = _getDateRangeForPeriod(filterToApply);
          _currentDateRange =
              dateRangeToApply; // Update current date range state

          // Create an empty statement if null
          final effectiveStatement =
              statement ??
              AccountStatement(entries: [], lastSyncTime: DateTime.now());

          // Filter entries from the new full statement
          final filteredEntries = _filterEntries(
            effectiveStatement.entries,
            dateRangeToApply.fromDate,
            dateRangeToApply.toDate,
          );

          // Ensure we have a customer
          if (_customer == null) {
            logger.w(
              'Customer is null after sync, attempting to get customer ID again',
            );
            _getCustomerId().then((_) {
              if (_customer != null) {
                _emitLoadedState(
                  effectiveStatement,
                  filterToApply,
                  dateRangeToApply,
                  filteredEntries,
                  dynamicPeriods,
                );
              } else {
                logger.e('Failed to get customer after sync');
                emit(
                  const AccountStatementError(
                    'Failed to get customer information',
                  ),
                );
              }
            });
          } else {
            _emitLoadedState(
              effectiveStatement,
              filterToApply,
              dateRangeToApply,
              filteredEntries,
              dynamicPeriods,
            );
          }
        },
      );
    } on TimeoutException catch (e) {
      logger.e('Sync operation timed out: ${e.toString()}');
      if (state is AccountStatementLoaded) {
        final currentState = state as AccountStatementLoaded;
        emit(
          currentState.copyWith(
            isSyncing: false,
            isBackgroundSyncInProgress: false,
            hasError: true,
            errorMessage: 'Sync timed out. Please try again later.',
          ),
        );
      } else {
        emit(
          const AccountStatementError(
            'Sync timed out. Please try again later.',
          ),
        );
      }
    } catch (e) {
      logger.e('Error during sync: ${e.toString()}');
      if (state is AccountStatementLoaded) {
        // Keep existing data but show error message
        final currentState = state as AccountStatementLoaded;
        emit(
          currentState.copyWith(
            isSyncing: false,
            isBackgroundSyncInProgress: false,
            hasError: true,
            errorMessage: 'Error during sync: ${e.toString()}',
          ),
        );
      } else {
        // If no data yet, show error state
        emit(AccountStatementError('Error during sync: ${e.toString()}'));
      }
    } finally {
      _isSyncing = false;
      if (!isManualSync) {
        _isBackgroundSyncing = false;
      }
    }
  }

  // Helper method to emit loaded state after sync
  void _emitLoadedState(
    AccountStatement statement,
    String filterToApply,
    _DateRange dateRangeToApply,
    List<AccountStatementEntity> filteredEntries,
    List<String> dynamicPeriods,
  ) {
    // Ensure proper handling of opening balance entries
    final processedEntries = _ensureProperOpeningBalance(statement.entries);
    final processedStatement = AccountStatement(
      entries: processedEntries,
      lastSyncTime: statement.lastSyncTime,
    );

    // Recalculate filtered entries based on processed entries
    final processedFilteredEntries = _filterEntries(
      processedEntries,
      dateRangeToApply.fromDate,
      dateRangeToApply.toDate,
    );

    emit(
      AccountStatementLoaded(
        customer: _customer!,
        statement: processedStatement,
        isSyncing: false,
        isBackgroundSyncInProgress: false,
        lastSyncTime: _lastSyncTime,
        hasError: false,
        errorMessage: null,
        filteredEntries: processedFilteredEntries,
        selectedFilter: filterToApply,
        dateRange: DateTimeRange(
          startDate: dateRangeToApply.fromDate,
          endDate: dateRangeToApply.toDate,
        ),
        hasEmptyStatement: processedStatement.entries.isEmpty,
        periods: dynamicPeriods,
        isFromCache: false,
        isCacheStale: false,
      ),
    );
    logger.i(
      'Loaded state emitted after sync. Entries: ${processedStatement.entries.length}',
    );
  }

  /// Force invalidate the cache for the current customer's account statement
  /// This will cause the next getAccountStatement call to trigger a sync
  Future<void> invalidateCache() async {
    if (_currentCustomerId == null) {
      await _getCustomerId();
      if (_currentCustomerId == null) {
        emit(
          const AccountStatementError(
            'User not authenticated or customer ID not available',
          ),
        );
        return;
      }
    }

    logger.i('Invalidating cache for customer: $_currentCustomerId');

    final result = await invalidateCacheUseCase(_currentCustomerId!);

    result.fold(
      (failure) {
        logger.e('Failed to invalidate cache: ${failure.toString()}');
        if (state is AccountStatementLoaded) {
          emit(
            (state as AccountStatementLoaded).copyWith(
              hasError: true,
              errorMessage: 'Failed to invalidate cache: ${failure.toString()}',
            ),
          );
        }
      },
      (success) {
        logger.i('Cache invalidated successfully');
        if (state is AccountStatementLoaded) {
          emit((state as AccountStatementLoaded).copyWith(isCacheStale: true));
        }

        // Reload data after cache invalidation
        loadAccountStatement();
      },
    );
  }

  // Helper method to filter entries and add opening balance
  // This method calculates the opening balance based on entries *before* fromDate
  // and returns a list containing one opening balance entry followed by transactions
  // within the specified date range.
  List<AccountStatementEntity> _filterEntries(
    List<AccountStatementEntity> entries,
    DateTime fromDate,
    DateTime toDate,
  ) {
    logger.i(
      "Filtering entries from $fromDate to $toDate. Total entries: ${entries.length}",
    );

    // Sort entries by transaction date
    final sortedEntries = List<AccountStatementEntity>.from(entries)
      ..sort((a, b) => a.txnDate.compareTo(b.txnDate));

    // Find the last balance *before* the start date of the period
    double openingBalance = 0;
    AccountStatementEntity? lastEntryBeforePeriod;

    for (var entry in sortedEntries) {
      // Normalize entry.txnDate to compare with fromDate (ignoring time for 'isBefore' check on date part)
      DateTime entryDateOnly = DateTime(
        entry.txnDate.year,
        entry.txnDate.month,
        entry.txnDate.day,
      );
      DateTime fromDateOnly = DateTime(
        fromDate.year,
        fromDate.month,
        fromDate.day,
      );

      if (entryDateOnly.isBefore(fromDateOnly)) {
        openingBalance = entry.balance;
        lastEntryBeforePeriod = entry;
        // logger.i(
        //   "Found potential opening balance: $openingBalance from entry dated ${entry.txnDate.toIso8601String()}",
        // );
      } else {
        // Optimization: if fromDate itself has an entry that is an Opening Balance,
        // and it's the very first relevant transaction, its balance might be more appropriate.
        // However, the current logic correctly takes the balance *before* fromDate.
        break;
      }
    }
    if (lastEntryBeforePeriod != null) {
      logger.i(
        "Calculated opening balance: $openingBalance from entry dated ${lastEntryBeforePeriod.txnDate.toIso8601String()} (just before $fromDate)",
      );
    } else {
      logger.i(
        "Calculated opening balance for period starting ${fromDate.toIso8601String()}: $openingBalance (No transactions before this date, or using initial 0)",
      );
    }

    // Filter entries strictly within the date range (inclusive of start and end day)
    var filteredTransactions =
        sortedEntries.where((entry) {
          final entryDateOnly = DateTime(
            entry.txnDate.year,
            entry.txnDate.month,
            entry.txnDate.day,
          );
          final periodStartDateOnly = DateTime(
            fromDate.year,
            fromDate.month,
            fromDate.day,
          );
          final periodEndDateOnly = DateTime(
            toDate.year,
            toDate.month,
            toDate.day,
          );

          return (entryDateOnly.isAtSameMomentAs(periodStartDateOnly) ||
                  entryDateOnly.isAfter(periodStartDateOnly)) &&
              (entryDateOnly.isAtSameMomentAs(periodEndDateOnly) ||
                  entryDateOnly.isBefore(periodEndDateOnly));
        }).toList();

    // Check for duplicate opening balance entries within the filtered range and keep only the first one
    // This typically handles cases where 'fromDate' itself might be an 'Opening Balance' entry day.
    var openingBalanceEntriesInFiltered =
        filteredTransactions
            .where((entry) => entry.vchType == 'Opening Balance')
            .toList();

    if (openingBalanceEntriesInFiltered.length > 1) {
      logger.w(
        "Found ${openingBalanceEntriesInFiltered.length} 'Opening Balance' type entries within the filtered date range. Consolidating.",
      );
      // Sort by date first, then by non-zero balance preference.
      openingBalanceEntriesInFiltered.sort((a, b) {
        int dateComp = a.txnDate.compareTo(b.txnDate);
        if (dateComp != 0) return dateComp;
        if (a.balance == 0 && b.balance != 0)
          return 1; // b (non-zero) comes first
        if (a.balance != 0 && b.balance == 0)
          return -1; // a (non-zero) comes first
        return 0;
      });

      var firstOpeningBalance = openingBalanceEntriesInFiltered.first;

      filteredTransactions.removeWhere(
        (entry) => entry.vchType == 'Opening Balance',
      );
      // Add back only the selected one, then re-sort all filtered transactions
      // to ensure it's correctly placed if its date isn't the absolute first.
      filteredTransactions.add(firstOpeningBalance);
      filteredTransactions.sort((a, b) => a.txnDate.compareTo(b.txnDate));
    }

    logger.i(
      "Found ${filteredTransactions.length} transactions within the range ${fromDate.toIso8601String().substring(0, 10)} to ${toDate.toIso8601String().substring(0, 10)}.",
    );

    // Determine if an "Opening Balance" entry from the original data is already the first item for the period
    bool existingOBIsFirstAndOnFromDate = false;
    if (filteredTransactions.isNotEmpty) {
      final firstEntry = filteredTransactions.first;
      final firstEntryDateOnly = DateTime(
        firstEntry.txnDate.year,
        firstEntry.txnDate.month,
        firstEntry.txnDate.day,
      );
      final fromDateOnly = DateTime(
        fromDate.year,
        fromDate.month,
        fromDate.day,
      );

      if (firstEntry.vchType == 'Opening Balance' &&
          firstEntry.particulars.toLowerCase().contains('opening balance') &&
          firstEntryDateOnly.isAtSameMomentAs(fromDateOnly)) {
        existingOBIsFirstAndOnFromDate = true;
      }
    }

    List<AccountStatementEntity> combinedList = [];

    if (existingOBIsFirstAndOnFromDate) {
      // If an original "Opening Balance" entry falls on fromDate and is the first transaction of the period, use it.
      // The `openingBalance` calculated earlier (balance *before* fromDate) is not used to create a *new* OB.
      // The balance of this existing OB entry will be used.
      combinedList = List<AccountStatementEntity>.from(filteredTransactions);
      logger.i(
        "Using existing 'Opening Balance' entry dated ${combinedList.first.txnDate.toIso8601String()} as it falls on fromDate.",
      );
    } else {
      // Create a synthetic opening balance entry using the calculated balance and the period's start date
      // Ensure the synthetic OB date is normalized to midnight for consistency.
      final syntheticOpeningBalanceEntry = AccountStatementEntity(
        txnDate: DateTime(
          fromDate.year,
          fromDate.month,
          fromDate.day,
        ), // Normalized fromDate
        vchType: 'Opening Balance',
        invoiceNumber: '',
        particulars: '***Opening Balance***',
        debit: 0,
        credit: 0,
        balance: openingBalance, // This is the balance from *before* fromDate
        amount: openingBalance, // Or null, as appropriate for OB
      );

      // Add synthetic opening balance entry
      combinedList = [syntheticOpeningBalanceEntry, ...filteredTransactions];
      logger.i(
        "Prepended synthetic 'Opening Balance' entry with balance $openingBalance for date ${fromDate.toIso8601String()}.",
      );
      // Ensure the list is sorted if a synthetic OB was added, especially if filteredTransactions could be empty.
      combinedList.sort((a, b) => a.txnDate.compareTo(b.txnDate));
    }

    logger.i("Returning combined list with ${combinedList.length} items.");
    if (combinedList.isNotEmpty) {
      logger.i("First item: ${combinedList.first.toString()}");
    }
    if (combinedList.length > 1) {
      logger.i("Second item: ${combinedList[1].toString()}");
    }

    return combinedList;
  }

  // Helper method to check connectivity
  Future<bool> _checkConnectivity() async {
    try {
      final connectivityResult = await connectivity.checkConnectivity();
      final isConnected =
          connectivityResult.contains(ConnectivityResult.mobile) ||
          connectivityResult.contains(ConnectivityResult.wifi) ||
          connectivityResult.contains(ConnectivityResult.ethernet);

      logger.i(
        'Connectivity check result: $isConnected (${connectivityResult.toString()})',
      );

      // Additional check to verify actual internet connectivity
      if (isConnected) {
        try {
          // Try to make a simple HTTP request to verify actual internet connectivity
          final dio = Dio();
          final response = await dio
              .get('https://www.google.com')
              .timeout(
                const Duration(seconds: 5),
                onTimeout: () {
                  logger.w('Internet connectivity check timed out');
                  return Response(
                    requestOptions: RequestOptions(path: ''),
                    statusCode: 408, // Timeout status code
                  );
                },
              );

          final hasActualInternet = response.statusCode == 200;
          logger.i('Actual internet connectivity: $hasActualInternet');
          return hasActualInternet;
        } catch (e) {
          logger.w(
            'Error checking actual internet connectivity: ${e.toString()}',
          );
          return false; // If we can't reach the internet, return false
        }
      }

      return isConnected;
    } catch (e) {
      logger.e('Error checking connectivity: ${e.toString()}');
      return false;
    }
  }

  /// Get and cache the customer ID
  Future<void> _getCustomerId() async {
    try {
      final customerResult = await authService.getCurrentCustomer();
      customerResult.fold(
        (failure) {
          logger.e('Failed to get customer ID: ${failure.toString()}');
          _currentCustomerId = null;
          // Only emit error if needed, e.g., during initial load where customer ID is required
          // emit(AccountStatementError("Failed to retrieve customer, try again"));
        },
        (customer) {
          if (customer != null && customer.customerId.isNotEmpty) {
            _currentCustomerId = customer.customerId;
            _customer = customer;
            logger.i('Retrieved customer ID: $_currentCustomerId');
          } else {
            logger.w('Retrieved empty customer ID');
            _currentCustomerId = null;
          }
        },
      );
    } catch (e) {
      logger.e('Error getting customer ID: ${e.toString()}');
      _currentCustomerId = null;
      // Only emit error if needed
    }
  }

  /// Clear all filters, returning to the default period view.
  void clearFilters() {
    logger.i('Clearing filters, resetting to default period: $_defaultPeriod');
    if (state is AccountStatementLoaded && _fullStatement != null) {
      final currentState = state as AccountStatementLoaded;
      // Use the default period and calculate its date range
      final dateRange = _getDateRangeForPeriod(_defaultPeriod);
      _currentDateRange = dateRange; // Update current date range state

      // Filter the full statement by the default period
      final filteredEntries = _filterEntries(
        _fullStatement!.entries,
        dateRange.fromDate,
        dateRange.toDate,
      );

      emit(
        currentState.copyWith(
          filteredEntries: filteredEntries,
          dateRange: DateTimeRange(
            startDate: dateRange.fromDate,
            endDate: dateRange.toDate,
          ),
          selectedFilter: _defaultPeriod,
          hasError: false, // Clear errors on filter clear
          errorMessage: null,
        ),
      );
      logger.i('Filters cleared successfully.');
    } else {
      logger.w("Cannot clear filters: No full statement loaded yet");
      // Optionally, reset to initial state or handle appropriately
    }
  }

  // Change the period filter based on a predefined period string.
  void changePeriod(String period) {
    logger.i("Attempting to change period filter to: $period");

    if (state is AccountStatementLoaded && _fullStatement != null) {
      final currentState = state as AccountStatementLoaded;

      // Get the date range for the selected period
      final newDateRange = _getDateRangeForPeriod(period);

      // Check if the new date range is identical to the current one to avoid redundant updates
      if (currentState.dateRange != null &&
          currentState.dateRange!.startDate == newDateRange.fromDate &&
          currentState.dateRange!.endDate == newDateRange.toDate &&
          currentState.selectedFilter == period) {
        logger.i(
          "Period filter '$period' with same date range already applied. Ignoring.",
        );
        return;
      }

      _currentDateRange = newDateRange; // Update current date range state

      // Get all entries from the full loaded statement
      final allEntries = _fullStatement!.entries;

      // Filter entries based on the new date range and add opening balance
      final filteredEntries = _filterEntries(
        allEntries,
        newDateRange.fromDate,
        newDateRange.toDate,
      );

      // Update state with filtered entries and new date range/filter
      emit(
        currentState.copyWith(
          filteredEntries: filteredEntries,
          dateRange: DateTimeRange(
            startDate: newDateRange.fromDate,
            endDate: newDateRange.toDate,
          ),
          selectedFilter: period,
          hasError: false, // Clear errors on filter change
          errorMessage: null,
        ),
      );

      logger.i(
        "Period filter applied successfully for: $period. Displaying ${filteredEntries.length} entries (including opening balance).",
      );
    } else {
      logger.w("Cannot change period: No full statement loaded yet");
    }
  }

  // Filter by a custom date range provided by the user.
  void applyCustomDateRange(DateTime startDate, DateTime endDate) {
    logger.i("Attempting to apply custom date range: $startDate to $endDate");

    if (state is AccountStatementLoaded && _fullStatement != null) {
      final currentState = state as AccountStatementLoaded;

      // Create a period string representing the custom range
      final customPeriod =
          'Custom (${startDate.day}/${startDate.month}/${startDate.year} - ${endDate.day}/${endDate.month}/${endDate.year})';

      // Use the provided dates as the date range
      final customDateRange = _DateRange(startDate, endDate);

      // Check if the custom date range is identical to the current one to avoid redundant updates
      if (currentState.dateRange != null &&
          currentState.dateRange!.startDate == customDateRange.fromDate &&
          currentState.dateRange!.endDate == customDateRange.toDate &&
          currentState.selectedFilter == customPeriod) {
        // Also check if the selected filter string matches
        logger.i("Custom date range already applied. Ignoring.");
        return;
      }

      _currentDateRange = customDateRange; // Update current date range state

      // Get all entries from the full loaded statement
      final allEntries = _fullStatement!.entries;

      // Filter entries based on the custom date range and add opening balance
      final filteredEntries = _filterEntries(
        allEntries,
        customDateRange.fromDate,
        customDateRange.toDate,
      );

      // Update state with filtered entries and custom date range/filter
      emit(
        currentState.copyWith(
          filteredEntries: filteredEntries,
          dateRange: DateTimeRange(
            startDate: customDateRange.fromDate,
            endDate: customDateRange.toDate,
          ),
          selectedFilter:
              customPeriod, // Represent custom range in selectedFilter
          hasError: false, // Clear errors on filter change
          errorMessage: null,
        ),
      );

      logger.i(
        "Custom date range applied successfully. Displaying ${filteredEntries.length} entries (including opening balance).",
      );
    } else {
      logger.w("Cannot apply custom date range: No full statement loaded yet");
      // Optionally, handle the case where no data is loaded
    }
  }

  @override
  Future<void> close() {
    // _debounceTimer?.cancel(); // If you add debounce timer
    _connectivitySubscription?.cancel();
    return super.close();
  }

  List<AccountStatementEntity> _ensureProperOpeningBalance(
    List<AccountStatementEntity> entries,
  ) {
    // If no entries, nothing to fix
    if (entries.isEmpty) return entries;

    // Check for opening balance entries
    final openingBalanceEntries =
        entries.where((entry) => entry.vchType == 'Opening Balance').toList();

    // If no opening balance entries or just one, no need to fix
    if (openingBalanceEntries.length <= 1) return entries;

    // Multiple opening balance entries found - keep only the most appropriate one
    logger.w(
      'Found ${openingBalanceEntries.length} opening balance entries. Keeping only one.',
    );

    // Sort opening balance entries by balance (non-zero balance first)
    openingBalanceEntries.sort((a, b) {
      if (a.balance == 0 && b.balance != 0) return 1;
      if (a.balance != 0 && b.balance == 0) return -1;
      return 0;
    });

    // Keep only the first opening balance entry (preferring non-zero balance)
    final firstOpeningBalance = openingBalanceEntries.first;

    // Remove all opening balance entries
    final filteredEntries =
        entries.where((entry) => entry.vchType != 'Opening Balance').toList();

    // Add back the selected opening balance entry at the beginning
    filteredEntries.insert(0, firstOpeningBalance);

    return filteredEntries;
  }
}

// Helper class for date ranges (keeping it here as it's internal to the cubit)
// Ensure this matches the definition in your account_statement_state.dart or flutter/material
class _DateRange {
  final DateTime fromDate;
  final DateTime toDate;
  _DateRange(this.fromDate, this.toDate);

  @override
  String toString() =>
      'From: ${fromDate.toIso8601String()}, To: ${toDate.toIso8601String()}';
}
