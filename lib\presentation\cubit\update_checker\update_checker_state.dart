import 'package:equatable/equatable.dart';
import '../../../domain/entities/update_info.dart';

abstract class UpdateCheckerState extends Equatable {
  const UpdateCheckerState();

  @override
  List<Object?> get props => [];
}

class UpdateC<PERSON>ckerInitial extends UpdateCheckerState {}

class UpdateCheckerLoading extends UpdateCheckerState {}

class UpdateNotRequired extends UpdateCheckerState {}

class UpdateRequired extends UpdateCheckerState {
  final UpdateInfo updateInfo;

  const UpdateRequired(this.updateInfo);

  @override
  List<Object?> get props => [updateInfo];
}

class UpdateRecommended extends UpdateCheckerState {
  final UpdateInfo updateInfo;

  const UpdateRecommended(this.updateInfo);

  @override
  List<Object?> get props => [updateInfo];
}

class UpdateCheckerError extends UpdateCheckerState {
  final String message;

  const UpdateCheckerError(this.message);

  @override
  List<Object?> get props => [message];
}
