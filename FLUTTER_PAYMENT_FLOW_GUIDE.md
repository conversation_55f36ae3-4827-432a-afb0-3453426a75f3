# Flutter Payment Flow Integration Guide
## Complete Payment Flow Implementation

## 🔄 Payment Flow Implementation

### PaymentFlowManager Class

```dart
import 'package:flutter/material.dart';

enum PaymentStatus {
  idle,
  creating,
  pending,
  processing,
  succeeded,
  failed,
  cancelled,
  expired,
}

class PaymentFlowManager extends ChangeNotifier {
  PaymentStatus _status = PaymentStatus.idle;
  PaymentSessionResponse? _currentSession;
  PaymentException? _lastError;
  
  PaymentStatus get status => _status;
  PaymentSessionResponse? get currentSession => _currentSession;
  PaymentException? get lastError => _lastError;
  
  /// Start payment flow
  Future<bool> startPayment(PaymentRequest request) async {
    try {
      _updateStatus(PaymentStatus.creating);
      
      // Create payment session
      _currentSession = await PaymentErrorHandler.handleWithRetry(
        () => ZohoPaymentService.createPaymentSession(request),
      );
      
      _updateStatus(PaymentStatus.pending);
      
      // Start monitoring payment status
      _startStatusMonitoring();
      
      return true;
    } catch (e) {
      _lastError = e is PaymentException ? e : PaymentException(e.toString());
      _updateStatus(PaymentStatus.failed);
      return false;
    }
  }
  
  /// Monitor payment status with polling
  void _startStatusMonitoring() {
    if (_currentSession == null) return;
    
    Timer.periodic(const Duration(seconds: 5), (timer) async {
      try {
        final statusResponse = await ZohoPaymentService.getPaymentStatus(
          _currentSession!.data.paymentSessionId,
        );
        
        final newStatus = _mapApiStatusToPaymentStatus(statusResponse.data.status);
        
        if (newStatus != _status) {
          _updateStatus(newStatus);
          
          // Stop monitoring if payment is complete
          if (_isPaymentComplete(newStatus)) {
            timer.cancel();
          }
        }
      } catch (e) {
        // Log error but continue monitoring
        debugPrint('Status monitoring error: $e');
      }
    });
  }
  
  /// Map API status to local payment status
  PaymentStatus _mapApiStatusToPaymentStatus(String apiStatus) {
    switch (apiStatus.toLowerCase()) {
      case 'created':
        return PaymentStatus.pending;
      case 'pending':
        return PaymentStatus.processing;
      case 'succeeded':
        return PaymentStatus.succeeded;
      case 'failed':
        return PaymentStatus.failed;
      case 'cancelled':
        return PaymentStatus.cancelled;
      case 'expired':
        return PaymentStatus.expired;
      default:
        return PaymentStatus.pending;
    }
  }
  
  /// Check if payment is in a final state
  bool _isPaymentComplete(PaymentStatus status) {
    return [
      PaymentStatus.succeeded,
      PaymentStatus.failed,
      PaymentStatus.cancelled,
      PaymentStatus.expired,
    ].contains(status);
  }
  
  /// Update status and notify listeners
  void _updateStatus(PaymentStatus newStatus) {
    _status = newStatus;
    notifyListeners();
  }
  
  /// Reset payment flow
  void reset() {
    _status = PaymentStatus.idle;
    _currentSession = null;
    _lastError = null;
    notifyListeners();
  }
}
```

### Payment UI Widget

```dart
class PaymentWidget extends StatefulWidget {
  final PaymentRequest paymentRequest;
  final VoidCallback? onSuccess;
  final VoidCallback? onFailure;
  final VoidCallback? onCancel;
  
  const PaymentWidget({
    Key? key,
    required this.paymentRequest,
    this.onSuccess,
    this.onFailure,
    this.onCancel,
  }) : super(key: key);
  
  @override
  _PaymentWidgetState createState() => _PaymentWidgetState();
}

class _PaymentWidgetState extends State<PaymentWidget> {
  late PaymentFlowManager _paymentManager;
  
  @override
  void initState() {
    super.initState();
    _paymentManager = PaymentFlowManager();
    _paymentManager.addListener(_onPaymentStatusChanged);
  }
  
  @override
  void dispose() {
    _paymentManager.removeListener(_onPaymentStatusChanged);
    _paymentManager.dispose();
    super.dispose();
  }
  
  void _onPaymentStatusChanged() {
    switch (_paymentManager.status) {
      case PaymentStatus.succeeded:
        widget.onSuccess?.call();
        break;
      case PaymentStatus.failed:
        widget.onFailure?.call();
        if (_paymentManager.lastError != null) {
          PaymentErrorHandler.showErrorDialog(context, _paymentManager.lastError!);
        }
        break;
      case PaymentStatus.cancelled:
        widget.onCancel?.call();
        break;
      default:
        break;
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _paymentManager,
      builder: (context, child) {
        return Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildStatusIndicator(),
            const SizedBox(height: 20),
            _buildActionButton(),
          ],
        );
      },
    );
  }
  
  Widget _buildStatusIndicator() {
    switch (_paymentManager.status) {
      case PaymentStatus.idle:
        return const Icon(Icons.payment, size: 64, color: Colors.blue);
      case PaymentStatus.creating:
      case PaymentStatus.processing:
        return const CircularProgressIndicator();
      case PaymentStatus.pending:
        return Column(
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 10),
            Text('Payment session created. Waiting for payment...'),
          ],
        );
      case PaymentStatus.succeeded:
        return const Icon(Icons.check_circle, size: 64, color: Colors.green);
      case PaymentStatus.failed:
        return const Icon(Icons.error, size: 64, color: Colors.red);
      case PaymentStatus.cancelled:
        return const Icon(Icons.cancel, size: 64, color: Colors.orange);
      case PaymentStatus.expired:
        return const Icon(Icons.timer_off, size: 64, color: Colors.grey);
    }
  }
  
  Widget _buildActionButton() {
    switch (_paymentManager.status) {
      case PaymentStatus.idle:
        return ElevatedButton(
          onPressed: () => _paymentManager.startPayment(widget.paymentRequest),
          child: const Text('Start Payment'),
        );
      case PaymentStatus.failed:
        return ElevatedButton(
          onPressed: () {
            _paymentManager.reset();
            _paymentManager.startPayment(widget.paymentRequest);
          },
          child: const Text('Retry Payment'),
        );
      default:
        return const SizedBox.shrink();
    }
  }
}
```

## 🔐 Production Considerations

### Security Best Practices

```dart
class PaymentSecurity {
  /// Validate payment amounts to prevent manipulation
  static bool validateAmount(double amount) {
    // Check for reasonable limits
    if (amount <= 0 || amount > 1000000) {
      return false;
    }
    
    // Check for precision (max 2 decimal places)
    final rounded = double.parse(amount.toStringAsFixed(2));
    return (amount - rounded).abs() < 0.001;
  }
  
  /// Sanitize user input
  static String sanitizeInput(String input) {
    return input.trim().replaceAll(RegExp(r'[<>"\']'), '');
  }
  
  /// Generate secure invoice numbers
  static String generateInvoiceNumber(String prefix) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = Random().nextInt(9999).toString().padLeft(4, '0');
    return '${prefix}_${timestamp}_$random';
  }
}
```

### Performance Optimization

```dart
class PaymentCache {
  static final Map<String, PaymentStatusResponse> _statusCache = {};
  static const Duration _cacheExpiry = Duration(minutes: 5);
  
  /// Cache payment status with expiry
  static void cacheStatus(String sessionId, PaymentStatusResponse status) {
    _statusCache[sessionId] = status;
    
    // Auto-expire cache
    Timer(_cacheExpiry, () => _statusCache.remove(sessionId));
  }
  
  /// Get cached status if available and not expired
  static PaymentStatusResponse? getCachedStatus(String sessionId) {
    return _statusCache[sessionId];
  }
  
  /// Clear all cached data
  static void clearCache() {
    _statusCache.clear();
  }
}
```

### Logging and Analytics

```dart
class PaymentAnalytics {
  /// Log payment events for analytics
  static void logPaymentEvent(String event, Map<String, dynamic> parameters) {
    // Remove sensitive data
    final sanitizedParams = Map<String, dynamic>.from(parameters);
    sanitizedParams.remove('customer_email');
    sanitizedParams.remove('customer_phone');
    
    // Log to your analytics service
    debugPrint('Payment Event: $event - $sanitizedParams');
  }
  
  /// Track payment funnel
  static void trackPaymentStep(String step, String sessionId) {
    logPaymentEvent('payment_step', {
      'step': step,
      'session_id': sessionId,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }
}
```

## 🧪 Testing Recommendations

### Unit Tests

```dart
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';

class MockHttpClient extends Mock implements http.Client {}

void main() {
  group('ZohoPaymentService Tests', () {
    late MockHttpClient mockClient;
    
    setUp(() {
      mockClient = MockHttpClient();
    });
    
    test('should create payment session successfully', () async {
      // Arrange
      final request = PaymentRequest(
        amount: 100.0,
        description: 'Test Payment',
        invoiceNumber: 'TEST-001',
        customerId: 'CUST-001',
      );
      
      when(mockClient.post(any, headers: anyNamed('headers'), body: anyNamed('body')))
          .thenAnswer((_) async => http.Response(
                jsonEncode({
                  'success': true,
                  'message': 'Payment session created',
                  'data': {
                    'payment_session_id': 'session_123',
                    'amount': '100.00',
                    'currency': 'INR',
                  }
                }),
                201,
              ));
      
      // Act
      final result = await ZohoPaymentService.createPaymentSession(request);
      
      // Assert
      expect(result.success, true);
      expect(result.data.paymentSessionId, 'session_123');
    });
    
    test('should handle payment creation errors', () async {
      // Arrange
      final request = PaymentRequest(
        amount: -100.0, // Invalid amount
        description: 'Test Payment',
        invoiceNumber: 'TEST-001',
        customerId: 'CUST-001',
      );
      
      // Act & Assert
      expect(
        () => ZohoPaymentService.createPaymentSession(request),
        throwsA(isA<PaymentException>()),
      );
    });
  });
}
```

### Integration Tests

```dart
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();
  
  group('Payment Flow Integration Tests', () {
    testWidgets('complete payment flow', (WidgetTester tester) async {
      // Test the complete payment flow in a real environment
      await tester.pumpWidget(MyApp());
      
      // Navigate to payment screen
      await tester.tap(find.text('Make Payment'));
      await tester.pumpAndSettle();
      
      // Fill payment form
      await tester.enterText(find.byKey(Key('amount_field')), '100.00');
      await tester.enterText(find.byKey(Key('description_field')), 'Test Payment');
      
      // Start payment
      await tester.tap(find.text('Start Payment'));
      await tester.pumpAndSettle();
      
      // Verify payment session creation
      expect(find.text('Payment session created'), findsOneWidget);
    });
  });
}
```

## 📱 Example Usage

```dart
class PaymentScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final paymentRequest = PaymentRequest(
      amount: 100.0,
      currency: 'INR',
      description: 'Aquaculture Product Purchase',
      invoiceNumber: PaymentSecurity.generateInvoiceNumber('AQP'),
      customerId: 'customer_123',
      customerName: 'John Doe',
      customerEmail: '<EMAIL>',
      customerPhone: '+919876543210',
    );
    
    return Scaffold(
      appBar: AppBar(title: Text('Payment')),
      body: PaymentWidget(
        paymentRequest: paymentRequest,
        onSuccess: () {
          PaymentAnalytics.logPaymentEvent('payment_success', {
            'amount': paymentRequest.amount,
            'invoice': paymentRequest.invoiceNumber,
          });
          Navigator.of(context).pushReplacementNamed('/payment-success');
        },
        onFailure: () {
          PaymentAnalytics.logPaymentEvent('payment_failure', {
            'amount': paymentRequest.amount,
            'invoice': paymentRequest.invoiceNumber,
          });
        },
        onCancel: () {
          Navigator.of(context).pop();
        },
      ),
    );
  }
}
```
