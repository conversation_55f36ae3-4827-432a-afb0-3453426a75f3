import 'package:aquapartner/domain/entities/customer.dart';
import 'package:aquapartner/presentation/cubit/account_statement/account_statement_state.dart';
import 'package:intl/intl.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:path_provider/path_provider.dart';
import 'dart:io';

class PdfService {
  static Future<File> generateAccountStatementPdf({
    required Customer? customer, // Made nullable as per your previous code
    required List<StatementItem> transactions,
    required double openingBalance,
    required double closingBalance,
    required DateTimeRange
    accountStatementPeriod, // Using Flutter's DateTimeRange
  }) async {
    final pdf = pw.Document();

    final dateFormat = DateFormat('dd-MMM-yyyy');
    final generationDate = dateFormat.format(DateTime.now());

    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4.copyWith(
          marginTop: 1.5 * PdfPageFormat.cm,
          marginLeft: 1.5 * PdfPageFormat.cm,
          marginRight: 1.5 * PdfPageFormat.cm,
          marginBottom: 1.5 * PdfPageFormat.cm,
        ),
        // HEADER: Displays only on the first page
        header: (pw.Context context) {
          if (context.pageNumber == 1) {
            // Changed from 0 to 1
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.center,
              children: [
                pw.Text(
                  "Coastal Aquaculture Research Institute Private Limited", // Replace with your company name
                  style: pw.TextStyle(
                    fontSize: 16,
                    fontWeight: pw.FontWeight.bold,
                    color: PdfColor.fromHex('#1544CE'),
                  ),
                  textAlign: pw.TextAlign.center,
                ),
                pw.SizedBox(height: 0.3 * PdfPageFormat.cm),
                pw.Text(
                  "Type II/17, Rajive Gandhi Salai, Dr.VSI Estate, Thiruvanmiyur, Chennai, Tamilnadu - 600041", // Replace with your address
                  style: const pw.TextStyle(fontSize: 10),
                  textAlign: pw.TextAlign.center,
                ),
                pw.SizedBox(height: 0.7 * PdfPageFormat.cm),
                pw.Divider(),
                pw.SizedBox(height: 0.5 * PdfPageFormat.cm),
                pw.Text(
                  "ACCOUNT STATEMENT",
                  style: pw.TextStyle(
                    fontSize: 14,
                    fontWeight: pw.FontWeight.bold,
                  ),
                  textAlign: pw.TextAlign.center,
                ),
                pw.SizedBox(height: 0.5 * PdfPageFormat.cm),
                pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  children: [
                    pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                        pw.Text(
                          customer?.companyName ?? 'N/A - Customer Name',
                          style: pw.TextStyle(
                            fontWeight: pw.FontWeight.bold,
                            fontSize: 11,
                          ),
                        ),
                        pw.SizedBox(height: 2),
                        pw.Text(
                          customer?.billingAddress.replaceAllMapped(
                                RegExp(r',(?!\s)'),
                                (match) => ', ',
                              ) ??
                              'N/A - Address',
                          style: const pw.TextStyle(fontSize: 9),
                          maxLines: 3,
                        ),
                        if (customer?.gstNo != null &&
                            customer!.gstNo.isNotEmpty) ...[
                          pw.SizedBox(height: 2),
                          pw.Text(
                            'GST No: ${customer.gstNo}',
                            style: const pw.TextStyle(fontSize: 9),
                          ),
                        ],
                      ],
                    ),
                    pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.end,
                      children: [
                        pw.Text(
                          "Statement Period:",
                          style: pw.TextStyle(
                            fontSize: 9,
                            fontWeight: pw.FontWeight.bold,
                          ),
                        ),
                        pw.Text(
                          "${dateFormat.format(accountStatementPeriod.startDate)} to ${dateFormat.format(accountStatementPeriod.endDate)}",
                          style: const pw.TextStyle(fontSize: 9),
                        ),
                        pw.SizedBox(height: 3),
                        pw.Text(
                          "Statement Date:",
                          style: pw.TextStyle(
                            fontSize: 9,
                            fontWeight: pw.FontWeight.bold,
                          ),
                        ),
                        pw.Text(
                          generationDate,
                          style: const pw.TextStyle(fontSize: 9),
                        ),
                      ],
                    ),
                  ],
                ),

                pw.SizedBox(
                  height: 0.8 * PdfPageFormat.cm,
                ), // Space before main content begins
              ],
            );
          }
          return pw.Container(
            height: 0,
          ); // No header on subsequent pages (or minimal height for margin calculation)
        },

        // FOOTER: Displays on all pages
        footer: (pw.Context context) {
          return pw.Column(
            mainAxisSize: pw.MainAxisSize.min,
            crossAxisAlignment: pw.CrossAxisAlignment.center,
            children: [
              pw.Divider(height: 1, thickness: 0.5, color: PdfColors.grey400),
              pw.SizedBox(height: 0.2 * PdfPageFormat.cm),
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Text(
                    'This is a computer-generated statement and does not require a signature.',
                    style: pw.TextStyle(
                      fontSize: 7,
                      color: PdfColors.grey600,
                      fontStyle: pw.FontStyle.italic,
                    ),
                  ),
                  pw.Text(
                    'Page ${context.pageNumber} of ${context.pagesCount}', // Removed the + 1
                    style: const pw.TextStyle(
                      fontSize: 8,
                      color: PdfColors.grey700,
                    ),
                  ),
                ],
              ),
            ],
          );
        },

        // BUILD: Main content that will flow across pages
        build:
            (pw.Context context) => [
              // Transaction Table
              pw.Table(
                border: pw.TableBorder.all(
                  color: PdfColors.grey300,
                  width: 0.5,
                ),
                columnWidths: {
                  0: const pw.FlexColumnWidth(1.5), // Date
                  1: const pw.FlexColumnWidth(4.5), // Description
                  2: const pw.FlexColumnWidth(1.8), // Ref/Type
                  3: const pw.FlexColumnWidth(2.0), // Debit
                  4: const pw.FlexColumnWidth(2.0), // Credit
                  5: const pw.FlexColumnWidth(2.2), // Balance
                },
                children: [
                  // Table Header Row (repeats automatically if table spans pages)
                  pw.TableRow(
                    decoration: pw.BoxDecoration(
                      color: PdfColor.fromHex('#E0E0E0'),
                    ), // Light grey header
                    children: [
                      _buildHeaderCell('Date'),
                      _buildHeaderCell('Description / Narration'),
                      _buildHeaderCell('Ref / Type'),
                      _buildHeaderCell('Debit', alignment: pw.TextAlign.right),
                      _buildHeaderCell('Credit', alignment: pw.TextAlign.right),
                      _buildHeaderCell(
                        'Balance',
                        alignment: pw.TextAlign.right,
                      ),
                    ],
                  ),
                  // Data Rows
                  // Optional: Row for Opening Balance if not implicitly the first balance
                  if (transactions.isEmpty ||
                      transactions.first.balance != openingBalance) ...[
                    // Or a more robust check
                    pw.TableRow(
                      decoration: const pw.BoxDecoration(
                        color: PdfColors.white,
                      ),
                      children: [
                        _buildTableCell(
                          dateFormat.format(accountStatementPeriod.startDate),
                          fontStyle: pw.FontStyle.italic,
                        ),
                        _buildTableCell(
                          'Opening Balance',
                          fontStyle: pw.FontStyle.italic,
                        ),
                        _buildTableCell('', fontStyle: pw.FontStyle.italic),
                        _buildTableCell(
                          '',
                          fontStyle: pw.FontStyle.italic,
                          alignment: pw.TextAlign.right,
                        ),
                        _buildTableCell(
                          '',
                          fontStyle: pw.FontStyle.italic,
                          alignment: pw.TextAlign.right,
                        ),
                        _buildTableCell(
                          openingBalance.toStringAsFixed(2),
                          fontStyle: pw.FontStyle.italic,
                          alignment: pw.TextAlign.right,
                        ),
                      ],
                    ),
                  ],
                  ...transactions.map((item) {
                    bool isDebit =
                        item.type.toUpperCase().contains('DEBIT') ||
                        item.amount < 0; // More flexible debit check
                    return pw.TableRow(
                      children: [
                        _buildTableCell(
                          dateFormat.format(item.date),
                          alignment: pw.TextAlign.left,
                        ),
                        _buildTableCell(item.description),
                        _buildTableCell(
                          item.type,
                          alignment: pw.TextAlign.center,
                        ), // e.g., Invoice, Payment
                        _buildTableCell(
                          isDebit ? item.amount.abs().toStringAsFixed(2) : '',
                          alignment: pw.TextAlign.right,
                        ),
                        _buildTableCell(
                          !isDebit ? item.amount.abs().toStringAsFixed(2) : '',
                          alignment: pw.TextAlign.right,
                        ),
                        _buildTableCell(
                          item.balance.toStringAsFixed(2),
                          alignment: pw.TextAlign.right,
                        ),
                      ],
                    );
                  }),
                ],
              ),
              pw.SizedBox(height: 0.8 * PdfPageFormat.cm),

              // Summary Section
              pw.Row(
                mainAxisAlignment:
                    pw.MainAxisAlignment.end, // Align summary to the right
                children: [
                  pw.Container(
                    width:
                        PdfPageFormat.a4.availableWidth /
                        2.5, // Constrain width
                    padding: const pw.EdgeInsets.all(8),
                    decoration: pw.BoxDecoration(
                      color: PdfColors.grey100,
                      border: pw.Border.all(
                        color: PdfColors.grey300,
                        width: 0.5,
                      ),
                      borderRadius: pw.BorderRadius.circular(3),
                    ),
                    child: pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                        pw.Text(
                          'Statement Summary',
                          style: pw.TextStyle(
                            fontSize: 11,
                            fontWeight: pw.FontWeight.bold,
                          ),
                        ),
                        pw.Divider(thickness: 0.5, color: PdfColors.grey400),
                        pw.SizedBox(height: 5),
                        _buildSummaryRow(
                          'Opening Balance:',
                          openingBalance.toStringAsFixed(2),
                        ),
                        pw.SizedBox(height: 3),
                        // You could calculate and add Total Debits and Total Credits here if needed
                        // For example:
                        // _buildSummaryRow('Total Debits:', totalDebits.toStringAsFixed(2)),
                        // _buildSummaryRow('Total Credits:', totalCredits.toStringAsFixed(2)),
                        // pw.SizedBox(height: 3),
                        _buildSummaryRow(
                          'Closing Balance:',
                          closingBalance.toStringAsFixed(2),
                          isBold: true,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              pw.SizedBox(
                height: 1 * PdfPageFormat.cm,
              ), // Ensure some space at the end
            ],
      ),
    );

    final outputDir = await getTemporaryDirectory();
    final timestamp =
        DateTime.now().toIso8601String().replaceAll(':', '-').split('.')[0];
    final customerNameSanitized =
        customer?.companyName.replaceAll(RegExp(r'[^a-zA-Z0-9]'), '_') ??
        'Statement';
    final fileName = 'AccountStatement_${customerNameSanitized}_$timestamp.pdf';
    final file = File('${outputDir.path}/$fileName');

    await file.writeAsBytes(await pdf.save());
    print('PDF Generated: ${file.path}'); // For debugging
    return file;
  }

  // Helper method for table header cells
  static pw.Widget _buildHeaderCell(
    String text, {
    pw.TextAlign alignment = pw.TextAlign.left,
  }) {
    return pw.Padding(
      padding: const pw.EdgeInsets.symmetric(
        horizontal: 3,
        vertical: 5,
      ), // Adjusted padding
      child: pw.Text(
        text,
        style: pw.TextStyle(
          fontWeight: pw.FontWeight.bold,
          fontSize: 8.5,
        ), // Adjusted font size
        textAlign: alignment,
        softWrap: true,
      ),
    );
  }

  // Helper method for table data cells
  static pw.Widget _buildTableCell(
    String text, {
    pw.TextAlign alignment = pw.TextAlign.left,
    pw.FontStyle fontStyle = pw.FontStyle.normal,
  }) {
    return pw.Padding(
      padding: const pw.EdgeInsets.symmetric(
        horizontal: 3,
        vertical: 4,
      ), // Adjusted padding
      child: pw.Text(
        text,
        style: pw.TextStyle(
          fontSize: 8,
          fontStyle: fontStyle,
        ), // Adjusted font size
        textAlign: alignment,
        softWrap: true,
      ),
    );
  }

  // Helper method for summary rows
  static pw.Widget _buildSummaryRow(
    String label,
    String value, {
    bool isBold = false,
  }) {
    return pw.Row(
      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
      children: [
        pw.Text(
          label,
          style: pw.TextStyle(
            fontSize: 9,
            fontWeight: isBold ? pw.FontWeight.bold : pw.FontWeight.normal,
          ),
        ),
        pw.Text(
          value,
          style: pw.TextStyle(
            fontSize: 9,
            fontWeight: isBold ? pw.FontWeight.bold : pw.FontWeight.normal,
          ),
        ),
      ],
    );
  }
}

class StatementItem {
  final DateTime date;
  final String description;
  final String type; // 'credit' or 'debit'
  final double amount;
  final double balance;

  StatementItem({
    required this.date,
    required this.description,
    required this.type,
    required this.amount,
    required this.balance,
  });
}
