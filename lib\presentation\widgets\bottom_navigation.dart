import 'package:aqua_ui/aqua_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../core/constants/app_constants.dart';
import '../cubit/navigation/navigation_cubit.dart';
import '../cubit/navigation/navigation_state.dart';

class BottomNavigation extends StatelessWidget {
  const BottomNavigation({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<NavigationCubit, NavigationState>(
      buildWhen: (previous, current) => previous.index != current.index,
      builder: (context, state) {
        return Container(
          decoration: BoxDecoration(
            border: Border(
              top: BorderSide(color: Color(0xFFE2E2E2), width: 1.0),
            ),
          ),
          child: BottomNavigationBar(
            backgroundColor: acWhiteColor,
            currentIndex: state.index,
            selectedItemColor: Color(0xFF0C84FE), // Color for selected item
            unselectedItemColor: acBlackColor, // Color for unselected items

            onTap:
                (index) => context.read<NavigationCubit>().getNavBarItem(index),
            type: BottomNavigationBarType.fixed,
            items: [
              BottomNavigationBarItem(
                icon: Image.asset(
                  AppConstants.iconHomeUnSelected,
                  width: 24,
                  height: 24,
                ),
                activeIcon: Image.asset(
                  AppConstants.iconHomeSelected,
                  width: 24,
                  height: 24,
                ),
                label: 'HOME',
              ),
              BottomNavigationBarItem(
                icon: Image.asset(
                  AppConstants.iconPriceListUnselected,
                  width: 24,
                  height: 24,
                ),
                activeIcon: Image.asset(
                  AppConstants.iconPriceListSelected,
                  width: 24,
                  height: 24,
                ),
                label: 'PRICE LIST',
              ),
              BottomNavigationBarItem(
                icon: Image.asset(
                  AppConstants.iconProductListUnselected,
                  width: 24,
                  height: 24,
                ),
                activeIcon: Image.asset(
                  AppConstants.iconProductsListSelected,
                  width: 24,
                  height: 24,
                ),
                label: 'PRODUCTS',
              ),
            ],
          ),
        );
      },
    );
  }
}
