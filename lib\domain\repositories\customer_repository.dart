import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../entities/customer.dart';

abstract class CustomerRepository {
  /// Get a customer by mobile number
  Future<Either<Failure, Customer?>> getCustomerByMobileNumber(
    String mobileNumber,
  );
  Future<Either<Failure, Customer?>> getCustomerByCustomerId(String customerId);

  Future<Either<Failure, void>> saveCustomer(Customer customer);

  Future<Either<Failure, DateTime?>> getLastSyncTime();

  Future<Either<Failure, void>> updateLastSyncTime(DateTime time);

  /// Sync a specific customer from server to local by mobile number
  Future<Either<Failure, Customer?>> syncCustomerByCustomerId(
    String customerId,
  );

  /// Check if sync is needed based on last sync time
  Future<Either<Failure, bool>> checkIfSyncNeeded();
}
