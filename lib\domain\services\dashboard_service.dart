import 'dart:async';
import 'package:dartz/dartz.dart';

import '../../core/error/failures.dart';
import '../../core/utils/logger.dart';
import '../entities/dashboard/dashboard_entity.dart';
import '../entities/sync_status.dart';
import '../repositories/dashboard_repository.dart';
import '../usecases/customer_usercases.dart';
import '../usecases/dashboard_usecases.dart';
import '../usecases/user_usecases.dart';

class DashboardService {
  final GetUserUseCase getUserUseCase;
  final GetCustomerByMobileNumber getCustomerByMobileNumber;
  final GetDashboardUseCase getDashboardUseCase;
  final DashboardRepository dashboardRepository;
  final AppLogger logger;

  // Cache the customer ID to avoid repeated lookups
  String? _cachedCustomerId;
  DateTime? _cacheTimestamp;
  static const _cacheDuration = Duration(
    minutes: 30,
  ); // Cache valid for 30 minutes

  // Stream controller for sync status
  final _syncStatusController = StreamController<SyncStatus>.broadcast();

  /// Stream of sync status updates
  Stream<SyncStatus> get syncStatusStream => _syncStatusController.stream;

  DashboardService({
    required this.getUserUseCase,
    required this.getCustomerByMobileNumber,
    required this.getDashboardUseCase,
    required this.dashboardRepository,
    required this.logger,
  });

  /// Gets the dashboard data by handling the entire chain of dependencies
  Future<Either<Failure, DashboardEntity>> getDashboard() async {
    try {
      // Step 1: Get the customer ID (from cache if available)
      final customerIdResult = await getCustomerId();

      return customerIdResult.fold((failure) => Left(failure), (customerId) {
        // Step 2: Use the customer ID to get dashboard data
        return getDashboardUseCase(customerId);
      });
    } catch (e) {
      logger.e('Unexpected error in DashboardService: $e');
      return Left(ServerFailure());
    }
  }

  /// Gets the customer ID by first getting the user's mobile number
  Future<Either<Failure, String>> getCustomerId() async {
    // Check if we have a valid cached customer ID
    if (_cachedCustomerId != null &&
        _cacheTimestamp != null &&
        DateTime.now().difference(_cacheTimestamp!) < _cacheDuration) {
      logger.i('Using cached customer ID: $_cachedCustomerId');
      return Right(_cachedCustomerId!);
    }

    // Step 1: Get the current user
    final userResult = await getUserUseCase();

    return userResult.fold(
      (failure) {
        logger.e('Failed to get user:');
        return Left(failure);
      },
      (user) async {
        // Step 2: Get the customer using the user's mobile number
        final customerResult = await getCustomerByMobileNumber(
          user.phoneNumber,
        );

        return customerResult.fold(
          (failure) {
            logger.e('Failed to get customer: ');
            return Left(failure);
          },
          (customer) {
            if (customer == null) {
              logger.e(
                'Customer not found for mobile number: ${user.phoneNumber}',
              );
              return Left(ServerFailure());
            }

            logger.i(
              'Customer retrieved successfully: ${customer.companyName}',
            );

            // Cache the customer ID
            _cachedCustomerId = customer.customerId;
            _cacheTimestamp = DateTime.now();

            logger.i(
              'Retrieved and cached customer ID: ${customer.customerId}',
            );
            return Right(customer.customerId);
          },
        );
      },
    );
  }

  /// Check if a sync operation is currently in progress for the specified customer
  bool isSyncing(String customerId) {
    return dashboardRepository.isSyncing(customerId);
  }

  /// Get the last sync time for the specified customer
  Future<DateTime?> getLastSyncTime(String customerId) async {
    return await dashboardRepository.getLastSyncTime(customerId);
  }

  /// Sync dashboard data for the specified customer
  Future<Either<Failure, bool>> syncDashboard(String customerId) async {
    return await dashboardRepository.syncDashboard(customerId);
  }

  /// Clears the cached customer ID
  void clearCache() {
    _cachedCustomerId = null;
    _cacheTimestamp = null;
  }

  /// Clean up resources
  void dispose() {
    _syncStatusController.close();
  }
}
