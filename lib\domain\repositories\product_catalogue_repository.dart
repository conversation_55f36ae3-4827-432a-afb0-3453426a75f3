import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../entities/product_catalogue.dart';

abstract class ProductCatalogueRepository {
  /// Gets product catalogues from local cache first
  Future<Either<Failure, List<ProductCatalogue>>> getProductCatalogues();
  
  /// Forces a sync from remote source
  Future<Either<Failure, List<ProductCatalogue>>> syncProductCatalogues();
  
  /// Gets a specific product catalogue by name
  Future<Either<Failure, ProductCatalogue?>> getProductCatalogueByName(String name);
}