import 'package:flutter/material.dart';
import 'package:uni_links/uni_links.dart';
import 'dart:async';

// ... other imports

bool _initialUriIsHandled = false;

void main() {
  runApp(const MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  StreamSubscription? _sub;

  @override
  void initState() {
    super.initState();
    _handleInitialUri();
    _handleIncomingLinks();
  }

  @override
  void dispose() {
    _sub?.cancel();
    super.dispose();
  }

  Future<void> _handleInitialUri() async {
    if (!_initialUriIsHandled) {
      _initialUriIsHandled = true;
      try {
        final uri = await getInitialUri();
        if (uri != null) {
          _handlePaymentDeepLink(uri);
        }
      } catch (e) {
        print('Error handling initial URI: $e');
      }
    }
  }

  void _handleIncomingLinks() {
    _sub = uriLinkStream.listen(
      (Uri? uri) {
        if (uri != null) {
          _handlePaymentDeepLink(uri);
        }
      },
      onError: (err) {
        print('Error handling incoming links: $err');
      },
    );
  }

  void _handlePaymentDeepLink(Uri uri) {
    // Handle payment success/failure deep links
    if (uri.path.contains('/payment/success')) {
      // Extract transaction ID and handle success
      final transactionId = uri.queryParameters['transaction_id'];
      // Navigate to success page or update UI
    } else if (uri.path.contains('/payment/failure')) {
      // Handle payment failure
    }
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Your App',
      theme: ThemeData(primarySwatch: Colors.blue),
      home: const HomeScreen(),
      routes: {
        '/payment-success': (context) => const PaymentSuccessScreen(),
        '/payment-failure': (context) => const PaymentFailureScreen(),
      },
    );
  }
}
