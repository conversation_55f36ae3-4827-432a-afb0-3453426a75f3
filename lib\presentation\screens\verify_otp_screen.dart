import 'package:aqua_ui/aqua_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';

import '../../core/constants/app_constants.dart';
import '../../core/mixins/analytics_mixin.dart';
import '../../core/routes/app_router.dart';
import '../../domain/validators/input_validators.dart';
import '../cubit/auth/auth_cubit.dart';
import '../cubit/auth/auth_state.dart';
import '../cubit/connectivity/connectivity_cubit.dart';
import '../cubit/connectivity/connectivity_state.dart';
import '../../core/utils/logger.dart';

class VerifyOtpScreen extends StatefulWidget {
  final String phoneNumber;

  const VerifyOtpScreen({super.key, required this.phoneNumber});

  @override
  State<VerifyOtpScreen> createState() => _VerifyOtpScreenState();
}

class _VerifyOtpScreenState extends State<VerifyOtpScreen>
    with AnalyticsMixin<VerifyOtpScreen> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _otpController = TextEditingController();
  final AppLogger _logger = AppLogger();

  // Track user interactions
  DateTime? _lastInteractionTime;
  int _otpAttemptCount = 0;

  @override
  void initState() {
    super.initState();
    _lastInteractionTime = DateTime.now();

    // Track screen view with additional context
    trackEvent(
      'otp_verification_screen_viewed',
      params: {
        'phone_number_length': widget.phoneNumber.length.toString(),
        'phone_number_prefix': widget.phoneNumber.substring(0, 2),
      },
    );
  }

  @override
  void dispose() {
    _otpController.dispose();
    super.dispose();
  }

  // Handle back button press
  void _goBackToLogin() {
    trackUserInteraction(
      'back_to_login',
      'button',
      additionalParams: {
        'time_on_screen':
            _lastInteractionTime != null
                ? DateTime.now()
                    .difference(_lastInteractionTime!)
                    .inSeconds
                    .toString()
                : '0',
      },
    );

    context.read<AuthCubit>().resetState();
    AppRouter.navigateToLogin(phoneNumber: widget.phoneNumber);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocConsumer<AuthCubit, AuthState>(
        listener: (context, state) {
          if (state is OtpVerificationError) {
            _logger.e("OTP verification error: ${state.message}");

            // Track failed verification attempts
            trackEvent(
              "login_error",
              params: {
                "method": "otp",
                "error_type": "invalid_otp",
                "attempt_number": _otpAttemptCount.toString(),
                "otp_length": _otpController.text.length.toString(),
              },
            );

            // Show appropriate error message for OTP verification
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text("Invalid OTP. Please try again.")),
            );
          } else if (state is AuthSuccess) {
            _logger.i("Authentication successful, navigating to home");

            // Track successful authentication flow completion
            trackUserFlow(
              flowName: 'authentication',
              stepName: 'otp_verified',
              status: 'completed',
              additionalParams: {
                'phone_number': widget.phoneNumber,
                'attempts_before_success': _otpAttemptCount.toString(),
                'time_to_success':
                    _lastInteractionTime != null
                        ? DateTime.now()
                            .difference(_lastInteractionTime!)
                            .inSeconds
                            .toString()
                        : '0',
              },
            );

            // Improved analytics for OTP verification
            trackEvent(
              "login_success",
              params: {
                "method": "otp",
                "attempts_before_success": _otpAttemptCount.toString(),
                "time_to_success":
                    _lastInteractionTime != null
                        ? DateTime.now()
                            .difference(_lastInteractionTime!)
                            .inSeconds
                            .toString()
                        : '0',
              },
            );

            AppRouter.navigateToHome();
          }
        },
        builder: (context, state) {
          return PopScope<Object?>(
            canPop: false,
            onPopInvokedWithResult: (bool didPop, Object? result) {},
            child: SafeArea(
              child: SingleChildScrollView(
                child: Container(
                  color: acWhiteColor,
                  padding: EdgeInsets.only(
                    top: screenHeightPercentage(context, percentage: 0.1),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Form(
                      key: _formKey,
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          SvgPicture.asset(
                            AppConstants.imgPartnerLogo,
                            height: 35,
                          ),
                          vSpace8,
                          AquaText.heading(
                            'Verify OTP',
                            weight: AquaFontWeight.semibold,
                            color: Colors.grey,
                          ),
                          vSpace2,
                          Row(
                            children: [
                              Expanded(
                                child: AquaText.subheadline(
                                  "6-Digit OTP sent to +91${widget.phoneNumber}",
                                  color: Colors.black,
                                ),
                              ),
                              // Edit button
                              TextButton(
                                onPressed:
                                    state is AuthLoading
                                        ? null
                                        : _goBackToLogin,
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    const Icon(
                                      Icons.edit,
                                      size: 16,
                                      color: acPrimaryBlue,
                                    ),
                                    const SizedBox(width: 4),
                                    AquaText.body('Edit'),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          vSpace2,
                          AquaTextFormField(
                            readOnly: state is AuthLoading,
                            floatingLabelBehavior: FloatingLabelBehavior.always,
                            controller: _otpController,
                            maxLength: 6,
                            keyboardType: TextInputType.number,
                            maxLines: 1,
                            prefixTextStyle:
                                AquaText.subheadline(
                                  "",
                                  color: acTextSecondaryColor,
                                ).style,
                            labelText: "",
                            hintText: "Enter the OTP",
                            validator: InputValidators.validateOtp,
                          ),
                          vSpace8,
                          BlocBuilder<AuthCubit, AuthState>(
                            builder: (context, state) {
                              final isLoading = state is AuthLoading;
                              final connectivityState =
                                  context.watch<ConnectivityCubit>().state;
                              final isOffline =
                                  connectivityState.status ==
                                  ConnectionStatus.disconnected;
                              return AquaButton(
                                title: "Verify OTP",
                                isLoading: isLoading,
                                onTap:
                                    isOffline || isLoading
                                        ? null
                                        : () {
                                          _logger.d("Verify OTP");
                                          _lastInteractionTime = DateTime.now();
                                          _otpAttemptCount++;

                                          // Track verify OTP button tap
                                          trackUserInteraction(
                                            'verify_otp_button_tapped',
                                            'button',
                                            additionalParams: {
                                              'attempt_number':
                                                  _otpAttemptCount.toString(),
                                              'otp_length':
                                                  _otpController.text.length
                                                      .toString(),
                                              'is_offline':
                                                  isOffline.toString(),
                                            },
                                          );

                                          // Debug: Check form validation
                                          final isFormValid =
                                              _formKey.currentState
                                                  ?.validate() ??
                                              false;
                                          _logger.d(
                                            "Form validation result: $isFormValid",
                                          );
                                          _logger.d(
                                            "OTP text: '${_otpController.text}'",
                                          );
                                          _logger.d(
                                            "OTP length: ${_otpController.text.length}",
                                          );

                                          if (isFormValid) {
                                            // Track start of OTP verification
                                            trackUserFlow(
                                              flowName: 'authentication',
                                              stepName:
                                                  'otp_verification_started',
                                              status: 'started',
                                              additionalParams: {
                                                'attempt_number':
                                                    _otpAttemptCount.toString(),
                                                'otp_length':
                                                    _otpController.text.length
                                                        .toString(),
                                              },
                                            );

                                            _logger.d(
                                              "Calling verifyOtp with: '${_otpController.text}'",
                                            );
                                            context.read<AuthCubit>().verifyOtp(
                                              _otpController.text,
                                            );
                                          } else {
                                            // Track validation failure
                                            _logger.e(
                                              "Form validation failed for OTP: '${_otpController.text}'",
                                            );
                                            trackEvent(
                                              'otp_form_validation_failed',
                                              params: {
                                                'attempt_number':
                                                    _otpAttemptCount.toString(),
                                                'otp_length':
                                                    _otpController.text.length
                                                        .toString(),
                                              },
                                            );
                                          }
                                        },
                              );
                            },
                          ),
                          vSpace4,
                          BlocBuilder<ConnectivityCubit, ConnectivityState>(
                            builder: (context, connectivityState) {
                              if (connectivityState.status ==
                                  ConnectionStatus.disconnected) {
                                return Padding(
                                  padding: const EdgeInsets.only(bottom: 16.0),
                                  child: AquaText.body(
                                    'No internet connection. You need to be online to login.',
                                    color: acRedColor,
                                    textAlign: TextAlign.center,
                                  ),
                                );
                              }
                              return const SizedBox.shrink();
                            },
                          ),
                          BlocBuilder<AuthCubit, AuthState>(
                            builder: (context, state) {
                              if (state is OtpVerificationError) {
                                return Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    vSpace3,
                                    AquaText.caption(
                                      "Invalid OTP, try again",
                                      color: acRedColor,
                                      textAlign: TextAlign.left,
                                    ),
                                  ],
                                );
                              }
                              return const SizedBox.shrink();
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  @override
  String get screenName => 'verify_otp';

  @override
  String get parentScreenName => 'login';
}
