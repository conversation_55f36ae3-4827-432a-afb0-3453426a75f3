import 'dart:io';
import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';
import '../../domain/entities/app_version.dart';
import '../../domain/usecases/check_mandatory_update.dart';
import '../../injection_container.dart' as di;
import '../services/version_checker_service.dart';

/// Helper class for testing the update mechanism
class UpdateTestHelper {
  /// Manually trigger an update check
  static Future<void> checkForUpdates(BuildContext context) async {
    if (!context.mounted) return;

    try {
      // Use the static methods directly
      final isUpdateRequired = await VersionCheckerService.isUpdateRequired();

      if (!context.mounted) return;

      if (isUpdateRequired) {
        await VersionCheckerService.showUpdateDialog(context);
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('No update required'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (!context.mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error checking for updates: $e'),
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  /// Get information about the current update configuration
  static Future<String> getUpdateConfigInfo() async {
    try {
      final checkMandatoryUpdate = di.sl<CheckMandatoryUpdate>();
      final result = await checkMandatoryUpdate.execute();

      // Get package info for current version
      final packageInfo = await PackageInfo.fromPlatform();
      final currentVersion =
          "${packageInfo.version}+${packageInfo.buildNumber}";

      final platform =
          Platform.isAndroid
              ? "Android"
              : Platform.isIOS
              ? "iOS"
              : "Unknown";
      final updateRequired = result.updateRequired ? "Yes" : "No";
      final updateType = result.isRecommendedOnly ? "Recommended" : "Mandatory";

      // Parse the current version
      final appVersion = AppVersion(currentVersion);

      return '''
Platform: $platform
Current Version: $currentVersion
  Major: ${appVersion.major}
  Minor: ${appVersion.minor}
  Patch: ${appVersion.patch}
  Build: ${appVersion.buildNumber}
Update Required: $updateRequired
Update Type: $updateType
Update Message: ${result.updateInfo?.message ?? "N/A"}
Store URL: ${result.updateInfo?.storeUrl ?? "N/A"}
''';
    } catch (e) {
      return 'Error getting update config: $e';
    }
  }

  /// Test a specific version against the current required version
  static Future<String> testVersionScenario(String testVersion) async {
    try {
      // Get the repository from the CheckMandatoryUpdate use case
      final repository = di.sl<CheckMandatoryUpdate>().repository;

      // Get the minimum required version from repository
      final minRequiredVersion = await repository.getMinRequiredVersion();
      final updatePolicy = await repository.getUpdatePolicy();

      // Parse the test version
      final testAppVersion = AppVersion(testVersion);

      // Note: With the current implementation, all updates are mandatory
      // so we don't need to check the policy anymore

      final isLowerThan = testAppVersion.isLowerThan(minRequiredVersion);

      final platform =
          Platform.isAndroid
              ? "Android"
              : Platform.isIOS
              ? "iOS"
              : "Unknown";

      return '''
Platform: $platform
Test Version: $testVersion
  Major: ${testAppVersion.major}
  Minor: ${testAppVersion.minor}
  Patch: ${testAppVersion.patch}
  Build: ${testAppVersion.buildNumber}
Required Version: ${minRequiredVersion.version}
  Major: ${minRequiredVersion.major}
  Minor: ${minRequiredVersion.minor}
  Patch: ${minRequiredVersion.patch}
  Build: ${minRequiredVersion.buildNumber}
Policy: $updatePolicy
Is Lower Than Required: $isLowerThan
Update Required: ${isLowerThan ? "Yes" : "No"}
Update Type: ${isLowerThan ? "Mandatory" : "None"} (All updates are mandatory)
''';
    } catch (e) {
      return 'Error testing version scenario: $e';
    }
  }
}
