// lib/data/repositories/credit_notes_repository_impl.dart
import 'package:dartz/dartz.dart';

import '../../core/error/exceptions.dart';
import '../../core/error/failures.dart';
import '../../core/network/network_info.dart'; // Assuming you have this

import '../../core/utils/logger.dart';
import '../../domain/entities/credit_notes/credit_note.dart';
import '../../domain/repositories/credit_notes_repository.dart';
import '../datasources/local/credit_notes_local_data_source.dart';
import '../datasources/remote/credit_notes_remote_data_source.dart';

class CreditNotesRepositoryImpl implements CreditNotesRepository {
  final CreditNotesRemoteDataSource remoteDataSource;
  final CreditNotesLocalDataSource localDataSource;
  final NetworkInfo networkInfo;
  final AppLogger logger;
  CreditNotesRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
    required this.networkInfo,
    required this.logger,
  });

  @override
  Future<Either<Failure, List<CreditNote>>> getCreditNotes(
    String customerId,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        final remoteNotes = await remoteDataSource.getCreditNotes(customerId);
        // Cache the fetched notes
        await localDataSource.cacheCreditNotes(remoteNotes);
        // Convert models to entities and return
        return Right(remoteNotes.map((model) => model.toEntity()).toList());
      } on ServerException catch (e) {
        logger.e('ServerException occurred: ${e.toString()}');
        return Left(ServerFailure());
      } on CacheException catch (e) {
        logger.e('CacheException occurred: ${e.toString()}');
        // Handle potential caching errors even when online
        return Left(CacheFailure());
      } catch (e) {
        return Left(ServerFailure());
      }
    } else {
      // Offline: Try to fetch from local cache
      try {
        final localNotes = await localDataSource.getCreditNotes(customerId);
        return Right(localNotes.map((model) => model.toEntity()).toList());
      } on CacheException catch (e) {
        logger.e('CacheException occurred: ${e.toString()}');
        return Left(CacheFailure());
      } catch (e) {
        logger.e('Exception occurred: ${e.toString()}');
        return Left(CacheFailure());
      }
    }
  }

  @override
  Future<Either<Failure, void>> syncCreditNotes(String customerId) async {
    if (await networkInfo.isConnected) {
      try {
        final remoteNotes = await remoteDataSource.getCreditNotes(customerId);
        await localDataSource.cacheCreditNotes(remoteNotes);
        return const Right(null); // Indicate success with void
      } on ServerException catch (e) {
        logger.e('ServerException occurred: ${e.toString()}');

        return Left(ServerFailure());
      } on CacheException catch (e) {
        logger.e('CacheException occurred: ${e.toString()}');

        return Left(CacheFailure());
      } catch (e) {
        return Left(ServerFailure());
      }
    } else {
      // Cannot sync when offline
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, List<CreditNote>>> getLocalCreditNotes(
    String customerId,
  ) async {
    try {
      final localNotes = await localDataSource.getCreditNotes(customerId);
      return Right(localNotes.map((model) => model.toEntity()).toList());
    } on CacheException catch (e) {
      logger.e('CacheException occurred: ${e.toString()}');

      return Left(CacheFailure());
    } catch (e) {
      return Left(CacheFailure());
    }
  }
}
