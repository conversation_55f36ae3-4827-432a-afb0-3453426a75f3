import 'package:aqua_ui/aqua_ui.dart';
import 'package:aquapartner/core/mixins/analytics_mixin.dart';
import 'package:aquapartner/core/utils/currency_formatter.dart';
import 'package:aquapartner/domain/entities/dues/dues.dart';

import 'package:aquapartner/presentation/cubit/dues/dues_cubit.dart';
import 'package:aquapartner/presentation/cubit/dues/dues_state.dart';
import 'package:aquapartner/presentation/widgets/horizontal_aging_filter_group.dart';
import 'package:aquapartner/presentation/widgets/loading_widget.dart';
import 'package:aquapartner/presentation/widgets/styled_generic_table.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';

class DuesPage extends StatefulWidget {
  const DuesPage({super.key});

  @override
  State<DuesPage> createState() => _DuesPageState();
}

class _DuesPageState extends State<DuesPage> with AnalyticsMixin {
  @override
  String get screenName => 'DuesPage';

  // Track screen view time
  DateTime? _screenViewStartTime;
  DateTime? _lastInteractionTime;

  int _filterChangeCount = 0;
  int _scrollCount = 0;

  // Track scroll position
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _screenViewStartTime = DateTime.now();
    _lastInteractionTime = _screenViewStartTime;

    // Add scroll listener for analytics
    _scrollController.addListener(_onScroll);

    context.read<DuesCubit>().loadDues();

    // Track page initialization
    trackEvent('dues_page_initialized');
  }

  @override
  void dispose() {
    // Track total time spent on screen
    _trackScreenDuration();

    // Track final engagement metrics
    _trackEngagementMetrics();

    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _trackScreenDuration() {
    if (_screenViewStartTime != null) {
      final duration = DateTime.now().difference(_screenViewStartTime!);
      trackEvent(
        'dues_screen_duration',
        params: {
          'duration_ms': duration.inMilliseconds.toString(),
          'duration_seconds': duration.inSeconds.toString(),
        },
      );
    }
  }

  void _trackEngagementMetrics() {
    trackEvent(
      'dues_engagement',
      params: {
        'filter_change_count': _filterChangeCount.toString(),
        'scroll_count': _scrollCount.toString(),
        'time_since_last_interaction':
            _lastInteractionTime != null
                ? DateTime.now()
                    .difference(_lastInteractionTime!)
                    .inSeconds
                    .toString()
                : '0',
      },
    );
  }

  void _onScroll() {
    // Don't track every tiny scroll event - only significant ones
    if (!_scrollController.hasClients) return;

    // Track scroll position as percentage
    final scrollPosition = _scrollController.position.pixels;
    final maxScroll = _scrollController.position.maxScrollExtent;

    if (maxScroll <= 0) return;

    final scrollPercentage = (scrollPosition / maxScroll * 100).round();

    // Only track at 25%, 50%, 75%, and 100% scroll positions
    if (scrollPercentage == 25 ||
        scrollPercentage == 50 ||
        scrollPercentage == 75 ||
        scrollPercentage == 100) {
      _scrollCount++;
      _lastInteractionTime = DateTime.now();

      trackEvent(
        'dues_scrolled',
        params: {
          'scroll_percentage': scrollPercentage.toString(),
          'scroll_position': scrollPosition.toString(),
        },
      );
    }
  }

  void _trackFilterChange(List<String> selectedFilters) {
    _filterChangeCount++;
    _lastInteractionTime = DateTime.now();

    trackUserInteraction(
      'filter_dues',
      'filter_group',
      additionalParams: {
        'selected_filters': selectedFilters.join(','),
        'filter_count': selectedFilters.length.toString(),
        'filter_change_count': _filterChangeCount.toString(),
        'time_on_screen_before_filter':
            _screenViewStartTime != null
                ? DateTime.now()
                    .difference(_screenViewStartTime!)
                    .inSeconds
                    .toString()
                : '0',
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<DuesCubit, DuesState>(
      builder: (context, state) {
        if (state is DuesLoaded) {
          List<DuesInvoice> filteredInvoices = _getFilteredInvoices(
            state.duesSummary.agingGroups,
            state.selectedAgingFilters,
          );
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 16),
                AquaText.subheadline("Due Aging in Days"),
                const SizedBox(height: 16),
                HorizontalAgingFilterGroup(
                  agingGroups: state.duesSummary.agingGroups,
                  initialSelection: state.selectedAgingFilters,
                  onSelectionChanged: (selectedAgings) {
                    _trackFilterChange(selectedAgings);
                    context.read<DuesCubit>().updateAgingFilters(
                      selectedAgings,
                    );
                  },
                ),
                const SizedBox(height: 16),

                if (state.selectedAgingFilters.isNotEmpty)
                  Expanded(
                    child: SingleChildScrollView(
                      child: StyledGenericTable<DuesInvoice>(
                        items: filteredInvoices,
                        showDividers: true,
                        columns: [
                          ColumnConfig<DuesInvoice>(
                            title: 'Date',
                            width: 100,
                            cellBuilder:
                                (invoice) => AquaText.body(
                                  DateFormat('dd-MM-yyyy').format(
                                    DateFormat(
                                      'dd MMM, yyyy HH:mm:ss',
                                    ).parse(invoice.invoiceDate),
                                  ),
                                ),
                          ),
                          ColumnConfig<DuesInvoice>(
                            title: 'ID',
                            width: 140,
                            cellBuilder:
                                (invoice) => AquaText.body(
                                  invoice.invoiceNumber,
                                  color: acPrimaryBlue,
                                  weight: AquaFontWeight.semibold,
                                ),
                          ),
                          ColumnConfig<DuesInvoice>(
                            title: 'Amount',
                            width: 100,
                            titleAlignment: Alignment.center,
                            bodyAlignment: Alignment.centerRight,
                            cellBuilder:
                                (invoice) => AquaText.body(
                                  CurrencyFormatter.formatAsINR(
                                    invoice.due,
                                    decimalPlaces: 0,
                                  ),
                                  weight: AquaFontWeight.bold,
                                ),
                          ),
                          ColumnConfig<DuesInvoice>(
                            title: 'Due Days',
                            width: 80,
                            titleAlignment: Alignment.center,
                            bodyAlignment: Alignment.centerRight,
                            cellBuilder:
                                (invoice) =>
                                    AquaText.body(invoice.dueDays.toString()),
                          ),
                          ColumnConfig<DuesInvoice>(
                            title: 'Aging',
                            width: 50,
                            titleAlignment: Alignment.center,
                            bodyAlignment: Alignment.centerRight,
                            cellBuilder:
                                (invoice) => AquaText.body(invoice.aging1),
                          ),
                        ],
                      ),
                    ),
                  )
                else
                  Center(
                    child: AquaText.body("No dues match the selected filters"),
                  ),
              ],
            ),
          );
        }
        return const LoadingWidget(
          message: "Your Dues Loading, please wait...",
        );
      },
      listener: (context, state) {
        // Track state changes for analytics
        if (state is DuesLoading) {
          trackEvent('dues_loading');
        } else if (state is DuesLoaded) {
          _lastInteractionTime =
              DateTime.now(); // Update interaction time when data loads
          trackEvent(
            'dues_loaded',
            params: {
              'invoice_count':
                  state.duesSummary.agingGroups
                      .expand((group) => group.invoices)
                      .length
                      .toString(),
              'aging_group_count':
                  state.duesSummary.agingGroups.length.toString(),
              'total_due_amount': state.duesSummary.totalDue.toString(),
              'is_from_cache': state.isFromCache ? 'true' : 'false',
              'time_to_load':
                  _screenViewStartTime != null
                      ? DateTime.now()
                          .difference(_screenViewStartTime!)
                          .inMilliseconds
                          .toString()
                      : '0',
            },
          );
        } else if (state is DuesError) {
          trackEvent('dues_error', params: {'error_message': state.message});
        }
      },
    );
  }

  List<DuesInvoice> _getFilteredInvoices(
    List<DuesAgingGroup> agingGroups,
    List<String> selectedFilters,
  ) {
    // Get invoices from selected aging groups
    final List<DuesInvoice> filteredInvoices =
        agingGroups
            .where((group) => selectedFilters.contains(group.aging))
            .expand((group) => group.invoices)
            .toList();

    // Sort invoices by aging (assuming aging is a comparable value like int or string)
    filteredInvoices.sort((a, b) => b.aging.compareTo(a.aging));

    return filteredInvoices;
  }
}
