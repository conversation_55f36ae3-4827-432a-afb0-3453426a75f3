import '../../../domain/entities/dues/dues.dart';
import '../../../objectbox.g.dart';
import '../../models/dues/dues_model.dart';

abstract class DuesLocalDataSource {
  Future<DuesSummary> getDues(String customerId);
  Future<void> cacheDues(DuesSummary dues, String customerId);
  Future<int> getLastSyncTime(String customerId);
}

class DuesLocalDataSourceImpl implements DuesLocalDataSource {
  final Box<DuesInvoiceModel> invoiceBox;
  final Box<DuesAgingGroupModel> agingGroupBox;
  final Box<DuesSummaryModel> summaryBox;
  final Store store;

  DuesLocalDataSourceImpl({
    required this.invoiceBox,
    required this.agingGroupBox,
    required this.summaryBox,
    required this.store,
  });

  @override
  Future<DuesSummary> getDues(String customerId) async {
    // Get all aging groups for this customer
    final agingGroupModels =
        agingGroupBox
            .query(DuesAgingGroupModel_.customerId.equals(customerId))
            .build()
            .find();

    if (agingGroupModels.isEmpty) {
      throw Exception('No cached dues aging groups found');
    }

    // Get the summary for this customer
    final summaryModel =
        summaryBox
            .query(DuesSummaryModel_.customerId.equals(customerId))
            .build()
            .findFirst();

    if (summaryModel == null) {
      throw Exception('No cached dues summary found');
    }

    // Build the DuesSummary entity
    final agingGroups = <DuesAgingGroup>[];

    for (var agingGroupModel in agingGroupModels) {
      // Get all invoices for this aging group
      final invoiceModels =
          invoiceBox
              .query(
                DuesInvoiceModel_.customerId.equals(customerId) &
                    DuesInvoiceModel_.agingGroup.equals(agingGroupModel.aging),
              )
              .build()
              .find();

      // Convert invoice models to entities
      final invoices = invoiceModels.map((model) => model.toEntity()).toList();

      // Create aging group entity
      final agingGroup = DuesAgingGroup(
        invoices: invoices,
        totalPayableAmount: agingGroupModel.totalPayableAmount,
        aging: agingGroupModel.aging,
        dueDays: agingGroupModel.dueDays,
      );

      agingGroups.add(agingGroup);
    }

    return DuesSummary(
      agingGroups: agingGroups,
      customerId: customerId,
      totalDue: summaryModel.totalDue,
    );
  }

  @override
  Future<void> cacheDues(DuesSummary dues, String customerId) async {
    // Use a transaction for atomicity
    store.runInTransaction(TxMode.write, () {
      // Clear existing data for this customer
      _clearExistingDuesData(customerId);

      final timestamp = DateTime.now().millisecondsSinceEpoch;

      // Store each aging group and its invoices
      for (var agingGroup in dues.agingGroups) {
        // Create and store aging group model
        final agingGroupModel = DuesAgingGroupModel(
          customerId: customerId,
          totalPayableAmount: agingGroup.totalPayableAmount,
          aging: agingGroup.aging,
          dueDays: agingGroup.dueDays,
          lastSyncTimestamp: timestamp,
        );

        agingGroupBox.put(agingGroupModel);

        // Store each invoice in this aging group
        for (var invoice in agingGroup.invoices) {
          final invoiceModel = DuesInvoiceModel(
            agingGroup: agingGroup.aging,
            customerId: customerId,
            id: invoice.id,
            invoiceId: invoice.invoiceId,
            invoiceNumber: invoice.invoiceNumber,
            invoiceDate: invoice.invoiceDate,
            customerCode: invoice.customerCode,
            customerName: invoice.customerName,
            customerDistrict: invoice.customerDistrict,
            customerState: invoice.customerState,
            customerType: invoice.customerType,
            onBoardedTime: invoice.onBoardedTime,
            categoryType: invoice.categoryType,
            invoiceType: invoice.invoiceType,
            retailType: invoice.retailType,
            invoiceStatus: invoice.invoiceStatus,
            invoiceRaisedBy: invoice.invoiceRaisedBy,
            mode: invoice.mode,
            businessVertical: invoice.businessVertical,
            feedCreditLimit: invoice.feedCreditLimit,
            nonFeedCreditLimit: invoice.nonFeedCreditLimit,
            harvestCreditLimit: invoice.harvestCreditLimit,
            totalSalesInclTax: invoice.totalSalesInclTax,
            totalSalesExclTax: invoice.totalSalesExclTax,
            tcsAmount: invoice.tcsAmount,
            tdsAmount: invoice.tdsAmount,
            amountAfterTcs: invoice.amountAfterTcs,
            shippingCharges: invoice.shippingCharges,
            feedAmountAfterTcs: invoice.feedAmountAfterTcs,
            nonFeedAmountAfterTcs: invoice.nonFeedAmountAfterTcs,
            creditNoteAmountWithTcs: invoice.creditNoteAmountWithTcs,
            payableAmount: invoice.payableAmount,
            paidAmount: invoice.paidAmount,
            due: invoice.due,
            dueDate: invoice.dueDate,
            dueDays: invoice.dueDays,
            aging: invoice.aging,
            aging1: invoice.aging1,
            paymentCredibility: invoice.paymentCredibility,
            totalPurchase: invoice.totalPurchase,
            totalSales: invoice.totalSales,
            salesTier: invoice.salesTier,
            healthcareSales: invoice.healthcareSales,
            feedSales: invoice.feedSales,
            chemicalSales: invoice.chemicalSales,
            equipmentSales: invoice.equipmentSales,
            harvestSales: invoice.harvestSales,
            grossMargin: invoice.grossMargin,
            lastPaidDate: invoice.lastPaidDate,
            lastSyncTimestamp: timestamp,
          );

          invoiceBox.put(invoiceModel);
        }
      }

      // Store the overall summary
      final summaryModel = DuesSummaryModel(
        customerId: customerId,
        totalDue: dues.totalDue,
        lastSyncTimestamp: timestamp,
      );

      summaryBox.put(summaryModel);
    });
  }

  void _clearExistingDuesData(String customerId) {
    // Find and remove all invoices for this customer
    final existingInvoices =
        invoiceBox
            .query(DuesInvoiceModel_.customerId.equals(customerId))
            .build()
            .find();
    invoiceBox.removeMany(existingInvoices.map((e) => e.dbId).toList());

    // Find and remove all aging groups for this customer
    final existingAgingGroups =
        agingGroupBox
            .query(DuesAgingGroupModel_.customerId.equals(customerId))
            .build()
            .find();
    agingGroupBox.removeMany(existingAgingGroups.map((e) => e.dbId).toList());

    // Find and remove the summary for this customer
    final existingSummary =
        summaryBox
            .query(DuesSummaryModel_.customerId.equals(customerId))
            .build()
            .findFirst();
    if (existingSummary != null) {
      summaryBox.remove(existingSummary.dbId);
    }
  }

  @override
  Future<int> getLastSyncTime(String customerId) async {
    final summary =
        summaryBox
            .query(DuesSummaryModel_.customerId.equals(customerId))
            .build()
            .findFirst();

    if (summary == null) {
      return 0; // No previous sync, return 0 to trigger sync
    }

    return summary.lastSyncTimestamp;
  }
}
