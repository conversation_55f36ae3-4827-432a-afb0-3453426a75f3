class DateTimeUtils {
  /// Format a date time as a relative time string (e.g. "2 hours ago")
  static String formatRelativeTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inSeconds < 60) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      final minutes = difference.inMinutes;
      return '$minutes ${minutes == 1 ? 'minute' : 'minutes'} ago';
    } else if (difference.inHours < 24) {
      final hours = difference.inHours;
      return '$hours ${hours == 1 ? 'hour' : 'hours'} ago';
    } else if (difference.inDays < 30) {
      final days = difference.inDays;
      return '$days ${days == 1 ? 'day' : 'days'} ago';
    } else {
      // Format as date for older times
      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    }
  }

  static String getFinancialYear() {
    final now = DateTime.now();
    final currentYear = now.year;
    final financialYearStartMonth = 4; // April

    int financialYearStart;
    int financialYearEnd;

    if (now.month >= financialYearStartMonth) {
      financialYearStart = currentYear;
      financialYearEnd = currentYear + 1;
    } else {
      financialYearStart = currentYear - 1;
      financialYearEnd = currentYear;
    }

    final formattedStartYear = financialYearStart.toString().substring(2);
    final formattedEndYear = financialYearEnd.toString().substring(2);

    return 'FY ($formattedStartYear-$formattedEndYear)';
  }
}
