import 'dart:convert';
import 'dart:async';
import 'package:http/http.dart' as http;

import '../../domain/entities/payments/payment_request.dart';

/// Exception class for payment-related errors
class PaymentException implements Exception {
  final String message;
  final int? statusCode;
  final String? errorCode;
  final String? details;
  final DateTime timestamp;

  PaymentException(
    this.message, {
    this.statusCode,
    this.errorCode,
    this.details,
  }) : timestamp = DateTime.now();

  @override
  String toString() {
    return 'PaymentException: $message'
        '${statusCode != null ? ' (HTTP $statusCode)' : ''}'
        '${errorCode != null ? ' [$errorCode]' : ''}';
  }

  /// Check if this is a network-related error
  bool get isNetworkError => statusCode == null || statusCode! >= 500;

  /// Check if this is a client error (4xx)
  bool get isClientError =>
      statusCode != null && statusCode! >= 400 && statusCode! < 500;

  /// Check if this is a validation error
  bool get isValidationError => statusCode == 400;

  /// Check if this is an authentication error
  bool get isAuthError => statusCode == 401 || statusCode == 403;

  /// Check if this is a not found error
  bool get isNotFoundError => statusCode == 404;

  /// Get user-friendly error message
  String get userFriendlyMessage {
    if (isNetworkError) {
      return 'Network connection error. Please check your internet connection and try again.';
    } else if (isValidationError) {
      return message; // Validation messages are usually user-friendly
    } else if (isAuthError) {
      return 'Authentication error. Please contact support.';
    } else if (isNotFoundError) {
      return 'The requested payment session was not found.';
    } else {
      return 'An error occurred while processing your payment. Please try again.';
    }
  }
}

/// Response models for Zoho Payment API
class PaymentSessionResponse {
  final bool success;
  final String message;
  final PaymentSessionData data;

  PaymentSessionResponse({
    required this.success,
    required this.message,
    required this.data,
  });

  factory PaymentSessionResponse.fromJson(Map<String, dynamic> json) {
    return PaymentSessionResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: PaymentSessionData.fromJson(json['data'] ?? {}),
    );
  }
}

class PaymentSessionData {
  final String paymentSessionId;
  final String amount;
  final String currency;
  final String description;
  final String invoiceNumber;
  final int createdTime;
  final String transactionId;
  final String expiresIn;

  PaymentSessionData({
    required this.paymentSessionId,
    required this.amount,
    required this.currency,
    required this.description,
    required this.invoiceNumber,
    required this.createdTime,
    required this.transactionId,
    required this.expiresIn,
  });

  factory PaymentSessionData.fromJson(Map<String, dynamic> json) {
    return PaymentSessionData(
      paymentSessionId: json['payment_session_id'] ?? '',
      amount: json['amount']?.toString() ?? '0',
      currency: json['currency'] ?? 'INR',
      description: json['description'] ?? '',
      invoiceNumber: json['invoice_number'] ?? '',
      createdTime: json['created_time'] ?? 0,
      transactionId: json['transaction_id'] ?? '',
      expiresIn: json['expires_in'] ?? '',
    );
  }
}

class PaymentStatusResponse {
  final bool success;
  final String message;
  final PaymentStatusData data;

  PaymentStatusResponse({
    required this.success,
    required this.message,
    required this.data,
  });

  factory PaymentStatusResponse.fromJson(Map<String, dynamic> json) {
    return PaymentStatusResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: PaymentStatusData.fromJson(json['data'] ?? {}),
    );
  }
}

class PaymentStatusData {
  final String transactionId;
  final String paymentSessionId;
  final String status;
  final double amount;
  final String currency;
  final String description;
  final String invoiceNumber;
  final String customerId;
  final String? customerName;
  final String? customerEmail;
  final String? paymentId;
  final String? paymentMethod;
  final DateTime? sessionCreatedTime;
  final DateTime? paymentCompletedTime;
  final DateTime? sessionExpiresAt;
  final String? errorCode;
  final String? errorMessage;

  PaymentStatusData({
    required this.transactionId,
    required this.paymentSessionId,
    required this.status,
    required this.amount,
    required this.currency,
    required this.description,
    required this.invoiceNumber,
    required this.customerId,
    this.customerName,
    this.customerEmail,
    this.paymentId,
    this.paymentMethod,
    this.sessionCreatedTime,
    this.paymentCompletedTime,
    this.sessionExpiresAt,
    this.errorCode,
    this.errorMessage,
  });

  factory PaymentStatusData.fromJson(Map<String, dynamic> json) {
    return PaymentStatusData(
      transactionId: json['transaction_id'] ?? '',
      paymentSessionId: json['payment_session_id'] ?? '',
      status: json['status'] ?? '',
      amount: (json['amount'] ?? 0).toDouble(),
      currency: json['currency'] ?? 'INR',
      description: json['description'] ?? '',
      invoiceNumber: json['invoice_number'] ?? '',
      customerId: json['customer_id'] ?? '',
      customerName: json['customer_name'],
      customerEmail: json['customer_email'],
      paymentId: json['payment_id'],
      paymentMethod: json['payment_method'],
      sessionCreatedTime:
          json['session_created_time'] != null
              ? DateTime.tryParse(json['session_created_time'])
              : null,
      paymentCompletedTime:
          json['payment_completed_time'] != null
              ? DateTime.tryParse(json['payment_completed_time'])
              : null,
      sessionExpiresAt:
          json['session_expires_at'] != null
              ? DateTime.tryParse(json['session_expires_at'])
              : null,
      errorCode: json['error_code'],
      errorMessage: json['error_message'],
    );
  }
}

class HealthCheckResponse {
  final String timestamp;
  final String service;
  final String version;
  final String status;
  final Map<String, dynamic> checks;

  HealthCheckResponse({
    required this.timestamp,
    required this.service,
    required this.version,
    required this.status,
    required this.checks,
  });

  factory HealthCheckResponse.fromJson(Map<String, dynamic> json) {
    return HealthCheckResponse(
      timestamp: json['timestamp'] ?? '',
      service: json['service'] ?? '',
      version: json['version'] ?? '',
      status: json['status'] ?? '',
      checks: json['checks'] ?? {},
    );
  }
}

/// Dedicated Zoho Payment Service following documentation specifications
class ZohoPaymentService {
  static const String _baseUrl =
      'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net';
  static const String _apiPath = '/api';

  // HTTP Client with timeout configuration
  static final http.Client _client = http.Client();
  static const Duration _timeout = Duration(seconds: 30);

  // Headers
  static Map<String, String> get _defaultHeaders => {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  // Endpoints with trailing slashes
  static String get _healthEndpoint => '$_baseUrl$_apiPath/zoho/health/';
  static String get _createSessionEndpoint =>
      '$_baseUrl$_apiPath/zoho/payments/create-session/';

  static String _paymentStatusEndpoint(String sessionId) =>
      '$_baseUrl$_apiPath/zoho/payments/status/$sessionId/';

  /// Check API health and configuration
  static Future<HealthCheckResponse> checkHealth() async {
    try {
      final response = await _client
          .get(Uri.parse(_healthEndpoint), headers: _defaultHeaders)
          .timeout(_timeout);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return HealthCheckResponse.fromJson(data);
      } else {
        throw PaymentException(
          'Health check failed',
          statusCode: response.statusCode,
          details: response.body,
        );
      }
    } catch (e) {
      if (e is PaymentException) rethrow;
      throw PaymentException(
        'Network error during health check: ${e.toString()}',
      );
    }
  }

  /// Create a new payment session
  static Future<PaymentSessionResponse> createPaymentSession(
    PaymentRequest request,
  ) async {
    try {
      // Validate request
      _validatePaymentRequest(request);

      final response = await _client
          .post(
            Uri.parse(_createSessionEndpoint),
            headers: _defaultHeaders,
            body: jsonEncode(request.toJson()),
          )
          .timeout(_timeout);

      if (response.statusCode == 201) {
        final data = jsonDecode(response.body);
        return PaymentSessionResponse.fromJson(data);
      } else {
        final error = jsonDecode(response.body);
        throw PaymentException(
          error['message'] ?? 'Payment session creation failed',
          statusCode: response.statusCode,
          errorCode: error['error'],
          details: response.body,
        );
      }
    } catch (e) {
      if (e is PaymentException) rethrow;
      throw PaymentException(
        'Network error during payment creation: ${e.toString()}',
      );
    }
  }

  /// Get payment status by session ID
  static Future<PaymentStatusResponse> getPaymentStatus(
    String sessionId,
  ) async {
    try {
      if (sessionId.isEmpty) {
        throw PaymentException('Session ID cannot be empty');
      }

      final response = await _client
          .get(
            Uri.parse(_paymentStatusEndpoint(sessionId)),
            headers: _defaultHeaders,
          )
          .timeout(_timeout);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return PaymentStatusResponse.fromJson(data);
      } else {
        final error = jsonDecode(response.body);
        throw PaymentException(
          error['message'] ?? 'Failed to get payment status',
          statusCode: response.statusCode,
          errorCode: error['error'],
          details: response.body,
        );
      }
    } catch (e) {
      if (e is PaymentException) rethrow;
      throw PaymentException(
        'Network error during status check: ${e.toString()}',
      );
    }
  }

  /// Validate payment request before sending
  static void _validatePaymentRequest(PaymentRequest request) {
    if (request.amount <= 0) {
      throw PaymentException('Amount must be greater than 0');
    }

    if (request.invoiceNumber.isEmpty) {
      throw PaymentException('Invoice number cannot be empty');
    }

    if (request.customerId.isEmpty) {
      throw PaymentException('Customer ID cannot be empty');
    }

    if (request.currency != 'INR') {
      throw PaymentException('Only INR currency is supported');
    }

    // Validate email format if provided
    if (request.customerEmail != null && request.customerEmail!.isNotEmpty) {
      final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
      if (!emailRegex.hasMatch(request.customerEmail!)) {
        throw PaymentException('Invalid email format');
      }
    }

    // Validate phone format if provided
    if (request.customerPhone != null && request.customerPhone!.isNotEmpty) {
      final phoneRegex = RegExp(r'^\+?[1-9]\d{1,14}$');
      if (!phoneRegex.hasMatch(
        request.customerPhone!.replaceAll(RegExp(r'[-\s]'), ''),
      )) {
        throw PaymentException('Invalid phone number format');
      }
    }
  }

  /// Dispose HTTP client
  static void dispose() {
    _client.close();
  }
}
