import 'dart:async';
import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../../core/error/exceptions.dart';
import '../../domain/entities/user.dart';
import '../../domain/repositories/user_repository.dart';
import '../datasources/local/user_local_data_source.dart';
import '../datasources/remote/mongodb_datasource.dart' show MongoDBDataSource;
import '../datasources/remote/user_remote_data_source.dart';
import '../../core/network/network_info.dart';
import '../../core/utils/logger.dart';
import '../models/user_model.dart';

class UserRepositoryImpl implements UserRepository {
  final UserRemoteDataSource remoteDataSource;
  final UserLocalDataSource localDataSource;
  final MongoDBDataSource mongoDBDataSource;
  final NetworkInfo networkInfo;
  final AppLogger logger;

  // Add the StreamController for sync status
  final _syncStatusController = StreamController<bool>.broadcast();

  UserRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
    required this.mongoDBDataSource,
    required this.networkInfo,
    required this.logger,
  }) {
    // Initialize with not syncing
    _syncStatusController.add(false);
  }

  // Add a dispose method to close the controller
  void dispose() {
    _syncStatusController.close();
  }

  @override
  Stream<bool> getSyncStatus() {
    try {
      logger.i("Setting up sync status stream");
      return _syncStatusController.stream;
    } catch (e) {
      logger.e("Error setting up sync status stream", e);
      return Stream.error(e);
    }
  }

  @override
  Future<Either<Failure, User>> getUser() async {
    try {
      final userModel = await localDataSource.getUser();
      if (userModel != null) {
        return Right(userModel);
      } else {
        return Left(CacheFailure());
      }
    } on CacheException catch (e) {
      logger.e("Cache exception when getting user", e);
      return Left(CacheFailure());
    } catch (e) {
      logger.e("Unexpected error when getting user", e);
      return Left(CacheFailure());
    }
  }

  @override
  Future<Either<Failure, bool>> saveUser(User user) async {
    try {
      logger.i("Saving user to local storage");
      await localDataSource.saveUser(user);
      logger.i("User saved successfully");

      // Try to sync the user if we have internet
      if (await networkInfo.isConnected) {
        try {
          await syncUser();
        } catch (e) {
          // Just log the error, don't fail the save operation
          logger.e("Error syncing after save", e);
        }
      }

      return const Right(true);
    } on CacheException catch (e) {
      logger.e("Cache exception when saving user", e);
      return Left(CacheFailure());
    } catch (e) {
      logger.e("Unexpected error when saving user", e);
      return Left(CacheFailure());
    }
  }

  @override
  Future<Either<Failure, bool>> updateUser(User user) async {
    try {
      logger.i("Updating user in local storage");
      final userModel = UserModel.fromEntity(user);
      await localDataSource.updateUser(userModel);
      logger.i("User updated successfully");

      // Try to sync the user if we have internet
      if (await networkInfo.isConnected) {
        try {
          await syncUser();
        } catch (e) {
          // Just log the error, don't fail the update operation
          logger.e("Error syncing after update", e);
        }
      }

      return const Right(true);
    } on CacheException catch (e) {
      logger.e("Cache exception when updating user", e);
      return Left(CacheFailure());
    } catch (e) {
      logger.e("Unexpected error when updating user", e);
      return Left(CacheFailure());
    }
  }

  @override
  Future<Either<Failure, bool>> deleteUser() async {
    try {
      logger.i("Deleting user from local storage");
      await localDataSource.deleteUser();
      logger.i("User deleted successfully");
      return const Right(true);
    } on CacheException catch (e) {
      logger.e("Cache exception when deleting user", e);
      return Left(CacheFailure());
    } catch (e) {
      logger.e("Unexpected error when deleting user", e);
      return Left(CacheFailure());
    }
  }

  @override
  Future<Either<Failure, bool>> hasUser() async {
    try {
      logger.i("Checking if user exists in local storage");
      final hasUser = await localDataSource.hasUser();
      logger.i("User exists: $hasUser");
      return Right(hasUser);
    } on CacheException catch (e) {
      logger.e("Cache exception when checking if user exists", e);
      return Left(CacheFailure());
    } catch (e) {
      logger.e("Unexpected error when checking if user exists", e);
      return Left(CacheFailure());
    }
  }

  // Replace your existing syncUsers method with this one
  @override
  Future<Either<Failure, bool>> syncUser() async {
    try {
      logger.i("Starting user synchronization");
      _syncStatusController.add(true); // Emit sync started

      if (!await networkInfo.isConnected) {
        logger.w("Cannot sync users: No internet connection");
        _syncStatusController.add(false); // Emit sync stopped
        return Left(NetworkFailure());
      }

      final unsyncedUsers = await localDataSource.getUnsyncedUsers();
      logger.i("Found ${unsyncedUsers.length} unsynced users");

      for (var user in unsyncedUsers) {
        await _syncUser(user);
      }

      logger.i("User synchronization completed successfully");
      _syncStatusController.add(false); // Emit sync completed
      return const Right(true);
    } catch (e) {
      logger.e("Error during user synchronization", e);
      _syncStatusController.add(false); // Emit sync stopped on error
      return Left(ServerFailure());
    }
  }

  // Keep your existing _syncUser method
  Future<void> _syncUser(UserModel user) async {
    try {
      if (user.mongoId == null) {
        // Create new user in MongoDB
        logger.i("Creating new user in MongoDB: ${user.toJson()}");
        final mongoId = await remoteDataSource.createUser(user);

        // Update local user with MongoDB ID
        final updatedUser = user.copyWith(
          mongoId: mongoId,
          needsSync: false,
          updatedAt: DateTime.now(),
        );
        await localDataSource.updateUser(updatedUser);
        logger.i("User created in MongoDB and updated locally");
      } else {
        // Update existing user in MongoDB
        logger.i("Updating existing user in MongoDB: ${user.mongoId}");
        await remoteDataSource.updateUser(user);

        // Update local sync status
        final updatedUser = user.copyWith(
          needsSync: false,
          updatedAt: DateTime.now(),
        );
        await localDataSource.updateUser(updatedUser);
        logger.i("User updated in MongoDB and locally");
      }
    } catch (e) {
      logger.e("Error syncing user: ${user.id}", e);
      // Re-throw to be handled by the calling method
      throw ServerException();
    }
  }
}
