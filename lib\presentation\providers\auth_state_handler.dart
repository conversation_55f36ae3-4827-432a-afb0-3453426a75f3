import 'package:aquapartner/core/services/analytics_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../core/routes/app_router.dart';
import '../../core/utils/logger.dart';
import '../../injection_container.dart' as di;
import '../cubit/auth/auth_cubit.dart';
import '../cubit/auth/auth_state.dart';

class AuthStateHandler extends StatelessWidget {
  final Widget child;
  final AppLogger logger = di.sl<AppLogger>();
  final AnalyticsService analyticsService = di.sl<AnalyticsService>();

  AuthStateHandler({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthCubit, AuthState>(
      listenWhen: (previous, current) {
        logger.d("Previous state: $previous, Current state: $current");
        // Only react to authentication state changes
        return (previous is AuthLoading &&
                (current is AuthSuccess || current is Unauthenticated)) ||
            (previous is Unauthenticated && current is AuthSuccess) ||
            (previous is AuthSuccess && current is Unauthenticated);
      },
      listener: (context, state) {
        logger.d("AuthStateHandler listener triggered with state: $state");
        if (state is AuthSuccess) {
          logger.i("Navigating to home screen");
          analyticsService.logScreenView(screenName: 'HomeScreen');
          AppRouter.navigateToHome();
        } else if (state is Unauthenticated) {
          logger.i("Navigating to login screen");
          analyticsService.logScreenView(screenName: 'LoginScreen');
          AppRouter.navigateToLogin();
        }
      },
      child: child,
    );
  }
}
