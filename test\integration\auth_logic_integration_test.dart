import 'package:aquapartner/core/error/failures.dart';
import 'package:aquapartner/domain/entities/customer.dart';
import 'package:aquapartner/domain/entities/user.dart' as domain;
import 'package:aquapartner/domain/usecases/auth_usecases.dart';
import 'package:aquapartner/domain/usecases/customer_usercases.dart';
import 'package:aquapartner/domain/usecases/sync_usecases.dart';
import 'package:aquapartner/domain/usecases/user_usecases.dart';
import 'package:aquapartner/presentation/cubit/auth/auth_cubit.dart';
import 'package:aquapartner/presentation/cubit/auth/auth_state.dart';
import 'package:aquapartner/core/services/analytics_service.dart';
import 'package:aquapartner/core/utils/logger.dart';
import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// Mock classes
class MockSendOtpUseCase extends Mock implements SendOtpUseCase {}

class MockVerifyOtpUseCase extends Mock implements VerifyOtpUseCase {}

class MockGetUserUseCase extends Mock implements GetUserUseCase {}

class MockSaveUserUseCase extends Mock implements SaveUserUseCase {}

class MockSignOutUseCase extends Mock implements SignOutUseCase {}

class MockUpdateUserUseCase extends Mock implements UpdateUserUseCase {}

class MockSyncUserUseCase extends Mock implements SyncUserUseCase {}

class MockGetSyncStatusUseCase extends Mock implements GetSyncStatusUseCase {}

class MockCheckAuthStatusUseCase extends Mock
    implements CheckAuthStatusUseCase {}

class MockGetCustomerByMobileNumber extends Mock
    implements GetCustomerByMobileNumber {}

class MockAnalyticsService extends Mock implements AnalyticsService {}

class MockAppLogger extends Mock implements AppLogger {}

void main() {
  group('Authentication Logic Integration Tests', () {
    late MockSendOtpUseCase mockSendOtpUseCase;
    late MockVerifyOtpUseCase mockVerifyOtpUseCase;
    late MockGetUserUseCase mockGetUserUseCase;
    late MockSaveUserUseCase mockSaveUserUseCase;
    late MockSyncUserUseCase mockSyncUserUseCase;
    late MockGetSyncStatusUseCase mockGetSyncStatusUseCase;
    late MockSignOutUseCase mockSignOutUseCase;
    late MockUpdateUserUseCase mockUpdateUserUseCase;
    late MockCheckAuthStatusUseCase mockCheckAuthStatusUseCase;
    late MockGetCustomerByMobileNumber mockGetCustomerByMobileNumber;
    late MockAnalyticsService mockAnalyticsService;
    late MockAppLogger mockAppLogger;

    late AuthCubit authCubit;

    // Test constants as specified in requirements
    const testPhoneNumber = '9999999999';
    const testVerificationId = 'test_verification_id';
    const validTestOtp = '123456';
    const invalidTestOtp = '123123';

    setUpAll(() {
      registerFallbackValue(
        domain.User(
          id: 0,
          phoneNumber: '',
          isVerified: false,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          needsSync: false,
        ),
      );
    });

    setUp(() {
      mockSendOtpUseCase = MockSendOtpUseCase();
      mockVerifyOtpUseCase = MockVerifyOtpUseCase();
      mockGetUserUseCase = MockGetUserUseCase();
      mockSaveUserUseCase = MockSaveUserUseCase();
      mockSyncUserUseCase = MockSyncUserUseCase();
      mockGetSyncStatusUseCase = MockGetSyncStatusUseCase();
      mockSignOutUseCase = MockSignOutUseCase();
      mockUpdateUserUseCase = MockUpdateUserUseCase();
      mockCheckAuthStatusUseCase = MockCheckAuthStatusUseCase();
      mockGetCustomerByMobileNumber = MockGetCustomerByMobileNumber();
      mockAnalyticsService = MockAnalyticsService();
      mockAppLogger = MockAppLogger();

      // Setup default sync status stream
      when(
        () => mockGetSyncStatusUseCase.call(),
      ).thenAnswer((_) => Stream.value(false));

      authCubit = AuthCubit(
        sendOtpUseCase: mockSendOtpUseCase,
        verifyOtpUseCase: mockVerifyOtpUseCase,
        signOutUseCase: mockSignOutUseCase,
        getUserUseCase: mockGetUserUseCase,
        saveUserUseCase: mockSaveUserUseCase,
        updateUserUseCase: mockUpdateUserUseCase,
        syncUserUseCase: mockSyncUserUseCase,
        getSyncStatusUseCase: mockGetSyncStatusUseCase,
        logger: mockAppLogger,
        checkAuthStatusUseCase: mockCheckAuthStatusUseCase,
        getCustomerByMobileNumber: mockGetCustomerByMobileNumber,
        analyticsService: mockAnalyticsService,
      );
    });

    tearDown(() {
      authCubit.close();
    });

    test('Complete Authentication Flow - Valid OTP Success Path', () async {
      // Test Description:
      // This test verifies the complete authentication flow with valid OTP (123456)
      // Expected flow: AuthInitial → AuthLoading → OtpSent → AuthLoading → AuthSuccess

      print('=== Starting Valid OTP Authentication Logic Test ===');

      // Setup successful authentication mocks
      when(
        () => mockSendOtpUseCase.call('+91$testPhoneNumber'),
      ).thenAnswer((_) async => const Right(testVerificationId));

      when(
        () => mockVerifyOtpUseCase.call(testVerificationId, validTestOtp),
      ).thenAnswer((_) async => const Right(true));

      when(
        () => mockGetSyncStatusUseCase.call(),
      ).thenAnswer((_) => Stream.value(false));

      when(() => mockGetUserUseCase.call()).thenAnswer(
        (_) async => Right(
          domain.User(
            id: 1,
            phoneNumber: testPhoneNumber,
            isVerified: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
            needsSync: false,
          ),
        ),
      );

      when(
        () => mockSaveUserUseCase.call(any()),
      ).thenAnswer((_) async => const Right(true));

      when(
        () => mockGetCustomerByMobileNumber.call(testPhoneNumber),
      ).thenAnswer(
        (_) async => Right(
          Customer(
            customerId: 'test_customer_id',
            customerName: 'Test Customer',
            email: '<EMAIL>',
            mobileNumber: '+91$testPhoneNumber',
            companyName: 'Test Company',
            gstNo: 'GST123456789',
            businessVertical: 'Technology',
            customerCode: 'TC001',
            billingAddress: 'Test Address',
          ),
        ),
      );

      // Track state changes
      List<AuthState> stateHistory = [];
      authCubit.stream.listen((state) {
        stateHistory.add(state);
        print('State changed to: ${state.runtimeType}');
      });

      // Verify initial state
      expect(authCubit.state, isA<AuthInitial>());

      // Step 1: Send OTP
      print('Step 1: Sending OTP for phone number $testPhoneNumber');
      await authCubit.sendOtp(testPhoneNumber);
      await Future.delayed(
        Duration(milliseconds: 100),
      ); // Allow state to settle

      // Verify OTP sent state
      expect(authCubit.state, isA<OtpSent>());
      final otpSentState = authCubit.state as OtpSent;
      expect(otpSentState.verificationId, equals(testVerificationId));
      expect(otpSentState.phoneNumber, equals(testPhoneNumber));

      print(
        'OTP sent successfully, verification ID: ${otpSentState.verificationId}',
      );

      // Step 2: Verify valid OTP
      print('Step 2: Verifying valid OTP ($validTestOtp)');
      await authCubit.verifyOtp(validTestOtp);
      await Future.delayed(
        Duration(milliseconds: 200),
      ); // Allow authentication to complete

      // Verify authentication success
      expect(authCubit.state, isA<AuthSuccess>());
      final authSuccessState = authCubit.state as AuthSuccess;
      expect(authSuccessState.user.phoneNumber, equals(testPhoneNumber));
      expect(authSuccessState.user.isVerified, isTrue);

      print(
        'Authentication successful! User verified: ${authSuccessState.user.isVerified}',
      );

      // Verify all use cases were called correctly
      verify(() => mockSendOtpUseCase.call('+91$testPhoneNumber')).called(1);
      verify(
        () => mockVerifyOtpUseCase.call(testVerificationId, validTestOtp),
      ).called(1);
      verify(
        () => mockGetCustomerByMobileNumber.call(testPhoneNumber),
      ).called(1);
      verify(() => mockSaveUserUseCase.call(any())).called(1);

      // Verify state progression
      expect(stateHistory.length, greaterThanOrEqualTo(4));
      expect(stateHistory[0], isA<AuthLoading>()); // OTP send loading
      expect(stateHistory[1], isA<OtpSent>()); // OTP sent
      expect(stateHistory[2], isA<AuthLoading>()); // OTP verify loading
      expect(stateHistory[3], isA<AuthSuccess>()); // Authentication success

      print(
        '=== Valid OTP Authentication Logic Test Completed Successfully ===',
      );
    });

    test('Complete Authentication Flow - Invalid OTP Error Path', () async {
      // Test Description:
      // This test verifies the complete authentication flow with invalid OTP (123123)
      // Expected flow: AuthInitial → AuthLoading → OtpSent → AuthLoading → OtpVerificationError

      print('=== Starting Invalid OTP Authentication Logic Test ===');

      // Setup mocks for OTP sending (successful) and verification (failure)
      when(
        () => mockSendOtpUseCase.call('+91$testPhoneNumber'),
      ).thenAnswer((_) async => const Right(testVerificationId));

      when(
        () => mockVerifyOtpUseCase.call(testVerificationId, invalidTestOtp),
      ).thenAnswer((_) async => Left(ValidationFailure('Invalid OTP')));

      when(
        () => mockGetSyncStatusUseCase.call(),
      ).thenAnswer((_) => Stream.value(false));

      // Track state changes
      List<AuthState> stateHistory = [];
      authCubit.stream.listen((state) {
        stateHistory.add(state);
        print('State changed to: ${state.runtimeType}');
      });

      // Verify initial state
      expect(authCubit.state, isA<AuthInitial>());

      // Step 1: Send OTP
      print('Step 1: Sending OTP for phone number $testPhoneNumber');
      await authCubit.sendOtp(testPhoneNumber);
      await Future.delayed(
        Duration(milliseconds: 100),
      ); // Allow state to settle

      // Verify OTP sent state
      expect(authCubit.state, isA<OtpSent>());
      final otpSentState = authCubit.state as OtpSent;
      expect(otpSentState.verificationId, equals(testVerificationId));
      expect(otpSentState.phoneNumber, equals(testPhoneNumber));

      print(
        'OTP sent successfully, verification ID: ${otpSentState.verificationId}',
      );

      // Step 2: Verify invalid OTP
      print('Step 2: Verifying invalid OTP ($invalidTestOtp)');
      await authCubit.verifyOtp(invalidTestOtp);
      await Future.delayed(
        Duration(milliseconds: 200),
      ); // Allow verification to complete

      // Verify authentication failure
      expect(authCubit.state, isA<OtpVerificationError>());
      final otpErrorState = authCubit.state as OtpVerificationError;
      expect(otpErrorState.message, contains('Invalid OTP'));

      print(
        'Authentication failed as expected with error: ${otpErrorState.message}',
      );

      // Verify use cases were called correctly
      verify(() => mockSendOtpUseCase.call('+91$testPhoneNumber')).called(1);
      verify(
        () => mockVerifyOtpUseCase.call(testVerificationId, invalidTestOtp),
      ).called(1);

      // Customer lookup and user saving should NOT be called for failed authentication
      verifyNever(() => mockGetCustomerByMobileNumber.call(any()));
      verifyNever(() => mockSaveUserUseCase.call(any()));

      // Verify state progression
      expect(stateHistory.length, greaterThanOrEqualTo(4));
      expect(stateHistory[0], isA<AuthLoading>()); // OTP send loading
      expect(stateHistory[1], isA<OtpSent>()); // OTP sent
      expect(stateHistory[2], isA<AuthLoading>()); // OTP verify loading
      expect(
        stateHistory[3],
        isA<OtpVerificationError>(),
      ); // Authentication error

      print(
        '=== Invalid OTP Authentication Logic Test Completed Successfully ===',
      );
    });

    test('Authentication State Progression Validation', () async {
      // Test Description:
      // This test validates the complete state progression through the authentication flow

      print('=== Starting State Progression Validation Test ===');

      // Setup successful authentication mocks
      when(
        () => mockSendOtpUseCase.call('+91$testPhoneNumber'),
      ).thenAnswer((_) async => const Right(testVerificationId));

      when(
        () => mockVerifyOtpUseCase.call(testVerificationId, validTestOtp),
      ).thenAnswer((_) async => const Right(true));

      when(
        () => mockGetSyncStatusUseCase.call(),
      ).thenAnswer((_) => Stream.value(false));

      when(() => mockGetUserUseCase.call()).thenAnswer(
        (_) async => Right(
          domain.User(
            id: 1,
            phoneNumber: testPhoneNumber,
            isVerified: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
            needsSync: false,
          ),
        ),
      );

      when(
        () => mockSaveUserUseCase.call(any()),
      ).thenAnswer((_) async => const Right(true));

      when(
        () => mockGetCustomerByMobileNumber.call(testPhoneNumber),
      ).thenAnswer(
        (_) async => Right(
          Customer(
            customerId: 'test_customer_id',
            customerName: 'Test Customer',
            email: '<EMAIL>',
            mobileNumber: '+91$testPhoneNumber',
            companyName: 'Test Company',
            gstNo: 'GST123456789',
            businessVertical: 'Technology',
            customerCode: 'TC001',
            billingAddress: 'Test Address',
          ),
        ),
      );

      // Track all state changes with timestamps
      List<Map<String, dynamic>> stateTimeline = [];
      authCubit.stream.listen((state) {
        stateTimeline.add({
          'state': state,
          'timestamp': DateTime.now(),
          'type': state.runtimeType.toString(),
        });
        print('State transition: ${state.runtimeType}');
      });

      // Execute complete authentication flow
      await authCubit.sendOtp(testPhoneNumber);
      await Future.delayed(Duration(milliseconds: 50));

      await authCubit.verifyOtp(validTestOtp);
      await Future.delayed(Duration(milliseconds: 200));

      // Validate complete state progression
      expect(stateTimeline.length, greaterThanOrEqualTo(4));

      // Expected state progression:
      // 1. AuthLoading (during OTP send)
      // 2. OtpSent (after OTP sent)
      // 3. AuthLoading (during OTP verification)
      // 4. AuthSuccess (after successful verification)

      expect(stateTimeline[0]['type'], equals('AuthLoading'));
      expect(stateTimeline[1]['type'], equals('OtpSent'));
      expect(stateTimeline[2]['type'], equals('AuthLoading'));
      expect(stateTimeline[3]['type'], equals('AuthSuccess'));

      print('State progression validated successfully:');
      for (int i = 0; i < stateTimeline.length; i++) {
        print('  ${i + 1}. ${stateTimeline[i]['type']}');
      }

      // Verify final state
      expect(authCubit.state, isA<AuthSuccess>());

      print('=== State Progression Validation Test Completed Successfully ===');
    });
  });
}
