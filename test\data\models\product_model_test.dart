import 'package:flutter_test/flutter_test.dart';
import 'package:aquapartner/data/models/product_model.dart';
import 'package:aquapartner/domain/entities/product.dart';

void main() {
  group('ProductModel Tests', () {
    late ProductModel testProductModel;
    late Map<String, dynamic> testJson;

    setUp(() {
      testJson = {
        'productName': 'Test Product',
        'category': 'Test Category',
        'categoryType': 'Feed',
        'subCategory': 'Fish Feed',
        'tagLine': 'Best quality feed',
        'productTag': 'NEW ARRIVAL',
        'productImage': 'https://example.com/image.jpg',
        'content': '<p>Test product content</p>',
        'sortOrder': '1',
        'status': 'active',
      };

      testProductModel = ProductModel(
        productName: 'Test Product',
        category: 'Test Category',
        categoryType: 'Feed',
        subCategory: 'Fish Feed',
        tagLine: 'Best quality feed',
        productTag: 'NEW ARRIVAL',
        productImage: 'https://example.com/image.jpg',
        content: '<p>Test product content</p>',
        sortOrder: '1',
        status: 'active',
      );
    });

    group('Constructor Tests', () {
      test('should create ProductModel with all required fields', () {
        expect(testProductModel.productName, equals('Test Product'));
        expect(testProductModel.category, equals('Test Category'));
        expect(testProductModel.categoryType, equals('Feed'));
        expect(testProductModel.subCategory, equals('Fish Feed'));
        expect(testProductModel.tagLine, equals('Best quality feed'));
        expect(testProductModel.productTag, equals('NEW ARRIVAL'));
        expect(testProductModel.productImage, equals('https://example.com/image.jpg'));
        expect(testProductModel.content, equals('<p>Test product content</p>'));
        expect(testProductModel.sortOrder, equals('1'));
        expect(testProductModel.status, equals('active'));
      });

      test('should create ProductModel with empty values', () {
        final emptyProductModel = ProductModel(
          productName: '',
          category: '',
          categoryType: '',
          subCategory: '',
          tagLine: '',
          productTag: '',
          productImage: '',
          content: '',
          sortOrder: '',
          status: '',
        );

        expect(emptyProductModel.productName, isEmpty);
        expect(emptyProductModel.category, isEmpty);
        expect(emptyProductModel.categoryType, isEmpty);
        expect(emptyProductModel.subCategory, isEmpty);
        expect(emptyProductModel.tagLine, isEmpty);
        expect(emptyProductModel.productTag, isEmpty);
        expect(emptyProductModel.productImage, isEmpty);
        expect(emptyProductModel.content, isEmpty);
        expect(emptyProductModel.sortOrder, isEmpty);
        expect(emptyProductModel.status, isEmpty);
      });
    });

    group('JSON Serialization Tests', () {
      test('should convert ProductModel to JSON correctly', () {
        final json = testProductModel.toJson();

        expect(json['productName'], equals('Test Product'));
        expect(json['category'], equals('Test Category'));
        expect(json['categoryType'], equals('Feed'));
        expect(json['subCategory'], equals('Fish Feed'));
        expect(json['tagLine'], equals('Best quality feed'));
        expect(json['productTag'], equals('NEW ARRIVAL'));
        expect(json['productImage'], equals('https://example.com/image.jpg'));
        expect(json['content'], equals('<p>Test product content</p>'));
        expect(json['sortOrder'], equals('1'));
        expect(json['status'], equals('active'));
      });

      test('should create ProductModel from JSON correctly', () {
        final productModel = ProductModel.fromJson(testJson);

        expect(productModel.productName, equals('Test Product'));
        expect(productModel.category, equals('Test Category'));
        expect(productModel.categoryType, equals('Feed'));
        expect(productModel.subCategory, equals('Fish Feed'));
        expect(productModel.tagLine, equals('Best quality feed'));
        expect(productModel.productTag, equals('NEW ARRIVAL'));
        expect(productModel.productImage, equals('https://example.com/image.jpg'));
        expect(productModel.content, equals('<p>Test product content</p>'));
        expect(productModel.sortOrder, equals('1'));
        expect(productModel.status, equals('active'));
      });

      test('should handle missing JSON fields with default empty strings', () {
        final incompleteJson = <String, dynamic>{
          'productName': 'Test Product',
          'category': 'Test Category',
        };

        final productModel = ProductModel.fromJson(incompleteJson);

        expect(productModel.productName, equals('Test Product'));
        expect(productModel.category, equals('Test Category'));
        expect(productModel.categoryType, isEmpty);
        expect(productModel.subCategory, isEmpty);
        expect(productModel.tagLine, isEmpty);
        expect(productModel.productTag, isEmpty);
        expect(productModel.productImage, isEmpty);
        expect(productModel.content, isEmpty);
        expect(productModel.sortOrder, isEmpty);
        expect(productModel.status, isEmpty);
      });

      test('should handle null JSON values with default empty strings', () {
        final nullJson = <String, dynamic>{
          'productName': null,
          'category': null,
          'categoryType': null,
          'subCategory': null,
          'tagLine': null,
          'productTag': null,
          'productImage': null,
          'content': null,
          'sortOrder': null,
          'status': null,
        };

        final productModel = ProductModel.fromJson(nullJson);

        expect(productModel.productName, isEmpty);
        expect(productModel.category, isEmpty);
        expect(productModel.categoryType, isEmpty);
        expect(productModel.subCategory, isEmpty);
        expect(productModel.tagLine, isEmpty);
        expect(productModel.productTag, isEmpty);
        expect(productModel.productImage, isEmpty);
        expect(productModel.content, isEmpty);
        expect(productModel.sortOrder, isEmpty);
        expect(productModel.status, isEmpty);
      });
    });

    group('Domain Entity Conversion Tests', () {
      test('should convert ProductModel to Product entity correctly', () {
        final productEntity = testProductModel.toDomain();

        expect(productEntity, isA<Product>());
        expect(productEntity.productName, equals(testProductModel.productName));
        expect(productEntity.category, equals(testProductModel.category));
        expect(productEntity.categoryType, equals(testProductModel.categoryType));
        expect(productEntity.subCategory, equals(testProductModel.subCategory));
        expect(productEntity.tagLine, equals(testProductModel.tagLine));
        expect(productEntity.productTag, equals(testProductModel.productTag));
        expect(productEntity.productImage, equals(testProductModel.productImage));
        expect(productEntity.content, equals(testProductModel.content));
        expect(productEntity.sortOrder, equals(testProductModel.sortOrder));
        expect(productEntity.status, equals(testProductModel.status));
      });

      test('should maintain data integrity during entity conversion', () {
        final originalJson = testProductModel.toJson();
        final convertedEntity = testProductModel.toDomain();
        
        expect(convertedEntity.productName, equals(originalJson['productName']));
        expect(convertedEntity.category, equals(originalJson['category']));
        expect(convertedEntity.categoryType, equals(originalJson['categoryType']));
        expect(convertedEntity.subCategory, equals(originalJson['subCategory']));
        expect(convertedEntity.tagLine, equals(originalJson['tagLine']));
        expect(convertedEntity.productTag, equals(originalJson['productTag']));
        expect(convertedEntity.productImage, equals(originalJson['productImage']));
        expect(convertedEntity.content, equals(originalJson['content']));
        expect(convertedEntity.sortOrder, equals(originalJson['sortOrder']));
        expect(convertedEntity.status, equals(originalJson['status']));
      });
    });

    group('Edge Cases and Validation Tests', () {
      test('should handle special characters in product fields', () {
        final specialCharJson = {
          'productName': 'Test Product & Co. (Special)',
          'category': 'Test Category - Special',
          'categoryType': 'Feed/Chemical',
          'subCategory': 'Fish Feed & Supplements',
          'tagLine': 'Best quality feed - 100% organic!',
          'productTag': 'NEW ARRIVAL & BESTSELLER',
          'productImage': 'https://example.com/image-special.jpg',
          'content': '<p>Test product content with <strong>HTML</strong> & special chars!</p>',
          'sortOrder': '1.5',
          'status': 'active/featured',
        };

        final productModel = ProductModel.fromJson(specialCharJson);

        expect(productModel.productName, equals('Test Product & Co. (Special)'));
        expect(productModel.category, equals('Test Category - Special'));
        expect(productModel.categoryType, equals('Feed/Chemical'));
        expect(productModel.subCategory, equals('Fish Feed & Supplements'));
        expect(productModel.tagLine, equals('Best quality feed - 100% organic!'));
        expect(productModel.productTag, equals('NEW ARRIVAL & BESTSELLER'));
        expect(productModel.productImage, equals('https://example.com/image-special.jpg'));
        expect(productModel.content, equals('<p>Test product content with <strong>HTML</strong> & special chars!</p>'));
        expect(productModel.sortOrder, equals('1.5'));
        expect(productModel.status, equals('active/featured'));
      });

      test('should handle very long string values', () {
        final longString = 'A' * 1000;
        final longStringJson = {
          'productName': longString,
          'category': longString,
          'categoryType': longString,
          'subCategory': longString,
          'tagLine': longString,
          'productTag': longString,
          'productImage': longString,
          'content': longString,
          'sortOrder': longString,
          'status': longString,
        };

        final productModel = ProductModel.fromJson(longStringJson);

        expect(productModel.productName.length, equals(1000));
        expect(productModel.category.length, equals(1000));
        expect(productModel.categoryType.length, equals(1000));
        expect(productModel.subCategory.length, equals(1000));
        expect(productModel.tagLine.length, equals(1000));
        expect(productModel.productTag.length, equals(1000));
        expect(productModel.productImage.length, equals(1000));
        expect(productModel.content.length, equals(1000));
        expect(productModel.sortOrder.length, equals(1000));
        expect(productModel.status.length, equals(1000));
      });

      test('should handle round-trip JSON serialization', () {
        final originalJson = testProductModel.toJson();
        final recreatedModel = ProductModel.fromJson(originalJson);
        final finalJson = recreatedModel.toJson();

        expect(finalJson, equals(originalJson));
      });

      test('should handle unicode characters', () {
        final unicodeJson = {
          'productName': 'Test Product 测试产品',
          'category': 'Test Category カテゴリ',
          'categoryType': 'Feed корм',
          'subCategory': 'Fish Feed मछली का चारा',
          'tagLine': 'Best quality feed أفضل جودة',
          'productTag': 'NEW ARRIVAL جديد',
          'productImage': 'https://example.com/image-测试.jpg',
          'content': '<p>Test content محتوى الاختبار</p>',
          'sortOrder': '1',
          'status': 'active نشط',
        };

        final productModel = ProductModel.fromJson(unicodeJson);
        final convertedJson = productModel.toJson();

        expect(convertedJson['productName'], equals('Test Product 测试产品'));
        expect(convertedJson['category'], equals('Test Category カテゴリ'));
        expect(convertedJson['categoryType'], equals('Feed корм'));
        expect(convertedJson['subCategory'], equals('Fish Feed मछली का चारा'));
        expect(convertedJson['tagLine'], equals('Best quality feed أفضل جودة'));
        expect(convertedJson['productTag'], equals('NEW ARRIVAL جديد'));
        expect(convertedJson['productImage'], equals('https://example.com/image-测试.jpg'));
        expect(convertedJson['content'], equals('<p>Test content محتوى الاختبار</p>'));
        expect(convertedJson['sortOrder'], equals('1'));
        expect(convertedJson['status'], equals('active نشط'));
      });
    });
  });
}
