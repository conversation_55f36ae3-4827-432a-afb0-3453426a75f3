# Mandatory Update Implementation

This document describes the mandatory update implementation in the Aqua Partner app.

## Overview

The app uses Firebase Remote Config to manage version requirements and force users to update when necessary. The implementation follows clean architecture principles and includes robust version comparison logic with platform-specific handling for iOS and Android.

## Key Components

### 1. Remote Config Keys

Platform-specific version requirements:

- `aquapartner_required_version_android`: Minimum required version for Android
- `aquapartner_required_version_ios`: Minimum required version for iOS
- `aquapartner_required_version`: Fallback minimum version (backward compatibility)

Platform-specific update messages:

- `update_message_android`: Update message for Android users
- `update_message_ios`: Update message for iOS users
- `update_message`: Fallback update message

Platform-specific update policies:

- `update_policy_android`: Update policy for Android
- `update_policy_ios`: Update policy for iOS
- `update_policy`: Fallback update policy

### 2. Version Management

The app uses semantic versioning with build number (x.y.z+b format) with support for:

- Major version (x)
- Minor version (y)
- Patch version (z)
- Build number (b)
- Two-component versions (x.y) are also supported

### 3. Update Flow

1. On app startup, Firebase Remote Config is initialized
2. The app detects the platform (iOS or Android) and fetches the appropriate minimum required version
3. Current app version is compared with the required version
4. The update policy is applied to determine if the update is mandatory or recommended
5. Based on the result:
   - For mandatory updates: A non-dismissible dialog is shown
   - For recommended updates: A dismissible dialog is shown
   - User is directed to the appropriate store (Play Store/App Store)

### 4. Update Policies

The app supports different update policies:

- `all`: All updates (including patch and build number changes) are mandatory
- `major_minor`: Major and minor updates are mandatory, patch updates are recommended
- `major_only`: Only major version updates are mandatory
- `flexible`: All updates are recommended but not mandatory

## Testing

Run the version comparison tests:

```bash
flutter test test/version_checker_test.dart
```

For manual testing, use the `UpdateTestHelper` class:

```dart
// Get information about the current update configuration
final configInfo = await UpdateTestHelper.getUpdateConfigInfo();
print(configInfo);

// Manually trigger an update check
await UpdateTestHelper.checkForUpdates(context);
```

## Remote Config Setup

1. Set default values:

```dart
// Platform-specific version requirements
'aquapartner_required_version_android': '0.0.0+0',
'aquapartner_required_version_ios': '0.0.0+0',
'aquapartner_required_version': '0.0.0+0', // Fallback

// Update messages
'update_message_android': 'Please update to continue using the app.',
'update_message_ios': 'Please update to continue using the app.',
'update_message': 'Please update to continue using the app.', // Fallback

// Update policies
'update_policy_android': 'major_minor',
'update_policy_ios': 'major_minor',
'update_policy': 'major_minor', // Fallback
```

2. Configuration settings:

- Fetch timeout: 1 minute
- Minimum fetch interval: 0 (immediate updates)

## Store URLs

### Android

- Primary: `market://details?id=blue.aquaconnect.aquapartner_self_service`
- Fallback: `https://play.google.com/store/apps/details?id=blue.aquaconnect.aquapartner_self_service`

### iOS

- Primary: `itms-apps://itunes.apple.com/app/id1234567890`
- Fallback: `https://apps.apple.com/app/id1234567890`

## Best Practices

1. Always test version comparison with various version formats
2. Update Remote Config values gradually to avoid forcing all users to update simultaneously
3. Provide clear update messages explaining the reason for the mandatory update
4. Use platform-specific version requirements when iOS and Android versions differ
5. Consider using different update policies for iOS and Android based on user behavior
6. Ensure store URLs are correct and accessible
7. Monitor update compliance through Firebase Analytics

## Troubleshooting

1. If remote config fetch fails:

   - Default versions (0.0.0+0) will be used
   - Update dialog won't be shown

2. If store URL launch fails:

   - App will attempt to use fallback URLs
   - Error will be logged but won't crash the app

3. Platform-specific issues:
   - iOS: App Store review may reject apps that force updates too frequently
   - Android: Some devices may have Play Store disabled or unavailable

## Future Improvements

1. Add support for staged rollouts based on user segments
2. Implement update deferral with countdown for non-critical updates
3. Add analytics for update compliance tracking by platform
4. Support for beta version formats and TestFlight/internal testing
5. Add A/B testing for update messages to improve conversion rates
