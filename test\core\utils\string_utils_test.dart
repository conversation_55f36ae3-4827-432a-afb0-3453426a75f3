import 'package:flutter_test/flutter_test.dart';
import 'package:aquapartner/core/utils/string_utils.dart';

void main() {
  group('StringUtils Tests', () {
    group('decodeUnicodeEscapes', () {
      test('should decode basic Unicode escape sequences', () {
        const input = 'Hello \\u0041\\u0042\\u0043';
        const expected = 'Hello ABC';
        
        final result = StringUtils.decodeUnicodeEscapes(input);
        expect(result, equals(expected));
      });

      test('should decode Unicode escape sequences with special characters', () {
        const input = 'Test \\u00A9 \\u00AE \\u2122';
        const expected = 'Test © ® ™';
        
        final result = StringUtils.decodeUnicodeEscapes(input);
        expect(result, equals(expected));
      });

      test('should decode Unicode escape sequences with emojis', () {
        const input = 'Fish \\uD83D\\uDC1F Water \\uD83C\\uDF0A';
        const expected = 'Fish 🐟 Water 🌊';
        
        final result = StringUtils.decodeUnicodeEscapes(input);
        expect(result, equals(expected));
      });

      test('should handle mixed content with Unicode escapes', () {
        const input = 'Product: \\u0041qua\\u0043onnect \\u2122';
        const expected = 'Product: AquaConnect ™';
        
        final result = StringUtils.decodeUnicodeEscapes(input);
        expect(result, equals(expected));
      });

      test('should handle string without Unicode escapes', () {
        const input = 'Regular string without escapes';
        const expected = 'Regular string without escapes';
        
        final result = StringUtils.decodeUnicodeEscapes(input);
        expect(result, equals(expected));
      });

      test('should handle empty string', () {
        const input = '';
        const expected = '';
        
        final result = StringUtils.decodeUnicodeEscapes(input);
        expect(result, equals(expected));
      });

      test('should handle string with only Unicode escapes', () {
        const input = '\\u0048\\u0065\\u006C\\u006C\\u006F';
        const expected = 'Hello';
        
        final result = StringUtils.decodeUnicodeEscapes(input);
        expect(result, equals(expected));
      });

      test('should handle malformed Unicode escapes gracefully', () {
        const input = 'Test \\u004 incomplete escape';
        // Should not crash and return the original string or handle gracefully
        
        expect(() => StringUtils.decodeUnicodeEscapes(input), returnsNormally);
      });

      test('should handle lowercase Unicode escapes', () {
        const input = 'Test \\u0061\\u0062\\u0063';
        const expected = 'Test abc';
        
        final result = StringUtils.decodeUnicodeEscapes(input);
        expect(result, equals(expected));
      });

      test('should handle multiple consecutive Unicode escapes', () {
        const input = '\\u0041\\u0042\\u0043\\u0044\\u0045';
        const expected = 'ABCDE';
        
        final result = StringUtils.decodeUnicodeEscapes(input);
        expect(result, equals(expected));
      });

      test('should handle Unicode escapes at string boundaries', () {
        const input1 = '\\u0041BC';
        const expected1 = 'ABC';
        
        const input2 = 'AB\\u0043';
        const expected2 = 'ABC';
        
        expect(StringUtils.decodeUnicodeEscapes(input1), equals(expected1));
        expect(StringUtils.decodeUnicodeEscapes(input2), equals(expected2));
      });

      test('should handle Unicode escapes with numbers', () {
        const input = 'Version \\u0031\\u002E\\u0030';
        const expected = 'Version 1.0';
        
        final result = StringUtils.decodeUnicodeEscapes(input);
        expect(result, equals(expected));
      });

      test('should handle Unicode escapes with punctuation', () {
        const input = 'Hello\\u002C \\u0057orld\\u0021';
        const expected = 'Hello, World!';
        
        final result = StringUtils.decodeUnicodeEscapes(input);
        expect(result, equals(expected));
      });

      test('should handle backslash that is not Unicode escape', () {
        const input = 'Path\\to\\file and \\u0041';
        // Should handle the actual Unicode escape but preserve other backslashes
        
        final result = StringUtils.decodeUnicodeEscapes(input);
        expect(result, contains('A')); // The \\u0041 should become 'A'
      });

      test('should handle very long strings with Unicode escapes', () {
        final buffer = StringBuffer();
        for (int i = 0; i < 100; i++) {
          buffer.write('\\u0041'); // 'A'
        }
        final input = buffer.toString();
        
        final result = StringUtils.decodeUnicodeEscapes(input);
        expect(result.length, equals(100));
        expect(result, equals('A' * 100));
      });

      test('should handle Unicode escapes with whitespace', () {
        const input = 'Word1\\u0020Word2\\u0009Word3\\u000AWord4';
        const expected = 'Word1 Word2\tWord3\nWord4';
        
        final result = StringUtils.decodeUnicodeEscapes(input);
        expect(result, equals(expected));
      });

      test('should handle high Unicode values', () {
        const input = 'Star \\u2605 Heart \\u2665';
        const expected = 'Star ★ Heart ♥';
        
        final result = StringUtils.decodeUnicodeEscapes(input);
        expect(result, equals(expected));
      });

      test('should handle null input gracefully', () {
        // This test depends on how the method handles null
        // If it doesn't accept null, this test might not be applicable
        expect(() => StringUtils.decodeUnicodeEscapes(''), returnsNormally);
      });

      test('should preserve existing Unicode characters', () {
        const input = 'Existing © and \\u00AE';
        const expected = 'Existing © and ®';
        
        final result = StringUtils.decodeUnicodeEscapes(input);
        expect(result, equals(expected));
      });

      test('should handle case sensitivity in hex digits', () {
        const input1 = '\\u004A'; // Uppercase hex
        const input2 = '\\u004a'; // Lowercase hex
        const expected = 'J';
        
        final result1 = StringUtils.decodeUnicodeEscapes(input1);
        final result2 = StringUtils.decodeUnicodeEscapes(input2);
        
        expect(result1, equals(expected));
        expect(result2, equals(expected));
      });
    });
  });
}
