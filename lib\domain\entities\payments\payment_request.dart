import 'package:equatable/equatable.dart';

/// Represents a payment request entity for creating payment sessions
class PaymentRequest extends Equatable {
  final double amount;
  final String currency;
  final String invoiceNumber;
  final String customerId;
  final String? description;
  final String? customerName;
  final String? customerEmail;
  final String? customerPhone;
  final Map<String, dynamic>? metadata;
  final String? returnUrl;
  final String? cancelUrl;
  final String? webhookUrl;

  const PaymentRequest({
    required this.amount,
    required this.currency,
    required this.invoiceNumber,
    required this.customerId,
    this.description,
    this.customerName,
    this.customerEmail,
    this.customerPhone,
    this.metadata,
    this.returnUrl,
    this.cancelUrl,
    this.webhookUrl,
  });

  @override
  List<Object?> get props => [
    amount,
    currency,
    invoiceNumber,
    customerId,
    description,
    customerName,
    customerEmail,
    customerPhone,
    metadata,
    returnUrl,
    cancelUrl,
    webhookUrl,
  ];

  PaymentRequest copyWith({
    double? amount,
    String? currency,
    String? invoiceNumber,
    String? customerId,
    String? description,
    String? customerName,
    String? customerEmail,
    String? customerPhone,
    Map<String, dynamic>? metadata,
    String? returnUrl,
    String? cancelUrl,
    String? webhookUrl,
  }) {
    return PaymentRequest(
      amount: amount ?? this.amount,
      currency: currency ?? this.currency,
      invoiceNumber: invoiceNumber ?? this.invoiceNumber,
      customerId: customerId ?? this.customerId,
      description: description ?? this.description,
      customerName: customerName ?? this.customerName,
      customerEmail: customerEmail ?? this.customerEmail,
      customerPhone: customerPhone ?? this.customerPhone,
      metadata: metadata ?? this.metadata,
      returnUrl: returnUrl ?? this.returnUrl,
      cancelUrl: cancelUrl ?? this.cancelUrl,
      webhookUrl: webhookUrl ?? this.webhookUrl,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'amount': amount,
      'currency': currency,
      'invoice_number': invoiceNumber,
      'customer_id': customerId,
      if (description != null) 'description': description,
      if (customerName != null) 'customer_name': customerName,
      if (customerEmail != null) 'customer_email': customerEmail,
      if (customerPhone != null) 'customer_phone': customerPhone,
      if (metadata != null) 'metadata': metadata,
      if (returnUrl != null) 'return_url': returnUrl,
      if (cancelUrl != null) 'cancel_url': cancelUrl,
      if (webhookUrl != null) 'webhook_url': webhookUrl,
    };
  }

  /// Validates the payment request
  bool get isValid {
    return validationErrors.isEmpty;
  }

  /// Returns validation errors if any
  List<String> get validationErrors {
    final errors = <String>[];

    if (amount <= 0) {
      errors.add('Amount must be greater than 0');
    }

    if (currency.isEmpty) {
      errors.add('Currency is required');
    }

    if (invoiceNumber.isEmpty) {
      errors.add('Invoice number is required');
    }

    if (customerId.isEmpty) {
      errors.add('Customer ID is required');
    }

    if (customerEmail != null && !_isValidEmail(customerEmail!)) {
      errors.add('Invalid email format');
    }

    return errors;
  }

  bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }
}
