import '../../../domain/entities/payments/payments_summary.dart';
import 'customer_payment_model.dart';

class PaymentsSummaryModel {
  final double totalSum;
  final List<CustomerPaymentModel> results;

  PaymentsSummaryModel({required this.totalSum, required this.results});

  factory PaymentsSummaryModel.fromJson(Map<String, dynamic> json) {
    return PaymentsSummaryModel(
      totalSum: (json['totalSum'] ?? 0).toDouble(),
      results:
          (json['results'] as List)
              .map((payment) => CustomerPaymentModel.fromJson(payment))
              .toList(),
    );
  }

  PaymentsSummary toEntity() {
    return PaymentsSummary(
      totalSum: totalSum,
      payments: results.map((model) => model.toEntity()).toList(),
    );
  }
}
