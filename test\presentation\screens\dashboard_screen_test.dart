import 'package:aquapartner/presentation/cubit/dashboard/dashboard_cubit.dart';
import 'package:aquapartner/presentation/cubit/dashboard/dashboard_state.dart';
import 'package:aquapartner/presentation/cubit/product_catalogue/product_catalogue_cubit.dart';
import 'package:aquapartner/presentation/cubit/product_catalogue/product_catalogue_state.dart';
import 'package:aquapartner/presentation/cubit/update_checker/update_checker_cubit.dart';
import 'package:aquapartner/presentation/cubit/update_checker/update_checker_state.dart';
import 'package:aquapartner/presentation/cubit/price_list/price_list_cubit.dart';
import 'package:aquapartner/presentation/cubit/price_list/price_list_state.dart';
import 'package:aquapartner/presentation/cubit/home/<USER>';
import 'package:aquapartner/presentation/cubit/home/<USER>';
import 'package:aquapartner/presentation/screens/dashboard_screen.dart';
import 'package:aquapartner/domain/entities/dashboard/dashboard_entity.dart';
import 'package:aquapartner/presentation/widgets/loading_widget.dart';
import 'package:bloc_test/bloc_test.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../helpers/test_helpers.dart';

// Mock classes
class MockDashboardCubit extends MockCubit<DashboardState>
    implements DashboardCubit {}

class MockProductCatalogueCubit extends MockCubit<ProductCatalogueState>
    implements ProductCatalogueCubit {}

class MockUpdateCheckerCubit extends MockCubit<UpdateCheckerState>
    implements UpdateCheckerCubit {}

class MockPriceListCubit extends MockCubit<PriceListState>
    implements PriceListCubit {}

class MockHomeCubit extends MockCubit<HomeState> implements HomeCubit {}

// Helper method to create test dashboard entity
DashboardEntity createTestDashboardEntity() {
  return const DashboardEntity(
    customerId: 'test_customer_123',
    sales: SalesEntity(yearlyData: {}),
    payments: PaymentsEntity(yearlyData: {}),
    dues: [],
    salesReturn: 0.0,
    categoryTypeSales: [],
    liquidation: LiquidationEntity(
      totalLiquidation: 0.0,
      liquidationByYear: [],
    ),
    myFarmers: MyFarmersEntity(totalFarmers: [], potentialFarmers: 0),
  );
}

void main() {
  group('DashboardScreen Tests', () {
    late MockDashboardCubit mockDashboardCubit;
    late MockProductCatalogueCubit mockProductCatalogueCubit;
    late MockUpdateCheckerCubit mockUpdateCheckerCubit;
    late MockPriceListCubit mockPriceListCubit;
    late MockHomeCubit mockHomeCubit;

    setUp(() {
      // Setup GetIt services for AnalyticsMixin
      TestHelpers.setupGetItServices();

      mockDashboardCubit = MockDashboardCubit();
      mockProductCatalogueCubit = MockProductCatalogueCubit();
      mockUpdateCheckerCubit = MockUpdateCheckerCubit();
      mockPriceListCubit = MockPriceListCubit();
      mockHomeCubit = MockHomeCubit();

      // Set up default states
      when(() => mockDashboardCubit.state).thenReturn(
        DashboardLoaded(
          dashboard: createTestDashboardEntity(),
          isSyncing: false,
          hasError: false,
        ),
      );
      when(() => mockDashboardCubit.stream).thenAnswer(
        (_) => Stream.value(
          DashboardLoaded(
            dashboard: createTestDashboardEntity(),
            isSyncing: false,
            hasError: false,
          ),
        ),
      );

      when(() => mockProductCatalogueCubit.state).thenReturn(
        ProductCatalogueState(
          status: ProductCatalogueStatus.success,
          productCatalogues: const [],
        ),
      );
      when(() => mockProductCatalogueCubit.stream).thenAnswer(
        (_) => Stream.value(
          ProductCatalogueState(
            status: ProductCatalogueStatus.success,
            productCatalogues: const [],
          ),
        ),
      );

      when(
        () => mockUpdateCheckerCubit.state,
      ).thenReturn(UpdateCheckerInitial());
      when(
        () => mockUpdateCheckerCubit.stream,
      ).thenAnswer((_) => Stream.value(UpdateCheckerInitial()));

      when(() => mockPriceListCubit.state).thenReturn(
        PriceListState(
          status: PriceListStatus.success,
          priceLists: const [],
          selectedState: null,
        ),
      );
      when(() => mockPriceListCubit.stream).thenAnswer(
        (_) => Stream.value(
          PriceListState(
            status: PriceListStatus.success,
            priceLists: const [],
            selectedState: null,
          ),
        ),
      );

      when(() => mockHomeCubit.state).thenReturn(const HomeState());
      when(
        () => mockHomeCubit.stream,
      ).thenAnswer((_) => Stream.value(const HomeState()));

      // Mock cubit methods
      when(
        () => mockDashboardCubit.loadDashboardData(),
      ).thenAnswer((_) async {});
      when(
        () => mockUpdateCheckerCubit.checkForUpdate(),
      ).thenAnswer((_) async {});
      when(
        () => mockDashboardCubit.refreshDashboard(),
      ).thenAnswer((_) async {});
      when(() => mockHomeCubit.gotoProductsView()).thenAnswer((_) async {});
    });

    tearDown(() {
      // Cleanup GetIt services
      TestHelpers.cleanupGetItServices();
    });

    Widget createDashboardScreen() {
      return TestHelpers.createTestApp(
        home: MultiBlocProvider(
          providers: [
            BlocProvider<DashboardCubit>.value(value: mockDashboardCubit),
            BlocProvider<ProductCatalogueCubit>.value(
              value: mockProductCatalogueCubit,
            ),
            BlocProvider<UpdateCheckerCubit>.value(
              value: mockUpdateCheckerCubit,
            ),
            BlocProvider<PriceListCubit>.value(value: mockPriceListCubit),
            BlocProvider<HomeCubit>.value(value: mockHomeCubit),
          ],
          child: DashboardScreen(),
        ),
      );
    }

    group('Widget Rendering', () {
      testWidgets('should render dashboard screen with main components', (
        tester,
      ) async {
        await tester.pumpWidget(createDashboardScreen());
        await tester.pump();

        // Verify main structure
        expect(find.byType(DashboardScreen), findsOneWidget);
      });

      testWidgets('should display dashboard data when loaded', (tester) async {
        // Ensure the dashboard is in loaded state
        when(() => mockDashboardCubit.stream).thenAnswer(
          (_) => Stream.value(
            DashboardLoaded(
              dashboard: createTestDashboardEntity(),
              isSyncing: false,
            ),
          ),
        );

        await tester.pumpWidget(createDashboardScreen());
        await tester.pump();

        // Verify dashboard content is displayed
        expect(find.byType(SingleChildScrollView), findsAtLeastNWidgets(1));

        // Verify that the dashboard content is rendered without specific text checks
        // The actual text rendering depends on complex widget interactions
        expect(find.byType(DashboardScreen), findsOneWidget);
      });

      testWidgets('should show loading state', (tester) async {
        // Set loading state
        when(() => mockDashboardCubit.state).thenReturn(DashboardLoading());
        when(
          () => mockDashboardCubit.stream,
        ).thenAnswer((_) => Stream.value(DashboardLoading()));

        await tester.pumpWidget(createDashboardScreen());
        await tester.pump();

        // Verify loading widget is displayed
        expect(find.byType(LoadingWidget), findsOneWidget);
      });

      testWidgets('should handle error state gracefully', (tester) async {
        // Set error state
        when(() => mockDashboardCubit.state).thenReturn(
          DashboardLoaded(
            dashboard: createTestDashboardEntity(),
            isSyncing: false,
            hasError: true,
            errorMessage: 'Test error',
          ),
        );

        await tester.pumpWidget(createDashboardScreen());
        await tester.pump();

        // Verify screen renders without crashing
        expect(find.byType(DashboardScreen), findsOneWidget);
      });
    });

    group('Dashboard Content', () {
      testWidgets('should display dashboard widgets', (tester) async {
        await tester.pumpWidget(createDashboardScreen());
        await tester.pump();

        // Verify dashboard widgets are present
        expect(find.byType(SingleChildScrollView), findsOneWidget);
        expect(find.byType(Container), findsAtLeastNWidgets(1));
      });

      testWidgets('should handle scrolling', (tester) async {
        await tester.pumpWidget(createDashboardScreen());
        await tester.pump();

        // Find scrollable widget and perform scroll
        final scrollable = find.byType(SingleChildScrollView);
        if (scrollable.evaluate().isNotEmpty) {
          await tester.fling(scrollable, const Offset(0, -300), 1000);
          await tester.pump();

          // Verify no crashes occurred
          expect(find.byType(DashboardScreen), findsOneWidget);
        }
      });
    });

    group('Basic Functionality', () {
      testWidgets('should handle user interactions without crashing', (
        tester,
      ) async {
        await tester.pumpWidget(createDashboardScreen());
        await tester.pump();

        // Find tappable elements (cards, buttons, etc.)
        final tappableElements = find.byType(GestureDetector);
        if (tappableElements.evaluate().isNotEmpty) {
          await tester.tap(tappableElements.first);
          await tester.pump();

          // Verify interaction was handled without crashes
          expect(find.byType(DashboardScreen), findsOneWidget);
        }
      });

      testWidgets('should handle analytics mixin integration', (tester) async {
        await tester.pumpWidget(createDashboardScreen());
        await tester.pump();

        // Verify screen renders without analytics errors
        expect(find.byType(DashboardScreen), findsOneWidget);

        // The screen should not crash when analytics methods are called
        // This is implicitly tested by the successful rendering
      });
    });
  });
}
