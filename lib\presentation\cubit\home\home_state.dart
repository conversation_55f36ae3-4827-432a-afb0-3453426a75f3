import 'package:equatable/equatable.dart';

import '../../../domain/entities/customer.dart';

enum ViewType {
  dashboard,
  accountStatement,
  billingAndPayments,
  priceList,
  productList,
  myFarmers,
  stocks,
  helpAndSupport,
}

class HomeState extends Equatable {
  // Base state with common properties
  final Customer? customer;
  final String? phoneNumber;
  final ViewType viewType;
  final bool isLoading;
  final bool isSyncing;
  final String? errorMessage;
  final bool isNetworkError;

  const HomeState({
    this.customer,
    this.phoneNumber,
    this.viewType = ViewType.dashboard,
    this.isLoading = false,
    this.isSyncing = false,
    this.errorMessage,
    this.isNetworkError = false,
  });

  // Create a copy of this state with modified properties
  HomeState copyWith({
    Customer? customer,
    String? phoneNumber,
    ViewType? viewType,
    bool? isLoading,
    bool? isSyncing,
    String? errorMessage,
    bool? isNetworkError,
  }) {
    return HomeState(
      customer: customer ?? this.customer,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      viewType: viewType ?? this.viewType,
      isLoading: isLoading ?? this.isLoading,
      isSyncing: isSyncing ?? this.isSyncing,
      errorMessage: errorMessage, // Null means clear error
      isNetworkError: isNetworkError ?? this.isNetworkError,
    );
  }

  @override
  List<Object?> get props => [
    customer,
    phoneNumber,
    viewType,
    isLoading,
    isSyncing,
    errorMessage,
    isNetworkError,
  ];
}
