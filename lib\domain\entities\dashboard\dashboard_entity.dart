import 'package:equatable/equatable.dart';

class DashboardEntity extends Equatable {
  final String customerId;
  final SalesEntity sales;
  final PaymentsEntity payments;
  final List<DueTierEntity> dues;
  final double salesReturn; // Added salesReturn field
  final List<CategorySalesEntity> categoryTypeSales;
  final LiquidationEntity liquidation;
  final MyFarmersEntity myFarmers;

  const DashboardEntity({
    required this.customerId,
    required this.sales,
    required this.payments,
    required this.dues,
    required this.salesReturn, // Added salesReturn to constructor
    required this.categoryTypeSales,
    required this.liquidation,
    required this.myFarmers,
  });

  @override
  List<Object?> get props => [
    customerId,
    sales,
    payments,
    dues,
    salesReturn, // Added salesReturn to props
    categoryTypeSales,
    liquidation,
    myFarmers,
  ];
}

class SalesEntity extends Equatable {
  final Map<String, List<MonthSalesEntity>> yearlyData;

  const SalesEntity({required this.yearlyData});

  @override
  List<Object?> get props => [yearlyData];
}

class MonthSalesEntity extends Equatable {
  final String month;
  final double totalSales;
  final List<WeekSalesEntity> weeks;

  const MonthSalesEntity({
    required this.month,
    required this.totalSales,
    required this.weeks,
  });

  @override
  List<Object?> get props => [month, totalSales, weeks];
}

class WeekSalesEntity extends Equatable {
  final String week;
  final double amount;

  const WeekSalesEntity({required this.week, required this.amount});

  @override
  List<Object?> get props => [week, amount];
}

class PaymentsEntity extends Equatable {
  final Map<String, List<MonthPaymentEntity>> yearlyData;

  const PaymentsEntity({required this.yearlyData});

  @override
  List<Object?> get props => [yearlyData];
}

class MonthPaymentEntity extends Equatable {
  final String month;
  final double totalPayment;
  final List<WeekPaymentEntity> weeks;

  const MonthPaymentEntity({
    required this.month,
    required this.totalPayment,
    required this.weeks,
  });

  @override
  List<Object?> get props => [month, totalPayment, weeks];
}

class WeekPaymentEntity extends Equatable {
  final String week;
  final double amount;

  const WeekPaymentEntity({required this.week, required this.amount});

  @override
  List<Object?> get props => [week, amount];
}

class DueTierEntity extends Equatable {
  final String ageTier;
  final double totalAmount;

  const DueTierEntity({required this.ageTier, required this.totalAmount});

  @override
  List<Object?> get props => [ageTier, totalAmount];
}

class CategorySalesEntity extends Equatable {
  final String categoryType;
  final Map<String, List<MonthCategorySalesEntity>> yearlySales;

  const CategorySalesEntity({
    required this.categoryType,
    required this.yearlySales,
  });

  @override
  List<Object?> get props => [categoryType, yearlySales];
}

class MonthCategorySalesEntity extends Equatable {
  final String month;
  final double totalPayableAmount;
  final List<WeekCategorySalesEntity> weeks;

  const MonthCategorySalesEntity({
    required this.month,
    required this.totalPayableAmount,
    required this.weeks,
  });

  @override
  List<Object?> get props => [month, totalPayableAmount, weeks];
}

class WeekCategorySalesEntity extends Equatable {
  final String week;
  final double amount;

  const WeekCategorySalesEntity({required this.week, required this.amount});

  @override
  List<Object?> get props => [week, amount];
}

class LiquidationEntity extends Equatable {
  final double totalLiquidation;
  final List<YearLiquidationEntity> liquidationByYear;

  const LiquidationEntity({
    required this.totalLiquidation,
    required this.liquidationByYear,
  });

  @override
  List<Object?> get props => [totalLiquidation, liquidationByYear];
}

class YearLiquidationEntity extends Equatable {
  final String year;
  final Map<String, double> months;

  const YearLiquidationEntity({required this.year, required this.months});

  @override
  List<Object?> get props => [year, months];
}

class MyFarmersEntity extends Equatable {
  final List<FarmerEntity> totalFarmers;
  final int potentialFarmers;

  const MyFarmersEntity({
    required this.totalFarmers,
    required this.potentialFarmers,
  });

  @override
  List<Object?> get props => [totalFarmers, potentialFarmers];
}

class FarmerEntity extends Equatable {
  final String farmerName;
  final List<FarmerVisitEntity> visits;

  const FarmerEntity({required this.farmerName, required this.visits});

  @override
  List<Object?> get props => [farmerName, visits];
}

class FarmerVisitEntity extends Equatable {
  final DateTime createdDateTime;
  final int doc;
  final String pondId;
  final String farmerId;
  final String mobileNumber;
  final String productUsed;

  const FarmerVisitEntity({
    required this.createdDateTime,
    required this.doc,
    required this.pondId,
    required this.farmerId,
    required this.mobileNumber,
    required this.productUsed,
  });

  @override
  List<Object?> get props => [
    createdDateTime,
    doc,
    pondId,
    farmerId,
    mobileNumber,
    productUsed,
  ];
}
