import 'package:dartz/dartz.dart';

import '../../core/error/failures.dart';
import '../../core/utils/logger.dart';
import '../entities/price_list.dart';
import '../repositories/price_list_repository.dart';

class GetPriceListsUseCase {
  final PriceListRepository repository;
  final AppLogger logger;

  GetPriceListsUseCase({
    required this.repository,
    required this.logger,
  });

  Future<Either<Failure, List<PriceList>>> call() async {
    logger.i("GetPriceListsUseCase: Getting price lists");
    return await repository.getPriceLists();
  }
}

class SyncPriceListsUseCase {
  final PriceListRepository repository;
  final AppLogger logger;

  SyncPriceListsUseCase({
    required this.repository,
    required this.logger,
  });

  Future<Either<Failure, List<PriceList>>> call() async {
    logger.i("SyncPriceListsUseCase: Syncing price lists");
    return await repository.syncPriceLists();
  }
}

class GetPriceListByStateUseCase {
  final PriceListRepository repository;
  final AppLogger logger;

  GetPriceListByStateUseCase({
    required this.repository,
    required this.logger,
  });

  Future<Either<Failure, PriceList?>> call(String state) async {
    logger.i("GetPriceListByStateUseCase: Getting price list by state: $state");
    return await repository.getPriceListByState(state);
  }
}