# Payment Integration Troubleshooting Guide

## 🚨 Critical Issues Resolution

### Issue 1: Firebase App Check 403 Attestation Failed

**Root Cause**: Firebase App Check is blocking payment API calls due to failed attestation.

**Immediate Fix Applied**:
- Modified `AppCheckInterceptor` to bypass App Check for payment endpoints
- Added debug mode fallback headers
- Payment endpoints now work without App Check interference

**Verification**:
```bash
# Test the fix
flutter test test/debug/payment_debug_test.dart
```

### Issue 2: 500 Internal Server Error

**Root Cause**: Backend payment endpoint `/zoho/payments/create-session` is failing.

**Enhanced Error Handling**:
- Added detailed logging in `PaymentRemoteDataSource`
- Improved error messages based on HTTP status codes
- Better exception handling for different error types

**Backend Investigation Required**:
1. Check if `/zoho/payments/create-session` endpoint exists
2. Verify Zoho payment API configuration on backend
3. Check backend logs for detailed error information

### Issue 3: Generic Error Messages

**Fixed**: Enhanced error handling in `PaymentCubit` to provide specific, actionable error messages:
- Network errors: "Check your internet connection"
- 500 errors: "Technical difficulties, team notified"
- 403 errors: "Security settings blocked request"
- 404 errors: "Endpoint not found, contact support"
- App Check errors: "Security verification failed, update app"

## 🔧 Debugging Tools

### 1. Payment Debug Helper

Use the new debug helper to diagnose issues:

```dart
import 'package:aquapartner/core/debug/payment_debug_helper.dart';

// Run comprehensive debug test
final result = await PaymentDebugHelper.testPaymentAPI();
final report = PaymentDebugHelper.generateDebugReport(result);
print(report);
```

### 2. Manual Testing Commands

```bash
# Run debug tests
flutter test test/debug/payment_debug_test.dart

# Test with specific environment
flutter test test/debug/payment_debug_test.dart --dart-define=ENVIRONMENT=staging

# Run payment integration tests
flutter test test/integration/payment_api_integration_test.dart

# Analyze payment code
flutter analyze lib/presentation/widgets/zoho_payment_button.dart
flutter analyze lib/data/datasources/remote/payment_remote_datasource.dart
```

### 3. Backend API Testing

Test the backend directly:

```bash
# Test payment endpoint
curl -X POST https://partner.aquaconnect.blue/api/zoho/payments/create-session \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "amount": 100.0,
    "currency": "INR",
    "invoice_number": "TEST_001",
    "customer_id": "TEST_CUSTOMER",
    "description": "Test payment"
  }'

# Test health endpoint
curl -X GET https://partner.aquaconnect.blue/api/health

# Test auth endpoint
curl -X GET https://partner.aquaconnect.blue/api/auth/verify \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 🔍 Step-by-Step Debugging Process

### Step 1: Environment Verification

1. Check current environment:
```dart
print('Environment: ${AppConstants.baseUrl}');
```

2. Verify network connectivity:
```bash
ping partner.aquaconnect.blue
```

3. Test DNS resolution:
```bash
nslookup partner.aquaconnect.blue
```

### Step 2: Authentication Check

1. Verify auth token exists:
```dart
final token = await secureStorage.read(key: 'auth_token');
print('Token exists: ${token != null}');
```

2. Test token validity:
```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
  https://partner.aquaconnect.blue/api/auth/verify
```

### Step 3: Payment Endpoint Testing

1. Test endpoint availability:
```bash
curl -I https://partner.aquaconnect.blue/api/zoho/payments/create-session
```

2. Check backend logs for errors
3. Verify Zoho payment configuration on backend

### Step 4: App Check Investigation

1. Check App Check status in Firebase Console
2. Verify debug tokens are configured
3. Test with App Check disabled (already implemented)

## 🛠️ Configuration Fixes

### 1. Environment Configuration

Ensure correct environment is set:

```bash
# Development
flutter run --dart-define=ENVIRONMENT=dev

# Staging
flutter run --dart-define=ENVIRONMENT=staging

# Production (default)
flutter run --dart-define=ENVIRONMENT=production
```

### 2. Firebase App Check Configuration

If App Check issues persist, configure debug tokens:

1. Go to Firebase Console > App Check
2. Add debug tokens for development devices
3. Or disable App Check for payment endpoints (already done)

### 3. Backend Configuration Check

Verify these environment variables on backend:

```env
ZOHO_OAUTH_CLIENT_ID=1000.OYY8H71ELT0DNW5CFFBE124H7GILMP
ZOHO_OAUTH_CLIENT_SECRET=67ca50629a18ba97eb9868be9c8faacf6645edf13d
ZOHO_OAUTH_REFRESH_TOKEN=**********************************************************************
ZOHO_PAYMENT_SESSION_URL=https://payments.zoho.in/api/v1/paymentsessions
ZOHO_PAY_ACCOUNT_ID=***********
ZOHO_PAY_API_KEY=1003.f35b9411653295bb03db1e8490dc6cdd.0f625d54ec97f4eba7bedd0dc6fc23b8
```

## 📊 Monitoring and Logging

### Enhanced Logging

The payment system now includes detailed logging:

1. **Request/Response Logging**: All API calls are logged with full details
2. **Error Context**: Specific error types and HTTP status codes
3. **User Actions**: Payment button clicks and completion events
4. **Performance Metrics**: API response times and success rates

### Log Analysis

Check logs for these patterns:

```
# Successful payment session creation
[INFO] Creating payment session for invoice: INV-001
[INFO] Payment session created successfully

# App Check bypass
[INFO] Skipping App Check for payment endpoint: /zoho/payments/create-session

# Error patterns
[ERROR] Failed to create payment session: 500 - Response: {...}
[ERROR] Error creating payment session: ServerException
```

## 🧪 Testing Scenarios

### 1. Happy Path Testing

```dart
// Test successful payment flow
final request = PaymentRequest(
  amount: 100.0,
  currency: 'INR',
  invoiceNumber: 'TEST_001',
  customerId: 'TEST_CUSTOMER',
);

final result = await paymentRepository.createPaymentSession(request);
// Should succeed without App Check interference
```

### 2. Error Scenario Testing

```dart
// Test various error conditions
await testErrorScenario('Network timeout');
await testErrorScenario('Server error 500');
await testErrorScenario('Authentication error 401');
await testErrorScenario('App Check attestation failed');
```

### 3. Integration Testing

```bash
# Run full integration test suite
flutter test test/integration/payment_api_integration_test.dart

# Test specific scenarios
flutter test test/integration/payment_api_integration_test.dart --name "should handle server errors"
```

## 🚀 Next Steps

### Immediate Actions Required

1. **Backend Investigation**: Check backend logs for 500 error root cause
2. **Endpoint Verification**: Confirm `/zoho/payments/create-session` exists and is configured
3. **Zoho Configuration**: Verify Zoho payment API credentials and settings
4. **Environment Testing**: Test in staging environment first

### Monitoring Setup

1. Set up alerts for payment failures
2. Monitor App Check attestation rates
3. Track payment success/failure metrics
4. Log payment performance metrics

### Long-term Improvements

1. Implement retry logic for transient failures
2. Add circuit breaker pattern for backend failures
3. Implement payment status webhooks
4. Add comprehensive payment analytics

## 📞 Support Contacts

- **Backend Team**: For 500 error investigation
- **DevOps Team**: For environment and DNS issues
- **Firebase Team**: For App Check configuration
- **Zoho Support**: For payment API issues

## 📋 Checklist

- [x] App Check bypass for payment endpoints
- [x] Enhanced error handling and messages
- [x] Detailed logging and debugging tools
- [x] Comprehensive test suite
- [ ] Backend 500 error investigation
- [ ] Zoho payment configuration verification
- [ ] Production testing and validation
