import 'package:flutter/material.dart';

class InputField extends StatelessWidget {
  final TextEditingController controller;
  final String placeholder;
  final Widget? leading;
  final Widget? trailing;
  final bool enableBorder;
  final int maxLines;
  final void Function()? trailingTapped;
  final String? Function(String?)? validator;
  final String? errorText;
  final TextInputType keyboardType;
  final Color errorColor; // Add this parameter

  final circularBorder = OutlineInputBorder(
    borderRadius: BorderRadius.circular(8),
  );

  InputField({
    super.key,
    required this.controller,
    this.placeholder = '',
    this.leading,
    this.trailing,
    this.trailingTapped,
    this.maxLines = 1,
    this.enableBorder = true,
    this.validator,
    this.errorText,
    this.keyboardType = TextInputType.text,
    this.errorColor = Colors.red, // Default to red, but can be customized
  });

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: ThemeData(
        primaryColor: Colors.blueAccent,
        colorScheme: ColorScheme.light(
            error: errorColor), // Modern way to set error color
      ),
      child: TextForm<PERSON>ield(
        controller: controller,
        validator: validator,
        keyboardType: keyboardType,
        maxLines: maxLines,
        decoration: InputDecoration(
          hintText: placeholder.isEmpty ? 'Enter your full name' : placeholder,
          hintStyle: const TextStyle(
            color: Colors.grey,
          ),
          errorText: errorText,
          errorStyle: TextStyle(
            color: errorColor, // Apply the custom error color
            fontSize: 12.0, // You can also customize other text properties
          ),
          contentPadding: const EdgeInsets.symmetric(
            vertical: 16.0,
            horizontal: 12.0,
          ),
          prefixIcon: leading,
          suffixIcon: trailing != null
              ? GestureDetector(
            onTap: trailingTapped,
            child: trailing,
          )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8.0),
            borderSide: const BorderSide(
              color: Colors.grey,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8.0),
            borderSide: const BorderSide(
              color: Colors.grey,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8.0),
            borderSide: const BorderSide(
              color: Colors.blue,
              width: 2.0,
            ),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8.0),
            borderSide: BorderSide(
              color: errorColor, // Use the custom error color for the border
              width: 1.0,
            ),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8.0),
            borderSide: BorderSide(
              color:
              errorColor, // Use the custom error color for the focused error border
              width: 2.0,
            ),
          ),
        ),
      ),
    );
  }
}
