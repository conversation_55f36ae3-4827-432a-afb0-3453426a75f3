import 'package:aquapartner/domain/services/auth_service.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/error/failures.dart';
import '../../../core/utils/logger.dart';
import '../../../domain/entities/farmer_visit/farmer.dart';
import '../../../domain/usecases/get_all_farmers_usecase.dart';
part 'farmer_visits_state.dart';

class FarmerVisitsCubit extends Cubit<FarmerVisitsState> {
  final GetAllFarmersUseCase getAllFarmersUseCase;
  final AuthService authService;
  final AppLogger logger = AppLogger();

  FarmerVisitsCubit({
    required this.getAllFarmersUseCase,
    required this.authService,
  }) : super(FarmerVisitsInitial());

  Future<void> getFarmerVisits() async {
    // Don't emit loading if we're already in a loaded state to prevent flickering
    if (state is! FarmerVisitsLoaded) {
      emit(FarmerVisitsLoading());
    }

    final authServiceResult = await authService.getCurrentCustomer();

    await authServiceResult.fold(
      (failure) async => emit(_mapFailureToErrorState(failure)),
      (customer) async {
        if (customer == null) {
          emit(const FarmerVisitsError('Customer information not found'));
          return;
        }

        final result = await getAllFarmersUseCase.call(
          GetAllFarmersParams(customerId: customer.customerId),
        );

        result.fold((failure) => emit(_mapFailureToErrorState(failure)), (
          farmersList,
        ) {
          if (farmersList.isEmpty) {
            emit(FarmerVisitsEmpty());
          } else {
            emit(FarmerVisitsLoaded(farmersList));
          }
        });
      },
    );
  }

  Future<void> syncFarmerVisits() async {
    // Preserve the current state to avoid UI flicker
    final currentState = state;

    // Only show syncing state if we already have data
    if (currentState is FarmerVisitsLoaded) {
      emit(FarmerVisitsSyncing(currentState.farmers));
    } else {
      emit(const FarmerVisitsSyncing());
    }

    final authServiceResult = await authService.getCurrentCustomer();

    await authServiceResult.fold(
      (failure) async {
        logger.e('Failed to get customer: ${failure.toString()}');
        // If we have current data, revert to it instead of showing error
        if (currentState is FarmerVisitsLoaded) {
          emit(currentState);
        } else {
          emit(_mapFailureToErrorState(failure));
        }
      },
      (customer) async {
        if (customer == null) {
          logger.e('Customer information not found');
          // If we have current data, revert to it instead of showing error
          if (currentState is FarmerVisitsLoaded) {
            emit(currentState);
          } else {
            emit(const FarmerVisitsError('Customer information not found'));
          }
          return;
        }

        // Perform the one-way sync
        final result = await getAllFarmersUseCase.syncFarmers(
          customer.customerId,
        );

        result.fold(
          (failure) {
            logger.e('Sync failed: ${failure.toString()}');
            // If we have current data, revert to it instead of showing error
            if (currentState is FarmerVisitsLoaded) {
              emit(currentState);
            } else {
              emit(_mapFailureToErrorState(failure));
            }
          },
          (farmers) {
            if (farmers.isEmpty) {
              logger.i('Sync completed but no farmers found');
              emit(FarmerVisitsEmpty());
            } else {
              logger.i(
                'Sync completed successfully: ${farmers.length} farmers synced',
              );
              emit(FarmerVisitsLoaded(farmers));
            }
          },
        );
      },
    );
  }

  FarmerVisitsError _mapFailureToErrorState(Failure failure) {
    return FarmerVisitsError(_mapFailureToErrorMessage(failure));
  }

  String _mapFailureToErrorMessage(Failure failure) {
    if (failure is ServerFailure) {
      return 'Failed to connect to the server. Please check your internet connection.';
    } else if (failure is CacheFailure) {
      return 'Failed to load cached data. Please try again.';
    } else if (failure is NetworkFailure) {
      return 'No internet connection. Please check your network settings.';
    } else {
      return 'An unexpected error occurred. Please try again later.';
    }
  }
}
