import 'package:flutter/material.dart';
import '../services/analytics_service.dart';
import '../../injection_container.dart' as di;

/// Mixin for tracking analytics in sub-screens (tabs, pages within screens)
mixin SubScreenAnalyticsMixin<T extends StatefulWidget> on State<T> {
  final AnalyticsService _analyticsService = di.sl<AnalyticsService>();
  DateTime? _subScreenStartTime;
  
  /// The name of this sub-screen
  String get subScreenName;
  
  /// The parent screen containing this sub-screen
  String get parentScreenName;

  @override
  void initState() {
    super.initState();
    _subScreenStartTime = DateTime.now();
    
    // Track sub-screen view
    _analyticsService.logEvent(
      name: 'sub_screen_view',
      parameters: {
        'sub_screen_name': subScreenName,
        'parent_screen': parentScreenName,
        'screen_class': widget.runtimeType.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  @override
  void dispose() {
    if (_subScreenStartTime != null) {
      final duration = DateTime.now().difference(_subScreenStartTime!);
      
      // Log sub-screen duration
      _analyticsService.logEvent(
        name: 'sub_screen_duration',
        parameters: {
          'sub_screen_name': subScreenName,
          'parent_screen': parentScreenName,
          'duration_ms': duration.inMilliseconds,
          'screen_class': widget.runtimeType.toString(),
          'timestamp': DateTime.now().toIso8601String(),
        },
      );
    }
    super.dispose();
  }

  void trackSubScreenEvent(String eventName, {Map<String, Object> params = const {}}) {
    final eventParams = {
      'sub_screen_name': subScreenName,
      'parent_screen': parentScreenName,
      'timestamp': DateTime.now().toIso8601String(),
      ...params,
    };

    _analyticsService.logEvent(name: eventName, parameters: eventParams);
  }
}