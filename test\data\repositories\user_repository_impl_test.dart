import 'dart:async';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:dartz/dartz.dart';
import 'package:aquapartner/data/repositories/user_repository_impl.dart';
import 'package:aquapartner/data/datasources/local/user_local_data_source.dart';
import 'package:aquapartner/data/datasources/remote/user_remote_data_source.dart';
import 'package:aquapartner/data/datasources/remote/mongodb_datasource.dart';
import 'package:aquapartner/core/network/network_info.dart';
import 'package:aquapartner/core/utils/logger.dart';
import 'package:aquapartner/core/error/failures.dart';
import 'package:aquapartner/core/error/exceptions.dart';
import 'package:aquapartner/domain/entities/user.dart';
import 'package:aquapartner/data/models/user_model.dart';

// Mock classes
class MockUserRemoteDataSource extends Mock implements UserRemoteDataSource {}

class MockUserLocalDataSource extends Mock implements UserLocalDataSource {}

class MockMongoDBDataSource extends Mock implements MongoDBDataSource {}

class MockNetworkInfo extends Mock implements NetworkInfo {}

class MockAppLogger extends Mock implements AppLogger {}

void main() {
  group('UserRepositoryImpl Tests', () {
    late UserRepositoryImpl repository;
    late MockUserRemoteDataSource mockRemoteDataSource;
    late MockUserLocalDataSource mockLocalDataSource;
    late MockMongoDBDataSource mockMongoDBDataSource;
    late MockNetworkInfo mockNetworkInfo;
    late MockAppLogger mockLogger;

    late User testUser;
    late UserModel testUserModel;

    setUpAll(() {
      // Register fallback values for mocktail
      registerFallbackValue(
        UserModel(phoneNumber: '+919999999999', isVerified: false),
      );
      registerFallbackValue(
        User(
          id: 0,
          phoneNumber: '+919999999999',
          isVerified: false,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      );
    });

    setUp(() {
      mockRemoteDataSource = MockUserRemoteDataSource();
      mockLocalDataSource = MockUserLocalDataSource();
      mockMongoDBDataSource = MockMongoDBDataSource();
      mockNetworkInfo = MockNetworkInfo();
      mockLogger = MockAppLogger();

      repository = UserRepositoryImpl(
        remoteDataSource: mockRemoteDataSource,
        localDataSource: mockLocalDataSource,
        mongoDBDataSource: mockMongoDBDataSource,
        networkInfo: mockNetworkInfo,
        logger: mockLogger,
      );

      testUser = User(
        id: 1,
        phoneNumber: '+919999999999',
        isVerified: true,
        mongoId: 'mongo_id_123',
        needsSync: false,
        createdAt: DateTime(2024, 1, 1, 12, 0, 0),
        updatedAt: DateTime(2024, 1, 2, 12, 0, 0),
      );

      testUserModel = UserModel(
        id: 1,
        phoneNumber: '+919999999999',
        isVerified: true,
        mongoId: 'mongo_id_123',
        needsSync: false,
        createdAt: DateTime(2024, 1, 1, 12, 0, 0),
        updatedAt: DateTime(2024, 1, 2, 12, 0, 0),
      );
    });

    tearDown(() {
      repository.dispose();
    });

    group('getUser Tests', () {
      test(
        'should return user from local data source when available',
        () async {
          // Arrange
          when(
            () => mockLocalDataSource.getUser(),
          ).thenAnswer((_) async => testUser);

          // Act
          final result = await repository.getUser();

          // Assert
          expect(result, equals(Right(testUser)));
          verify(() => mockLocalDataSource.getUser()).called(1);
          // Note: The actual implementation doesn't log these messages
        },
      );

      test(
        'should return CacheFailure when local data source throws CacheException',
        () async {
          // Arrange
          when(() => mockLocalDataSource.getUser()).thenThrow(CacheException());

          // Act
          final result = await repository.getUser();

          // Assert
          expect(result, equals(Left(CacheFailure())));
          verify(() => mockLocalDataSource.getUser()).called(1);
          verify(
            () => mockLogger.e('Cache exception when getting user', any()),
          ).called(1);
        },
      );

      test(
        'should return CacheFailure when unexpected exception occurs',
        () async {
          // Arrange
          when(
            () => mockLocalDataSource.getUser(),
          ).thenThrow(Exception('Unexpected error'));

          // Act
          final result = await repository.getUser();

          // Assert
          expect(result, equals(Left(CacheFailure())));
          verify(() => mockLocalDataSource.getUser()).called(1);
          verify(
            () => mockLogger.e('Unexpected error when getting user', any()),
          ).called(1);
        },
      );
    });

    group('saveUser Tests', () {
      test(
        'should save user successfully and sync when network available',
        () async {
          // Arrange
          when(
            () => mockLocalDataSource.saveUser(any()),
          ).thenAnswer((_) async {});
          when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
          when(
            () => mockLocalDataSource.getUnsyncedUsers(),
          ).thenAnswer((_) async => [testUserModel]);
          when(
            () => mockRemoteDataSource.createUser(any()),
          ).thenAnswer((_) async => 'mongo_id_123');
          when(
            () => mockLocalDataSource.updateUser(any()),
          ).thenAnswer((_) async {});

          // Act
          final result = await repository.saveUser(testUser);

          // Assert
          expect(result, equals(const Right(true)));
          verify(() => mockLocalDataSource.saveUser(any())).called(1);
          verify(() => mockLogger.i('Saving user to local storage')).called(1);
          verify(() => mockLogger.i('User saved successfully')).called(1);
        },
      );

      test(
        'should save user successfully without sync when network unavailable',
        () async {
          // Arrange
          when(
            () => mockLocalDataSource.saveUser(any()),
          ).thenAnswer((_) async {});
          when(
            () => mockNetworkInfo.isConnected,
          ).thenAnswer((_) async => false);

          // Act
          final result = await repository.saveUser(testUser);

          // Assert
          expect(result, equals(const Right(true)));
          verify(() => mockLocalDataSource.saveUser(any())).called(1);
          verify(() => mockLogger.i('Saving user to local storage')).called(1);
          verify(() => mockLogger.i('User saved successfully')).called(1);
          verifyNever(() => mockLocalDataSource.getUnsyncedUsers());
        },
      );

      test('should return CacheFailure when save fails', () async {
        // Arrange
        when(
          () => mockLocalDataSource.saveUser(any()),
        ).thenThrow(CacheException());

        // Act
        final result = await repository.saveUser(testUser);

        // Assert
        expect(result, equals(Left(CacheFailure())));
        verify(() => mockLocalDataSource.saveUser(any())).called(1);
        verify(
          () => mockLogger.e('Cache exception when saving user', any()),
        ).called(1);
      });

      test('should save user even if sync fails', () async {
        // Arrange
        when(
          () => mockLocalDataSource.saveUser(any()),
        ).thenAnswer((_) async {});
        when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        when(
          () => mockLocalDataSource.getUnsyncedUsers(),
        ).thenThrow(Exception('Sync error'));

        // Act
        final result = await repository.saveUser(testUser);

        // Assert
        expect(result, equals(const Right(true)));
        verify(() => mockLocalDataSource.saveUser(any())).called(1);
        verify(() => mockLogger.i('Saving user to local storage')).called(1);
        verify(() => mockLogger.i('User saved successfully')).called(1);
        verify(
          () => mockLogger.e('Error during user synchronization', any()),
        ).called(1);
      });
    });

    group('updateUser Tests', () {
      test('should update user successfully', () async {
        // Arrange
        when(
          () => mockLocalDataSource.updateUser(any()),
        ).thenAnswer((_) async {});
        when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => false);

        // Act
        final result = await repository.updateUser(testUser);

        // Assert
        expect(result, equals(const Right(true)));
        verify(() => mockLocalDataSource.updateUser(any())).called(1);
        verify(() => mockLogger.i('Updating user in local storage')).called(1);
        verify(() => mockLogger.i('User updated successfully')).called(1);
      });

      test('should return CacheFailure when update fails', () async {
        // Arrange
        when(
          () => mockLocalDataSource.updateUser(any()),
        ).thenThrow(CacheException());

        // Act
        final result = await repository.updateUser(testUser);

        // Assert
        expect(result, equals(Left(CacheFailure())));
        verify(() => mockLocalDataSource.updateUser(any())).called(1);
        verify(
          () => mockLogger.e('Cache exception when updating user', any()),
        ).called(1);
      });
    });

    group('deleteUser Tests', () {
      test('should delete user successfully', () async {
        // Arrange
        when(() => mockLocalDataSource.deleteUser()).thenAnswer((_) async {});

        // Act
        final result = await repository.deleteUser();

        // Assert
        expect(result, equals(const Right(true)));
        verify(() => mockLocalDataSource.deleteUser()).called(1);
        verify(
          () => mockLogger.i('Deleting user from local storage'),
        ).called(1);
        verify(() => mockLogger.i('User deleted successfully')).called(1);
      });

      test('should return CacheFailure when delete fails', () async {
        // Arrange
        when(
          () => mockLocalDataSource.deleteUser(),
        ).thenThrow(CacheException());

        // Act
        final result = await repository.deleteUser();

        // Assert
        expect(result, equals(Left(CacheFailure())));
        verify(() => mockLocalDataSource.deleteUser()).called(1);
        verify(
          () => mockLogger.e('Cache exception when deleting user', any()),
        ).called(1);
      });
    });

    group('syncUser Tests', () {
      test('should sync users successfully when network available', () async {
        // Arrange
        when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        when(
          () => mockLocalDataSource.getUnsyncedUsers(),
        ).thenAnswer((_) async => [testUserModel.copyWith(mongoId: null)]);
        when(
          () => mockRemoteDataSource.createUser(any()),
        ).thenAnswer((_) async => 'new_mongo_id');
        when(
          () => mockLocalDataSource.updateUser(any()),
        ).thenAnswer((_) async {});

        // Act
        final result = await repository.syncUser();

        // Assert
        expect(result, equals(const Right(true)));
        verify(() => mockLogger.i('Starting user synchronization')).called(1);
        verify(() => mockLogger.i('Found 1 unsynced users')).called(1);
        verify(
          () => mockLogger.i('User synchronization completed successfully'),
        ).called(1);
      });

      test(
        'should return NetworkFailure when no internet connection',
        () async {
          // Arrange
          when(
            () => mockNetworkInfo.isConnected,
          ).thenAnswer((_) async => false);

          // Act
          final result = await repository.syncUser();

          // Assert
          expect(result, equals(Left(NetworkFailure())));
          verify(
            () => mockLogger.w('Cannot sync users: No internet connection'),
          ).called(1);
        },
      );

      test('should update existing user when mongoId exists', () async {
        // Arrange
        final existingUserModel = testUserModel.copyWith(
          mongoId: 'existing_mongo_id',
          needsSync: true,
        );

        when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        when(
          () => mockLocalDataSource.getUnsyncedUsers(),
        ).thenAnswer((_) async => [existingUserModel]);
        when(
          () => mockRemoteDataSource.updateUser(any()),
        ).thenAnswer((_) async {});
        when(
          () => mockLocalDataSource.updateUser(any()),
        ).thenAnswer((_) async {});

        // Act
        final result = await repository.syncUser();

        // Assert
        expect(result, equals(const Right(true)));
        verify(() => mockRemoteDataSource.updateUser(any())).called(1);
        verifyNever(() => mockRemoteDataSource.createUser(any()));
      });

      test('should return ServerFailure when sync fails', () async {
        // Arrange
        when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        when(
          () => mockLocalDataSource.getUnsyncedUsers(),
        ).thenThrow(Exception('Sync error'));

        // Act
        final result = await repository.syncUser();

        // Assert
        expect(result, equals(Left(ServerFailure())));
        verify(
          () => mockLogger.e('Error during user synchronization', any()),
        ).called(1);
      });
    });

    group('getSyncStatus Tests', () {
      test('should return sync status stream', () {
        // Act
        final stream = repository.getSyncStatus();

        // Assert
        expect(stream, isA<Stream<bool>>());
      });

      test('should emit sync status updates', () async {
        // Arrange
        when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        when(
          () => mockLocalDataSource.getUnsyncedUsers(),
        ).thenAnswer((_) async => [testUserModel.copyWith(mongoId: null)]);
        when(
          () => mockRemoteDataSource.createUser(any()),
        ).thenAnswer((_) async => 'new_mongo_id');
        when(
          () => mockLocalDataSource.updateUser(any()),
        ).thenAnswer((_) async {});

        // Act
        final streamValues = <bool>[];
        final subscription = repository.getSyncStatus().listen(
          streamValues.add,
        );

        await repository.syncUser();
        await Future.delayed(const Duration(milliseconds: 100));

        subscription.cancel();

        // Assert
        expect(streamValues, isNotEmpty); // Stream should emit values
      });
    });

    group('Edge Cases and Error Handling', () {
      test('should handle empty unsynced users list', () async {
        // Arrange
        when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        when(
          () => mockLocalDataSource.getUnsyncedUsers(),
        ).thenAnswer((_) async => []);

        // Act
        final result = await repository.syncUser();

        // Assert
        expect(result, equals(const Right(true)));
        verify(() => mockLogger.i('Found 0 unsynced users')).called(1);
        verifyNever(() => mockRemoteDataSource.createUser(any()));
        verifyNever(() => mockRemoteDataSource.updateUser(any()));
      });

      test('should handle null user from local data source', () async {
        // Arrange
        when(() => mockLocalDataSource.getUser()).thenThrow(CacheException());

        // Act
        final result = await repository.getUser();

        // Assert
        expect(result, equals(Left(CacheFailure())));
      });

      test('should handle network connectivity changes during sync', () async {
        // Arrange
        when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => false);

        // Act
        final result = await repository.syncUser();

        // Assert
        expect(result, equals(Left(NetworkFailure())));
      });

      test('should handle partial sync failures gracefully', () async {
        // Arrange
        // Create users without mongoId so they will be created, not updated
        final user1 = testUserModel.copyWith(mongoId: null);
        final user2 = testUserModel.copyWith(
          id: 2,
          phoneNumber: '+919876543211',
          mongoId: null,
        );

        when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        when(
          () => mockLocalDataSource.getUnsyncedUsers(),
        ).thenAnswer((_) async => [user1, user2]);
        when(
          () => mockRemoteDataSource.createUser(user1),
        ).thenAnswer((_) async => 'mongo_id_1');
        when(
          () => mockRemoteDataSource.createUser(user2),
        ).thenThrow(ServerException());
        when(
          () => mockLocalDataSource.updateUser(any()),
        ).thenAnswer((_) async {});

        // Act
        final result = await repository.syncUser();

        // Assert
        expect(result, equals(Left(ServerFailure())));
        verify(() => mockRemoteDataSource.createUser(user1)).called(1);
        verify(() => mockRemoteDataSource.createUser(user2)).called(1);
      });

      test('should handle concurrent save and sync operations', () async {
        // Arrange
        when(
          () => mockLocalDataSource.saveUser(any()),
        ).thenAnswer((_) async {});
        when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        when(
          () => mockLocalDataSource.getUnsyncedUsers(),
        ).thenAnswer((_) async => [testUserModel.copyWith(mongoId: null)]);
        when(
          () => mockRemoteDataSource.createUser(any()),
        ).thenAnswer((_) async => 'mongo_id_123');
        when(
          () => mockLocalDataSource.updateUser(any()),
        ).thenAnswer((_) async {});

        // Act
        final futures = [repository.saveUser(testUser), repository.syncUser()];

        final results = await Future.wait(futures);

        // Assert
        expect(results[0], equals(const Right(true)));
        expect(results[1], equals(const Right(true)));
      });

      test('should dispose resources properly', () {
        // Act
        repository.dispose();

        // Assert - No exception should be thrown
        expect(() => repository.dispose(), returnsNormally);
      });
    });
  });
}
