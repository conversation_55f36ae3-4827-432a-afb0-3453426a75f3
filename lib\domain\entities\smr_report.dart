class SMRReport {
  final String id;
  final String so;
  final String partner;
  final String partnerId;
  final String productName;
  final DateTime startDate;
  final DateTime lastDate;
  final int openingBalance;
  final int invoice;
  final int srn;
  final int closingBalance;
  final int sales;
  final String status;

  SMRReport({
    required this.id,
    required this.so,
    required this.partner,
    required this.partnerId,
    required this.productName,
    required this.startDate,
    required this.lastDate,
    required this.openingBalance,
    required this.invoice,
    required this.srn,
    required this.closingBalance,
    required this.sales,
    required this.status,
  });
}
