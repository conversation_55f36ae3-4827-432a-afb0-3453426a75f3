#!/usr/bin/env dart

import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;

/// Comprehensive API testing script for Zoho Payment integration
/// 
/// Usage: dart scripts/test_payment_api.dart [environment]
/// 
/// Environments:
/// - production (default): https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/api
/// - staging: https://staging-aquapartner.azurewebsites.net/api
/// - local: http://localhost:3000/api

void main(List<String> args) async {
  final environment = args.isNotEmpty ? args[0] : 'production';
  final baseUrl = _getBaseUrl(environment);
  
  print('🧪 Testing Zoho Payment API');
  print('📍 Environment: $environment');
  print('🌐 Base URL: $baseUrl');
  print('⏰ Started at: ${DateTime.now()}');
  print('=' * 60);
  
  final tester = PaymentApiTester(baseUrl);
  await tester.runAllTests();
}

String _getBaseUrl(String environment) {
  switch (environment.toLowerCase()) {
    case 'production':
      return 'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/api';
    case 'staging':
      return 'https://staging-aquapartner.azurewebsites.net/api';
    case 'local':
      return 'http://localhost:3000/api';
    default:
      throw ArgumentError('Unknown environment: $environment');
  }
}

class PaymentApiTester {
  final String baseUrl;
  final http.Client client;
  int _testCount = 0;
  int _passedTests = 0;
  int _failedTests = 0;
  
  PaymentApiTester(this.baseUrl) : client = http.Client();
  
  Future<void> runAllTests() async {
    try {
      await _testConnectivity();
      await _testHealthEndpoint();
      await _testCreateSessionEndpoint();
      await _testStatusEndpoint();
      await _testEndpointSecurity();
      await _testPerformance();
      
      _printSummary();
    } finally {
      client.close();
    }
  }
  
  Future<void> _testConnectivity() async {
    _printSection('🌐 Connectivity Tests');
    
    await _runTest('Base URL Reachability', () async {
      final response = await client.get(
        Uri.parse(baseUrl),
        headers: {'User-Agent': 'AquaPartner-API-Tester/1.0'},
      ).timeout(const Duration(seconds: 10));
      
      if (response.statusCode >= 200 && response.statusCode < 500) {
        return TestResult.pass('Base URL is reachable (${response.statusCode})');
      } else {
        return TestResult.fail('Base URL unreachable: ${response.statusCode}');
      }
    });
    
    await _runTest('HTTPS Enforcement', () async {
      if (baseUrl.startsWith('https://')) {
        return TestResult.pass('Using HTTPS');
      } else if (baseUrl.startsWith('http://localhost')) {
        return TestResult.pass('Local development (HTTP allowed)');
      } else {
        return TestResult.fail('Not using HTTPS in production');
      }
    });
  }
  
  Future<void> _testHealthEndpoint() async {
    _printSection('❤️ Health Check Tests');
    
    await _runTest('Health Endpoint with Trailing Slash', () async {
      final response = await client.get(
        Uri.parse('$baseUrl/zoho/health/'),
        headers: {'Content-Type': 'application/json'},
      ).timeout(const Duration(seconds: 10));
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return TestResult.pass('Health check successful: ${data['status'] ?? 'OK'}');
      } else if (response.statusCode == 404) {
        return TestResult.warn('Health endpoint not implemented (404)');
      } else {
        return TestResult.fail('Health check failed: ${response.statusCode}');
      }
    });
    
    await _runTest('Health Endpoint without Trailing Slash', () async {
      final response = await client.get(
        Uri.parse('$baseUrl/zoho/health'),
        headers: {'Content-Type': 'application/json'},
      ).timeout(const Duration(seconds: 10));
      
      if (response.statusCode == 200) {
        return TestResult.pass('Works without trailing slash');
      } else if (response.statusCode == 301 || response.statusCode == 302) {
        return TestResult.pass('Redirects to trailing slash version');
      } else if (response.statusCode == 404) {
        return TestResult.warn('Requires trailing slash (404 without)');
      } else {
        return TestResult.fail('Unexpected response: ${response.statusCode}');
      }
    });
  }
  
  Future<void> _testCreateSessionEndpoint() async {
    _printSection('💳 Create Payment Session Tests');
    
    await _runTest('Create Session Endpoint Format', () async {
      final testPayload = {
        'amount': 100.0,
        'currency': 'INR',
        'invoice_number': 'API-TEST-${DateTime.now().millisecondsSinceEpoch}',
        'customer_id': 'TEST-CUSTOMER-001',
        'description': 'API Test Payment',
        'customer_name': 'Test Customer',
        'customer_email': '<EMAIL>',
      };
      
      final response = await client.post(
        Uri.parse('$baseUrl/zoho/payments/create-session/'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: jsonEncode(testPayload),
      ).timeout(const Duration(seconds: 15));
      
      if (response.statusCode == 201) {
        final data = jsonDecode(response.body);
        if (data['success'] == true && data['data']?['payment_session_id'] != null) {
          return TestResult.pass('Session created successfully: ${data['data']['payment_session_id']}');
        } else {
          return TestResult.fail('Invalid response format: ${response.body}');
        }
      } else if (response.statusCode == 400) {
        return TestResult.warn('Validation error (expected for test data): ${response.body}');
      } else if (response.statusCode == 401 || response.statusCode == 403) {
        return TestResult.warn('Authentication required: ${response.statusCode}');
      } else if (response.statusCode == 404) {
        return TestResult.fail('Endpoint not found - check URL format');
      } else {
        return TestResult.fail('Unexpected response: ${response.statusCode} - ${response.body}');
      }
    });
    
    await _runTest('Validation Error Handling', () async {
      final invalidPayload = {
        'amount': -100.0, // Invalid amount
        'currency': 'USD', // Invalid currency
        'invoice_number': '', // Empty invoice
        'customer_id': '', // Empty customer
      };
      
      final response = await client.post(
        Uri.parse('$baseUrl/zoho/payments/create-session/'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: jsonEncode(invalidPayload),
      ).timeout(const Duration(seconds: 15));
      
      if (response.statusCode == 400) {
        final data = jsonDecode(response.body);
        if (data['success'] == false && data['message'] != null) {
          return TestResult.pass('Validation errors handled correctly');
        } else {
          return TestResult.fail('Invalid error response format');
        }
      } else {
        return TestResult.fail('Should return 400 for invalid data: ${response.statusCode}');
      }
    });
  }
  
  Future<void> _testStatusEndpoint() async {
    _printSection('📊 Payment Status Tests');
    
    await _runTest('Status Endpoint Format', () async {
      const testSessionId = 'test-session-123';
      
      final response = await client.get(
        Uri.parse('$baseUrl/zoho/payments/status/$testSessionId/'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      ).timeout(const Duration(seconds: 10));
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return TestResult.pass('Status endpoint working: ${data['data']?['status'] ?? 'unknown'}');
      } else if (response.statusCode == 404) {
        return TestResult.pass('Correctly returns 404 for non-existent session');
      } else if (response.statusCode == 401 || response.statusCode == 403) {
        return TestResult.warn('Authentication required: ${response.statusCode}');
      } else {
        return TestResult.fail('Unexpected response: ${response.statusCode}');
      }
    });
  }
  
  Future<void> _testEndpointSecurity() async {
    _printSection('🔒 Security Tests');
    
    await _runTest('Content-Type Validation', () async {
      final response = await client.post(
        Uri.parse('$baseUrl/zoho/payments/create-session/'),
        headers: {'Content-Type': 'text/plain'}, // Wrong content type
        body: '{"test": "data"}',
      ).timeout(const Duration(seconds: 10));
      
      if (response.statusCode == 400 || response.statusCode == 415) {
        return TestResult.pass('Correctly rejects wrong content type');
      } else {
        return TestResult.warn('Should validate content type: ${response.statusCode}');
      }
    });
    
    await _runTest('HTTP Method Validation', () async {
      final response = await client.get(
        Uri.parse('$baseUrl/zoho/payments/create-session/'),
        headers: {'Content-Type': 'application/json'},
      ).timeout(const Duration(seconds: 10));
      
      if (response.statusCode == 405) {
        return TestResult.pass('Correctly rejects wrong HTTP method');
      } else {
        return TestResult.warn('Should validate HTTP method: ${response.statusCode}');
      }
    });
  }
  
  Future<void> _testPerformance() async {
    _printSection('⚡ Performance Tests');
    
    await _runTest('Response Time', () async {
      final stopwatch = Stopwatch()..start();
      
      final response = await client.get(
        Uri.parse('$baseUrl/zoho/health/'),
        headers: {'Content-Type': 'application/json'},
      ).timeout(const Duration(seconds: 5));
      
      stopwatch.stop();
      final responseTime = stopwatch.elapsedMilliseconds;
      
      if (responseTime < 1000) {
        return TestResult.pass('Fast response: ${responseTime}ms');
      } else if (responseTime < 3000) {
        return TestResult.warn('Acceptable response: ${responseTime}ms');
      } else {
        return TestResult.fail('Slow response: ${responseTime}ms');
      }
    });
  }
  
  Future<void> _runTest(String name, Future<TestResult> Function() test) async {
    _testCount++;
    stdout.write('  $name... ');
    
    try {
      final result = await test();
      
      switch (result.status) {
        case TestStatus.pass:
          _passedTests++;
          print('✅ ${result.message}');
          break;
        case TestStatus.warn:
          print('⚠️  ${result.message}');
          break;
        case TestStatus.fail:
          _failedTests++;
          print('❌ ${result.message}');
          break;
      }
    } catch (e) {
      _failedTests++;
      print('💥 Error: $e');
    }
  }
  
  void _printSection(String title) {
    print('\n$title');
    print('-' * title.length);
  }
  
  void _printSummary() {
    print('\n' + '=' * 60);
    print('📊 Test Summary');
    print('=' * 60);
    print('Total Tests: $_testCount');
    print('Passed: $_passedTests ✅');
    print('Failed: $_failedTests ❌');
    print('Warnings: ${_testCount - _passedTests - _failedTests} ⚠️');
    
    final successRate = (_passedTests / _testCount * 100).toStringAsFixed(1);
    print('Success Rate: $successRate%');
    
    if (_failedTests == 0) {
      print('\n🎉 All critical tests passed!');
    } else {
      print('\n⚠️  Some tests failed. Please review the results above.');
    }
    
    print('\n⏰ Completed at: ${DateTime.now()}');
  }
}

enum TestStatus { pass, warn, fail }

class TestResult {
  final TestStatus status;
  final String message;
  
  TestResult._(this.status, this.message);
  
  factory TestResult.pass(String message) => TestResult._(TestStatus.pass, message);
  factory TestResult.warn(String message) => TestResult._(TestStatus.warn, message);
  factory TestResult.fail(String message) => TestResult._(TestStatus.fail, message);
}
