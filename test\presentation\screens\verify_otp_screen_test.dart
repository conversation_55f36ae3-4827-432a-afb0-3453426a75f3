import 'package:aquapartner/core/services/analytics_service.dart';
import 'package:aquapartner/core/services/navigation_service.dart';
import 'package:aquapartner/domain/repositories/auth_repository.dart';
import 'package:aquapartner/domain/repositories/customer_repository.dart';
import 'package:aquapartner/domain/repositories/user_repository.dart';
import 'package:aquapartner/domain/usecases/auth_usecases.dart';
import 'package:aquapartner/domain/usecases/customer_usercases.dart';
import 'package:aquapartner/domain/usecases/sync_usecases.dart';
import 'package:aquapartner/domain/usecases/user_usecases.dart';
import 'package:aquapartner/presentation/cubit/auth/auth_cubit.dart';
import 'package:aquapartner/presentation/cubit/auth/auth_state.dart';
import 'package:aquapartner/presentation/cubit/connectivity/connectivity_cubit.dart';
import 'package:aquapartner/presentation/cubit/connectivity/connectivity_state.dart';
import 'package:aquapartner/presentation/screens/verify_otp_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:mocktail/mocktail.dart';

import '../../helpers/test_helpers.dart';
import '../../mocks/mock_analytics.dart';

// Mock classes
class MockAuthCubit extends Mock implements AuthCubit {}

class MockConnectivityCubit extends Mock implements ConnectivityCubit {}

class MockAuthRepository extends Mock implements AuthRepository {}

class MockUserRepository extends Mock implements UserRepository {}

class MockCustomerRepository extends Mock implements CustomerRepository {}

class MockSendOtpUseCase extends Mock implements SendOtpUseCase {}

class MockVerifyOtpUseCase extends Mock implements VerifyOtpUseCase {}

class MockSignOutUseCase extends Mock implements SignOutUseCase {}

class MockGetUserUseCase extends Mock implements GetUserUseCase {}

class MockSaveUserUseCase extends Mock implements SaveUserUseCase {}

class MockUpdateUserUseCase extends Mock implements UpdateUserUseCase {}

class MockSyncUserUseCase extends Mock implements SyncUserUseCase {}

class MockGetSyncStatusUseCase extends Mock implements GetSyncStatusUseCase {}

class MockCheckAuthStatusUseCase extends Mock
    implements CheckAuthStatusUseCase {}

class MockGetCustomerByMobileNumber extends Mock
    implements GetCustomerByMobileNumber {}

class MockNavigationService extends Mock implements NavigationService {}

void main() {
  group('VerifyOtpScreen Tests', () {
    late MockAuthCubit mockAuthCubit;
    late MockConnectivityCubit mockConnectivityCubit;
    late MockAnalyticsService mockAnalyticsService;

    const testVerificationId = 'test_verification_id';
    const testPhoneNumber = '+919999999999';

    setUpAll(() {
      registerFallbackValue(AuthInitial());
      registerFallbackValue(
        ConnectivityState(status: ConnectionStatus.connected),
      );

      // Register services with GetIt for AnalyticsMixin and navigation
      if (!GetIt.instance.isRegistered<AnalyticsService>()) {
        GetIt.instance.registerLazySingleton<AnalyticsService>(
          () => MockAnalyticsService(),
        );
      }
      if (!GetIt.instance.isRegistered<NavigationService>()) {
        GetIt.instance.registerLazySingleton<NavigationService>(
          () => MockNavigationService(),
        );
      }
    });

    tearDownAll(() {
      // Clean up GetIt registration
      if (GetIt.instance.isRegistered<AnalyticsService>()) {
        GetIt.instance.unregister<AnalyticsService>();
      }
      if (GetIt.instance.isRegistered<NavigationService>()) {
        GetIt.instance.unregister<NavigationService>();
      }
    });

    setUp(() {
      mockAuthCubit = MockAuthCubit();
      mockConnectivityCubit = MockConnectivityCubit();
      mockAnalyticsService = MockAnalyticsService();

      // Set up default behavior
      when(() => mockAuthCubit.state).thenReturn(
        OtpSent(
          verificationId: testVerificationId,
          phoneNumber: testPhoneNumber,
        ),
      );
      when(() => mockAuthCubit.stream).thenAnswer(
        (_) => Stream.fromIterable([
          OtpSent(
            verificationId: testVerificationId,
            phoneNumber: testPhoneNumber,
          ),
        ]),
      );
      when(
        () => mockConnectivityCubit.state,
      ).thenReturn(ConnectivityState(status: ConnectionStatus.connected));
      when(() => mockConnectivityCubit.stream).thenAnswer(
        (_) => Stream.fromIterable([
          ConnectivityState(status: ConnectionStatus.connected),
        ]),
      );
    });

    Widget createVerifyOtpScreen() {
      return MaterialApp(
        home: MultiBlocProvider(
          providers: [
            BlocProvider<AuthCubit>.value(value: mockAuthCubit),
            BlocProvider<ConnectivityCubit>.value(value: mockConnectivityCubit),
          ],
          child: VerifyOtpScreen(phoneNumber: testPhoneNumber),
        ),
      );
    }

    group('Widget Rendering', () {
      testWidgets('should render verify OTP screen without crashing', (
        tester,
      ) async {
        await TestHelpers.pumpAndSettle(tester, createVerifyOtpScreen());

        // Verify the screen loads without crashing
        expect(find.byType(VerifyOtpScreen), findsOneWidget);
      });

      testWidgets('should handle loading state', (tester) async {
        // Test with loading state
        when(() => mockAuthCubit.state).thenReturn(AuthLoading());
        when(
          () => mockAuthCubit.stream,
        ).thenAnswer((_) => Stream.value(AuthLoading()));

        await tester.pumpWidget(createVerifyOtpScreen());
        await tester.pump();

        // Verify the screen handles loading state
        expect(find.byType(VerifyOtpScreen), findsOneWidget);
      });

      testWidgets('should handle error states', (tester) async {
        const errorMessage = 'Invalid OTP';
        when(
          () => mockAuthCubit.state,
        ).thenReturn(OtpVerificationError(message: errorMessage));
        when(() => mockAuthCubit.stream).thenAnswer(
          (_) => Stream.value(OtpVerificationError(message: errorMessage)),
        );

        await tester.pumpWidget(createVerifyOtpScreen());
        await tester.pump();

        // Verify the screen handles error state
        expect(find.byType(VerifyOtpScreen), findsOneWidget);
      });
    });

    group('Basic Functionality', () {
      testWidgets('should handle cubit method calls', (tester) async {
        when(() => mockAuthCubit.verifyOtp(any())).thenAnswer((_) async {});
        when(() => mockAuthCubit.sendOtp(any())).thenAnswer((_) async {});

        await TestHelpers.pumpAndSettle(tester, createVerifyOtpScreen());

        // Verify the screen is rendered and can handle method calls
        expect(find.byType(VerifyOtpScreen), findsOneWidget);

        // Test that the cubit methods can be called without errors
        mockAuthCubit.verifyOtp('123456');
        mockAuthCubit.sendOtp(testPhoneNumber);

        verify(() => mockAuthCubit.verifyOtp('123456')).called(1);
        verify(() => mockAuthCubit.sendOtp(testPhoneNumber)).called(1);
      });
    });

    group('State Management', () {
      testWidgets('should handle auth state changes', (tester) async {
        // Start with OtpSent state
        when(() => mockAuthCubit.state).thenReturn(
          OtpSent(
            verificationId: testVerificationId,
            phoneNumber: testPhoneNumber,
          ),
        );
        when(() => mockAuthCubit.stream).thenAnswer(
          (_) => Stream.value(
            OtpSent(
              verificationId: testVerificationId,
              phoneNumber: testPhoneNumber,
            ),
          ),
        );

        await tester.pumpWidget(createVerifyOtpScreen());
        await tester.pump();

        // Verify the screen handles state changes
        expect(find.byType(VerifyOtpScreen), findsOneWidget);
        expect(mockAuthCubit.state, isA<OtpSent>());
      });
    });

    group('Connectivity Handling', () {
      testWidgets('should handle connectivity changes', (tester) async {
        when(
          () => mockConnectivityCubit.state,
        ).thenReturn(ConnectivityState(status: ConnectionStatus.disconnected));

        await TestHelpers.pumpAndSettle(tester, createVerifyOtpScreen());

        // Verify the screen handles offline state
        expect(find.byType(VerifyOtpScreen), findsOneWidget);

        // Go back online
        when(
          () => mockConnectivityCubit.state,
        ).thenReturn(ConnectivityState(status: ConnectionStatus.connected));
        await tester.pump();

        // Verify the screen handles online state
        expect(find.byType(VerifyOtpScreen), findsOneWidget);
      });
    });

    group('Analytics Integration', () {
      testWidgets('should work with analytics service', (tester) async {
        await TestHelpers.pumpAndSettle(tester, createVerifyOtpScreen());

        // Verify the screen loads and analytics service is available
        expect(find.byType(VerifyOtpScreen), findsOneWidget);
        expect(mockAnalyticsService, isNotNull);
      });
    });

    group('Edge Cases', () {
      testWidgets('should handle different phone number formats', (
        tester,
      ) async {
        const longPhoneNumber = '+919999999999123456789';

        Widget createScreenWithLongNumber() {
          return MaterialApp(
            home: MultiBlocProvider(
              providers: [
                BlocProvider<AuthCubit>.value(value: mockAuthCubit),
                BlocProvider<ConnectivityCubit>.value(
                  value: mockConnectivityCubit,
                ),
              ],
              child: VerifyOtpScreen(phoneNumber: longPhoneNumber),
            ),
          );
        }

        await TestHelpers.pumpAndSettle(tester, createScreenWithLongNumber());

        // Should handle long phone numbers gracefully
        expect(find.byType(VerifyOtpScreen), findsOneWidget);
      });

      testWidgets('should handle cubit method calls without errors', (
        tester,
      ) async {
        when(() => mockAuthCubit.verifyOtp(any())).thenAnswer((_) async {});

        await TestHelpers.pumpAndSettle(tester, createVerifyOtpScreen());

        // Test that cubit methods can be called
        mockAuthCubit.verifyOtp('123456');
        verify(() => mockAuthCubit.verifyOtp('123456')).called(1);
      });
    });
  });
}
