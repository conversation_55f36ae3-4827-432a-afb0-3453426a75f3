import 'package:dartz/dartz.dart';

import '../entities/sales_order/sales_order.dart';
import '../../core/error/failures.dart';

abstract class SalesOrderRepository {
  /// Get sales orders for a customer
  ///
  /// Returns a list of sales orders if successful, or a failure if not
  Future<Either<Failure, List<SalesOrder>>> getSalesOrders(String customerId);

  /// Sync sales orders for a customer
  ///
  /// Returns a list of sales orders if successful, or a failure if not
  Future<Either<Failure, List<SalesOrder>>> syncSalesOrders(String customerId);

  /// Check if sales orders need to be synced
  ///
  /// Returns true if sync is needed, false otherwise
  Future<Either<Failure, bool>> checkIfSyncNeeded(String customerId);
}
