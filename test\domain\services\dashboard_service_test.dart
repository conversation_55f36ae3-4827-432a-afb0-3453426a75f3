import 'dart:async';
import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import 'package:aquapartner/core/error/failures.dart';
import 'package:aquapartner/core/utils/logger.dart';
import 'package:aquapartner/domain/entities/customer.dart';
import 'package:aquapartner/domain/entities/dashboard/dashboard_entity.dart';
import 'package:aquapartner/domain/entities/sync_status.dart';
import 'package:aquapartner/domain/entities/user.dart';
import 'package:aquapartner/domain/repositories/dashboard_repository.dart';
import 'package:aquapartner/domain/services/dashboard_service.dart';
import 'package:aquapartner/domain/usecases/customer_usercases.dart';
import 'package:aquapartner/domain/usecases/dashboard_usecases.dart';
import 'package:aquapartner/domain/usecases/user_usecases.dart';

// Mock classes
class MockGetUserUseCase extends Mock implements GetUserUseCase {}

class MockGetCustomerByMobileNumber extends Mock
    implements GetCustomerByMobileNumber {}

class MockGetDashboardUseCase extends Mock implements GetDashboardUseCase {}

class MockDashboardRepository extends Mock implements DashboardRepository {}

class MockAppLogger extends Mock implements AppLogger {}

void main() {
  setUpAll(() {
    // Register fallback values for mocktail
    registerFallbackValue(
      User(
        id: 0,
        phoneNumber: '',
        isVerified: false,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        needsSync: false,
      ),
    );
    registerFallbackValue('');
  });

  group('DashboardService', () {
    late DashboardService dashboardService;
    late MockGetUserUseCase mockGetUserUseCase;
    late MockGetCustomerByMobileNumber mockGetCustomerByMobileNumber;
    late MockGetDashboardUseCase mockGetDashboardUseCase;
    late MockDashboardRepository mockDashboardRepository;
    late MockAppLogger mockLogger;

    setUp(() {
      mockGetUserUseCase = MockGetUserUseCase();
      mockGetCustomerByMobileNumber = MockGetCustomerByMobileNumber();
      mockGetDashboardUseCase = MockGetDashboardUseCase();
      mockDashboardRepository = MockDashboardRepository();
      mockLogger = MockAppLogger();

      dashboardService = DashboardService(
        getUserUseCase: mockGetUserUseCase,
        getCustomerByMobileNumber: mockGetCustomerByMobileNumber,
        getDashboardUseCase: mockGetDashboardUseCase,
        dashboardRepository: mockDashboardRepository,
        logger: mockLogger,
      );
    });

    tearDown(() {
      dashboardService.dispose();
    });

    group('getDashboard', () {
      test(
        'should get dashboard successfully when all dependencies work',
        () async {
          // arrange
          final testUser = User(
            id: 1,
            phoneNumber: '+919999999999',
            isVerified: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
            needsSync: false,
          );
          final testCustomer = Customer(
            customerId: 'CUST001',
            customerName: 'Test Customer',
            email: '<EMAIL>',
            mobileNumber: '+919999999999',
            companyName: 'Test Company',
            gstNo: 'GST123456789',
            businessVertical: 'Technology',
            customerCode: 'TC001',
            billingAddress: 'Test Address',
          );
          final testDashboard = _createTestDashboard('CUST001');

          when(
            () => mockGetUserUseCase.call(),
          ).thenAnswer((_) async => Right(testUser));
          when(
            () => mockGetCustomerByMobileNumber.call(testUser.phoneNumber),
          ).thenAnswer((_) async => Right(testCustomer));
          when(
            () => mockGetDashboardUseCase.call(testCustomer.customerId),
          ).thenAnswer((_) async => Right(testDashboard));

          // act
          final result = await dashboardService.getDashboard();

          // assert
          expect(result, Right(testDashboard));
          verify(() => mockGetUserUseCase.call()).called(1);
          verify(
            () => mockGetCustomerByMobileNumber.call(testUser.phoneNumber),
          ).called(1);
          verify(
            () => mockGetDashboardUseCase.call(testCustomer.customerId),
          ).called(1);
        },
      );

      test('should return failure when getUserUseCase fails', () async {
        // arrange
        when(
          () => mockGetUserUseCase.call(),
        ).thenAnswer((_) async => Left(CacheFailure()));

        // act
        final result = await dashboardService.getDashboard();

        // assert
        expect(result, Left(CacheFailure()));
        verify(() => mockGetUserUseCase.call()).called(1);
        verifyNever(() => mockGetCustomerByMobileNumber.call(any()));
        verifyNever(() => mockGetDashboardUseCase.call(any()));
      });

      test('should handle unexpected exceptions', () async {
        // arrange
        when(
          () => mockGetUserUseCase.call(),
        ).thenThrow(Exception('Unexpected error'));

        // act
        final result = await dashboardService.getDashboard();

        // assert
        expect(result, Left(ServerFailure()));
        verify(
          () => mockLogger.e(
            'Unexpected error in DashboardService: Exception: Unexpected error',
          ),
        ).called(1);
      });
    });

    group('getCustomerId', () {
      test('should get customer ID successfully', () async {
        // arrange
        final testUser = User(
          id: 1,
          phoneNumber: '+919999999999',
          isVerified: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          needsSync: false,
        );
        final testCustomer = Customer(
          customerId: 'CUST001',
          customerName: 'Test Customer',
          email: '<EMAIL>',
          mobileNumber: '+919999999999',
          companyName: 'Test Company',
          gstNo: 'GST123456789',
          businessVertical: 'Technology',
          customerCode: 'TC001',
          billingAddress: 'Test Address',
        );

        when(
          () => mockGetUserUseCase.call(),
        ).thenAnswer((_) async => Right(testUser));
        when(
          () => mockGetCustomerByMobileNumber.call(testUser.phoneNumber),
        ).thenAnswer((_) async => Right(testCustomer));

        // act
        final result = await dashboardService.getCustomerId();

        // assert
        expect(result, Right(testCustomer.customerId));
        verify(
          () => mockLogger.i(
            'Customer retrieved successfully: ${testCustomer.companyName}',
          ),
        ).called(1);
        verify(
          () => mockLogger.i(
            'Retrieved and cached customer ID: ${testCustomer.customerId}',
          ),
        ).called(1);
      });

      test('should return cached customer ID on subsequent calls', () async {
        // arrange
        final testUser = User(
          id: 1,
          phoneNumber: '+919999999999',
          isVerified: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          needsSync: false,
        );
        final testCustomer = Customer(
          customerId: 'CUST001',
          customerName: 'Test Customer',
          email: '<EMAIL>',
          mobileNumber: '+919999999999',
          companyName: 'Test Company',
          gstNo: 'GST123456789',
          businessVertical: 'Technology',
          customerCode: 'TC001',
          billingAddress: 'Test Address',
        );

        when(
          () => mockGetUserUseCase.call(),
        ).thenAnswer((_) async => Right(testUser));
        when(
          () => mockGetCustomerByMobileNumber.call(testUser.phoneNumber),
        ).thenAnswer((_) async => Right(testCustomer));

        // act - first call
        final result1 = await dashboardService.getCustomerId();
        // act - second call (should use cache)
        final result2 = await dashboardService.getCustomerId();

        // assert
        expect(result1, Right(testCustomer.customerId));
        expect(result2, Right(testCustomer.customerId));
        verify(
          () => mockGetUserUseCase.call(),
        ).called(1); // Only called once due to caching
        verify(
          () => mockGetCustomerByMobileNumber.call(testUser.phoneNumber),
        ).called(1); // Only called once due to caching
        verify(
          () => mockLogger.i(
            'Using cached customer ID: ${testCustomer.customerId}',
          ),
        ).called(1);
      });

      test('should return failure when customer is null', () async {
        // arrange
        final testUser = User(
          id: 1,
          phoneNumber: '+919999999999',
          isVerified: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          needsSync: false,
        );

        when(
          () => mockGetUserUseCase.call(),
        ).thenAnswer((_) async => Right(testUser));
        when(
          () => mockGetCustomerByMobileNumber.call(testUser.phoneNumber),
        ).thenAnswer((_) async => const Right(null));

        // act
        final result = await dashboardService.getCustomerId();

        // assert
        expect(result, Left(ServerFailure()));
        verify(
          () => mockLogger.e(
            'Customer not found for mobile number: ${testUser.phoneNumber}',
          ),
        ).called(1);
      });

      test(
        'should return failure when getCustomerByMobileNumber fails',
        () async {
          // arrange
          final testUser = User(
            id: 1,
            phoneNumber: '+919999999999',
            isVerified: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
            needsSync: false,
          );

          when(
            () => mockGetUserUseCase.call(),
          ).thenAnswer((_) async => Right(testUser));
          when(
            () => mockGetCustomerByMobileNumber.call(testUser.phoneNumber),
          ).thenAnswer((_) async => Left(NetworkFailure()));

          // act
          final result = await dashboardService.getCustomerId();

          // assert
          expect(result, Left(NetworkFailure()));
          verify(() => mockLogger.e('Failed to get customer: ')).called(1);
        },
      );
    });

    group('Sync Operations', () {
      test('should check if syncing correctly', () {
        // arrange
        const customerId = 'CUST001';
        when(
          () => mockDashboardRepository.isSyncing(customerId),
        ).thenReturn(true);

        // act
        final result = dashboardService.isSyncing(customerId);

        // assert
        expect(result, true);
        verify(() => mockDashboardRepository.isSyncing(customerId)).called(1);
      });

      test('should get last sync time correctly', () async {
        // arrange
        const customerId = 'CUST001';
        final testTime = DateTime.now();
        when(
          () => mockDashboardRepository.getLastSyncTime(customerId),
        ).thenAnswer((_) async => testTime);

        // act
        final result = await dashboardService.getLastSyncTime(customerId);

        // assert
        expect(result, testTime);
        verify(
          () => mockDashboardRepository.getLastSyncTime(customerId),
        ).called(1);
      });

      test('should sync dashboard correctly', () async {
        // arrange
        const customerId = 'CUST001';
        when(
          () => mockDashboardRepository.syncDashboard(customerId),
        ).thenAnswer((_) async => const Right(true));

        // act
        final result = await dashboardService.syncDashboard(customerId);

        // assert
        expect(result, const Right(true));
        verify(
          () => mockDashboardRepository.syncDashboard(customerId),
        ).called(1);
      });
    });

    group('Cache Management', () {
      test('should clear cache correctly', () async {
        // arrange
        final testUser = User(
          id: 1,
          phoneNumber: '+919999999999',
          isVerified: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          needsSync: false,
        );
        final testCustomer = Customer(
          customerId: 'CUST001',
          customerName: 'Test Customer',
          email: '<EMAIL>',
          mobileNumber: '+919999999999',
          companyName: 'Test Company',
          gstNo: 'GST123456789',
          businessVertical: 'Technology',
          customerCode: 'TC001',
          billingAddress: 'Test Address',
        );

        when(
          () => mockGetUserUseCase.call(),
        ).thenAnswer((_) async => Right(testUser));
        when(
          () => mockGetCustomerByMobileNumber.call(testUser.phoneNumber),
        ).thenAnswer((_) async => Right(testCustomer));

        // act - populate cache
        await dashboardService.getCustomerId();
        // clear cache
        dashboardService.clearCache();
        // call again - should fetch fresh data
        await dashboardService.getCustomerId();

        // assert
        verify(
          () => mockGetUserUseCase.call(),
        ).called(2); // Called twice due to cache clear
        verify(
          () => mockGetCustomerByMobileNumber.call(testUser.phoneNumber),
        ).called(2); // Called twice due to cache clear
      });
    });

    group('Stream Operations', () {
      test('should provide sync status stream', () {
        // act
        final stream = dashboardService.syncStatusStream;

        // assert
        expect(stream, isA<Stream<SyncStatus>>());
      });

      test('should dispose correctly', () {
        // act
        dashboardService.dispose();

        // assert - no exception should be thrown
        expect(dashboardService, isNotNull);
      });
    });
  });
}

// Helper function to create test dashboard entity
DashboardEntity _createTestDashboard(String customerId) {
  return DashboardEntity(
    customerId: customerId,
    sales: const SalesEntity(yearlyData: {}),
    payments: const PaymentsEntity(yearlyData: {}),
    dues: const [],
    salesReturn: 0.0,
    categoryTypeSales: const [],
    liquidation: const LiquidationEntity(
      totalLiquidation: 0.0,
      liquidationByYear: [],
    ),
    myFarmers: const MyFarmersEntity(totalFarmers: [], potentialFarmers: 0),
  );
}
