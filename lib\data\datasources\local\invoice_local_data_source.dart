import 'package:shared_preferences/shared_preferences.dart';

import '../../../core/error/exceptions.dart';
import '../../../objectbox.g.dart';
import '../../models/invoices/invoice_model.dart';
import '../../models/invoices/objectbox_invoice_item_model.dart';
import '../../models/invoices/objectbox_invoice_model.dart';

abstract class InvoiceLocalDataSource {
  Future<List<InvoiceModel>> getInvoices(String customerId);
  Future<InvoiceModel> getInvoiceById(String invoiceId);
  Future<void> saveInvoices(List<InvoiceModel> invoices);
  Future<DateTime> getLastSyncTime();
  Future<void> saveLastSyncTime(DateTime time);
}

class InvoiceLocalDataSourceImpl implements InvoiceLocalDataSource {
  final Box<ObjectBoxInvoiceModel> invoiceBox;
  final SharedPreferences sharedPreferences;

  InvoiceLocalDataSourceImpl({
    required this.invoiceBox,
    required this.sharedPreferences,
  });

  @override
  Future<List<InvoiceModel>> getInvoices(String customerId) async {
    try {
      final query =
          invoiceBox
              .query(ObjectBoxInvoiceModel_.customerId.equals(customerId))
              .build();

      final invoices = query.find();
      query.close();

      // Return empty list instead of throwing exception
      if (invoices.isEmpty) {
        return [];
      }

      return invoices.map((invoice) {
        final entity = invoice.toEntity();
        return InvoiceModel(
          invoiceId: entity.invoiceId,
          addressId: entity.addressId,
          ageInDays: entity.ageInDays,
          ageTier: entity.ageTier,
          balance: entity.balance,
          customerId: entity.customerId,
          deliveryMode: entity.deliveryMode,
          deliveryStatus: entity.deliveryStatus,
          dueDate: entity.dueDate,
          invoiceDate: entity.invoiceDate,
          invoiceNumber: entity.invoiceNumber,
          invoiceStatus: entity.invoiceStatus,
          subTotal: entity.subTotal,
          total: entity.total,
          items: entity.items,
        );
      }).toList();
    } catch (e) {
      throw CacheException();
    }
  }

  @override
  Future<InvoiceModel> getInvoiceById(String invoiceId) async {
    try {
      final query =
          invoiceBox
              .query(ObjectBoxInvoiceModel_.invoiceId.equals(invoiceId))
              .build();

      final invoices = query.find();
      query.close();

      if (invoices.isEmpty) {
        throw CacheException();
      }

      final entity = invoices.first.toEntity();
      return InvoiceModel(
        invoiceId: entity.invoiceId,
        addressId: entity.addressId,
        ageInDays: entity.ageInDays,
        ageTier: entity.ageTier,
        balance: entity.balance,
        customerId: entity.customerId,
        deliveryMode: entity.deliveryMode,
        deliveryStatus: entity.deliveryStatus,
        dueDate: entity.dueDate,
        invoiceDate: entity.invoiceDate,
        invoiceNumber: entity.invoiceNumber,
        invoiceStatus: entity.invoiceStatus,
        subTotal: entity.subTotal,
        total: entity.total,
        items: entity.items,
      );
    } catch (e) {
      throw CacheException();
    }
  }

  @override
  Future<void> saveInvoices(List<InvoiceModel> invoices) async {
    // If the list is empty, just update the sync time and return
    if (invoices.isEmpty) {
      await saveLastSyncTime(DateTime.now());
      return;
    }

    for (var invoice in invoices) {
      final objectBoxInvoice = ObjectBoxInvoiceModel.fromEntity(invoice);

      // Remove existing items to avoid duplicates
      final query =
          invoiceBox
              .query(ObjectBoxInvoiceModel_.invoiceId.equals(invoice.invoiceId))
              .build();

      final existingInvoices = query.find();
      query.close();

      if (existingInvoices.isNotEmpty) {
        invoiceBox.remove(existingInvoices.first.id);
      }

      // Add invoice items
      for (var item in invoice.items) {
        final objectBoxItem = ObjectBoxInvoiceItemModel.fromEntity(item);
        objectBoxInvoice.items.add(objectBoxItem);
      }

      invoiceBox.put(objectBoxInvoice);
    }

    await saveLastSyncTime(DateTime.now());
  }

  @override
  Future<DateTime> getLastSyncTime() async {
    final timeStr = sharedPreferences.getString('INVOICE_LAST_SYNC_TIME');
    if (timeStr == null) {
      throw CacheException();
    }
    return DateTime.parse(timeStr);
  }

  @override
  Future<void> saveLastSyncTime(DateTime time) async {
    await sharedPreferences.setString(
      'INVOICE_LAST_SYNC_TIME',
      time.toIso8601String(),
    );
  }
}
