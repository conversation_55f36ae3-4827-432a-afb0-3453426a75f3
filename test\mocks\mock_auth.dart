import 'package:aquapartner/core/error/failures.dart';
import 'package:dartz/dartz.dart';
import 'package:mocktail/mocktail.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:aquapartner/domain/repositories/auth_repository.dart';

/// Mock Firebase Auth for testing
class MockFirebase<PERSON>uth extends Mock implements FirebaseAuth {}

/// Mock User for testing
class Mock<PERSON><PERSON> extends Mock implements User {
  final String _uid;
  final String? _phoneNumber;
  final String? _email;

  MockUser({required String uid, String? phoneNumber, String? email})
    : _uid = uid,
      _phoneNumber = phoneNumber,
      _email = email;

  @override
  String get uid => _uid;

  @override
  String? get phoneNumber => _phoneNumber;

  @override
  String? get email => _email;

  @override
  bool get emailVerified => true;

  @override
  bool get isAnonymous => false;
}

/// Mock UserCredential for testing
class MockUserCredential extends Mock implements UserCredential {
  final User? _user;

  MockUserCredential({User? user}) : _user = user;

  @override
  User? get user => _user;
}

/// Mock PhoneAuthCredential for testing
class MockPhoneAuthCredential extends Mock implements PhoneAuthCredential {
  final String _verificationId;
  final String _smsCode;

  MockPhoneAuthCredential({
    required String verificationId,
    required String smsCode,
  }) : _verificationId = verificationId,
       _smsCode = smsCode;

  @override
  String get verificationId => _verificationId;

  @override
  String get smsCode => _smsCode;
}

/// Mock AuthRepository for testing
class MockAuthRepository extends Mock implements AuthRepository {
  bool _isAuthenticated = false;
  User? _currentUser;
  String? _lastVerificationId;
  String? _lastPhoneNumber;
  Exception? _nextException;

  bool get isAuthenticated => _isAuthenticated;
  User? get currentUser => _currentUser;
  String? get lastVerificationId => _lastVerificationId;
  String? get lastPhoneNumber => _lastPhoneNumber;

  void setAuthenticated(bool authenticated, {User? user}) {
    _isAuthenticated = authenticated;
    _currentUser = user;
  }

  void setNextException(Exception? exception) {
    _nextException = exception;
  }

  void reset() {
    _isAuthenticated = false;
    _currentUser = null;
    _lastVerificationId = null;
    _lastPhoneNumber = null;
    _nextException = null;
  }

  @override
  Future<Either<Failure, String>> sendOtp(String phoneNumber) async {
    return Right('test_verification_id');
  }

  @override
  Future<Either<Failure, bool>> verifyOtp(
    String verificationId,
    String otp,
  ) async {
    if (_nextException != null) {
      throw _nextException!;
    }

    // Simulate different OTP scenarios
    if (otp == '000000') {
      throw FirebaseAuthException(
        code: 'invalid-verification-code',
        message: 'Invalid OTP',
      );
    }

    if (verificationId != _lastVerificationId) {
      throw FirebaseAuthException(
        code: 'invalid-verification-id',
        message: 'Invalid verification ID',
      );
    }

    // Successful verification
    final user = MockUser(
      uid: 'test_user_${DateTime.now().millisecondsSinceEpoch}',
      phoneNumber: _lastPhoneNumber,
    );

    _isAuthenticated = true;
    _currentUser = user;

    return const Right(true);
  }

  @override
  Future<void> signOut() async {
    if (_nextException != null) {
      throw _nextException!;
    }

    _isAuthenticated = false;
    _currentUser = null;
  }

  @override
  Future<User?> getCurrentUser() async {
    return _currentUser;
  }

  Stream<User?> authStateChanges() {
    return Stream.value(_currentUser);
  }
}

/// Test Auth Service for comprehensive testing scenarios
class TestAuthService {
  final MockAuthRepository _repository = MockAuthRepository();

  MockAuthRepository get repository => _repository;

  /// Simulate successful OTP send
  void simulateSuccessfulOtpSend() {
    _repository.reset();
  }

  /// Simulate OTP send failure
  void simulateOtpSendFailure(Exception exception) {
    _repository.setNextException(exception);
  }

  /// Simulate successful OTP verification
  void simulateSuccessfulOtpVerification() {
    _repository.reset();
    _repository.setAuthenticated(
      true,
      user: MockUser(uid: 'test_user_123', phoneNumber: '+************'),
    );
  }

  /// Simulate OTP verification failure
  void simulateOtpVerificationFailure() {
    _repository.setNextException(
      FirebaseAuthException(
        code: 'invalid-verification-code',
        message: 'Invalid OTP',
      ),
    );
  }

  /// Simulate network error
  void simulateNetworkError() {
    _repository.setNextException(
      FirebaseAuthException(
        code: 'network-request-failed',
        message: 'Network error',
      ),
    );
  }

  /// Simulate too many requests error
  void simulateTooManyRequestsError() {
    _repository.setNextException(
      FirebaseAuthException(
        code: 'too-many-requests',
        message: 'Too many requests',
      ),
    );
  }

  /// Reset all mocks
  void reset() {
    _repository.reset();
  }
}
