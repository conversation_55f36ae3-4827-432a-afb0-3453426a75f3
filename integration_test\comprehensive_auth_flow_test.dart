import 'package:aqua_ui/aqua_ui.dart';
import 'package:aquapartner/core/services/analytics_service.dart';
import 'package:aquapartner/core/services/navigation_service.dart';
import 'package:aquapartner/core/utils/logger.dart';
import 'package:aquapartner/domain/entities/customer.dart';
import 'package:aquapartner/domain/entities/user.dart' as domain;
import 'package:aquapartner/domain/usecases/auth_usecases.dart';
import 'package:aquapartner/domain/usecases/customer_usercases.dart';
import 'package:aquapartner/domain/usecases/sync_usecases.dart';
import 'package:aquapartner/domain/usecases/user_usecases.dart';
import 'package:aquapartner/presentation/cubit/auth/auth_cubit.dart';
import 'package:aquapartner/presentation/cubit/auth/auth_state.dart';
import 'package:aquapartner/presentation/cubit/connectivity/connectivity_cubit.dart';
import 'package:aquapartner/presentation/cubit/connectivity/connectivity_state.dart';
import 'package:aquapartner/presentation/screens/login_screen.dart';
import 'package:aquapartner/presentation/screens/verify_otp_screen.dart';
import 'package:aquapartner/core/error/failures.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dartz/dartz.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:get_it/get_it.dart';

// Mock classes
class MockSendOtpUseCase extends Mock implements SendOtpUseCase {}

class MockVerifyOtpUseCase extends Mock implements VerifyOtpUseCase {}

class MockGetUserUseCase extends Mock implements GetUserUseCase {}

class MockSaveUserUseCase extends Mock implements SaveUserUseCase {}

class MockSignOutUseCase extends Mock implements SignOutUseCase {}

class MockUpdateUserUseCase extends Mock implements UpdateUserUseCase {}

class MockSyncUserUseCase extends Mock implements SyncUserUseCase {}

class MockGetSyncStatusUseCase extends Mock implements GetSyncStatusUseCase {}

class MockCheckAuthStatusUseCase extends Mock
    implements CheckAuthStatusUseCase {}

class MockGetCustomerByMobileNumber extends Mock
    implements GetCustomerByMobileNumber {}

class MockAppLogger extends Mock implements AppLogger {}

class MockNavigationService extends Mock implements NavigationService {
  late GlobalKey<NavigatorState> _navigatorKey;

  MockNavigationService() {
    _navigatorKey = GlobalKey<NavigatorState>();
  }

  @override
  GlobalKey<NavigatorState> get navigatorKey => _navigatorKey;

  @override
  Future<dynamic> pushNamed(String routeName, {Object? arguments}) async {
    print('MockNavigationService.pushNamed called with route: $routeName');
    return Future.value();
  }

  @override
  Future<dynamic> pushReplacement(String routeName, {Object? arguments}) async {
    print(
      'MockNavigationService.pushReplacement called with route: $routeName',
    );
    return Future.value();
  }

  @override
  Future<dynamic> navigateTo(String routeName, {Object? arguments}) async {
    print('MockNavigationService.navigateTo called with route: $routeName');
    return Future.value();
  }

  @override
  void goBack({dynamic result}) {
    print('MockNavigationService.goBack called');
  }

  @override
  void pop() {
    print('MockNavigationService.pop called');
  }
}

class MockConnectivity extends Mock implements Connectivity {
  @override
  Stream<List<ConnectivityResult>> get onConnectivityChanged =>
      Stream.value([ConnectivityResult.wifi]);
}

class MockAnalyticsService extends Mock implements AnalyticsService {
  @override
  Future<void> logScreenView({
    required String screenName,
    String? screenClass,
  }) async {}

  @override
  Future<void> logEvent({
    required String name,
    required Map<String, Object> parameters,
  }) async {}

  @override
  Future<void> logUserInteraction({
    required String screenName,
    required String actionName,
    required String elementType,
    String? elementId,
    Map<String, Object> additionalParams = const {},
  }) async {}

  @override
  Future<void> logScreenDuration({
    required String screenName,
    required int durationMs,
    String? screenClass,
  }) async {}

  @override
  Future<void> logUserLogin(String userId, String userType) async {}
}

void setupTestDependencies() {
  final sl = GetIt.instance;
  if (sl.isRegistered<AnalyticsService>()) {
    sl.reset();
  }

  // Register all required services
  sl.registerLazySingleton<AnalyticsService>(() => MockAnalyticsService());
  sl.registerLazySingleton<AppLogger>(() => MockAppLogger());
  sl.registerLazySingleton<NavigationService>(() => MockNavigationService());

  print('Test dependencies registered successfully');
}

void tearDownTestDependencies() {
  GetIt.instance.reset();
}

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Comprehensive Authentication Flow Integration Tests', () {
    late MockSendOtpUseCase mockSendOtpUseCase;
    late MockVerifyOtpUseCase mockVerifyOtpUseCase;
    late MockGetUserUseCase mockGetUserUseCase;
    late MockSaveUserUseCase mockSaveUserUseCase;
    late MockSyncUserUseCase mockSyncUserUseCase;
    late MockGetSyncStatusUseCase mockGetSyncStatusUseCase;
    late MockSignOutUseCase mockSignOutUseCase;
    late MockUpdateUserUseCase mockUpdateUserUseCase;
    late MockCheckAuthStatusUseCase mockCheckAuthStatusUseCase;
    late MockGetCustomerByMobileNumber mockGetCustomerByMobileNumber;
    late MockAnalyticsService mockAnalyticsService;
    late MockConnectivity mockConnectivity;

    late AuthCubit authCubit;
    late ConnectivityCubit connectivityCubit;

    // Test constants as specified in requirements
    const testPhoneNumber = '9999999999';
    const testVerificationId = 'test_verification_id';
    const validTestOtp = '123456';
    const invalidTestOtp = '123123';

    setUpAll(() {
      registerFallbackValue(AuthInitial());
      registerFallbackValue(
        ConnectivityState(status: ConnectionStatus.connected),
      );
      registerFallbackValue(
        domain.User(
          id: 0,
          phoneNumber: '',
          isVerified: false,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          needsSync: false,
        ),
      );
      setupTestDependencies();
    });

    tearDownAll(() {
      tearDownTestDependencies();
    });

    setUp(() {
      mockSendOtpUseCase = MockSendOtpUseCase();
      mockVerifyOtpUseCase = MockVerifyOtpUseCase();
      mockGetUserUseCase = MockGetUserUseCase();
      mockSaveUserUseCase = MockSaveUserUseCase();
      mockSyncUserUseCase = MockSyncUserUseCase();
      mockGetSyncStatusUseCase = MockGetSyncStatusUseCase();
      mockSignOutUseCase = MockSignOutUseCase();
      mockUpdateUserUseCase = MockUpdateUserUseCase();
      mockCheckAuthStatusUseCase = MockCheckAuthStatusUseCase();
      mockGetCustomerByMobileNumber = MockGetCustomerByMobileNumber();
      mockAnalyticsService = MockAnalyticsService();
      mockConnectivity = MockConnectivity();
    });

    // Helper method to create AuthCubit with mocked dependencies
    AuthCubit createAuthCubit() {
      return AuthCubit(
        sendOtpUseCase: mockSendOtpUseCase,
        verifyOtpUseCase: mockVerifyOtpUseCase,
        signOutUseCase: mockSignOutUseCase,
        getUserUseCase: mockGetUserUseCase,
        saveUserUseCase: mockSaveUserUseCase,
        updateUserUseCase: mockUpdateUserUseCase,
        syncUserUseCase: mockSyncUserUseCase,
        getSyncStatusUseCase: mockGetSyncStatusUseCase,
        logger: MockAppLogger(),
        checkAuthStatusUseCase: mockCheckAuthStatusUseCase,
        getCustomerByMobileNumber: mockGetCustomerByMobileNumber,
        analyticsService: mockAnalyticsService,
      );
    }

    // Helper method to create ConnectivityCubit
    ConnectivityCubit createConnectivityCubit() {
      return ConnectivityCubit(
        connectivity: mockConnectivity,
        logger: MockAppLogger(),
      );
    }

    // Helper method to setup successful authentication mocks
    void setupSuccessfulAuthMocks() {
      when(
        () => mockSendOtpUseCase.call(any()),
      ).thenAnswer((_) async => const Right(testVerificationId));

      when(
        () => mockGetSyncStatusUseCase.call(),
      ).thenAnswer((_) => Stream.value(false));

      when(() => mockGetUserUseCase.call()).thenAnswer(
        (_) async => Right(
          domain.User(
            id: 1,
            phoneNumber: testPhoneNumber,
            isVerified: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
            needsSync: false,
          ),
        ),
      );

      when(
        () => mockSaveUserUseCase.call(any()),
      ).thenAnswer((_) async => const Right(true));

      when(
        () => mockGetCustomerByMobileNumber.call(testPhoneNumber),
      ).thenAnswer(
        (_) async => Right(
          Customer(
            customerId: 'test_customer_id',
            customerName: 'Test Customer',
            email: '<EMAIL>',
            mobileNumber: '+91$testPhoneNumber',
            companyName: 'Test Company',
            gstNo: 'GST123456789',
            businessVertical: 'Technology',
            customerCode: 'TC001',
            billingAddress: 'Test Address',
          ),
        ),
      );
    }

    // Helper method to create test widget
    Widget createTestWidget(
      AuthCubit authCubit,
      ConnectivityCubit connectivityCubit,
      Widget child,
    ) {
      return MultiBlocProvider(
        providers: [
          BlocProvider<AuthCubit>.value(value: authCubit),
          BlocProvider<ConnectivityCubit>.value(value: connectivityCubit),
        ],
        child: MaterialApp(
          home: child,
          onGenerateRoute: (settings) {
            switch (settings.name) {
              case '/verify-otp':
                final args = settings.arguments as Map<String, dynamic>;
                return MaterialPageRoute(
                  builder:
                      (_) => VerifyOtpScreen(phoneNumber: args['phoneNumber']),
                );
              default:
                return null;
            }
          },
        ),
      );
    }

    testWidgets('Complete Authentication Flow - Valid OTP Success Path', (
      tester,
    ) async {
      // Test Description:
      // This test verifies the complete authentication flow with valid OTP (123456)
      // Expected flow: AuthInitial -> AuthLoading -> OtpSent -> AuthLoading -> AuthSuccess

      print('=== Starting Valid OTP Authentication Flow Test ===');

      // Setup successful authentication mocks
      setupSuccessfulAuthMocks();
      when(
        () => mockVerifyOtpUseCase.call(testVerificationId, validTestOtp),
      ).thenAnswer((_) async => const Right(true));

      // Create cubits
      authCubit = createAuthCubit();
      connectivityCubit = createConnectivityCubit();

      // Verify initial state
      expect(
        authCubit.state,
        isA<AuthInitial>(),
        reason: 'AuthCubit should start in AuthInitial state',
      );

      // Start with login screen
      await tester.pumpWidget(
        createTestWidget(authCubit, connectivityCubit, LoginScreen()),
      );
      await tester.pumpAndSettle();

      print('Login screen loaded successfully');

      // Step 1: Enter phone number and send OTP
      print('Step 1: Entering phone number and sending OTP');
      await tester.enterText(find.byType(TextFormField), testPhoneNumber);
      await tester.pump();

      final sendOtpButton = find.byWidgetPredicate((widget) {
        return widget is AquaButton && widget.title == 'Send OTP';
      });
      expect(
        sendOtpButton,
        findsOneWidget,
        reason: 'Send OTP button should be present',
      );

      await tester.tap(sendOtpButton);
      await tester.pumpAndSettle();

      // Verify OTP sent state
      expect(
        authCubit.state,
        isA<OtpSent>(),
        reason: 'State should transition to OtpSent after sending OTP',
      );

      final otpSentState = authCubit.state as OtpSent;
      expect(
        otpSentState.verificationId,
        equals(testVerificationId),
        reason: 'Verification ID should match the test verification ID',
      );
      expect(
        otpSentState.phoneNumber,
        equals(testPhoneNumber),
        reason: 'Phone number should match the test phone number',
      );

      print(
        'OTP sent successfully, verification ID: ${otpSentState.verificationId}',
      );

      // Step 2: Navigate to OTP verification screen
      print('Step 2: Navigating to OTP verification screen');
      await tester.pumpWidget(
        createTestWidget(
          authCubit,
          connectivityCubit,
          VerifyOtpScreen(phoneNumber: testPhoneNumber),
        ),
      );
      await tester.pumpAndSettle();

      // Verify OTP screen is displayed
      expect(
        find.byType(VerifyOtpScreen),
        findsOneWidget,
        reason: 'VerifyOtpScreen should be displayed',
      );

      final verifyOtpButton = find.byWidgetPredicate((widget) {
        return widget is AquaButton && widget.title == 'Verify OTP';
      });
      expect(
        verifyOtpButton,
        findsOneWidget,
        reason: 'Verify OTP button should be present',
      );

      // Step 3: Enter valid OTP and verify
      print('Step 3: Entering valid OTP ($validTestOtp) and verifying');
      final otpField = find.byType(TextFormField);
      await tester.tap(otpField);
      await tester.enterText(otpField, validTestOtp);
      await tester.pump();

      await tester.tap(verifyOtpButton);
      await tester.pump(Duration(milliseconds: 100));

      // Step 4: Verify authentication success
      print('Step 4: Verifying authentication success');

      // Wait for authentication to complete
      await tester.pump(Duration(milliseconds: 200));

      // Verify AuthSuccess state is reached
      expect(
        authCubit.state,
        isA<AuthSuccess>(),
        reason:
            'Authentication should succeed with valid OTP and reach AuthSuccess state',
      );

      final authSuccessState = authCubit.state as AuthSuccess;
      expect(
        authSuccessState.user.phoneNumber,
        equals(testPhoneNumber),
        reason: 'Authenticated user should have the correct phone number',
      );
      expect(
        authSuccessState.user.isVerified,
        isTrue,
        reason: 'Authenticated user should be verified',
      );

      print(
        'Authentication successful! User verified: ${authSuccessState.user.isVerified}',
      );

      // Handle expected navigation error in test environment
      bool navigationErrorOccurred = false;
      try {
        await tester.pumpAndSettle();
      } catch (e) {
        if (e.toString().contains('Null check operator used on a null value')) {
          navigationErrorOccurred = true;
          print(
            'Expected navigation error occurred in test environment (not an app bug)',
          );
          await tester.pump(Duration(milliseconds: 100));
        } else {
          rethrow;
        }
      }

      // Verify all use cases were called correctly
      verify(() => mockSendOtpUseCase.call('+91$testPhoneNumber')).called(1);
      verify(
        () => mockVerifyOtpUseCase.call(testVerificationId, validTestOtp),
      ).called(1);
      verify(
        () => mockGetCustomerByMobileNumber.call(testPhoneNumber),
      ).called(1);
      verify(() => mockSaveUserUseCase.call(any())).called(1);

      print(
        '=== Valid OTP Authentication Flow Test Completed Successfully ===',
      );

      // Test Summary:
      // ✅ Phone number entry and OTP sending works correctly
      // ✅ State transitions properly: AuthInitial -> AuthLoading -> OtpSent
      // ✅ OTP verification with valid OTP (123456) succeeds
      // ✅ State transitions to AuthSuccess after successful verification
      // ✅ User is properly authenticated and verified
      // ✅ All use cases called with correct parameters
      // ✅ Navigation error handled as expected in test environment
    });

    testWidgets('Complete Authentication Flow - Invalid OTP Error Path', (
      tester,
    ) async {
      // Test Description:
      // This test verifies the complete authentication flow with invalid OTP (123123)
      // Expected flow: AuthInitial -> AuthLoading -> OtpSent -> AuthLoading -> OtpVerificationError

      print('=== Starting Invalid OTP Authentication Flow Test ===');

      // Setup mocks for OTP sending (successful) and verification (failure)
      setupSuccessfulAuthMocks();
      when(
        () => mockVerifyOtpUseCase.call(testVerificationId, invalidTestOtp),
      ).thenAnswer((_) async => Left(ValidationFailure('Invalid OTP')));

      // Create cubits
      authCubit = createAuthCubit();
      connectivityCubit = createConnectivityCubit();

      // Verify initial state
      expect(
        authCubit.state,
        isA<AuthInitial>(),
        reason: 'AuthCubit should start in AuthInitial state',
      );

      // Start with login screen
      await tester.pumpWidget(
        createTestWidget(authCubit, connectivityCubit, LoginScreen()),
      );
      await tester.pumpAndSettle();

      print('Login screen loaded successfully');

      // Step 1: Enter phone number and send OTP
      print('Step 1: Entering phone number and sending OTP');
      await tester.enterText(find.byType(TextFormField), testPhoneNumber);
      await tester.pump();

      final sendOtpButton = find.byWidgetPredicate((widget) {
        return widget is AquaButton && widget.title == 'Send OTP';
      });
      expect(
        sendOtpButton,
        findsOneWidget,
        reason: 'Send OTP button should be present',
      );

      await tester.tap(sendOtpButton);
      await tester.pumpAndSettle();

      // Verify OTP sent state
      expect(
        authCubit.state,
        isA<OtpSent>(),
        reason: 'State should transition to OtpSent after sending OTP',
      );

      final otpSentState = authCubit.state as OtpSent;
      expect(
        otpSentState.verificationId,
        equals(testVerificationId),
        reason: 'Verification ID should match the test verification ID',
      );
      expect(
        otpSentState.phoneNumber,
        equals(testPhoneNumber),
        reason: 'Phone number should match the test phone number',
      );

      print(
        'OTP sent successfully, verification ID: ${otpSentState.verificationId}',
      );

      // Step 2: Navigate to OTP verification screen
      print('Step 2: Navigating to OTP verification screen');
      await tester.pumpWidget(
        createTestWidget(
          authCubit,
          connectivityCubit,
          VerifyOtpScreen(phoneNumber: testPhoneNumber),
        ),
      );
      await tester.pumpAndSettle();

      // Verify OTP screen is displayed
      expect(
        find.byType(VerifyOtpScreen),
        findsOneWidget,
        reason: 'VerifyOtpScreen should be displayed',
      );

      final verifyOtpButton = find.byWidgetPredicate((widget) {
        return widget is AquaButton && widget.title == 'Verify OTP';
      });
      expect(
        verifyOtpButton,
        findsOneWidget,
        reason: 'Verify OTP button should be present',
      );

      // Step 3: Enter invalid OTP and verify
      print('Step 3: Entering invalid OTP ($invalidTestOtp) and verifying');
      final otpField = find.byType(TextFormField);
      await tester.tap(otpField);
      await tester.enterText(otpField, invalidTestOtp);
      await tester.pump();

      await tester.tap(verifyOtpButton);
      await tester.pump(Duration(milliseconds: 100));

      // Step 4: Verify authentication failure
      print('Step 4: Verifying authentication failure');

      // Wait for verification to complete
      await tester.pump(Duration(milliseconds: 200));

      // Verify OtpVerificationError state is reached
      expect(
        authCubit.state,
        isA<OtpVerificationError>(),
        reason:
            'Authentication should fail with invalid OTP and reach OtpVerificationError state',
      );

      final otpErrorState = authCubit.state as OtpVerificationError;
      expect(
        otpErrorState.message,
        contains('Invalid OTP'),
        reason: 'Error message should indicate invalid OTP',
      );

      print(
        'Authentication failed as expected with error: ${otpErrorState.message}',
      );

      // Verify use cases were called correctly
      verify(() => mockSendOtpUseCase.call('+91$testPhoneNumber')).called(1);
      verify(
        () => mockVerifyOtpUseCase.call(testVerificationId, invalidTestOtp),
      ).called(1);

      // Customer lookup and user saving should NOT be called for failed authentication
      verifyNever(() => mockGetCustomerByMobileNumber.call(any()));
      verifyNever(() => mockSaveUserUseCase.call(any()));

      print(
        '=== Invalid OTP Authentication Flow Test Completed Successfully ===',
      );

      // Test Summary:
      // ✅ Phone number entry and OTP sending works correctly
      // ✅ State transitions properly: AuthInitial -> AuthLoading -> OtpSent
      // ✅ OTP verification with invalid OTP (123123) fails as expected
      // ✅ State transitions to OtpVerificationError after failed verification
      // ✅ Error message is properly displayed
      // ✅ Customer lookup and user saving are not called for failed authentication
      // ✅ Proper error handling for invalid OTP scenarios
    });

    testWidgets('Complete Authentication Flow - State Progression Validation', (
      tester,
    ) async {
      // Test Description:
      // This test validates the complete state progression through the authentication flow
      // and ensures proper state transitions at each step

      print('=== Starting State Progression Validation Test ===');

      // Setup successful authentication mocks
      setupSuccessfulAuthMocks();
      when(
        () => mockVerifyOtpUseCase.call(testVerificationId, validTestOtp),
      ).thenAnswer((_) async => const Right(true));

      // Create cubits
      authCubit = createAuthCubit();
      connectivityCubit = createConnectivityCubit();

      // Track state changes
      List<AuthState> stateHistory = [];
      authCubit.stream.listen((state) {
        stateHistory.add(state);
        print('State changed to: ${state.runtimeType}');
      });

      // Verify initial state
      expect(
        authCubit.state,
        isA<AuthInitial>(),
        reason: 'AuthCubit should start in AuthInitial state',
      );

      // Start with login screen
      await tester.pumpWidget(
        createTestWidget(authCubit, connectivityCubit, LoginScreen()),
      );
      await tester.pumpAndSettle();

      // Step 1: Send OTP and verify state progression
      print('Step 1: Testing OTP sending state progression');
      await tester.enterText(find.byType(TextFormField), testPhoneNumber);
      await tester.pump();

      final sendOtpButton = find.byWidgetPredicate((widget) {
        return widget is AquaButton && widget.title == 'Send OTP';
      });

      await tester.tap(sendOtpButton);
      await tester.pump(Duration(milliseconds: 50)); // Allow state to change

      // Verify AuthLoading state appears
      expect(
        authCubit.state,
        isA<AuthLoading>(),
        reason: 'State should be AuthLoading immediately after sending OTP',
      );

      await tester.pumpAndSettle();

      // Verify final OtpSent state
      expect(
        authCubit.state,
        isA<OtpSent>(),
        reason: 'State should transition to OtpSent after OTP is sent',
      );

      // Step 2: Navigate to OTP screen and verify OTP
      print('Step 2: Testing OTP verification state progression');
      await tester.pumpWidget(
        createTestWidget(
          authCubit,
          connectivityCubit,
          VerifyOtpScreen(phoneNumber: testPhoneNumber),
        ),
      );
      await tester.pumpAndSettle();

      final verifyOtpButton = find.byWidgetPredicate((widget) {
        return widget is AquaButton && widget.title == 'Verify OTP';
      });

      final otpField = find.byType(TextFormField);
      await tester.tap(otpField);
      await tester.enterText(otpField, validTestOtp);
      await tester.pump();

      await tester.tap(verifyOtpButton);
      await tester.pump(Duration(milliseconds: 50)); // Allow state to change

      // Verify AuthLoading state appears during verification
      expect(
        authCubit.state,
        isA<AuthLoading>(),
        reason:
            'State should be AuthLoading immediately after starting OTP verification',
      );

      await tester.pump(
        Duration(milliseconds: 200),
      ); // Allow verification to complete

      // Verify final AuthSuccess state
      expect(
        authCubit.state,
        isA<AuthSuccess>(),
        reason:
            'State should transition to AuthSuccess after successful verification',
      );

      // Step 3: Validate complete state progression
      print('Step 3: Validating complete state progression');

      // Expected state progression:
      // 1. AuthLoading (during OTP send)
      // 2. OtpSent (after OTP sent)
      // 3. AuthLoading (during OTP verification)
      // 4. AuthSuccess (after successful verification)

      expect(
        stateHistory.length,
        greaterThanOrEqualTo(4),
        reason: 'Should have at least 4 state transitions',
      );

      expect(
        stateHistory[0],
        isA<AuthLoading>(),
        reason: 'First state change should be AuthLoading (OTP send)',
      );
      expect(
        stateHistory[1],
        isA<OtpSent>(),
        reason: 'Second state change should be OtpSent',
      );
      expect(
        stateHistory[2],
        isA<AuthLoading>(),
        reason: 'Third state change should be AuthLoading (OTP verify)',
      );
      expect(
        stateHistory[3],
        isA<AuthSuccess>(),
        reason: 'Fourth state change should be AuthSuccess',
      );

      print('State progression validated successfully:');
      for (int i = 0; i < stateHistory.length; i++) {
        print('  ${i + 1}. ${stateHistory[i].runtimeType}');
      }

      // Handle expected navigation error
      try {
        await tester.pumpAndSettle();
      } catch (e) {
        if (e.toString().contains('Null check operator used on a null value')) {
          print('Expected navigation error handled in test environment');
        } else {
          rethrow;
        }
      }

      print('=== State Progression Validation Test Completed Successfully ===');

      // Test Summary:
      // ✅ Complete state progression validated
      // ✅ AuthLoading states appear during async operations
      // ✅ OtpSent state contains correct verification data
      // ✅ AuthSuccess state reached after successful authentication
      // ✅ State transitions occur in correct order
      // ✅ All intermediate states are properly handled
    });
  });
}
