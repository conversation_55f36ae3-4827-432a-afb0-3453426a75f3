import 'dart:async';

import 'package:mongo_dart/mongo_dart.dart';

import '../../../core/error/exceptions.dart';
import '../../../core/network/mongodb_connection_manager.dart';
import '../../../core/utils/logger.dart';
import '../../models/price_list_model.dart';

abstract class PriceListRemoteDataSource {
  /// Gets all price lists from the remote server
  Future<List<PriceListModel>> getPriceLists();

  /// Gets a specific price list by ID from the remote server
  Future<PriceListModel?> getPriceListById(String id);
}

class PriceListRemoteDataSourceImpl implements PriceListRemoteDataSource {
  final MongoDbConnectionManager connectionManager;
  final AppLogger logger;

  // Add a timeout for operations
  final Duration operationTimeout = const Duration(seconds: 15);

  PriceListRemoteDataSourceImpl({
    required this.connectionManager,
    required this.logger,
  });

  @override
  Future<List<PriceListModel>> getPriceLists() async {
    try {
      final mongoService = await connectionManager.getConnection();

      // Get the collection with automatic reconnection
      final collection = await mongoService.priceListCollection;

      // Get all price lists with timeout
      final cursor = collection.find();
      final result = await cursor.toList().timeout(
        operationTimeout,
        onTimeout: () {
          throw TimeoutException("MongoDB operation timed out");
        },
      );

      if (result.isEmpty) {
        logger.w("No price lists found in MongoDB");
        return [];
      }

      final priceLists =
          result.map((json) => PriceListModel.fromJson(json)).toList();

      logger.i("Found ${priceLists.length} price lists in MongoDB");
      return priceLists;
    } on TimeoutException catch (e) {
      logger.e("MongoDB operation timed out", e);
      throw TimeoutException("MongoDB operation timed out");
    } on ServerException {
      rethrow;
    } catch (e) {
      logger.e("Error getting price lists from MongoDB: $e", e);
      throw ServerException();
    }
  }

  @override
  Future<PriceListModel?> getPriceListById(String id) async {
    try {
      logger.i("Getting price list by ID: $id from MongoDB");

      final mongoService = await connectionManager.getConnection();

      // Get the collection with automatic reconnection
      final collection = await mongoService.priceListCollection;

      // Try to convert the string ID to ObjectId if it's in the correct format
      dynamic queryId = id;
      try {
        // Check if the ID is a valid 24-character hex string (ObjectId format)
        if (id.length == 24 && RegExp(r'^[0-9a-fA-F]{24}$').hasMatch(id)) {
          queryId = ObjectId.parse(id);
          logger.i("Converted string ID to ObjectId: $id");
        }
      } catch (e) {
        // If conversion fails, use the string ID as is
        logger.w("Could not convert ID to ObjectId, using string ID: $id");
      }

      // Find price list by ID with timeout
      final result = await collection
          .findOne(where.eq('_id', queryId))
          .timeout(
            operationTimeout,
            onTimeout: () {
              throw TimeoutException("MongoDB operation timed out");
            },
          );

      if (result == null) {
        logger.w("No price list found with ID: $id in MongoDB");
        return null;
      }

      logger.i("Found price list with ID: $id in MongoDB");
      return PriceListModel.fromJson(result);
    } on TimeoutException catch (e) {
      logger.e("MongoDB operation timed out", e);
      throw TimeoutException("Operation timed out. Please try again later.");
    } on ServerException {
      rethrow;
    } catch (e) {
      logger.e("Error getting price list by ID from MongoDB: $e", e);
      throw ServerException();
    }
  }
}
