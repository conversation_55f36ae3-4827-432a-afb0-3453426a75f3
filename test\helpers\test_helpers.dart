import 'package:aquapartner/core/services/analytics_service.dart';
import 'package:aquapartner/domain/entities/customer.dart';
import 'package:aquapartner/domain/entities/account_statement/account_statement.dart';
import 'package:aquapartner/domain/entities/account_statement/account_statement_entity.dart';
import 'package:aquapartner/domain/entities/dues/dues.dart';
import 'package:aquapartner/domain/entities/smr_report.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:get_it/get_it.dart';

import '../mocks/mock_analytics.dart';

/// Test helpers and utilities for AquaPartner tests
class TestHelpers {
  /// Creates a test widget with MaterialApp wrapper
  static Widget createTestWidget(Widget child) {
    return MaterialApp(home: Scaffold(body: child));
  }

  /// Creates a test widget with full app context
  static Widget createTestApp({
    required Widget home,
    Map<String, WidgetBuilder>? routes,
  }) {
    return MaterialApp(title: 'Test App', home: home, routes: routes ?? {});
  }

  /// Pumps widget and settles animations
  static Future<void> pumpAndSettle(WidgetTester tester, Widget widget) async {
    await tester.pumpWidget(widget);
    await tester.pumpAndSettle();
  }

  /// Sets up SharedPreferences mock
  static void setupSharedPreferences() {
    SharedPreferences.setMockInitialValues({});
  }

  /// Sets up GetIt services for testing
  static void setupGetItServices() {
    final getIt = GetIt.instance;

    // Reset GetIt
    if (getIt.isRegistered<AnalyticsService>()) {
      getIt.unregister<AnalyticsService>();
    }

    // Register mock analytics service
    getIt.registerSingleton<AnalyticsService>(MockAnalyticsService());
  }

  /// Cleans up GetIt services after testing
  static void cleanupGetItServices() {
    final getIt = GetIt.instance;
    if (getIt.isRegistered<AnalyticsService>()) {
      getIt.unregister<AnalyticsService>();
    }
  }

  /// Creates a mock DateTime for consistent testing
  static DateTime createMockDateTime() {
    return DateTime(2024, 1, 15, 10, 30, 0);
  }

  /// Creates test data for various entities
  static Map<String, dynamic> createTestUserData() {
    return {
      'id': 'test_user_123',
      'name': 'Test User',
      'email': '<EMAIL>',
      'phone': '+************',
      'createdAt': createMockDateTime().toIso8601String(),
    };
  }

  static Map<String, dynamic> createTestProductData() {
    return {
      'id': 'product_123',
      'productName': 'Test Product',
      'category': 'Test Category',
      'productImage': 'https://example.com/image.jpg',
      'content': '<p>Test product content</p>',
      'productTag': 'NEW ARRIVAL',
    };
  }

  static Map<String, dynamic> createTestFarmerData() {
    return {
      'id': 1,
      'name': 'Test Farmer',
      'phone': '+************',
      'location': 'Test Location',
      'visits': [],
    };
  }

  // Additional helper methods for screen tests
  static dynamic createTestDashboardData() {
    return {
      'totalSales': 150000.0,
      'totalOrders': 25,
      'pendingPayments': 5000.0,
      'recentOrders': createTestOrders(),
      'topProducts': createTestProducts().take(3).toList(),
    };
  }

  static List<dynamic> createTestProducts() {
    return [
      {
        'id': '1',
        'name': 'Test Product 1',
        'description': 'Test product description 1',
        'price': 1500.0,
        'category': 'Test Category',
        'imageUrl': 'https://example.com/image1.jpg',
      },
      {
        'id': '2',
        'name': 'Test Product 2',
        'description': 'Test product description 2',
        'price': 2500.0,
        'category': 'Test Category',
        'imageUrl': 'https://example.com/image2.jpg',
      },
    ];
  }

  static dynamic createTestProduct() {
    return {
      'id': '1',
      'name': 'Test Product',
      'description': 'This is a test product with detailed description',
      'price': 1500.0,
      'category': 'Test Category',
      'imageUrl': 'https://example.com/image.jpg',
    };
  }

  static List<dynamic> createTestCategories() {
    return [
      {'id': '1', 'name': 'Test Category 1'},
      {'id': '2', 'name': 'Test Category 2'},
    ];
  }

  static List<dynamic> createTestOrders() {
    return [
      {
        'id': '1',
        'customerId': TestConstants.testUserId,
        'totalAmount': 5000.0,
        'status': 'Pending',
        'orderDate': createMockDateTime().toIso8601String(),
        'items': [],
      },
      {
        'id': '2',
        'customerId': TestConstants.testUserId,
        'totalAmount': 7500.0,
        'status': 'Completed',
        'orderDate':
            createMockDateTime().subtract(Duration(days: 1)).toIso8601String(),
        'items': [],
      },
    ];
  }

  static Customer createTestCustomer() {
    return Customer(
      id: TestConstants.testUserId,
      customerId: TestConstants.testUserId,
      customerName: 'Test Customer',
      email: '<EMAIL>',
      mobileNumber: TestConstants.testPhoneNumber,
      companyName: 'Test Company',
      gstNo: 'TEST123456789',
      businessVertical: 'Test Business',
      customerCode: 'TC001',
      billingAddress: 'Test Address',
    );
  }

  static AccountStatement createTestAccountStatement() {
    return AccountStatement(
      entries: [
        AccountStatementEntity(
          txnDate: createMockDateTime(),
          vchType: 'Sales',
          invoiceNumber: 'INV001',
          particulars: 'Payment received',
          debit: 0.0,
          credit: 5000.0,
          balance: 5000.0,
          amount: 5000.0,
        ),
        AccountStatementEntity(
          txnDate: createMockDateTime().subtract(Duration(days: 1)),
          vchType: 'Payment',
          invoiceNumber: 'PAY001',
          particulars: 'Purchase',
          debit: 2000.0,
          credit: 0.0,
          balance: 3000.0,
          amount: 2000.0,
        ),
      ],
      lastSyncTime: createMockDateTime(),
    );
  }

  static AccountStatement createEmptyAccountStatement() {
    return AccountStatement(entries: [], lastSyncTime: createMockDateTime());
  }

  static List<dynamic> createTestFarmers() {
    return [
      {
        'id': '1',
        'name': 'Test Farmer 1',
        'phone': '+************',
        'location': 'Test Location 1',
        'visitCount': 5,
      },
      {
        'id': '2',
        'name': 'Test Farmer 2',
        'phone': '+************',
        'location': 'Test Location 2',
        'visitCount': 3,
      },
    ];
  }

  static List<dynamic> createTestFarmerVisits() {
    return [
      {
        'id': '1',
        'farmerId': '1',
        'date': createMockDateTime().toIso8601String(),
        'purpose': 'Consultation',
        'notes': 'Regular checkup',
      },
      {
        'id': '2',
        'farmerId': '2',
        'date':
            createMockDateTime().subtract(Duration(days: 2)).toIso8601String(),
        'purpose': 'Product delivery',
        'notes': 'Delivered feed',
      },
    ];
  }

  static List<dynamic> createTestStockItems() {
    return [
      {
        'id': '1',
        'productId': '1',
        'productName': 'Test Product 1',
        'quantity': 100,
        'minQuantity': 20,
        'value': 15000.0,
        'category': 'Test Category',
      },
      {
        'id': '2',
        'productId': '2',
        'productName': 'Test Product 2',
        'quantity': 50,
        'minQuantity': 10,
        'value': 25000.0,
        'category': 'Test Category',
      },
    ];
  }

  static List<dynamic> createLowStockItems() {
    return [
      {
        'id': '1',
        'productId': '1',
        'productName': 'Low Stock Product',
        'quantity': 5,
        'minQuantity': 20,
        'value': 5000.0,
        'category': 'Test Category',
        'isLowStock': true,
      },
    ];
  }

  static List<dynamic> createLargeProductList() {
    return List.generate(
      100,
      (index) => {
        'id': 'product_$index',
        'name': 'Product $index',
        'description': 'Description for product $index',
        'price': (index + 1) * 100.0,
        'category': 'Category ${index % 5}',
        'imageUrl': 'https://example.com/image$index.jpg',
      },
    );
  }

  static List<dynamic> createLargeStockList() {
    return List.generate(
      200,
      (index) => {
        'id': 'stock_$index',
        'productId': 'product_$index',
        'productName': 'Stock Product $index',
        'quantity': (index + 1) * 10,
        'minQuantity': 20,
        'value': (index + 1) * 1000.0,
        'category': 'Category ${index % 5}',
      },
    );
  }

  static AccountStatement createLargeAccountStatement() {
    return AccountStatement(
      entries: List.generate(
        500,
        (index) => AccountStatementEntity(
          txnDate: createMockDateTime().subtract(Duration(days: index)),
          vchType: index % 2 == 0 ? 'Sales' : 'Payment',
          invoiceNumber: 'TXN$index',
          particulars: 'Transaction $index',
          debit: index % 2 == 0 ? 0.0 : (index + 1) * 100.0,
          credit: index % 2 == 0 ? (index + 1) * 100.0 : 0.0,
          balance: 50000.0 - (index * 50.0),
          amount: (index + 1) * 100.0,
        ),
      ),
      lastSyncTime: createMockDateTime(),
    );
  }

  static List<dynamic> createTestDues() {
    return [
      {
        'id': '1',
        'invoiceNumber': 'INV001',
        'amount': 5000.0,
        'dueDate':
            createMockDateTime().add(Duration(days: 7)).toIso8601String(),
        'status': 'pending',
        'description': 'Product purchase',
      },
      {
        'id': '2',
        'invoiceNumber': 'INV002',
        'amount': 10000.0,
        'dueDate':
            createMockDateTime().add(Duration(days: 15)).toIso8601String(),
        'status': 'pending',
        'description': 'Service charges',
      },
    ];
  }

  static List<dynamic> createOverdueDues() {
    return [
      {
        'id': '1',
        'invoiceNumber': 'INV001',
        'amount': 5000.0,
        'dueDate':
            createMockDateTime().subtract(Duration(days: 5)).toIso8601String(),
        'status': 'overdue',
        'description': 'Overdue payment',
      },
      {
        'id': '2',
        'invoiceNumber': 'INV002',
        'amount': 20000.0,
        'dueDate':
            createMockDateTime().subtract(Duration(days: 10)).toIso8601String(),
        'status': 'overdue',
        'description': 'Overdue service charges',
      },
    ];
  }

  static DuesSummary createTestDuesSummary() {
    return DuesSummary(
      agingGroups: [
        DuesAgingGroup(
          invoices: [
            createTestDuesInvoice('INV001', 5000.0, '0-30 days'),
            createTestDuesInvoice('INV002', 10000.0, '0-30 days'),
          ],
          totalPayableAmount: 15000.0,
          aging: '0-30 days',
          dueDays: 15,
        ),
      ],
      customerId: 'test_customer_123',
      totalDue: 15000.0,
    );
  }

  static DuesSummary createEmptyDuesSummary() {
    return DuesSummary(
      agingGroups: [],
      customerId: 'test_customer_123',
      totalDue: 0.0,
    );
  }

  static DuesSummary createOverdueDuesSummary() {
    return DuesSummary(
      agingGroups: [
        DuesAgingGroup(
          invoices: [
            createTestDuesInvoice('INV001', 5000.0, 'Overdue'),
            createTestDuesInvoice('INV002', 20000.0, 'Overdue'),
          ],
          totalPayableAmount: 25000.0,
          aging: 'Overdue',
          dueDays: -5,
        ),
      ],
      customerId: 'test_customer_123',
      totalDue: 25000.0,
    );
  }

  static DuesInvoice createTestDuesInvoice(
    String invoiceNumber,
    double amount,
    String aging,
  ) {
    return DuesInvoice(
      id: 'test_id_$invoiceNumber',
      invoiceId: 'invoice_id_$invoiceNumber',
      invoiceNumber: invoiceNumber,
      invoiceDate: createMockDateTime().toIso8601String(),
      customerId: 'test_customer_123',
      customerCode: 'CUST001',
      customerName: 'Test Customer',
      customerDistrict: 'Test District',
      customerState: 'Test State',
      customerType: 'Regular',
      onBoardedTime: createMockDateTime().toIso8601String(),
      categoryType: 'Standard',
      invoiceType: 'Sales',
      retailType: 'Direct',
      invoiceStatus: 'Pending',
      invoiceRaisedBy: 'System',
      mode: 'Online',
      businessVertical: 'Aquaculture',
      feedCreditLimit: '50000',
      nonFeedCreditLimit: '25000',
      harvestCreditLimit: '10000',
      totalSalesInclTax: amount,
      totalSalesExclTax: amount * 0.9,
      tcsAmount: 0.0,
      tdsAmount: 0.0,
      amountAfterTcs: amount,
      shippingCharges: 0.0,
      feedAmountAfterTcs: '$amount',
      nonFeedAmountAfterTcs: 0.0,
      creditNoteAmountWithTcs: 0.0,
      payableAmount: amount,
      paidAmount: 0.0,
      due: amount,
      dueDate:
          aging == 'Overdue'
              ? createMockDateTime()
                  .subtract(Duration(days: 5))
                  .toIso8601String()
              : createMockDateTime().add(Duration(days: 15)).toIso8601String(),
      dueDays: aging == 'Overdue' ? -5 : 15,
      aging: aging,
      aging1: aging,
      paymentCredibility: 'Good',
    );
  }

  /// Verifies that a widget exists and is visible
  static void verifyWidgetExists(WidgetTester tester, Finder finder) {
    expect(finder, findsOneWidget);
    expect(tester.widget(finder), isNotNull);
  }

  /// Verifies that text exists in the widget tree
  static void verifyTextExists(String text) {
    expect(find.text(text), findsOneWidget);
  }

  /// Verifies that a button exists and is enabled
  static void verifyButtonEnabled(Finder buttonFinder) {
    expect(buttonFinder, findsOneWidget);
    final button =
        find.byType(ElevatedButton).evaluate().first.widget as ElevatedButton;
    expect(button.onPressed, isNotNull);
  }

  /// Simulates a tap and waits for animations
  static Future<void> tapAndSettle(WidgetTester tester, Finder finder) async {
    await tester.tap(finder);
    await tester.pumpAndSettle();
  }

  /// Enters text in a text field
  static Future<void> enterText(
    WidgetTester tester,
    Finder finder,
    String text,
  ) async {
    await tester.enterText(finder, text);
    await tester.pump();
  }

  /// Creates a test analytics event
  static Map<String, dynamic> createTestAnalyticsEvent({
    required String name,
    Map<String, dynamic>? parameters,
  }) {
    return {
      'name': name,
      'parameters': parameters ?? {},
      'timestamp': createMockDateTime().millisecondsSinceEpoch,
    };
  }
}

/// Mock classes for testing
class MockFirebaseAnalytics extends Mock implements FirebaseAnalytics {}

class MockFirebaseAuth extends Mock implements FirebaseAuth {}

class MockUser extends Mock implements User {}

class MockUserCredential extends Mock implements UserCredential {}

class MockPhoneAuthCredential extends Mock implements PhoneAuthCredential {}

class MockConnectivity extends Mock implements Connectivity {}

class MockSharedPreferences extends Mock implements SharedPreferences {}

/// Test constants
class TestConstants {
  static const String testPhoneNumber = '+************';
  static const String testOtp = '123456';
  static const String testVerificationId = 'test_verification_id';
  static const String testUserId = 'test_user_123';
  static const String testProductId = 'product_123';
  static const String testFarmerId = 'farmer_123';

  // Analytics event names
  static const String loginScreenViewed = 'login_screen_viewed';
  static const String otpSent = 'otp_sent';
  static const String otpVerified = 'otp_verified';
  static const String productViewed = 'product_viewed';
  static const String farmerViewed = 'farmer_viewed';
}

/// Test matchers
class TestMatchers {
  /// Matches analytics events by name
  static Matcher isAnalyticsEvent(String eventName) {
    return predicate<Map<String, dynamic>>(
      (event) => event['name'] == eventName,
      'is analytics event with name $eventName',
    );
  }

  /// Matches analytics events with specific parameters
  static Matcher hasAnalyticsParameter(String key, dynamic value) {
    return predicate<Map<String, dynamic>>(
      (event) => event['parameters']?[key] == value,
      'has analytics parameter $key with value $value',
    );
  }

  /// Matches error states
  static Matcher isErrorState(String errorMessage) {
    return predicate<dynamic>(
      (state) => state.toString().contains(errorMessage),
      'is error state with message $errorMessage',
    );
  }

  static List<SMRReport> createTestSMRReports() {
    return [
      SMRReport(
        id: '1',
        so: 'SO001',
        partner: 'Test Partner 1',
        partnerId: 'P001',
        productName: 'Test Product 1',
        startDate: DateTime.now().subtract(Duration(days: 30)),
        lastDate: DateTime.now(),
        openingBalance: 100,
        invoice: 50,
        srn: 10,
        closingBalance: 140,
        sales: 20,
        status: 'Active',
      ),
      SMRReport(
        id: '2',
        so: 'SO002',
        partner: 'Test Partner 2',
        partnerId: 'P002',
        productName: 'Test Product 2',
        startDate: DateTime.now().subtract(Duration(days: 30)),
        lastDate: DateTime.now(),
        openingBalance: 200,
        invoice: 75,
        srn: 25,
        closingBalance: 250,
        sales: 50,
        status: 'Active',
      ),
    ];
  }
}
