import 'package:aquapartner/core/utils/logger.dart';
import 'package:aquapartner/domain/services/auth_service.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../domain/usecases/dues/check_if_dues_sync_needed_usecase.dart';
import '../../../domain/usecases/dues/get_dues_usecase.dart';
import '../../../domain/usecases/dues/sync_dues_usecase.dart';
import 'dues_state.dart';

class DuesCubit extends Cubit<DuesState> {
  final GetDuesUseCase getDuesUseCase;
  final SyncDuesUseCase syncDuesUseCase;
  final CheckIfDuesSyncNeededUseCase checkIfDuesSyncNeededUseCase;
  final AuthService authService;
  final AppLogger logger;

  bool _isBackgroundSyncing = false;

  DuesCubit({
    required this.getDuesUseCase,
    required this.syncDuesUseCase,
    required this.checkIfDuesSyncNeededUseCase,
    required this.authService,
    required this.logger,
  }) : super(DuesInitial());

  /// Load dues with a caching-first approach
  /// Always returns cached data immediately if available
  /// and triggers a background sync if needed
  Future<void> loadDues() async {
    emit(DuesLoading());

    final customerResult = await authService.getCurrentCustomer();

    customerResult.fold(
      (failure) {
        emit(DuesError("Try again later."));
      },
      (customer) async {
        logger.i('Loading dues for customer: ${customer!.customerId}');

        // CACHING-FIRST APPROACH:
        // 1. Always try to get data from local cache first
        final result = await getDuesUseCase(customer.customerId);

        result.fold(
          (failure) {
            logger.e('Error loading dues from cache: $failure');

            // If cache fetch fails, try to sync from remote
            emit(DuesError('Unable to load Dues, please try again'));

            // Try to sync if local fetch fails
            syncDues(customer.customerId);
          },
          (duesSummary) {
            logger.i('Successfully loaded dues from cache');

            // Select all aging options by default
            final allAgings =
                duesSummary.agingGroups
                    .map((group) => group.aging)
                    .toSet()
                    .toList();

            // 2. Emit loaded state with cached data immediately
            emit(
              DuesLoaded(
                duesSummary,
                selectedAgingFilters: allAgings,
                isFromCache: true,
                isCacheStale: false, // We don't know yet, will update if needed
                isBackgroundSyncInProgress: false,
              ),
            );

            // 3. Check if sync is needed in the background
            _checkAndSyncIfNeeded(customer.customerId);
          },
        );
      },
    );
  }

  /// Check if sync is needed and perform background sync if necessary
  Future<void> _checkAndSyncIfNeeded(String customerId) async {
    try {
      // Check if sync is needed
      final syncNeededResult = await checkIfDuesSyncNeededUseCase(customerId);

      final syncNeeded = syncNeededResult.fold((failure) {
        logger.w('Error checking if sync needed: $failure');
        return false; // Don't sync on error
      }, (needed) => needed);

      if (syncNeeded && !_isBackgroundSyncing) {
        logger.i('Cache is stale, performing background sync');

        // Update state to indicate cache is stale and background sync is in progress
        if (state is DuesLoaded) {
          emit(
            (state as DuesLoaded).copyWith(
              isCacheStale: true,
              isBackgroundSyncInProgress: true,
            ),
          );
        }

        // Perform background sync
        _isBackgroundSyncing = true;
        await _backgroundSync(customerId);
        _isBackgroundSyncing = false;

        // Update state to indicate background sync is complete
        if (state is DuesLoaded) {
          emit(
            (state as DuesLoaded).copyWith(isBackgroundSyncInProgress: false),
          );
        }
      }
    } catch (e) {
      logger.e('Error in _checkAndSyncIfNeeded: $e');
      _isBackgroundSyncing = false;
    }
  }

  /// Perform a background sync without changing the UI state
  Future<void> _backgroundSync(String customerId) async {
    try {
      logger.i('Performing background sync for customer: $customerId');

      // Sync dues
      final result = await syncDuesUseCase(customerId);

      result.fold(
        (failure) {
          logger.e('Error in background sync: $failure');
          // Don't emit error state as this is a background operation
        },
        (duesSummary) {
          logger.i('Background sync completed successfully');

          // Update the state with new data but preserve filters
          if (state is DuesLoaded) {
            final currentState = state as DuesLoaded;
            emit(
              DuesLoaded(
                duesSummary,
                selectedAgingFilters: currentState.selectedAgingFilters,
                isFromCache: false,
                isCacheStale: false,
                isBackgroundSyncInProgress: false,
              ),
            );
          }
        },
      );
    } catch (e) {
      logger.e('Error in background sync: $e');
      // Don't emit error state as this is a background operation
    }
  }

  /// Sync dues for the current customer
  /// This is a manual sync that shows loading indicators
  Future<void> syncDues(String customerId) async {
    // Preserve current state if it's loaded
    final currentState = state;
    emit(DuesSyncing());

    final result = await syncDuesUseCase(customerId);
    result.fold(
      (failure) {
        emit(DuesSyncError("Unable to Sync, check your internet connection"));
        // Revert to previous state if it was loaded
        if (currentState is DuesLoaded) {
          emit(currentState);
        }
      },
      (duesSummary) {
        emit(DuesSyncSuccess());
        // If we had filters before, preserve them
        if (currentState is DuesLoaded) {
          emit(
            DuesLoaded(
              duesSummary,
              selectedAgingFilters: currentState.selectedAgingFilters,
              isFromCache: false,
              isCacheStale: false,
              isBackgroundSyncInProgress: false,
            ),
          );
        } else {
          // Select all aging options by default
          final allAgings =
              duesSummary.agingGroups
                  .map((group) => group.aging)
                  .toSet()
                  .toList();
          emit(
            DuesLoaded(
              duesSummary,
              selectedAgingFilters: allAgings,
              isFromCache: false,
              isCacheStale: false,
              isBackgroundSyncInProgress: false,
            ),
          );
        }
      },
    );
  }

  /// Load dues from local cache only
  Future<void> loadDuesFromLocal(String customerId) async {
    final result = await getDuesUseCase(customerId);

    result.fold(
      (failure) => emit(DuesError('Unable to load Dues, please try again')),
      (duesSummary) {
        final allAgings =
            duesSummary.agingGroups
                .map((group) => group.aging)
                .toSet()
                .toList();
        emit(
          DuesLoaded(
            duesSummary,
            selectedAgingFilters: allAgings,
            isFromCache: true,
            isCacheStale:
                true, // Mark as potentially stale since we're not checking
            isBackgroundSyncInProgress: false,
          ),
        );
      },
    );
  }

  /// Force refresh dues by syncing with remote
  Future<void> refreshDues(String customerId) async {
    // Force sync regardless of last sync time
    await syncDues(customerId);
  }

  /// Force invalidate the cache and trigger a sync
  Future<void> invalidateAndSync(String customerId) async {
    try {
      logger.i('Invalidating cache and syncing for customer: $customerId');

      // Mark cache as stale if we have loaded data
      if (state is DuesLoaded) {
        emit((state as DuesLoaded).copyWith(isCacheStale: true));
      }

      // Perform a full sync
      await syncDues(customerId);
    } catch (e) {
      logger.e('Error invalidating cache: $e');
      emit(DuesError('Failed to invalidate cache: ${e.toString()}'));
    }
  }

  /// Update aging filters while preserving cache status
  Future<void> updateAgingFilters(List<String> selectedAgings) async {
    final currentState = state;
    if (currentState is DuesLoaded) {
      logger.i("Updating Aging Filter ${selectedAgings.toString()}");
      // Preserve all other state properties when updating filters
      emit(currentState.copyWith(selectedAgingFilters: selectedAgings));
    }
  }
}
