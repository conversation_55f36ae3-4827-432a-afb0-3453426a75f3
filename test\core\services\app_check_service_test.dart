import 'package:aquapartner/core/services/app_check_service.dart';
import 'package:aquapartner/core/utils/logger.dart';
import 'package:firebase_app_check/firebase_app_check.dart';
import 'package:flutter_test/flutter_test.dart';

// Custom logger mock that captures error calls
class TestAppLogger implements AppLogger {
  List<String> errorMessages = [];
  List<Object> errorObjects = [];
  List<String> infoMessages = [];
  List<String> debugMessages = [];
  List<String> warningMessages = [];

  @override
  void e(String message, [dynamic error, StackTrace? stackTrace]) {
    errorMessages.add(message);
    if (error != null) errorObjects.add(error);
  }

  @override
  void i(String message) {
    infoMessages.add(message);
  }

  @override
  void d(String message) {
    debugMessages.add(message);
  }

  @override
  void w(String message) {
    warningMessages.add(message);
  }

  @override
  void enableFirebaseVerboseLogging() {
    // No-op for testing
  }
}

// Custom FirebaseAppCheck mock
class TestFirebaseAppCheck implements FirebaseAppCheck {
  String? tokenToReturn;
  Exception? exceptionToThrow;
  bool activateCalled = false;
  bool tokenRefreshCalled = false;

  @override
  Future<String?> getToken([bool? forceRefresh]) async {
    if (exceptionToThrow != null) {
      throw exceptionToThrow!;
    }
    return tokenToReturn;
  }

  @override
  Future<void> activate({
    webProvider,
    AndroidProvider androidProvider = AndroidProvider.playIntegrity,
    AppleProvider appleProvider = AppleProvider.deviceCheck,
  }) async {
    activateCalled = true;
  }

  @override
  Future<void> setTokenAutoRefreshEnabled(bool enabled) async {
    tokenRefreshCalled = true;
  }

  // Implement other required methods with minimal functionality
  @override
  dynamic noSuchMethod(Invocation invocation) => super.noSuchMethod(invocation);
}

void main() {
  late TestFirebaseAppCheck mockFirebaseAppCheck;
  late TestAppLogger testLogger;
  late AppCheckService service;

  setUp(() {
    mockFirebaseAppCheck = TestFirebaseAppCheck();
    testLogger = TestAppLogger();
    service = AppCheckService(mockFirebaseAppCheck, testLogger);
  });

  group('AppCheckService', () {
    test(
      'initialize should activate App Check and enable token refresh',
      () async {
        // Act
        await service.initialize();

        // Assert
        expect(mockFirebaseAppCheck.activateCalled, isTrue);
        expect(mockFirebaseAppCheck.tokenRefreshCalled, isTrue);
        expect(testLogger.infoMessages, isNotEmpty);
      },
    );

    test('getToken should return token when successful', () async {
      // Arrange
      mockFirebaseAppCheck.tokenToReturn = 'test-token';

      // Act
      final result = await service.getToken();

      // Assert
      expect(result, equals('test-token'));
    });

    test(
      'getToken should return debug token when error occurs in debug mode',
      () async {
        // Arrange
        mockFirebaseAppCheck.exceptionToThrow = Exception('Test error');

        // Act
        final result = await service.getToken();

        // Assert
        // In debug mode, the service returns a debug token when the real token fails
        expect(result, equals('debug-app-check-token'));
        expect(testLogger.warningMessages, isNotEmpty);
      },
    );

    test('getToken should initialize if not already initialized', () async {
      // Arrange
      mockFirebaseAppCheck.tokenToReturn = 'test-token';

      // Create a new service instance to reset the initialization state
      service = AppCheckService(mockFirebaseAppCheck, testLogger);

      // Act
      final result = await service.getToken();

      // Assert
      expect(result, equals('test-token'));
      expect(mockFirebaseAppCheck.activateCalled, isTrue);
    });
  });
}
