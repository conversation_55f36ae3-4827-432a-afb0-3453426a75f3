import 'package:flutter/material.dart';
import '../../core/mixins/analytics_mixin.dart';

class ScrollableContentScreen extends StatefulWidget {
  const ScrollableContentScreen({Key? key}) : super(key: key);

  @override
  State<ScrollableContentScreen> createState() => _ScrollableContentScreenState();
}

class _ScrollableContentScreenState extends State<ScrollableContentScreen> with AnalyticsMixin {
  @override
  String get screenName => 'scrollable_content_screen';
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Scrollable Content')),
      body: NotificationListener<ScrollNotification>(
        onNotification: (notification) {
          if (notification is ScrollUpdateNotification) {
            // Only track if this is the primary scroll view
            if (notification.depth == 0) {
              trackScrollPosition(
                notification.metrics.pixels,
                notification.metrics.maxScrollExtent,
              );
            }
          }
          return false;
        },
        child: ListView.builder(
          itemCount: 100,
          itemBuilder: (context, index) {
            return ListTile(
              title: Text('Item $index'),
              onTap: () {
                trackUserInteraction(
                  'tap',
                  'list_item',
                  elementId: 'item_$index',
                );
              },
            );
          },
        ),
      ),
    );
  }
}