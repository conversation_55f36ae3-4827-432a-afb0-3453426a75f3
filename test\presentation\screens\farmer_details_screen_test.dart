import 'package:aqua_ui/aqua_ui.dart';
import 'package:aquapartner/core/services/analytics_service.dart';
import 'package:aquapartner/domain/entities/farmer_visit/farmer.dart';
import 'package:aquapartner/domain/entities/farmer_visit/visit.dart';
import 'package:aquapartner/presentation/screens/famer_details_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:mocktail/mocktail.dart';

// Mock classes
class MockAnalyticsService extends Mock implements AnalyticsService {}

// Test data
final testVisits = [
  Visit(
    id: 1,
    pondId: 'POND001',
    doc: 30,
    createdDateTime: DateTime(2024, 1, 15),
    mobileNumber: '+919999999999',
    productUsed: 'Product A, Product B',
    farmerId: 'FARMER001',
    farmerName: 'Test Farmer',
  ),
  Visit(
    id: 2,
    pondId: 'POND002',
    doc: 45,
    createdDateTime: DateTime(2024, 1, 10),
    mobileNumber: '+919999999999',
    productUsed: 'Product C',
    farmerId: 'FARMER001',
    farmerName: 'Test Farmer',
  ),
  Visit(
    id: 3,
    pondId: 'POND003',
    doc: 60,
    createdDateTime: DateTime(2024, 1, 5),
    mobileNumber: '+919999999999',
    productUsed: 'Product D, Product E, Product F',
    farmerId: 'FARMER001',
    farmerName: 'Test Farmer',
  ),
];

final testFarmer = Farmer(
  id: 1,
  name: 'Test Farmer',
  mobileNumber: '+919999999999',
  visits: testVisits,
);

final testFarmerWithoutVisits = Farmer(
  id: 2,
  name: 'Farmer Without Visits',
  mobileNumber: '+919876543211',
  visits: [],
);

void main() {
  group('FarmerDetailsScreen Tests', () {
    late MockAnalyticsService mockAnalyticsService;

    setUp(() {
      // Setup any required mocks here
      mockAnalyticsService = MockAnalyticsService();

      // Stub mock methods
      when(
        () => mockAnalyticsService.logScreenView(
          screenName: any(named: 'screenName'),
          screenClass: any(named: 'screenClass'),
        ),
      ).thenAnswer((_) async {});
      when(
        () => mockAnalyticsService.logUserInteraction(
          screenName: any(named: 'screenName'),
          actionName: any(named: 'actionName'),
          elementType: any(named: 'elementType'),
          elementId: any(named: 'elementId'),
          additionalParams: any(named: 'additionalParams'),
        ),
      ).thenAnswer((_) async {});
      when(
        () => mockAnalyticsService.logError(
          errorType: any(named: 'errorType'),
          errorMessage: any(named: 'errorMessage'),
          screenName: any(named: 'screenName'),
          additionalParams: any(named: 'additionalParams'),
        ),
      ).thenAnswer((_) async {});
      when(
        () => mockAnalyticsService.logUserFlow(
          flowName: any(named: 'flowName'),
          stepName: any(named: 'stepName'),
          status: any(named: 'status'),
          additionalParams: any(named: 'additionalParams'),
        ),
      ).thenAnswer((_) async {});
      when(
        () => mockAnalyticsService.logEvent(
          name: any(named: 'name'),
          parameters: any(named: 'parameters'),
        ),
      ).thenAnswer((_) async {});
      when(
        () => mockAnalyticsService.logScreenDuration(
          screenName: any(named: 'screenName'),
          durationMs: any(named: 'durationMs'),
          screenClass: any(named: 'screenClass'),
        ),
      ).thenAnswer((_) async {});

      // Register mock services in GetIt
      if (!GetIt.instance.isRegistered<AnalyticsService>()) {
        GetIt.instance.registerSingleton<AnalyticsService>(
          mockAnalyticsService,
        );
      }
    });

    tearDown(() {
      // Clean up GetIt registrations
      if (GetIt.instance.isRegistered<AnalyticsService>()) {
        GetIt.instance.unregister<AnalyticsService>();
      }
    });

    Widget createTestWidget(Farmer farmer) {
      return MaterialApp(home: FarmerDetailsScreen(farmer: farmer));
    }

    // Helper function to find AquaText with specific text
    Finder findAquaText(String text) {
      return find.byWidgetPredicate(
        (widget) => widget is AquaText && widget.text == text,
      );
    }

    group('Widget Rendering', () {
      testWidgets('should render farmer details correctly', (tester) async {
        await tester.pumpWidget(createTestWidget(testFarmer));
        await tester.pumpAndSettle();

        // Verify the screen renders without errors
        expect(find.byType(FarmerDetailsScreen), findsOneWidget);
        expect(find.byType(Scaffold), findsOneWidget);
        expect(find.byType(AppBar), findsOneWidget);

        // Verify AquaText widgets are present (indicating content is rendered)
        expect(find.byType(AquaText), findsWidgets);

        // Find farmer name in AquaText widgets
        expect(findAquaText('Test Farmer'), findsOneWidget);
      });

      testWidgets('should render app bar with correct title', (tester) async {
        await tester.pumpWidget(createTestWidget(testFarmer));
        await tester.pumpAndSettle();

        // Verify app bar title shows farmer name
        expect(findAquaText('Test Farmer'), findsOneWidget);

        // Verify back button is present (IconButton with arrow_back)
        expect(find.byIcon(Icons.arrow_back), findsOneWidget);
      });

      testWidgets('should render visits table when visits exist', (
        tester,
      ) async {
        await tester.pumpWidget(createTestWidget(testFarmer));
        await tester.pumpAndSettle();

        // Verify visit table headers using AquaText finder
        expect(
          find.byWidgetPredicate(
            (widget) => widget is AquaText && widget.text == 'Visited Date',
          ),
          findsOneWidget,
        );
        expect(
          find.byWidgetPredicate(
            (widget) => widget is AquaText && widget.text == 'Pond ID',
          ),
          findsOneWidget,
        );
        expect(
          find.byWidgetPredicate(
            (widget) => widget is AquaText && widget.text == 'DOC',
          ),
          findsOneWidget,
        );
        expect(
          find.byWidgetPredicate(
            (widget) => widget is AquaText && widget.text == 'Products',
          ),
          findsOneWidget,
        );

        // Verify visit data using AquaText finder
        expect(
          find.byWidgetPredicate(
            (widget) => widget is AquaText && widget.text == 'POND001',
          ),
          findsOneWidget,
        );
        expect(
          find.byWidgetPredicate(
            (widget) => widget is AquaText && widget.text == 'POND002',
          ),
          findsOneWidget,
        );
        expect(
          find.byWidgetPredicate(
            (widget) => widget is AquaText && widget.text == 'POND003',
          ),
          findsOneWidget,
        );

        expect(
          find.byWidgetPredicate(
            (widget) => widget is AquaText && widget.text == '30',
          ),
          findsOneWidget,
        );
        expect(
          find.byWidgetPredicate(
            (widget) => widget is AquaText && widget.text == '45',
          ),
          findsOneWidget,
        );
        expect(
          find.byWidgetPredicate(
            (widget) => widget is AquaText && widget.text == '60',
          ),
          findsOneWidget,
        );
      });

      testWidgets('should not render visits table when no visits exist', (
        tester,
      ) async {
        await tester.pumpWidget(createTestWidget(testFarmerWithoutVisits));
        await tester.pumpAndSettle();

        // Verify farmer name is still displayed
        expect(findAquaText('Farmer Without Visits'), findsOneWidget);

        // Verify no visits message is shown
        expect(findAquaText('No visit data available'), findsOneWidget);
        expect(findAquaText('POND001'), findsNothing);
        expect(findAquaText('Visited Date'), findsNothing);
      });

      testWidgets('should format dates correctly', (tester) async {
        await tester.pumpWidget(createTestWidget(testFarmer));
        await tester.pumpAndSettle();

        // Verify date formatting (dd-MM-yyyy format)
        expect(findAquaText('15-01-2024'), findsOneWidget);
        expect(findAquaText('10-01-2024'), findsOneWidget);
        expect(findAquaText('05-01-2024'), findsOneWidget);
      });

      testWidgets('should display products correctly', (tester) async {
        await tester.pumpWidget(createTestWidget(testFarmer));
        await tester.pumpAndSettle();

        // Debug: Print all AquaText widgets to see what products are displayed
        final allAquaText = find.byType(AquaText);
        print('Found ${allAquaText.evaluate().length} AquaText widgets:');
        for (final element in allAquaText.evaluate()) {
          final widget = element.widget as AquaText;
          print('AquaText: "${widget.text}"');
        }

        // Verify products are displayed exactly as they are in the data (as comma-separated strings)
        expect(findAquaText('Product A, Product B'), findsOneWidget);
        expect(findAquaText('Product C'), findsOneWidget);
        expect(findAquaText('Product D, Product E, Product F'), findsOneWidget);
      });
    });

    group('User Interactions', () {
      testWidgets('should handle back button tap', (tester) async {
        await tester.pumpWidget(createTestWidget(testFarmer));
        await tester.pumpAndSettle();

        // Find and tap back button (IconButton with arrow_back)
        final backButton = find.byIcon(Icons.arrow_back);
        expect(backButton, findsOneWidget);

        await tester.tap(backButton);
        await tester.pumpAndSettle();

        // Verify navigation occurred (screen should be popped)
        expect(find.byType(FarmerDetailsScreen), findsNothing);
      });

      testWidgets('should handle scrolling through visits', (tester) async {
        // Create farmer with many visits to test scrolling
        final manyVisits = List.generate(
          20,
          (index) => Visit(
            id: index + 1,
            pondId: 'POND${index.toString().padLeft(3, '0')}',
            doc: 30 + index,
            createdDateTime: DateTime(2024, 1, 1).add(Duration(days: index)),
            mobileNumber: '+919999999999',
            productUsed: 'Product ${index + 1}',
            farmerId: 'FARMER001',
            farmerName: 'Farmer With Many Visits',
          ),
        );

        final farmerWithManyVisits = Farmer(
          id: 1,
          name: 'Farmer With Many Visits',
          mobileNumber: '+919999999999',
          visits: manyVisits,
        );

        await tester.pumpWidget(createTestWidget(farmerWithManyVisits));
        await tester.pumpAndSettle();

        // Find scrollable widget
        final scrollable = find.byType(Scrollable);
        if (scrollable.evaluate().isNotEmpty) {
          // Test scrolling
          await tester.drag(scrollable.first, const Offset(0, -300));
          await tester.pumpAndSettle();

          // Verify scrolling worked
          expect(find.byType(FarmerDetailsScreen), findsOneWidget);
        }
      });

      testWidgets('should handle visit row taps if implemented', (
        tester,
      ) async {
        await tester.pumpWidget(createTestWidget(testFarmer));
        await tester.pumpAndSettle();

        // Look for tappable visit rows
        final visitRow = findAquaText('POND001');
        if (visitRow.evaluate().isNotEmpty) {
          await tester.tap(visitRow);
          await tester.pumpAndSettle();

          // Verify interaction handling
        }
      });
    });

    group('Analytics Tracking', () {
      testWidgets('should track farmer details view on init', (tester) async {
        await tester.pumpWidget(createTestWidget(testFarmer));
        await tester.pumpAndSettle();

        // Verify analytics tracking would be called
        // Note: Since analytics is handled in initState, we verify the screen was created
        expect(find.byType(FarmerDetailsScreen), findsOneWidget);
      });

      testWidgets('should track visit interactions', (tester) async {
        await tester.pumpWidget(createTestWidget(testFarmer));
        await tester.pumpAndSettle();

        // Find and interact with visit elements
        final visitElement = findAquaText('POND001');
        if (visitElement.evaluate().isNotEmpty) {
          await tester.tap(visitElement);
          await tester.pumpAndSettle();

          // Verify interaction tracking
        }
      });
    });

    group('Data Display', () {
      testWidgets('should display visit history in chronological order', (
        tester,
      ) async {
        await tester.pumpWidget(createTestWidget(testFarmer));
        await tester.pumpAndSettle();

        // Verify visits are displayed (order might be newest first or oldest first)
        expect(findAquaText('15-01-2024'), findsOneWidget);
        expect(findAquaText('10-01-2024'), findsOneWidget);
        expect(findAquaText('05-01-2024'), findsOneWidget);
      });

      testWidgets('should handle long product lists correctly', (tester) async {
        final visitWithManyProducts = Visit(
          id: 1,
          pondId: 'POND001',
          doc: 30,
          createdDateTime: DateTime.now(),
          mobileNumber: '+919999999999',
          productUsed: List.generate(
            10,
            (index) => 'Very Long Product Name ${index + 1}',
          ).join(', '),
          farmerId: 'FARMER001',
          farmerName: 'Test Farmer',
        );

        final farmerWithLongProducts = Farmer(
          id: 1,
          name: 'Test Farmer',
          mobileNumber: '+919999999999',
          visits: [visitWithManyProducts],
        );

        await tester.pumpWidget(createTestWidget(farmerWithLongProducts));
        await tester.pumpAndSettle();

        // Verify screen renders without overflow
        expect(find.byType(FarmerDetailsScreen), findsOneWidget);
        expect(tester.takeException(), isNull);
      });

      testWidgets('should display DOC values correctly', (tester) async {
        await tester.pumpWidget(createTestWidget(testFarmer));
        await tester.pumpAndSettle();

        // Verify DOC values are displayed as numbers
        expect(findAquaText('30'), findsOneWidget);
        expect(findAquaText('45'), findsOneWidget);
        expect(findAquaText('60'), findsOneWidget);
      });
    });

    group('Error Handling', () {
      testWidgets('should handle farmer with null visits gracefully', (
        tester,
      ) async {
        const farmerWithNullVisits = Farmer(
          id: 1,
          name: 'Test Farmer',
          mobileNumber: '+919999999999',
          visits: [],
        );

        await tester.pumpWidget(createTestWidget(farmerWithNullVisits));
        await tester.pumpAndSettle();

        // Verify screen renders without crashing
        expect(find.byType(FarmerDetailsScreen), findsOneWidget);
        expect(findAquaText('Test Farmer'), findsOneWidget);
      });

      testWidgets('should handle very long farmer names', (tester) async {
        const farmerWithLongName = Farmer(
          id: 1,
          name:
              'This Is A Very Long Farmer Name That Might Cause UI Layout Issues',
          mobileNumber: '+919999999999',
          visits: [],
        );

        await tester.pumpWidget(createTestWidget(farmerWithLongName));
        await tester.pumpAndSettle();

        // Verify screen renders without overflow
        expect(find.byType(FarmerDetailsScreen), findsOneWidget);
        expect(tester.takeException(), isNull);
      });

      testWidgets('should handle invalid mobile numbers gracefully', (
        tester,
      ) async {
        const farmerWithInvalidMobile = Farmer(
          id: 1,
          name: 'Test Farmer',
          mobileNumber: 'invalid-mobile',
          visits: [],
        );

        await tester.pumpWidget(createTestWidget(farmerWithInvalidMobile));
        await tester.pumpAndSettle();

        // Verify screen renders without crashing
        expect(find.byType(FarmerDetailsScreen), findsOneWidget);
        // Note: Mobile number is not displayed on the screen, so we don't test for it
      });

      testWidgets('should handle visits with empty product lists', (
        tester,
      ) async {
        final visitWithNoProducts = Visit(
          id: 1,
          pondId: 'POND001',
          doc: 30,
          createdDateTime: DateTime.now(),
          mobileNumber: '+919999999999',
          productUsed: '',
          farmerId: 'FARMER001',
          farmerName: 'Test Farmer',
        );

        final farmerWithEmptyProducts = Farmer(
          id: 1,
          name: 'Test Farmer',
          mobileNumber: '+919999999999',
          visits: [visitWithNoProducts],
        );

        await tester.pumpWidget(createTestWidget(farmerWithEmptyProducts));
        await tester.pumpAndSettle();

        // Verify screen renders without crashing
        expect(find.byType(FarmerDetailsScreen), findsOneWidget);
        expect(findAquaText('POND001'), findsOneWidget);
      });
    });

    group('Accessibility', () {
      testWidgets('should have proper semantic labels', (tester) async {
        await tester.pumpWidget(createTestWidget(testFarmer));
        await tester.pumpAndSettle();

        // Verify semantic labels are present for accessibility
        expect(find.byType(Semantics), findsWidgets);
      });

      testWidgets('should support screen readers', (tester) async {
        await tester.pumpWidget(createTestWidget(testFarmer));
        await tester.pumpAndSettle();

        // Verify important elements have semantic properties
        // Using a simple check since the deprecated API is being replaced
        expect(find.byType(FarmerDetailsScreen), findsOneWidget);
      });
    });

    group('Performance', () {
      testWidgets('should render efficiently with large visit history', (
        tester,
      ) async {
        // Create farmer with many visits
        final manyVisits = List.generate(
          100,
          (index) => Visit(
            id: index + 1,
            pondId: 'POND${index.toString().padLeft(3, '0')}',
            doc: 30 + index,
            createdDateTime: DateTime(2024, 1, 1).add(Duration(days: index)),
            mobileNumber: '+919999999999',
            productUsed: 'Product ${index + 1}',
            farmerId: 'FARMER001',
            farmerName: 'Farmer With Many Visits',
          ),
        );

        final farmerWithManyVisits = Farmer(
          id: 1,
          name: 'Farmer With Many Visits',
          mobileNumber: '+919999999999',
          visits: manyVisits,
        );

        final stopwatch = Stopwatch()..start();
        await tester.pumpWidget(createTestWidget(farmerWithManyVisits));
        await tester.pumpAndSettle();
        stopwatch.stop();

        // Verify reasonable render time (less than 1 second)
        expect(stopwatch.elapsedMilliseconds, lessThan(1000));
        expect(find.byType(FarmerDetailsScreen), findsOneWidget);
      });
    });
  });
}
