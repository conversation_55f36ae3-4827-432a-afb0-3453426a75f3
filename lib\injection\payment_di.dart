import 'package:get_it/get_it.dart';

import '../core/network/api_client.dart';
import '../core/network/network_info.dart';
import '../core/utils/logger.dart';
import '../data/datasources/remote/payment_remote_datasource.dart';
import '../data/repositories/payment_repository_impl.dart';
import '../domain/repositories/payment_repository.dart';
import '../domain/usecases/payments/create_payment_session_usecase.dart';
import '../domain/usecases/payments/get_customer_transactions_usecase.dart';
import '../domain/usecases/payments/verify_payment_usecase.dart';
import '../presentation/cubit/payments/payment_cubit.dart';

final sl = GetIt.instance;

/// Registers payment-related dependencies
void initPaymentDependencies() {
  // Data sources
  sl.registerLazySingleton<PaymentRemoteDataSource>(
    () => PaymentRemoteDataSourceImpl(
      apiClient: sl<ApiClient>(),
      logger: sl<AppLogger>(),
    ),
  );

  // Repositories
  sl.registerLazySingleton<PaymentRepository>(
    () => PaymentRepositoryImpl(
      remoteDataSource: sl<PaymentRemoteDataSource>(),
      networkInfo: sl<NetworkInfo>(),
      logger: sl<AppLogger>(),
    ),
  );

  // Use cases
  sl.registerLazySingleton(() => CreatePaymentSessionUseCase(sl<PaymentRepository>()));
  sl.registerLazySingleton(() => VerifyPaymentUseCase(sl<PaymentRepository>()));
  sl.registerLazySingleton(() => CheckPaymentStatusUseCase(sl<PaymentRepository>()));
  sl.registerLazySingleton(() => GetPaymentSessionUseCase(sl<PaymentRepository>()));
  sl.registerLazySingleton(() => GetCustomerTransactionsUseCase(sl<PaymentRepository>()));
  sl.registerLazySingleton(() => GetInvoiceTransactionsUseCase(sl<PaymentRepository>()));
  sl.registerLazySingleton(() => GetPaymentTransactionUseCase(sl<PaymentRepository>()));

  // Cubits
  sl.registerFactory(
    () => PaymentCubit(
      createPaymentSession: sl<CreatePaymentSessionUseCase>(),
      verifyPayment: sl<VerifyPaymentUseCase>(),
      checkPaymentStatus: sl<CheckPaymentStatusUseCase>(),
      logger: sl<AppLogger>(),
    ),
  );
}
