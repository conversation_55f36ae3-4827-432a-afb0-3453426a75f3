import 'package:dartz/dartz.dart';

import '../error/failures.dart';

/// A generic UseCase interface that defines the contract for all use cases.
/// 
/// [Type] is the return type of the use case
/// [Params] is the type of the parameters that the use case requires
abstract class UseCase<Type, Params> {
  Future<Either<Failure, Type>> call(Params params);
}

/// A special case for use cases that don't require any parameters.
class NoParams {}