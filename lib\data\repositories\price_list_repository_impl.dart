import 'package:dartz/dartz.dart';

import '../../core/error/exceptions.dart';
import '../../core/error/failures.dart';
import '../../core/network/network_info.dart';
import '../../core/utils/logger.dart';
import '../../domain/entities/price_list.dart';
import '../../domain/repositories/price_list_repository.dart';
import '../datasources/local/price_list_local_data_source.dart';
import '../datasources/remote/price_list_remote_data_source.dart';

class PriceListRepositoryImpl implements PriceListRepository {
  final PriceListRemoteDataSource remoteDataSource;
  final PriceListLocalDataSource localDataSource;
  final NetworkInfo networkInfo;
  final AppLogger logger;

  PriceListRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
    required this.networkInfo,
    required this.logger,
  });

  @override
  Future<Either<Failure, List<PriceList>>> getPriceLists() async {
    logger.i("PriceListRepositoryImpl: Getting price lists");

    try {
      // First check if we have cached data
      final hasCachedData = await localDataSource.hasPriceLists();

      if (hasCachedData) {
        logger.i("Using cached price lists");
        // Return cached data immediately
        final cachedResult = await _getCachedPriceLists();

        // If we have network, try to sync in the background without waiting
        if (await networkInfo.isConnected) {
          logger.i("Network available, will attempt background sync");
          // Don't await this to avoid blocking the UI
          _attemptBackgroundSync();
        }

        return cachedResult;
      }

      // If no cached data, try to fetch from remote
      if (await networkInfo.isConnected) {
        logger.i("No cache available, fetching from remote");
        return await _getRemotePriceLists();
      } else {
        logger.w("No network and no cached data available");
        return Left(CacheFailure());
      }
    } catch (e) {
      logger.e("Unexpected error getting price lists", e);
      return Left(ServerFailure());
    }
  }

  // New method for background sync
  Future<void> _attemptBackgroundSync() async {
    try {
      logger.i("Attempting background sync of price lists");
      await _getRemotePriceLists();
    } catch (e) {
      logger.e("Background sync failed", e);
      // Don't propagate the error since this is a background operation
    }
  }

  @override
  Future<Either<Failure, List<PriceList>>> syncPriceLists() async {
    logger.i("PriceListRepositoryImpl: Syncing price lists");

    if (await networkInfo.isConnected) {
      try {
        logger.i("Network available, fetching fresh data");
        return await _getRemotePriceLists();
      } on ServerException catch (_) {
        // If we have cached data, return it instead of failing
        final hasCachedData = await localDataSource.hasPriceLists();
        if (hasCachedData) {
          logger.i("Returning cached data due to sync failure");
          final cachedResult = await _getCachedPriceLists();

          // If the cached result is successful, return it with a warning
          return cachedResult.fold(
            (failure) => Left(ServerFailure()),
            (priceLists) => Right(priceLists), // Return cached data
          );
        }

        return Left(ServerFailure());
      } catch (e) {
        logger.e("Unexpected error syncing price lists", e);
        return Left(ServerFailure());
      }
    } else {
      logger.w("No network, cannot sync price lists");

      // If we have cached data, return it with a network warning
      final hasCachedData = await localDataSource.hasPriceLists();
      if (hasCachedData) {
        logger.i("Returning cached data due to no network");
        return await _getCachedPriceLists();
      }

      return Left(NetworkFailure());
    }
  }

  Future<Either<Failure, List<PriceList>>> _getCachedPriceLists() async {
    try {
      logger.i("Getting cached price lists");

      final priceLists = await localDataSource.getPriceLists();

      if (priceLists.isEmpty) {
        logger.w("No cached price lists found");
        return Left(CacheFailure());
      }

      logger.i("Found ${priceLists.length} cached price lists");
      return Right(priceLists.map((element) => element.toDomain()).toList());
    } on CacheException catch (e) {
      logger.e("Cache exception", e);
      return Left(CacheFailure());
    }
  }

  Future<Either<Failure, List<PriceList>>> _getRemotePriceLists() async {
    try {
      final remotePriceLists = await remoteDataSource.getPriceLists();

      // Save to local storage
      if (remotePriceLists.isNotEmpty) {
        await localDataSource.savePriceLists(remotePriceLists);
      } else {}

      return Right(
        remotePriceLists.map((element) => element.toDomain()).toList(),
      );
    } on ServerException catch (_) {
      return Left(ServerFailure());
    } catch (e) {
      return Left(ServerFailure());
    }
  }

  @override
  Future<Either<Failure, PriceList?>> getPriceListByState(String state) async {
    logger.i("PriceListRepositoryImpl: Getting price list by state: $state");

    try {
      // First try to get from local cache
      final cachedPriceList = await localDataSource.getPriceListByState(state);

      if (cachedPriceList != null) {
        logger.i("Found price list with state: $state in cache");
        return Right(cachedPriceList.toDomain());
      }

      // If not in cache and we have network, try to get from remote
      if (await networkInfo.isConnected) {
        logger.i("Price list not in cache, fetching from remote");
        try {
          final remotePriceLists = await remoteDataSource.getPriceLists();

          // Find the price list with the matching state
          final matchingPriceLists =
              remotePriceLists.where((pl) => pl.state == state).toList();

          if (matchingPriceLists.isNotEmpty) {
            final matchingPriceList = matchingPriceLists.first;
            // Save to cache
            await localDataSource.savePriceLists([matchingPriceList]);
            return Right(matchingPriceList.toDomain());
          } else {
            logger.w("Price list not found on server");
            return const Right(null);
          }
        } on ServerException catch (_) {
          return Left(ServerFailure());
        }
      } else {
        logger.w("No network and price list not in cache");
        return const Right(null);
      }
    } catch (e) {
      logger.e("Unexpected error getting price list by state", e);
      return Left(ServerFailure());
    }
  }
}
