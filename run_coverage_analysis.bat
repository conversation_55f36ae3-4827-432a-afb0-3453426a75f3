@echo off
REM Windows Batch Script for Flutter Coverage Analysis
REM This script runs the PowerShell coverage analysis tool

echo.
echo ========================================
echo Flutter Test Coverage Analysis
echo ========================================
echo.

REM Check if PowerShell is available
powershell -Command "Write-Host 'PowerShell is available'" >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: PowerShell is not available or not in PATH
    echo Please ensure PowerShell is installed and accessible
    pause
    exit /b 1
)

REM Check if Flutter is available
flutter --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Flutter is not available or not in PATH
    echo Please ensure Flutter is installed and accessible
    pause
    exit /b 1
)

echo Running coverage analysis...
echo.

REM Run the PowerShell script with parameters
powershell -ExecutionPolicy Bypass -File "scripts/coverage_analysis_windows.ps1" -GenerateHtml -Verbose

echo.
echo Coverage analysis completed!
echo.
echo To view detailed coverage:
echo 1. Install VS Code Coverage Gutters extension
echo 2. Open the project in VS Code
echo 3. Use Ctrl+Shift+P and search for "Coverage Gutters: Display Coverage"
echo 4. Or upload coverage/lcov.info to https://lcov-viewer.netlify.app/
echo.

pause
