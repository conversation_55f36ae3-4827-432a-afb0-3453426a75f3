/// Enum representing different update policies
enum UpdatePolicy {
  /// All updates (including patch and build number changes) are mandatory
  allUpdates,

  /// Major and minor version updates are mandatory, patch updates are recommended
  majorMinorUpdates,

  /// Only major version updates are mandatory, minor and patch are recommended
  majorUpdatesOnly,

  /// All updates are flexible (recommended but not mandatory)
  flexible;

  /// Convert a string to an UpdatePolicy
  static UpdatePolicy fromString(String value) {
    switch (value.toLowerCase()) {
      case 'all':
        return UpdatePolicy.allUpdates;
      case 'major_minor':
        return UpdatePolicy.majorMinorUpdates;
      case 'major_only':
        return UpdatePolicy.majorUpdatesOnly;
      case 'flexible':
        return UpdatePolicy.flexible;
      default:
        return UpdatePolicy.majorMinorUpdates; // Default policy
    }
  }

  /// Check if an update is mandatory based on version differences and policy
  bool isUpdateMandatory({
    required int currentMajor,
    required int currentMinor,
    required int currentPatch,
    required int currentBuild,
    required int requiredMajor,
    required int requiredMinor,
    required int requiredPatch,
    required int requiredBuild,
  }) {
    // If current version is higher than required, no update needed
    if (currentMajor > requiredMajor) {
      return false;
    }
    if (currentMajor == requiredMajor && currentMinor > requiredMinor) {
      return false;
    }
    if (currentMajor == requiredMajor &&
        currentMinor == requiredMinor &&
        currentPatch > requiredPatch) {
      return false;
    }
    if (currentMajor == requiredMajor &&
        currentMinor == requiredMinor &&
        currentPatch == requiredPatch &&
        currentBuild >= requiredBuild) {
      return false;
    }

    // MODIFIED: All updates are mandatory regardless of policy
    return true;

    /* Original implementation (commented out)
    // Apply policy based on which version components differ
    switch (this) {
      case UpdatePolicy.allUpdates:
        // All updates are mandatory
        return true;

      case UpdatePolicy.majorMinorUpdates:
        // Major and minor updates are mandatory
        return currentMajor < requiredMajor || currentMinor < requiredMinor;

      case UpdatePolicy.majorUpdatesOnly:
        // Only major updates are mandatory
        return currentMajor < requiredMajor;

      case UpdatePolicy.flexible:
        // No updates are mandatory
        return false;
    }
    */
  }
}
