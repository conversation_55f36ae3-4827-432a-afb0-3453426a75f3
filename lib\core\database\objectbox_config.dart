import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';
import '../../data/entities/farmer_visits/farmer_entity.dart';
import '../../data/models/account_statement/account_statement_models.dart';
import '../../data/models/customer_scheme_model.dart';
import '../../data/models/dashboard/dashboard_model.dart';
import '../../data/models/dues/dues_model.dart';
import '../../data/models/invoices/objectbox_invoice_model.dart';
import '../../data/models/objectbox_customer_model.dart';
import '../../data/models/objectbox_interested_product_model.dart';
import '../../data/models/objectbox_price_list_model.dart';
import '../../data/models/objectbox_product_catalogue_model.dart';
import '../../data/models/objectbox_smr_report_model.dart';
import '../../data/models/sales_order/sales_order_model.dart';
import '../../data/models/user_model.dart';
import '../../objectbox.g.dart';

class ObjectBox {
  late final Store store;
  late final Box<UserModel> userBox;
  late final Box<ObjectBoxPriceListModel> priceListBox;
  late final Box<ObjectBoxProductCatalogueModel> productCatalogueBox;
  late final Box<ObjectBoxInterestedProductModel> interestedProductBox;

  late final Box<ObjectBoxCustomerModel> customerModel;
  late final Box<DashboardModel> dashboardModel;
  late final Box<CustomerSchemeModel> customerSchemeModel;

  late final Box<AccountStatementModel> accountStatementModel;
  late final Box<FarmerEntity> farmerEntity;
  late final Box<ObjectBoxSMRReportModel> sMMRReportModel;
  late final Box<SalesOrderModel> salesOrderModel;
  late final Box<DuesInvoiceModel> duesInvoiceModel;
  late final Box<DuesAgingGroupModel> duesAgingGroupMOdel;
  late final Box<DuesSummaryModel> duesSummaryMOdel;
  late final Box<ObjectBoxInvoiceModel> invoiceModel;

  ObjectBox._create(this.store) {
    userBox = Box<UserModel>(store);
    priceListBox = Box<ObjectBoxPriceListModel>(store);
    productCatalogueBox = Box<ObjectBoxProductCatalogueModel>(store);
    interestedProductBox = Box<ObjectBoxInterestedProductModel>(store);

    customerModel = Box<ObjectBoxCustomerModel>(store);
    dashboardModel = Box<DashboardModel>(store);
    customerSchemeModel = Box<CustomerSchemeModel>(store);
    accountStatementModel = Box<AccountStatementModel>(store);
    farmerEntity = Box<FarmerEntity>(store);
    sMMRReportModel = Box<ObjectBoxSMRReportModel>(store);
    salesOrderModel = Box<SalesOrderModel>(store);
    duesInvoiceModel = Box<DuesInvoiceModel>(store);
    duesAgingGroupMOdel = Box<DuesAgingGroupModel>(store);
    duesSummaryMOdel = Box<DuesSummaryModel>(store);
    invoiceModel = Box<ObjectBoxInvoiceModel>(store);
  }

  static Future<ObjectBox> create() async {
    final docsDir = await getApplicationDocumentsDirectory();
    final store = await openStore(
      directory: p.join(docsDir.path, "objectbox"),
      maxDBSizeInKB: 1048576,
      maxDataSizeInKB: 2048,
      maxReaders: 4,
    );
    return ObjectBox._create(store);
  }
}
