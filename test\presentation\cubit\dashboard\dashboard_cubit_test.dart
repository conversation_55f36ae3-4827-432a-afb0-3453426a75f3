import 'dart:async';
import 'package:aquapartner/core/error/failures.dart';
import 'package:aquapartner/core/utils/logger.dart';
import 'package:aquapartner/domain/entities/customer.dart';
import 'package:aquapartner/domain/entities/dashboard/dashboard_entity.dart';
import 'package:aquapartner/domain/entities/sync_status.dart';
import 'package:aquapartner/domain/services/auth_service.dart';
import 'package:aquapartner/domain/services/dashboard_service.dart';
import 'package:aquapartner/domain/usecases/dashboard_usecases.dart';
import 'package:aquapartner/presentation/cubit/dashboard/dashboard_cubit.dart';
import 'package:aquapartner/presentation/cubit/dashboard/dashboard_state.dart';
import 'package:bloc_test/bloc_test.dart';
import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:mocktail/mocktail.dart';

import '../../../mocks/mock_analytics.dart';

// Mock classes
class MockGetDashboardUseCase extends Mock implements GetDashboardUseCase {}

class MockSyncDashboardUseCase extends Mock implements SyncDashboardUseCase {}

class MockGetDashboardSyncStatusUseCase extends Mock
    implements GetDashboardSyncStatusUseCase {}

class MockDashboardService extends Mock implements DashboardService {}

class MockInternetConnectionChecker extends Mock
    implements InternetConnectionChecker {}

class MockAuthService extends Mock implements AuthService {}

// Custom logger for testing
class TestAppLogger implements AppLogger {
  List<String> errorMessages = [];
  List<Object> errorObjects = [];
  List<String> infoMessages = [];
  List<String> debugMessages = [];
  List<String> warningMessages = [];

  @override
  void e(String message, [dynamic error, StackTrace? stackTrace]) {
    errorMessages.add(message);
    if (error != null) errorObjects.add(error);
  }

  @override
  void i(String message) {
    infoMessages.add(message);
  }

  @override
  void d(String message) {
    debugMessages.add(message);
  }

  @override
  void w(String message) {
    warningMessages.add(message);
  }

  @override
  void enableFirebaseVerboseLogging() {
    // No-op for testing
  }
}

void main() {
  group('DashboardCubit Tests', () {
    late MockGetDashboardUseCase mockGetDashboardUseCase;
    late MockSyncDashboardUseCase mockSyncDashboardUseCase;
    late MockGetDashboardSyncStatusUseCase mockGetSyncStatusUseCase;
    late MockDashboardService mockDashboardService;
    late MockInternetConnectionChecker mockConnectivityChecker;
    late MockAuthService mockAuthService;
    late MockAnalyticsService mockAnalyticsService;
    late TestAppLogger testLogger;
    late StreamController<SyncStatus> syncStatusController;

    const testCustomerId = 'test_customer_123';
    final testCustomer = Customer(
      customerId: testCustomerId,
      customerName: 'Test Customer',
      email: '<EMAIL>',
      mobileNumber: '+************',
      companyName: 'Test Company',
      gstNo: 'TEST123456789',
      businessVertical: 'Aquaculture',
      customerCode: 'TC123',
      billingAddress: 'Test Address, Test City, Test State - 123456',
    );
    final testDashboard = DashboardEntity(
      customerId: testCustomerId,
      sales: const SalesEntity(yearlyData: {}),
      payments: const PaymentsEntity(yearlyData: {}),
      dues: const [],
      salesReturn: 0.0,
      categoryTypeSales: const [],
      liquidation: const LiquidationEntity(
        totalLiquidation: 0.0,
        liquidationByYear: [],
      ),
      myFarmers: const MyFarmersEntity(totalFarmers: [], potentialFarmers: 0),
    );

    setUpAll(() {
      registerFallbackValue(DashboardInitial());
      registerFallbackValue(
        SyncStatus(
          customerId: testCustomerId,
          status: SyncStatusType.notStarted,
        ),
      );
    });

    setUp(() {
      mockGetDashboardUseCase = MockGetDashboardUseCase();
      mockSyncDashboardUseCase = MockSyncDashboardUseCase();
      mockGetSyncStatusUseCase = MockGetDashboardSyncStatusUseCase();
      mockDashboardService = MockDashboardService();
      mockConnectivityChecker = MockInternetConnectionChecker();
      mockAuthService = MockAuthService();
      mockAnalyticsService = MockAnalyticsService();
      testLogger = TestAppLogger();
      syncStatusController = StreamController<SyncStatus>.broadcast();

      // Setup default mock behaviors
      when(
        () => mockDashboardService.syncStatusStream,
      ).thenAnswer((_) => syncStatusController.stream);
      when(
        () => mockConnectivityChecker.hasConnection,
      ).thenAnswer((_) async => true);
      when(
        () => mockDashboardService.getLastSyncTime(any()),
      ).thenAnswer((_) async => DateTime.now());
      when(() => mockDashboardService.isSyncing(any())).thenReturn(false);
      when(
        () => mockAuthService.getCurrentCustomer(),
      ).thenAnswer((_) async => Right(testCustomer));

      // Setup default use case mocks
      when(
        () => mockGetDashboardUseCase.call(testCustomerId),
      ).thenAnswer((_) async => Right(testDashboard));
      when(
        () => mockSyncDashboardUseCase.call(testCustomerId),
      ).thenAnswer((_) async => Right(true));
    });

    tearDown(() {
      syncStatusController.close();
    });

    group('Cubit Creation and Initial State', () {
      test('should create cubit without errors', () async {
        final cubit = DashboardCubit(
          getDashboardUseCase: mockGetDashboardUseCase,
          syncDashboardUseCase: mockSyncDashboardUseCase,
          getSyncStatusUseCase: mockGetSyncStatusUseCase,
          dashboardService: mockDashboardService,
          logger: testLogger,
          connectivityChecker: mockConnectivityChecker,
          authService: mockAuthService,
          analyticsService: mockAnalyticsService,
        );

        expect(cubit.state, isA<DashboardState>());

        // Wait a bit for prefetch to complete before closing
        await Future.delayed(Duration(milliseconds: 200));
        cubit.close();
      });
    });

    group('Dashboard Data Loading', () {
      blocTest<DashboardCubit, DashboardState>(
        'should handle successful data loading',
        build:
            () => DashboardCubit(
              getDashboardUseCase: mockGetDashboardUseCase,
              syncDashboardUseCase: mockSyncDashboardUseCase,
              getSyncStatusUseCase: mockGetSyncStatusUseCase,
              dashboardService: mockDashboardService,
              logger: testLogger,
              connectivityChecker: mockConnectivityChecker,
              authService: mockAuthService,
              analyticsService: mockAnalyticsService,
            ),
        act: (cubit) async {
          // Wait a bit for prefetch to complete
          await Future.delayed(Duration(milliseconds: 100));
          cubit.loadDashboardData();
        },
        wait: Duration(milliseconds: 500),
        expect:
            () => [
              // Expect at least one state change
              isA<DashboardState>(),
            ],
        verify: (_) {
          // Verify that the use case was called
          verify(
            () => mockGetDashboardUseCase.call(testCustomerId),
          ).called(greaterThan(0));
        },
      );
    });

    group('Sync Operations', () {
      blocTest<DashboardCubit, DashboardState>(
        'should handle sync operations',
        build:
            () => DashboardCubit(
              getDashboardUseCase: mockGetDashboardUseCase,
              syncDashboardUseCase: mockSyncDashboardUseCase,
              getSyncStatusUseCase: mockGetSyncStatusUseCase,
              dashboardService: mockDashboardService,
              logger: testLogger,
              connectivityChecker: mockConnectivityChecker,
              authService: mockAuthService,
              analyticsService: mockAnalyticsService,
            ),
        act: (cubit) async {
          await Future.delayed(Duration(milliseconds: 100));
          cubit.syncDashboard();
        },
        wait: Duration(milliseconds: 500),
        expect:
            () => [
              // Expect prefetch state first
              isA<DashboardLoaded>(),
              // Then sync state (isSyncing: true)
              isA<DashboardLoaded>(),
            ],
        verify: (_) {
          // Verify that sync was attempted
          verify(
            () => mockSyncDashboardUseCase.call(testCustomerId),
          ).called(greaterThan(0));
        },
      );
    });

    group('Error Handling', () {
      blocTest<DashboardCubit, DashboardState>(
        'should handle errors gracefully',
        build: () {
          when(
            () => mockGetDashboardUseCase.call(testCustomerId),
          ).thenAnswer((_) async => Left(ServerFailure()));
          return DashboardCubit(
            getDashboardUseCase: mockGetDashboardUseCase,
            syncDashboardUseCase: mockSyncDashboardUseCase,
            getSyncStatusUseCase: mockGetSyncStatusUseCase,
            dashboardService: mockDashboardService,
            logger: testLogger,
            connectivityChecker: mockConnectivityChecker,
            authService: mockAuthService,
            analyticsService: mockAnalyticsService,
          );
        },
        act: (cubit) async {
          await Future.delayed(Duration(milliseconds: 100));
          cubit.loadDashboardData();
        },
        wait: Duration(milliseconds: 500),
        expect:
            () => [
              // Expect loading state first
              isA<DashboardLoading>(),
              // Then error state
              isA<DashboardError>(),
            ],
        verify: (_) {
          // Verify that error was logged
          expect(testLogger.errorMessages.isNotEmpty, isTrue);
        },
      );
    });

    group('Auto-Refresh', () {
      test('should enable and disable auto-refresh', () async {
        final cubit = DashboardCubit(
          getDashboardUseCase: mockGetDashboardUseCase,
          syncDashboardUseCase: mockSyncDashboardUseCase,
          getSyncStatusUseCase: mockGetSyncStatusUseCase,
          dashboardService: mockDashboardService,
          logger: testLogger,
          connectivityChecker: mockConnectivityChecker,
          authService: mockAuthService,
          analyticsService: mockAnalyticsService,
        );

        // Test enabling auto-refresh
        cubit.enableAutoRefresh(interval: Duration(seconds: 1));

        // Test disabling auto-refresh
        cubit.disableAutoRefresh();

        // Wait for prefetch to complete before closing
        await Future.delayed(Duration(milliseconds: 200));
        cubit.close();
      });

      blocTest<DashboardCubit, DashboardState>(
        'should trigger periodic refresh when auto-refresh is enabled',
        build:
            () => DashboardCubit(
              getDashboardUseCase: mockGetDashboardUseCase,
              syncDashboardUseCase: mockSyncDashboardUseCase,
              getSyncStatusUseCase: mockGetSyncStatusUseCase,
              dashboardService: mockDashboardService,
              logger: testLogger,
              connectivityChecker: mockConnectivityChecker,
              authService: mockAuthService,
              analyticsService: mockAnalyticsService,
            ),
        act: (cubit) async {
          await Future.delayed(Duration(milliseconds: 100));
          cubit.enableAutoRefresh(interval: Duration(milliseconds: 50));
          await Future.delayed(
            Duration(milliseconds: 150),
          ); // Wait for at least 2 timer triggers
        },
        wait: Duration(milliseconds: 300),
        expect:
            () => [
              isA<DashboardLoaded>(), // Initial prefetch
              predicate<DashboardLoaded>(
                (state) => state.isSyncing,
              ), // Auto-refresh trigger
            ],
        verify: (_) {
          // Should be called at least 1 time for auto-refresh
          verify(
            () => mockSyncDashboardUseCase.call(testCustomerId),
          ).called(greaterThan(0));
        },
      );
    });

    group('Pull-to-Refresh Functionality', () {
      blocTest<DashboardCubit, DashboardState>(
        'should handle pull-to-refresh without showing loading state',
        build:
            () => DashboardCubit(
              getDashboardUseCase: mockGetDashboardUseCase,
              syncDashboardUseCase: mockSyncDashboardUseCase,
              getSyncStatusUseCase: mockGetSyncStatusUseCase,
              dashboardService: mockDashboardService,
              logger: testLogger,
              connectivityChecker: mockConnectivityChecker,
              authService: mockAuthService,
              analyticsService: mockAnalyticsService,
            ),
        act: (cubit) async {
          await Future.delayed(Duration(milliseconds: 100));
          cubit.refreshDashboard();
        },
        wait: Duration(milliseconds: 500),
        expect:
            () => [
              isA<DashboardLoaded>(), // Initial prefetch
              isA<DashboardLoaded>(), // Refresh with sync indicator
            ],
        verify: (_) {
          verify(() => mockSyncDashboardUseCase.call(testCustomerId)).called(1);
        },
      );

      blocTest<DashboardCubit, DashboardState>(
        'should handle offline pull-to-refresh by loading from cache',
        build: () {
          when(
            () => mockConnectivityChecker.hasConnection,
          ).thenAnswer((_) async => false);
          return DashboardCubit(
            getDashboardUseCase: mockGetDashboardUseCase,
            syncDashboardUseCase: mockSyncDashboardUseCase,
            getSyncStatusUseCase: mockGetSyncStatusUseCase,
            dashboardService: mockDashboardService,
            logger: testLogger,
            connectivityChecker: mockConnectivityChecker,
            authService: mockAuthService,
            analyticsService: mockAnalyticsService,
          );
        },
        act: (cubit) async {
          await Future.delayed(Duration(milliseconds: 100));
          cubit.refreshDashboard();
        },
        wait: Duration(milliseconds: 500),
        expect:
            () => [
              predicate<DashboardLoaded>(
                (state) =>
                    state.hasError &&
                    (state.errorMessage == 'Offline mode - using cached data' ||
                        state.errorMessage == 'No internet connection'),
              ), // Initial prefetch with offline indicator
              predicate<DashboardLoaded>(
                (state) =>
                    state.hasError &&
                    (state.errorMessage == 'Offline mode - using cached data' ||
                        state.errorMessage == 'No internet connection'),
              ), // Refresh attempt with offline indicator
            ],
        verify: (_) {
          verify(
            () => mockGetDashboardUseCase.call(testCustomerId),
          ).called(greaterThan(0));
          verifyNever(() => mockSyncDashboardUseCase.call(any()));
        },
      );
    });

    group('One-Way Synchronization', () {
      blocTest<DashboardCubit, DashboardState>(
        'should clear local data and replace with server data during sync',
        build: () {
          return DashboardCubit(
            getDashboardUseCase: mockGetDashboardUseCase,
            syncDashboardUseCase: mockSyncDashboardUseCase,
            getSyncStatusUseCase: mockGetSyncStatusUseCase,
            dashboardService: mockDashboardService,
            logger: testLogger,
            connectivityChecker: mockConnectivityChecker,
            authService: mockAuthService,
            analyticsService: mockAnalyticsService,
          );
        },
        act: (cubit) async {
          await Future.delayed(Duration(milliseconds: 100));
          cubit.syncDashboard();

          // Simulate sync completion by emitting sync status
          await Future.delayed(Duration(milliseconds: 50));
          syncStatusController.add(
            SyncStatus(
              customerId: testCustomerId,
              status: SyncStatusType.completed,
            ),
          );
        },
        wait: Duration(milliseconds: 500),
        expect:
            () => [
              isA<DashboardLoaded>(), // Initial prefetch
              predicate<DashboardLoaded>(
                (state) => state.isSyncing,
              ), // Sync started (isSyncing: true)
              isA<DashboardLoaded>(), // Sync completed
            ],
        verify: (_) {
          verify(() => mockSyncDashboardUseCase.call(testCustomerId)).called(1);
          verify(
            () => mockGetDashboardUseCase.call(testCustomerId),
          ).called(greaterThan(0));
        },
      );

      blocTest<DashboardCubit, DashboardState>(
        'should handle sync failure and maintain existing data',
        build: () {
          when(
            () => mockSyncDashboardUseCase.call(testCustomerId),
          ).thenAnswer((_) async => Left(NetworkFailure()));

          return DashboardCubit(
            getDashboardUseCase: mockGetDashboardUseCase,
            syncDashboardUseCase: mockSyncDashboardUseCase,
            getSyncStatusUseCase: mockGetSyncStatusUseCase,
            dashboardService: mockDashboardService,
            logger: testLogger,
            connectivityChecker: mockConnectivityChecker,
            authService: mockAuthService,
            analyticsService: mockAnalyticsService,
          );
        },
        act: (cubit) async {
          await Future.delayed(Duration(milliseconds: 100));
          cubit.syncDashboard();
        },
        wait: Duration(milliseconds: 500),
        expect:
            () => [
              isA<DashboardLoaded>(), // Initial prefetch
              predicate<DashboardLoaded>(
                (state) => state.isSyncing,
              ), // Sync started
              predicate<DashboardLoaded>(
                (state) => !state.isSyncing && state.hasError,
              ), // Sync failed, data preserved with error
            ],
        verify: (_) {
          verify(() => mockSyncDashboardUseCase.call(testCustomerId)).called(1);
          expect(testLogger.errorMessages.isNotEmpty, isTrue);
        },
      );
    });

    group('Analytics Tracking', () {
      test('should set current user in analytics during prefetch', () async {
        final cubit = DashboardCubit(
          getDashboardUseCase: mockGetDashboardUseCase,
          syncDashboardUseCase: mockSyncDashboardUseCase,
          getSyncStatusUseCase: mockGetSyncStatusUseCase,
          dashboardService: mockDashboardService,
          logger: testLogger,
          connectivityChecker: mockConnectivityChecker,
          authService: mockAuthService,
          analyticsService: mockAnalyticsService,
        );

        // Wait for prefetch to complete
        await Future.delayed(Duration(milliseconds: 200));

        // Just verify the cubit was created successfully
        expect(cubit.state, isA<DashboardLoaded>());

        cubit.close();
      });

      blocTest<DashboardCubit, DashboardState>(
        'should track dashboard load events',
        build:
            () => DashboardCubit(
              getDashboardUseCase: mockGetDashboardUseCase,
              syncDashboardUseCase: mockSyncDashboardUseCase,
              getSyncStatusUseCase: mockGetSyncStatusUseCase,
              dashboardService: mockDashboardService,
              logger: testLogger,
              connectivityChecker: mockConnectivityChecker,
              authService: mockAuthService,
              analyticsService: mockAnalyticsService,
            ),
        act: (cubit) async {
          await Future.delayed(Duration(milliseconds: 100));
          cubit.loadDashboardData();
        },
        wait: Duration(milliseconds: 500),
        expect:
            () => [
              isA<DashboardLoaded>(), // Initial prefetch
            ],
        verify: (_) {
          // Verify dashboard load was called
          verify(
            () => mockGetDashboardUseCase.call(testCustomerId),
          ).called(greaterThan(0));
        },
      );
    });

    group('Connectivity Handling', () {
      blocTest<DashboardCubit, DashboardState>(
        'should show offline indicator when connectivity is lost',
        build: () {
          when(
            () => mockConnectivityChecker.hasConnection,
          ).thenAnswer((_) async => false);

          return DashboardCubit(
            getDashboardUseCase: mockGetDashboardUseCase,
            syncDashboardUseCase: mockSyncDashboardUseCase,
            getSyncStatusUseCase: mockGetSyncStatusUseCase,
            dashboardService: mockDashboardService,
            logger: testLogger,
            connectivityChecker: mockConnectivityChecker,
            authService: mockAuthService,
            analyticsService: mockAnalyticsService,
          );
        },
        act: (cubit) async {
          await Future.delayed(Duration(milliseconds: 100));
          cubit.loadDashboardData();
        },
        wait: Duration(milliseconds: 500),
        expect:
            () => [
              predicate<DashboardLoaded>(
                (state) =>
                    state.hasError &&
                    state.errorMessage == 'Offline mode - using cached data',
              ), // Initial prefetch with offline indicator
            ],
        verify: (_) {
          verify(
            () => mockGetDashboardUseCase.call(testCustomerId),
          ).called(greaterThan(0));
        },
      );

      blocTest<DashboardCubit, DashboardState>(
        'should prevent sync when offline',
        build: () {
          when(
            () => mockConnectivityChecker.hasConnection,
          ).thenAnswer((_) async => false);

          return DashboardCubit(
            getDashboardUseCase: mockGetDashboardUseCase,
            syncDashboardUseCase: mockSyncDashboardUseCase,
            getSyncStatusUseCase: mockGetSyncStatusUseCase,
            dashboardService: mockDashboardService,
            logger: testLogger,
            connectivityChecker: mockConnectivityChecker,
            authService: mockAuthService,
            analyticsService: mockAnalyticsService,
          );
        },
        act: (cubit) async {
          await Future.delayed(Duration(milliseconds: 100));
          cubit.syncDashboard();
        },
        wait: Duration(milliseconds: 500),
        expect:
            () => [
              predicate<DashboardLoaded>(
                (state) =>
                    state.hasError &&
                    (state.errorMessage == 'Offline mode - using cached data' ||
                        state.errorMessage == 'No internet connection'),
              ), // Initial prefetch with offline indicator
              predicate<DashboardLoaded>(
                (state) =>
                    state.hasError &&
                    (state.errorMessage == 'Offline mode - using cached data' ||
                        state.errorMessage == 'No internet connection'),
              ), // Sync attempt with offline indicator
            ],
        verify: (_) {
          // Sync should not be called when offline
          verifyNever(() => mockSyncDashboardUseCase.call(any()));
        },
      );
    });

    group('Edge Cases and Error Recovery', () {
      blocTest<DashboardCubit, DashboardState>(
        'should handle auth service failure gracefully',
        build: () {
          when(
            () => mockAuthService.getCurrentCustomer(),
          ).thenAnswer((_) async => Left(ServerFailure()));

          return DashboardCubit(
            getDashboardUseCase: mockGetDashboardUseCase,
            syncDashboardUseCase: mockSyncDashboardUseCase,
            getSyncStatusUseCase: mockGetSyncStatusUseCase,
            dashboardService: mockDashboardService,
            logger: testLogger,
            connectivityChecker: mockConnectivityChecker,
            authService: mockAuthService,
            analyticsService: mockAnalyticsService,
          );
        },
        act: (cubit) async {
          await Future.delayed(Duration(milliseconds: 200));
        },
        wait: Duration(milliseconds: 500),
        expect:
            () => [
              // Should remain in initial state due to auth failure
            ],
        verify: (_) {
          expect(testLogger.warningMessages.isNotEmpty, isTrue);
          verifyNever(() => mockGetDashboardUseCase.call(any()));
        },
      );

      blocTest<DashboardCubit, DashboardState>(
        'should handle multiple rapid sync requests',
        build: () {
          when(
            () => mockDashboardService.isSyncing(testCustomerId),
          ).thenReturn(true);

          return DashboardCubit(
            getDashboardUseCase: mockGetDashboardUseCase,
            syncDashboardUseCase: mockSyncDashboardUseCase,
            getSyncStatusUseCase: mockGetSyncStatusUseCase,
            dashboardService: mockDashboardService,
            logger: testLogger,
            connectivityChecker: mockConnectivityChecker,
            authService: mockAuthService,
            analyticsService: mockAnalyticsService,
          );
        },
        act: (cubit) async {
          await Future.delayed(Duration(milliseconds: 100));
          // Trigger multiple sync requests rapidly
          cubit.syncDashboard();
          cubit.syncDashboard();
          cubit.syncDashboard();
        },
        wait: Duration(milliseconds: 500),
        expect:
            () => [
              isA<DashboardLoaded>(), // Initial prefetch
            ],
        verify: (_) {
          // Should call sync multiple times for rapid requests
          verify(
            () => mockSyncDashboardUseCase.call(testCustomerId),
          ).called(greaterThan(0));
        },
      );

      test('should properly dispose resources on close', () async {
        final cubit = DashboardCubit(
          getDashboardUseCase: mockGetDashboardUseCase,
          syncDashboardUseCase: mockSyncDashboardUseCase,
          getSyncStatusUseCase: mockGetSyncStatusUseCase,
          dashboardService: mockDashboardService,
          logger: testLogger,
          connectivityChecker: mockConnectivityChecker,
          authService: mockAuthService,
          analyticsService: mockAnalyticsService,
        );

        // Enable auto-refresh to test timer cleanup
        cubit.enableAutoRefresh(interval: Duration(seconds: 1));

        // Wait for prefetch
        await Future.delayed(Duration(milliseconds: 200));

        // Close cubit
        await cubit.close();

        // Verify no memory leaks or hanging timers
        expect(cubit.isClosed, isTrue);
      });
    });
  });
}
