import 'package:flutter_test/flutter_test.dart';
import 'package:aquapartner/domain/entities/user.dart';

void main() {
  group('User Entity', () {
    group('Constructor and Properties', () {
      test('should create user with all required properties', () {
        // arrange
        final createdAt = DateTime(2024, 1, 1, 10, 0, 0);
        final updatedAt = DateTime(2024, 1, 2, 10, 0, 0);

        // act
        final user = User(
          id: 1,
          phoneNumber: '+919999999999',
          isVerified: true,
          createdAt: createdAt,
          updatedAt: updatedAt,
          needsSync: false,
        );

        // assert
        expect(user.id, 1);
        expect(user.mongoId, isNull);
        expect(user.phoneNumber, '+919999999999');
        expect(user.isVerified, true);
        expect(user.createdAt, createdAt);
        expect(user.updatedAt, updatedAt);
        expect(user.needsSync, false);
      });

      test('should create user with all properties including mongoId', () {
        // arrange
        final createdAt = DateTime(2024, 1, 1, 10, 0, 0);
        final updatedAt = DateTime(2024, 1, 2, 10, 0, 0);

        // act
        final user = User(
          id: 1,
          mongoId: 'mongo_123',
          phoneNumber: '+919999999999',
          isVerified: true,
          createdAt: createdAt,
          updatedAt: updatedAt,
          needsSync: true,
        );

        // assert
        expect(user.id, 1);
        expect(user.mongoId, 'mongo_123');
        expect(user.phoneNumber, '+919999999999');
        expect(user.isVerified, true);
        expect(user.createdAt, createdAt);
        expect(user.updatedAt, updatedAt);
        expect(user.needsSync, true);
      });

      test('should create user with default values', () {
        // arrange
        final createdAt = DateTime(2024, 1, 1, 10, 0, 0);
        final updatedAt = DateTime(2024, 1, 2, 10, 0, 0);

        // act
        final user = User(
          id: 1,
          phoneNumber: '+919999999999',
          createdAt: createdAt,
          updatedAt: updatedAt,
        );

        // assert
        expect(user.id, 1);
        expect(user.mongoId, isNull);
        expect(user.phoneNumber, '+919999999999');
        expect(user.isVerified, false); // default value
        expect(user.createdAt, createdAt);
        expect(user.updatedAt, updatedAt);
        expect(user.needsSync, true); // default value
      });
    });

    group('Equality', () {
      test('should be equal when all properties are the same', () {
        // arrange
        final createdAt = DateTime(2024, 1, 1, 10, 0, 0);
        final updatedAt = DateTime(2024, 1, 2, 10, 0, 0);

        final user1 = User(
          id: 1,
          mongoId: 'mongo_123',
          phoneNumber: '+919999999999',
          isVerified: true,
          createdAt: createdAt,
          updatedAt: updatedAt,
          needsSync: false,
        );

        final user2 = User(
          id: 1,
          mongoId: 'mongo_123',
          phoneNumber: '+919999999999',
          isVerified: true,
          createdAt: createdAt,
          updatedAt: updatedAt,
          needsSync: false,
        );

        // act & assert
        expect(user1, equals(user2));
        expect(user1.hashCode, equals(user2.hashCode));
      });

      test('should not be equal when IDs are different', () {
        // arrange
        final createdAt = DateTime(2024, 1, 1, 10, 0, 0);
        final updatedAt = DateTime(2024, 1, 2, 10, 0, 0);

        final user1 = User(
          id: 1,
          phoneNumber: '+919999999999',
          isVerified: true,
          createdAt: createdAt,
          updatedAt: updatedAt,
          needsSync: false,
        );

        final user2 = User(
          id: 2,
          phoneNumber: '+919999999999',
          isVerified: true,
          createdAt: createdAt,
          updatedAt: updatedAt,
          needsSync: false,
        );

        // act & assert
        expect(user1, isNot(equals(user2)));
      });

      test('should not be equal when phone numbers are different', () {
        // arrange
        final createdAt = DateTime(2024, 1, 1, 10, 0, 0);
        final updatedAt = DateTime(2024, 1, 2, 10, 0, 0);

        final user1 = User(
          id: 1,
          phoneNumber: '+919999999999',
          isVerified: true,
          createdAt: createdAt,
          updatedAt: updatedAt,
          needsSync: false,
        );

        final user2 = User(
          id: 1,
          phoneNumber: '+919876543211',
          isVerified: true,
          createdAt: createdAt,
          updatedAt: updatedAt,
          needsSync: false,
        );

        // act & assert
        expect(user1, isNot(equals(user2)));
      });

      test('should not be equal when verification status is different', () {
        // arrange
        final createdAt = DateTime(2024, 1, 1, 10, 0, 0);
        final updatedAt = DateTime(2024, 1, 2, 10, 0, 0);

        final user1 = User(
          id: 1,
          phoneNumber: '+919999999999',
          isVerified: true,
          createdAt: createdAt,
          updatedAt: updatedAt,
          needsSync: false,
        );

        final user2 = User(
          id: 1,
          phoneNumber: '+919999999999',
          isVerified: false,
          createdAt: createdAt,
          updatedAt: updatedAt,
          needsSync: false,
        );

        // act & assert
        expect(user1, isNot(equals(user2)));
      });

      test('should not be equal when mongoId is different', () {
        // arrange
        final createdAt = DateTime(2024, 1, 1, 10, 0, 0);
        final updatedAt = DateTime(2024, 1, 2, 10, 0, 0);

        final user1 = User(
          id: 1,
          mongoId: 'mongo_123',
          phoneNumber: '+919999999999',
          isVerified: true,
          createdAt: createdAt,
          updatedAt: updatedAt,
          needsSync: false,
        );

        final user2 = User(
          id: 1,
          mongoId: 'mongo_456',
          phoneNumber: '+919999999999',
          isVerified: true,
          createdAt: createdAt,
          updatedAt: updatedAt,
          needsSync: false,
        );

        // act & assert
        expect(user1, isNot(equals(user2)));
      });

      test('should not be equal when needsSync is different', () {
        // arrange
        final createdAt = DateTime(2024, 1, 1, 10, 0, 0);
        final updatedAt = DateTime(2024, 1, 2, 10, 0, 0);

        final user1 = User(
          id: 1,
          phoneNumber: '+919999999999',
          isVerified: true,
          createdAt: createdAt,
          updatedAt: updatedAt,
          needsSync: true,
        );

        final user2 = User(
          id: 1,
          phoneNumber: '+919999999999',
          isVerified: true,
          createdAt: createdAt,
          updatedAt: updatedAt,
          needsSync: false,
        );

        // act & assert
        expect(user1, isNot(equals(user2)));
      });
    });

    group('Edge Cases', () {
      test('should handle empty phone number', () {
        // arrange
        final createdAt = DateTime(2024, 1, 1, 10, 0, 0);
        final updatedAt = DateTime(2024, 1, 2, 10, 0, 0);

        // act
        final user = User(
          id: 1,
          phoneNumber: '',
          isVerified: false,
          createdAt: createdAt,
          updatedAt: updatedAt,
          needsSync: true,
        );

        // assert
        expect(user.phoneNumber, '');
      });

      test('should handle phone number with special characters', () {
        // arrange
        final createdAt = DateTime(2024, 1, 1, 10, 0, 0);
        final updatedAt = DateTime(2024, 1, 2, 10, 0, 0);

        // act
        final user = User(
          id: 1,
          phoneNumber: '+91-9876-543-210',
          isVerified: true,
          createdAt: createdAt,
          updatedAt: updatedAt,
          needsSync: false,
        );

        // assert
        expect(user.phoneNumber, '+91-9876-543-210');
      });

      test('should handle zero ID', () {
        // arrange
        final createdAt = DateTime(2024, 1, 1, 10, 0, 0);
        final updatedAt = DateTime(2024, 1, 2, 10, 0, 0);

        // act
        final user = User(
          id: 0,
          phoneNumber: '+919999999999',
          isVerified: false,
          createdAt: createdAt,
          updatedAt: updatedAt,
          needsSync: true,
        );

        // assert
        expect(user.id, 0);
      });

      test('should handle negative ID', () {
        // arrange
        final createdAt = DateTime(2024, 1, 1, 10, 0, 0);
        final updatedAt = DateTime(2024, 1, 2, 10, 0, 0);

        // act
        final user = User(
          id: -1,
          phoneNumber: '+919999999999',
          isVerified: false,
          createdAt: createdAt,
          updatedAt: updatedAt,
          needsSync: true,
        );

        // assert
        expect(user.id, -1);
      });

      test('should handle very large ID', () {
        // arrange
        final createdAt = DateTime(2024, 1, 1, 10, 0, 0);
        final updatedAt = DateTime(2024, 1, 2, 10, 0, 0);

        // act
        final user = User(
          id: 999999999,
          phoneNumber: '+919999999999',
          isVerified: true,
          createdAt: createdAt,
          updatedAt: updatedAt,
          needsSync: false,
        );

        // assert
        expect(user.id, 999999999);
      });

      test('should handle null mongoId', () {
        // arrange
        final createdAt = DateTime(2024, 1, 1, 10, 0, 0);
        final updatedAt = DateTime(2024, 1, 2, 10, 0, 0);

        // act
        final user = User(
          id: 1,
          mongoId: null,
          phoneNumber: '+919999999999',
          isVerified: true,
          createdAt: createdAt,
          updatedAt: updatedAt,
          needsSync: false,
        );

        // assert
        expect(user.mongoId, isNull);
      });

      test('should handle empty mongoId', () {
        // arrange
        final createdAt = DateTime(2024, 1, 1, 10, 0, 0);
        final updatedAt = DateTime(2024, 1, 2, 10, 0, 0);

        // act
        final user = User(
          id: 1,
          mongoId: '',
          phoneNumber: '+919999999999',
          isVerified: true,
          createdAt: createdAt,
          updatedAt: updatedAt,
          needsSync: false,
        );

        // assert
        expect(user.mongoId, '');
      });

      test('should handle same createdAt and updatedAt', () {
        // arrange
        final timestamp = DateTime(2024, 1, 1, 10, 0, 0);

        // act
        final user = User(
          id: 1,
          phoneNumber: '+919999999999',
          isVerified: true,
          createdAt: timestamp,
          updatedAt: timestamp,
          needsSync: false,
        );

        // assert
        expect(user.createdAt, timestamp);
        expect(user.updatedAt, timestamp);
        expect(user.createdAt, equals(user.updatedAt));
      });

      test('should handle updatedAt before createdAt', () {
        // arrange
        final createdAt = DateTime(2024, 1, 2, 10, 0, 0);
        final updatedAt = DateTime(2024, 1, 1, 10, 0, 0);

        // act
        final user = User(
          id: 1,
          phoneNumber: '+919999999999',
          isVerified: true,
          createdAt: createdAt,
          updatedAt: updatedAt,
          needsSync: false,
        );

        // assert
        expect(user.createdAt, createdAt);
        expect(user.updatedAt, updatedAt);
        expect(user.updatedAt.isBefore(user.createdAt), true);
      });
    });

    group('Business Logic Validation', () {
      test('should represent a new user correctly', () {
        // arrange
        final createdAt = DateTime.now();
        final updatedAt = DateTime.now();

        // act
        final newUser = User(
          id: 0, // typically 0 for new users
          phoneNumber: '+919999999999',
          isVerified: false, // new users are not verified
          createdAt: createdAt,
          updatedAt: updatedAt,
          needsSync: true, // new users need sync
        );

        // assert
        expect(newUser.id, 0);
        expect(newUser.isVerified, false);
        expect(newUser.needsSync, true);
        expect(newUser.mongoId, isNull);
      });

      test('should represent a verified user correctly', () {
        // arrange
        final createdAt = DateTime.now().subtract(Duration(days: 1));
        final updatedAt = DateTime.now();

        // act
        final verifiedUser = User(
          id: 1,
          mongoId: 'mongo_123',
          phoneNumber: '+919999999999',
          isVerified: true,
          createdAt: createdAt,
          updatedAt: updatedAt,
          needsSync: false,
        );

        // assert
        expect(verifiedUser.id, 1);
        expect(verifiedUser.isVerified, true);
        expect(verifiedUser.needsSync, false);
        expect(verifiedUser.mongoId, isNotNull);
      });

      test('should represent a user needing sync correctly', () {
        // arrange
        final createdAt = DateTime.now().subtract(Duration(days: 1));
        final updatedAt = DateTime.now();

        // act
        final userNeedingSync = User(
          id: 1,
          phoneNumber: '+919999999999',
          isVerified: true,
          createdAt: createdAt,
          updatedAt: updatedAt,
          needsSync: true,
        );

        // assert
        expect(userNeedingSync.needsSync, true);
        expect(userNeedingSync.isVerified, true);
      });
    });
  });
}
