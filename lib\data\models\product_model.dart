import '../../domain/entities/product.dart';

class ProductModel extends Product {
  ProductModel({
    required super.productName,
    required super.category,
    required super.categoryType,
    required super.subCategory,
    required super.tagLine,
    required super.productTag,
    required super.productImage,
    required super.content,
    required super.sortOrder,
    required super.status,
  });

  factory ProductModel.fromJson(Map<String, dynamic> json) {
    return ProductModel(
      productName: json['productName'] ?? '',
      category: json['category'] ?? '',
      categoryType: json['categoryType'] ?? '',
      subCategory: json['subCategory'] ?? '',
      tagLine: json['tagLine'] ?? '',
      productTag: json['productTag'] ?? '',
      productImage: json['productImage'] ?? '',
      content: json['content'] ?? '',
      sortOrder: json['sortOrder'] ?? '',
      status: json['status'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'productName': productName,
      'category': category,
      'categoryType': categoryType,
      'subCategory': subCategory,
      'tagLine': tagLine,
      'productTag': productTag,
      'productImage': productImage,
      'content': content,
      'sortOrder': sortOrder,
      'status': status,
    };
  }

  /// Converts this model to a domain entity
  Product toDomain() {
    return Product(
      productName: productName,
      category: category,
      categoryType: categoryType,
      subCategory: subCategory,
      tagLine: tagLine,
      productTag: productTag,
      productImage: productImage,
      content: content,
      sortOrder: sortOrder,
      status: status,
    );
  }
}
