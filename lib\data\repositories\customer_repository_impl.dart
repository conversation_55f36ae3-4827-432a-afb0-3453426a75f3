import 'package:aquapartner/core/error/failures.dart';
import 'package:aquapartner/data/models/customer_model.dart';
import 'package:aquapartner/domain/entities/customer.dart';
import 'package:dartz/dartz.dart';
import '../../core/network/network_info.dart';
import '../../core/utils/logger.dart';
import '../../domain/repositories/customer_repository.dart';
import '../datasources/local/customer_local_data_source.dart';
import '../datasources/remote/customer_remote_data_source.dart';
import '../../core/error/exceptions.dart';

class CustomerRepositoryImpl implements CustomerRepository {
  final CustomerRemoteDataSource remoteDataSource;
  final CustomerLocalDataSource localDataSource;
  final NetworkInfo networkInfo;
  final AppLogger logger;

  CustomerRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
    required this.networkInfo,
    required this.logger,
  });

  @override
  Future<Either<Failure, Customer>> getCustomerByMobileNumber(
    String mobileNumber,
  ) async {
    try {
      // First try to get data from local storage
      final localCustomer = await localDataSource.getCustomerByMobileNumber(
        mobileNumber,
      );

      // Check if we have internet connection
      final isConnected = await networkInfo.isConnected;

      if (isConnected) {
        try {
          // Try to fetch from remote

          final remoteCustomer = await remoteDataSource
              .getCustomerByMobileNumber(mobileNumber);

          if (remoteCustomer != null) {
            // Save to local storage
            await localDataSource.saveCustomer(remoteCustomer);
            return Right(remoteCustomer.toEntity());
          } else if (localCustomer != null) {
            // If remote returns null but we have local data, return local
            return Right(localCustomer.toEntity());
          } else {
            // No data found anywhere
            return Left(NotFoundFailure());
          }
        } on ServerException catch (e) {
          logger.e('ServerException occurred: ${e.toString()}');
          // If remote fetch fails, return local data if available
          if (localCustomer != null) {
            return Right(localCustomer.toEntity());
          }
          return Left(ServerFailure());
        } on NetworkException catch (_) {
          if (localCustomer != null) {
            return Right(localCustomer.toEntity());
          }
          return Left(NetworkFailure());
        }
      }

      // If no internet, return local data if available
      if (localCustomer != null) {
        return Right(localCustomer.toEntity());
      }

      return Left(NotFoundFailure());
    } on CacheException catch (e) {
      logger.e('CacheException occurred: ${e.toString()}');

      return Left(CacheFailure());
    } catch (e) {
      return Left(UnexpectedFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, Customer>> getCustomerByCustomerId(
    String customerId,
  ) async {
    try {
      // First try to get data from local storage
      final localCustomer = await localDataSource.getCustomerByCustomerId(
        customerId,
      );

      // Check if we have internet connection
      final isConnected = await networkInfo.isConnected;

      if (isConnected) {
        try {
          // Try to fetch from remote

          final remoteCustomer = await remoteDataSource.getCustomerByCustomerId(
            customerId,
          );

          if (remoteCustomer != null) {
            // Save to local storage
            await localDataSource.saveCustomer(remoteCustomer);
            return Right(remoteCustomer.toEntity());
          } else if (localCustomer != null) {
            // If remote returns null but we have local data, return local
            return Right(localCustomer.toEntity());
          } else {
            // No data found anywhere
            return Left(NotFoundFailure());
          }
        } on ServerException catch (e) {
          logger.e('ServerException occurred: ${e.toString()}');
          // If remote fetch fails, return local data if available
          if (localCustomer != null) {
            return Right(localCustomer.toEntity());
          }
          return Left(ServerFailure());
        } on NetworkException catch (_) {
          if (localCustomer != null) {
            return Right(localCustomer.toEntity());
          }
          return Left(NetworkFailure());
        }
      }

      // If no internet, return local data if available
      if (localCustomer != null) {
        return Right(localCustomer.toEntity());
      }

      return Left(NotFoundFailure());
    } on CacheException catch (e) {
      logger.e('CacheException occurred: ${e.toString()}');

      return Left(CacheFailure());
    } catch (e) {
      return Left(UnexpectedFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> saveCustomer(customer) async {
    try {
      await localDataSource.saveCustomer(CustomerModel.fromEntity(customer));
      return const Right(null);
    } on CacheException catch (e) {
      logger.e('CacheException occurred: ${e.toString()}');

      return Left(CacheFailure());
    } catch (e) {
      return Left(UnexpectedFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, Customer>> syncCustomerByCustomerId(
    String customerId,
  ) async {
    try {
      final isConnected = await networkInfo.isConnected;

      if (!isConnected) {
        return Left(NetworkFailure());
      }

      final remoteCustomer = await remoteDataSource.getCustomerByCustomerId(
        customerId,
      );

      if (remoteCustomer != null) {
        await localDataSource.saveCustomer(remoteCustomer);
        await updateLastSyncTime(DateTime.now());
        return Right(remoteCustomer.toEntity());
      }

      return Left(NotFoundFailure());
    } on ServerException catch (e) {
      logger.e('ServerException occurred: ${e.toString()}');

      return Left(ServerFailure());
    } on NetworkException catch (e) {
      logger.e('NetworkException occurred: ${e.toString()}');

      return Left(NetworkFailure());
    } on CacheException catch (e) {
      logger.e('CacheException occurred: ${e.toString()}');

      return Left(CacheFailure());
    } catch (e) {
      return Left(UnexpectedFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, DateTime?>> getLastSyncTime() async {
    try {
      final lastSyncTime = await localDataSource.getLastSyncTime();
      return Right(lastSyncTime);
    } on CacheException catch (e) {
      logger.e('CacheException occurred: ${e.toString()}');

      return Left(CacheFailure());
    } catch (e) {
      return Left(UnexpectedFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> updateLastSyncTime(DateTime time) async {
    try {
      await localDataSource.updateLastSyncTime(time);
      return const Right(null);
    } on CacheException catch (_) {
      return Left(CacheFailure());
    } catch (e) {
      return Left(UnexpectedFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> checkIfSyncNeeded() async {
    try {
      final lastSyncTime = await localDataSource.getLastSyncTime();

      // If never synced before, sync is needed
      if (lastSyncTime == null) {
        return const Right(true);
      }

      // Check if last sync was more than 24 hours ago
      final now = DateTime.now();
      final difference = now.difference(lastSyncTime);

      // If more than 24 hours, sync is needed
      if (difference.inHours > 24) {
        return const Right(true);
      }

      return const Right(false);
    } on CacheException catch (e) {
      logger.e('CacheException occurred: ${e.toString()}');
      return Left(CacheFailure());
    } catch (e) {
      return Left(UnexpectedFailure(e.toString()));
    }
  }
}
