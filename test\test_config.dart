import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:aquapartner/injection_container.dart' as di;

/// Global test configuration and setup
class TestConfig {
  static bool _isInitialized = false;

  /// Initialize test environment
  static Future<void> initialize() async {
    if (_isInitialized) return;

    TestWidgetsFlutterBinding.ensureInitialized();

    // Setup SharedPreferences mock
    SharedPreferences.setMockInitialValues({});

    // Setup method channel mocks
    _setupMethodChannelMocks();

    // Initialize dependency injection for tests
    await _initializeDependencyInjection();

    _isInitialized = true;
  }

  /// Setup method channel mocks for Firebase and other native services
  static void _setupMethodChannelMocks() {
    // Firebase Core
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMethodCallHandler(
          const MethodChannel('plugins.flutter.io/firebase_core'),
          (MethodCall methodCall) async {
            switch (methodCall.method) {
              case 'Firebase#initializeCore':
                return {
                  'name': '[DEFAULT]',
                  'options': {
                    'apiKey': 'test-api-key',
                    'appId': 'test-app-id',
                    'messagingSenderId': 'test-sender-id',
                    'projectId': 'test-project-id',
                  },
                  'pluginConstants': {},
                };
              case 'Firebase#initializeApp':
                return {
                  'name': methodCall.arguments['appName'],
                  'options': methodCall.arguments['options'],
                  'pluginConstants': {},
                };
              default:
                return null;
            }
          },
        );

    // Firebase Analytics
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMethodCallHandler(
          const MethodChannel('plugins.flutter.io/firebase_analytics'),
          (MethodCall methodCall) async {
            switch (methodCall.method) {
              case 'Analytics#logEvent':
                return null;
              case 'Analytics#setUserProperty':
                return null;
              case 'Analytics#setCurrentScreen':
                return null;
              case 'Analytics#setUserId':
                return null;
              default:
                return null;
            }
          },
        );

    // Firebase Auth
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMethodCallHandler(
          const MethodChannel('plugins.flutter.io/firebase_auth'),
          (MethodCall methodCall) async {
            switch (methodCall.method) {
              case 'Auth#registerIdTokenListener':
                return null;
              case 'Auth#registerAuthStateListener':
                return null;
              case 'Auth#signOut':
                return null;
              case 'Auth#verifyPhoneNumber':
                return null;
              case 'Auth#signInWithCredential':
                return {
                  'user': {
                    'uid': 'test-uid',
                    'email': null,
                    'phoneNumber': '+************',
                    'displayName': null,
                    'photoURL': null,
                    'emailVerified': false,
                    'isAnonymous': false,
                    'metadata': {
                      'creationTime': DateTime.now().millisecondsSinceEpoch,
                      'lastSignInTime': DateTime.now().millisecondsSinceEpoch,
                    },
                    'providerData': [],
                  },
                };
              default:
                return null;
            }
          },
        );

    // Connectivity Plus
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMethodCallHandler(
          const MethodChannel('dev.fluttercommunity.plus/connectivity'),
          (MethodCall methodCall) async {
            switch (methodCall.method) {
              case 'check':
                return ['wifi'];
              case 'wifiName':
                return 'Test WiFi';
              case 'wifiBSSID':
                return 'test-bssid';
              case 'wifiIPAddress':
                return '***********';
              default:
                return null;
            }
          },
        );

    // Path Provider
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMethodCallHandler(
          const MethodChannel('plugins.flutter.io/path_provider'),
          (MethodCall methodCall) async {
            switch (methodCall.method) {
              case 'getTemporaryDirectory':
                return '/tmp';
              case 'getApplicationDocumentsDirectory':
                return '/documents';
              case 'getApplicationSupportDirectory':
                return '/support';
              default:
                return null;
            }
          },
        );

    // Package Info Plus
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMethodCallHandler(
          const MethodChannel('dev.fluttercommunity.plus/package_info'),
          (MethodCall methodCall) async {
            switch (methodCall.method) {
              case 'getAll':
                return {
                  'appName': 'AquaPartner Test',
                  'packageName': 'com.aquaconnect.partner.test',
                  'version': '1.0.0',
                  'buildNumber': '1',
                  'buildSignature': 'test-signature',
                };
              default:
                return null;
            }
          },
        );

    // URL Launcher
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMethodCallHandler(
          const MethodChannel('plugins.flutter.io/url_launcher'),
          (MethodCall methodCall) async {
            switch (methodCall.method) {
              case 'canLaunch':
                return true;
              case 'launch':
                return true;
              default:
                return null;
            }
          },
        );

    // Geolocator
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMethodCallHandler(
          const MethodChannel('flutter.baseflow.com/geolocator'),
          (MethodCall methodCall) async {
            switch (methodCall.method) {
              case 'checkPermission':
                return 3; // LocationPermission.whileInUse
              case 'requestPermission':
                return 3; // LocationPermission.whileInUse
              case 'isLocationServiceEnabled':
                return true;
              case 'getCurrentPosition':
                return {
                  'latitude': 12.9716,
                  'longitude': 77.5946,
                  'timestamp': DateTime.now().millisecondsSinceEpoch,
                  'accuracy': 5.0,
                  'altitude': 920.0,
                  'heading': 0.0,
                  'speed': 0.0,
                  'speedAccuracy': 0.0,
                };
              default:
                return null;
            }
          },
        );
  }

  /// Initialize dependency injection for tests
  static Future<void> _initializeDependencyInjection() async {
    // Reset dependency injection if it has any registrations
    try {
      await di.sl.reset();
    } catch (e) {
      // If reset fails, it means there are no registrations yet
      // This is fine for tests
    }

    // Initialize with test dependencies
    // This would typically call a test-specific DI setup
    // For now, we'll just ensure it's ready for test registration
  }

  /// Clean up test environment
  static Future<void> cleanup() async {
    if (!_isInitialized) return;

    // Clean up dependency injection
    try {
      await di.sl.reset();
    } catch (e) {
      // If reset fails, it means there are no registrations
      // This is fine for cleanup
    }

    // Clear SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    await prefs.clear();

    _isInitialized = false;
  }

  /// Setup test data
  static Map<String, dynamic> getTestUserData() {
    return {
      'id': 'test_user_123',
      'name': 'Test User',
      'email': '<EMAIL>',
      'phone': '+************',
      'createdAt': DateTime.now().toIso8601String(),
    };
  }

  static Map<String, dynamic> getTestProductData() {
    return {
      'id': 'product_123',
      'productName': 'Test Product',
      'category': 'Test Category',
      'productImage': 'https://example.com/image.jpg',
      'content': '<p>Test product content</p>',
      'productTag': 'NEW ARRIVAL',
    };
  }

  static Map<String, dynamic> getTestFarmerData() {
    return {
      'id': 1,
      'name': 'Test Farmer',
      'phone': '+************',
      'location': 'Test Location',
      'visits': [],
    };
  }

  /// Test constants
  static const String testPhoneNumber = '+************';
  static const String testOtp = '123456';
  static const String testVerificationId = 'test_verification_id';
  static const String testUserId = 'test_user_123';
  static const String testProductId = 'product_123';
  static const String testFarmerId = 'farmer_123';
}
