import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'package:aquapartner/core/services/analytics_service.dart';
import 'package:aquapartner/core/utils/logger.dart';
import 'package:aquapartner/domain/entities/payments/payment_session.dart';
import 'package:aquapartner/domain/entities/payments/payment_transaction.dart';
import 'package:aquapartner/presentation/cubit/payments/payment_cubit.dart';
import 'package:aquapartner/presentation/cubit/payments/payment_state.dart';
import 'package:aquapartner/presentation/widgets/zoho_payment_button.dart';

import 'zoho_payment_button_test.mocks.dart';

@GenerateMocks([PaymentCubit, AppLogger, AnalyticsService])
void main() {
  late MockPaymentCubit mockPaymentCubit;
  late MockAppLogger mockLogger;
  late MockAnalyticsService mockAnalyticsService;

  setUp(() {
    mockPaymentCubit = MockPaymentCubit();
    mockLogger = MockAppLogger();
    mockAnalyticsService = MockAnalyticsService();

    // Register mocks in GetIt
    final getIt = GetIt.instance;
    if (getIt.isRegistered<PaymentCubit>()) {
      getIt.unregister<PaymentCubit>();
    }
    if (getIt.isRegistered<AnalyticsService>()) {
      getIt.unregister<AnalyticsService>();
    }
    getIt.registerFactory<PaymentCubit>(() => mockPaymentCubit);
    getIt.registerFactory<AnalyticsService>(() => mockAnalyticsService);
  });

  tearDown(() {
    GetIt.instance.reset();
  });

  Widget createWidgetUnderTest({
    required bool Function(bool, String?) onPaymentComplete,
  }) {
    return MaterialApp(
      home: Scaffold(
        body: ZohoPaymentButton(
          amount: 100.0,
          invoiceNumber: 'INV-001',
          customerId: 'CUST-001',
          description: 'Test payment',
          customerName: 'Test Customer',
          customerEmail: '<EMAIL>',
          customerPhone: '+1234567890',
          onPaymentComplete: onPaymentComplete,
          buttonText: 'Pay Now',
        ),
      ),
    );
  }

  group('ZohoPaymentButton Widget Tests', () {
    testWidgets('should display button with correct text', (tester) async {
      // arrange
      when(mockPaymentCubit.state).thenReturn(const PaymentInitial());
      when(mockPaymentCubit.stream).thenAnswer((_) => const Stream.empty());

      // act
      await tester.pumpWidget(
        createWidgetUnderTest(onPaymentComplete: (success, txnId) => true),
      );

      // assert
      expect(find.text('Pay Now'), findsOneWidget);
      expect(find.byType(ElevatedButton), findsOneWidget);
    });

    testWidgets('should show loading indicator when payment is loading', (
      tester,
    ) async {
      // arrange
      when(mockPaymentCubit.state).thenReturn(const PaymentLoading());
      when(mockPaymentCubit.stream).thenAnswer((_) => const Stream.empty());

      // act
      await tester.pumpWidget(
        createWidgetUnderTest(onPaymentComplete: (success, txnId) => true),
      );

      // assert
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text('Pay Now'), findsNothing);
    });

    testWidgets('should show processing status when payment is processing', (
      tester,
    ) async {
      // arrange
      when(mockPaymentCubit.state).thenReturn(
        PaymentProcessing(
          sessionId: 'PS_123456789',
          currentStatus: PaymentSessionStatus.pending,
        ),
      );
      when(mockPaymentCubit.stream).thenAnswer((_) => const Stream.empty());

      // act
      await tester.pumpWidget(
        createWidgetUnderTest(onPaymentComplete: (success, txnId) => true),
      );

      // assert
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text('Payment is being processed...'), findsOneWidget);
    });

    testWidgets('should call createPaymentSession when button is tapped', (
      tester,
    ) async {
      // arrange
      when(mockPaymentCubit.state).thenReturn(const PaymentInitial());
      when(mockPaymentCubit.stream).thenAnswer((_) => const Stream.empty());

      // act
      await tester.pumpWidget(
        createWidgetUnderTest(onPaymentComplete: (success, txnId) => true),
      );

      await tester.tap(find.byType(ElevatedButton));
      await tester.pump();

      // assert
      verify(mockPaymentCubit.createPaymentSession(any)).called(1);
    });

    testWidgets(
      'should not call createPaymentSession when button is disabled (loading)',
      (tester) async {
        // arrange
        when(mockPaymentCubit.state).thenReturn(const PaymentLoading());
        when(mockPaymentCubit.stream).thenAnswer((_) => const Stream.empty());

        // act
        await tester.pumpWidget(
          createWidgetUnderTest(onPaymentComplete: (success, txnId) => true),
        );

        await tester.tap(find.byType(ElevatedButton));
        await tester.pump();

        // assert
        verifyNever(mockPaymentCubit.createPaymentSession(any));
      },
    );

    testWidgets('should show success snackbar when payment succeeds', (
      tester,
    ) async {
      // arrange
      final paymentTransaction = PaymentTransaction(
        transactionId: 'TXN_123456789',
        sessionId: 'PS_123456789',
        invoiceNumber: 'INV-001',
        customerId: 'CUST-001',
        amount: 100.0,
        currency: 'INR',
        status: PaymentTransactionStatus.success,
        paymentMethod: PaymentMethod.creditCard,
        transactionDate: DateTime(2024, 1, 1),
      );

      when(mockPaymentCubit.state).thenReturn(const PaymentInitial());
      when(mockPaymentCubit.stream).thenAnswer(
        (_) => Stream.fromIterable([
          PaymentSuccess(transaction: paymentTransaction),
        ]),
      );

      bool paymentCompleted = false;
      String? transactionId;

      // act
      await tester.pumpWidget(
        createWidgetUnderTest(
          onPaymentComplete: (success, txnId) {
            paymentCompleted = success;
            transactionId = txnId;
            return true;
          },
        ),
      );

      await tester.pump(); // Trigger the stream event

      // assert
      expect(find.text('Payment completed successfully!'), findsOneWidget);
      expect(paymentCompleted, isTrue);
      expect(transactionId, equals('TXN_123456789'));
    });

    testWidgets('should show error snackbar when payment fails', (
      tester,
    ) async {
      // arrange
      when(mockPaymentCubit.state).thenReturn(const PaymentInitial());
      when(mockPaymentCubit.stream).thenAnswer(
        (_) => Stream.fromIterable([
          const PaymentFailed(
            sessionId: 'PS_123456789',
            reason: 'Payment processing failed',
          ),
        ]),
      );

      bool paymentCompleted = true;
      String? transactionId = 'should_be_null';

      // act
      await tester.pumpWidget(
        createWidgetUnderTest(
          onPaymentComplete: (success, txnId) {
            paymentCompleted = success;
            transactionId = txnId;
            return true;
          },
        ),
      );

      await tester.pump(); // Trigger the stream event

      // assert
      expect(
        find.text('Payment failed: Payment processing failed'),
        findsOneWidget,
      );
      expect(paymentCompleted, isFalse);
      expect(transactionId, isNull);
    });

    testWidgets('should show error snackbar when payment expires', (
      tester,
    ) async {
      // arrange
      when(mockPaymentCubit.state).thenReturn(const PaymentInitial());
      when(mockPaymentCubit.stream).thenAnswer(
        (_) => Stream.fromIterable([
          const PaymentExpired(sessionId: 'PS_123456789'),
        ]),
      );

      bool paymentCompleted = true;
      String? transactionId = 'should_be_null';

      // act
      await tester.pumpWidget(
        createWidgetUnderTest(
          onPaymentComplete: (success, txnId) {
            paymentCompleted = success;
            transactionId = txnId;
            return true;
          },
        ),
      );

      await tester.pump(); // Trigger the stream event

      // assert
      expect(
        find.text('Payment session expired. Please try again.'),
        findsOneWidget,
      );
      expect(paymentCompleted, isFalse);
      expect(transactionId, isNull);
    });

    testWidgets('should call resetPayment on dispose', (tester) async {
      // arrange
      when(mockPaymentCubit.state).thenReturn(const PaymentInitial());
      when(mockPaymentCubit.stream).thenAnswer((_) => const Stream.empty());

      // act
      await tester.pumpWidget(
        createWidgetUnderTest(onPaymentComplete: (success, txnId) => true),
      );

      // Remove the widget to trigger dispose
      await tester.pumpWidget(const MaterialApp(home: Scaffold()));

      // assert
      verify(mockPaymentCubit.resetPayment()).called(1);
    });

    testWidgets('should handle payment cancellation', (tester) async {
      // arrange
      when(mockPaymentCubit.state).thenReturn(const PaymentInitial());
      when(mockPaymentCubit.stream).thenAnswer(
        (_) => Stream.fromIterable([
          const PaymentCancelled(sessionId: 'PS_123456789'),
        ]),
      );

      bool paymentCompleted = true;
      String? transactionId = 'should_be_null';

      // act
      await tester.pumpWidget(
        createWidgetUnderTest(
          onPaymentComplete: (success, txnId) {
            paymentCompleted = success;
            transactionId = txnId;
            return true;
          },
        ),
      );

      await tester.pump(); // Trigger the stream event

      // assert
      expect(paymentCompleted, isFalse);
      expect(transactionId, isNull);
    });

    testWidgets('should handle generic payment error', (tester) async {
      // arrange
      when(mockPaymentCubit.state).thenReturn(const PaymentInitial());
      when(mockPaymentCubit.stream).thenAnswer(
        (_) => Stream.fromIterable([
          const PaymentError(message: 'Network error occurred'),
        ]),
      );

      bool paymentCompleted = true;
      String? transactionId = 'should_be_null';

      // act
      await tester.pumpWidget(
        createWidgetUnderTest(
          onPaymentComplete: (success, txnId) {
            paymentCompleted = success;
            transactionId = txnId;
            return true;
          },
        ),
      );

      await tester.pump(); // Trigger the stream event

      // assert
      expect(find.text('Network error occurred'), findsOneWidget);
      expect(paymentCompleted, isFalse);
      expect(transactionId, isNull);
    });
  });
}
