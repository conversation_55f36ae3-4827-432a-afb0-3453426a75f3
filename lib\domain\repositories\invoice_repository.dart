import 'package:dartz/dartz.dart';

import '../../../core/error/failures.dart';
import '../entities/invoices/invoice.dart';
import '../entities/sync_status.dart';

abstract class InvoiceRepository {
  Future<Either<Failure, List<Invoice>>> getInvoices(String customerId);
  Future<Either<Failure, SyncStatus>> syncInvoices(String customerId);
  Future<Either<Failure, bool>> checkIfSyncNeeded();
  Future<Either<Failure, Invoice>> getInvoiceById(String invoiceId);
}
