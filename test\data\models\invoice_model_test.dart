import 'package:flutter_test/flutter_test.dart';
import 'package:aquapartner/data/models/invoices/invoice_model.dart';
import 'package:aquapartner/data/models/invoices/invoice_item_model.dart';

void main() {
  group('InvoiceModel Tests', () {
    late InvoiceModel testInvoiceModel;
    late InvoiceItemModel testInvoiceItemModel;
    late Map<String, dynamic> testJson;
    late DateTime testDateTime;

    setUp(() {
      testDateTime = DateTime(2024, 1, 15, 10, 30, 0);

      testInvoiceItemModel = InvoiceItemModel(
        id: 'item_123',
        productId: 'product_123',
        itemName: 'Test Product',
        quantity: 2.0,
        invoiceId: 'invoice_123',
        createdTime: testDateTime,
        discountAmount: 100.0,
        hsnSac: '1234567890',
        itemPrice: 1000.0,
        lastModifiedTime: testDateTime,
        placeOfSupply: 'Tamil Nadu',
        productCategory: 'Feed',
        source: 'direct',
        subTotal: 2000.0,
        total: 1900.0,
      );

      testInvoiceModel = InvoiceModel(
        invoiceId: 'invoice_123',
        addressId: 'addr_123',
        ageInDays: 30,
        ageTier: '0-30 days',
        balance: 1900.0,
        customerId: 'customer_123',
        deliveryMode: 'courier',
        deliveryStatus: 'pending',
        dueDate: testDateTime.add(Duration(days: 30)),
        invoiceDate: testDateTime,
        invoiceNumber: 'INV-2024-001',
        invoiceStatus: 'pending',
        subTotal: 2000.0,
        total: 1900.0,
        items: [testInvoiceItemModel],
      );

      testJson = {
        'invoiceId': 'invoice_123',
        'addressId': 'addr_123',
        'ageInDays': 30,
        'ageTier': '0-30 days',
        'balance': 1900.0,
        'customerId': 'customer_123',
        'deliveryMode': 'courier',
        'deliveryStatus': 'pending',
        'dueDate': testDateTime.add(Duration(days: 30)).toIso8601String(),
        'invoiceDate': testDateTime.toIso8601String(),
        'invoiceNumber': 'INV-2024-001',
        'invoiceStatus': 'pending',
        'subTotal': 2000.0,
        'total': 1900.0,
        'items': [
          {
            '_id': 'item_123',
            'productId': 'product_123',
            'itemName': 'Test Product',
            'quantity': 2.0,
            'invoiceId': 'invoice_123',
            'createdTime': testDateTime.toIso8601String(),
            'discountAmount': 100.0,
            'hsnSac': '1234567890',
            'itemPrice': 1000.0,
            'lastModifiedTime': testDateTime.toIso8601String(),
            'placeOfSupply': 'Tamil Nadu',
            'productCategory': 'Feed',
            'source': 'direct',
            'subTotal': 2000.0,
            'total': 1900.0,
          },
        ],
      };
    });

    group('Constructor Tests', () {
      test('should create InvoiceModel with all required fields', () {
        expect(testInvoiceModel.invoiceId, equals('invoice_123'));
        expect(testInvoiceModel.addressId, equals('addr_123'));
        expect(testInvoiceModel.ageInDays, equals(30));
        expect(testInvoiceModel.ageTier, equals('0-30 days'));
        expect(testInvoiceModel.balance, equals(1900.0));
        expect(testInvoiceModel.customerId, equals('customer_123'));
        expect(testInvoiceModel.deliveryMode, equals('courier'));
        expect(testInvoiceModel.deliveryStatus, equals('pending'));
        expect(
          testInvoiceModel.dueDate,
          equals(testDateTime.add(Duration(days: 30))),
        );
        expect(testInvoiceModel.invoiceDate, equals(testDateTime));
        expect(testInvoiceModel.invoiceNumber, equals('INV-2024-001'));
        expect(testInvoiceModel.invoiceStatus, equals('pending'));
        expect(testInvoiceModel.subTotal, equals(2000.0));
        expect(testInvoiceModel.total, equals(1900.0));
        expect(testInvoiceModel.items, hasLength(1));
        expect(testInvoiceModel.items.first, isA<InvoiceItemModel>());
      });

      test('should create InvoiceModel with empty items list', () {
        final emptyInvoiceModel = InvoiceModel(
          invoiceId: 'invoice_123',
          addressId: 'addr_123',
          ageInDays: 0,
          ageTier: '',
          balance: 0.0,
          customerId: 'customer_123',
          deliveryMode: '',
          deliveryStatus: '',
          dueDate: testDateTime,
          invoiceDate: testDateTime,
          invoiceNumber: '',
          invoiceStatus: '',
          subTotal: 0.0,
          total: 0.0,
          items: [],
        );

        expect(emptyInvoiceModel.items, isEmpty);
        expect(emptyInvoiceModel.invoiceId, equals('invoice_123'));
        expect(emptyInvoiceModel.customerId, equals('customer_123'));
        expect(emptyInvoiceModel.balance, equals(0.0));
        expect(emptyInvoiceModel.subTotal, equals(0.0));
        expect(emptyInvoiceModel.total, equals(0.0));
      });
    });

    group('JSON Serialization Tests', () {
      test('should convert InvoiceModel to JSON correctly', () {
        // Create a model from JSON to ensure proper item types
        final invoiceModelFromJson = InvoiceModel.fromJson(testJson);
        final json = invoiceModelFromJson.toJson();

        expect(json['invoiceId'], equals('invoice_123'));
        expect(json['addressId'], equals('addr_123'));
        expect(json['ageInDays'], equals(30));
        expect(json['ageTier'], equals('0-30 days'));
        expect(json['balance'], equals(1900.0));
        expect(json['customerId'], equals('customer_123'));
        expect(json['deliveryMode'], equals('courier'));
        expect(json['deliveryStatus'], equals('pending'));
        expect(
          json['dueDate'],
          equals(testDateTime.add(Duration(days: 30)).toIso8601String()),
        );
        expect(json['invoiceDate'], equals(testDateTime.toIso8601String()));
        expect(json['invoiceNumber'], equals('INV-2024-001'));
        expect(json['invoiceStatus'], equals('pending'));
        expect(json['subTotal'], equals(2000.0));
        expect(json['total'], equals(1900.0));
        expect(json['items'], isA<List>());
        expect(json['items'], hasLength(1));
      });

      test('should create InvoiceModel from JSON correctly', () {
        final invoiceModel = InvoiceModel.fromJson(testJson);

        expect(invoiceModel.invoiceId, equals('invoice_123'));
        expect(invoiceModel.addressId, equals('addr_123'));
        expect(invoiceModel.ageInDays, equals(30));
        expect(invoiceModel.ageTier, equals('0-30 days'));
        expect(invoiceModel.balance, equals(1900.0));
        expect(invoiceModel.customerId, equals('customer_123'));
        expect(invoiceModel.deliveryMode, equals('courier'));
        expect(invoiceModel.deliveryStatus, equals('pending'));
        expect(
          invoiceModel.dueDate,
          equals(testDateTime.add(Duration(days: 30))),
        );
        expect(invoiceModel.invoiceDate, equals(testDateTime));
        expect(invoiceModel.invoiceNumber, equals('INV-2024-001'));
        expect(invoiceModel.invoiceStatus, equals('pending'));
        expect(invoiceModel.subTotal, equals(2000.0));
        expect(invoiceModel.total, equals(1900.0));
        expect(invoiceModel.items, hasLength(1));
        expect(invoiceModel.items.first, isA<InvoiceItemModel>());
      });

      test('should handle missing JSON fields with defaults', () {
        final incompleteJson = <String, dynamic>{
          'invoiceId': 'invoice_123',
          'customerId': 'customer_123',
        };

        final invoiceModel = InvoiceModel.fromJson(incompleteJson);

        expect(invoiceModel.invoiceId, equals('invoice_123'));
        expect(invoiceModel.customerId, equals('customer_123'));
        expect(invoiceModel.addressId, isEmpty);
        expect(invoiceModel.ageInDays, equals(0));
        expect(invoiceModel.ageTier, isEmpty);
        expect(invoiceModel.balance, equals(0.0));
        expect(invoiceModel.deliveryMode, isEmpty);
        expect(invoiceModel.deliveryStatus, isEmpty);
        expect(invoiceModel.invoiceNumber, isEmpty);
        expect(invoiceModel.invoiceStatus, isEmpty);
        expect(invoiceModel.subTotal, equals(0.0));
        expect(invoiceModel.total, equals(0.0));
        expect(invoiceModel.items, isEmpty);
        expect(invoiceModel.dueDate, isA<DateTime>());
        expect(invoiceModel.invoiceDate, isA<DateTime>());
      });

      test('should handle null date fields gracefully', () {
        final jsonWithNullDates = <String, dynamic>{
          'invoiceId': 'invoice_123',
          'customerId': 'customer_123',
          'dueDate': null,
          'invoiceDate': null,
        };

        final invoiceModel = InvoiceModel.fromJson(jsonWithNullDates);

        expect(invoiceModel.invoiceId, equals('invoice_123'));
        expect(invoiceModel.customerId, equals('customer_123'));
        expect(invoiceModel.dueDate, isA<DateTime>());
        expect(invoiceModel.invoiceDate, isA<DateTime>());
      });

      test('should handle empty items array', () {
        final jsonWithEmptyItems = <String, dynamic>{
          'invoiceId': 'invoice_123',
          'customerId': 'customer_123',
          'items': [],
        };

        final invoiceModel = InvoiceModel.fromJson(jsonWithEmptyItems);

        expect(invoiceModel.items, isEmpty);
      });

      test('should handle null items array', () {
        final jsonWithNullItems = <String, dynamic>{
          'invoiceId': 'invoice_123',
          'customerId': 'customer_123',
          'items': null,
        };

        final invoiceModel = InvoiceModel.fromJson(jsonWithNullItems);

        expect(invoiceModel.items, isEmpty);
      });
    });

    group('Edge Cases and Validation Tests', () {
      test('should handle special characters in string fields', () {
        final specialCharJson = {
          'invoiceId': 'invoice_123 & special',
          'addressId': 'addr_123 (special)',
          'ageTier': '0-30 days (current)',
          'customerId': 'customer_123-special',
          'deliveryMode': 'courier & express',
          'deliveryStatus': 'pending/processing',
          'invoiceNumber': 'INV-2024-001 & revised',
          'invoiceStatus': 'pending/approved',
          'dueDate': testDateTime.toIso8601String(),
          'invoiceDate': testDateTime.toIso8601String(),
          'items': [],
        };

        final invoiceModel = InvoiceModel.fromJson(specialCharJson);

        expect(invoiceModel.invoiceId, equals('invoice_123 & special'));
        expect(invoiceModel.addressId, equals('addr_123 (special)'));
        expect(invoiceModel.ageTier, equals('0-30 days (current)'));
        expect(invoiceModel.customerId, equals('customer_123-special'));
        expect(invoiceModel.deliveryMode, equals('courier & express'));
        expect(invoiceModel.deliveryStatus, equals('pending/processing'));
        expect(invoiceModel.invoiceNumber, equals('INV-2024-001 & revised'));
        expect(invoiceModel.invoiceStatus, equals('pending/approved'));
      });

      test('should handle extreme numeric values', () {
        final extremeJson = {
          'invoiceId': 'invoice_123',
          'customerId': 'customer_123',
          'ageInDays': 999999,
          'balance': double.maxFinite,
          'subTotal': double.maxFinite,
          'total': double.maxFinite,
          'dueDate': testDateTime.toIso8601String(),
          'invoiceDate': testDateTime.toIso8601String(),
          'items': [],
        };

        final invoiceModel = InvoiceModel.fromJson(extremeJson);

        expect(invoiceModel.ageInDays, equals(999999));
        expect(invoiceModel.balance, equals(double.maxFinite));
        expect(invoiceModel.subTotal, equals(double.maxFinite));
        expect(invoiceModel.total, equals(double.maxFinite));
      });

      test('should handle negative numeric values', () {
        final negativeJson = {
          'invoiceId': 'invoice_123',
          'customerId': 'customer_123',
          'ageInDays': -30,
          'balance': -1900.0,
          'subTotal': -2000.0,
          'total': -1900.0,
          'dueDate': testDateTime.toIso8601String(),
          'invoiceDate': testDateTime.toIso8601String(),
          'items': [],
        };

        final invoiceModel = InvoiceModel.fromJson(negativeJson);

        expect(invoiceModel.ageInDays, equals(-30));
        expect(invoiceModel.balance, equals(-1900.0));
        expect(invoiceModel.subTotal, equals(-2000.0));
        expect(invoiceModel.total, equals(-1900.0));
      });

      test('should handle round-trip JSON serialization', () {
        // Use the JSON test data for round-trip testing
        final recreatedModel = InvoiceModel.fromJson(testJson);
        final finalJson = recreatedModel.toJson();

        expect(finalJson['invoiceId'], equals(testJson['invoiceId']));
        expect(finalJson['customerId'], equals(testJson['customerId']));
        expect(finalJson['balance'], equals(testJson['balance']));
        expect(finalJson['subTotal'], equals(testJson['subTotal']));
        expect(finalJson['total'], equals(testJson['total']));
        expect(finalJson['dueDate'], equals(testJson['dueDate']));
        expect(finalJson['invoiceDate'], equals(testJson['invoiceDate']));
        expect(finalJson['items'], hasLength(testJson['items'].length));
      });

      test('should handle string to double conversion for numeric fields', () {
        final stringNumericJson = {
          'invoiceId': 'invoice_123',
          'customerId': 'customer_123',
          'ageInDays': 30,
          'balance': 1900.50,
          'subTotal': 2000.75,
          'total': 1900.25,
          'dueDate': testDateTime.toIso8601String(),
          'invoiceDate': testDateTime.toIso8601String(),
          'items': [],
        };

        final invoiceModel = InvoiceModel.fromJson(stringNumericJson);

        expect(invoiceModel.ageInDays, equals(30));
        expect(invoiceModel.balance, equals(1900.50));
        expect(invoiceModel.subTotal, equals(2000.75));
        expect(invoiceModel.total, equals(1900.25));
      });

      test('should handle multiple items in JSON', () {
        final multiItemJson = Map<String, dynamic>.from(testJson);
        multiItemJson['items'] = [
          testJson['items'][0],
          {
            '_id': 'item_456',
            'productId': 'product_456',
            'itemName': 'Test Product 2',
            'quantity': 1.0,
            'invoiceId': 'invoice_123',
            'createdTime': testDateTime.toIso8601String(),
            'discountAmount': 50.0,
            'hsnSac': '0987654321',
            'itemPrice': 500.0,
            'lastModifiedTime': testDateTime.toIso8601String(),
            'placeOfSupply': 'Karnataka',
            'productCategory': 'Chemical',
            'source': 'online',
            'subTotal': 500.0,
            'total': 450.0,
          },
        ];

        final invoiceModel = InvoiceModel.fromJson(multiItemJson);

        expect(invoiceModel.items, hasLength(2));
        expect(invoiceModel.items[0].id, equals('item_123'));
        expect(invoiceModel.items[1].id, equals('item_456'));
        expect(invoiceModel.items[0].itemName, equals('Test Product'));
        expect(invoiceModel.items[1].itemName, equals('Test Product 2'));
      });
    });
  });

  group('InvoiceItemModel Tests', () {
    late InvoiceItemModel testInvoiceItemModel;
    late Map<String, dynamic> testItemJson;
    late DateTime testDateTime;

    setUp(() {
      testDateTime = DateTime(2024, 1, 15, 10, 30, 0);

      testItemJson = {
        '_id': 'item_123',
        'productId': 'product_123',
        'itemName': 'Test Product',
        'quantity': 2.0,
        'invoiceId': 'invoice_123',
        'createdTime': testDateTime.toIso8601String(),
        'discountAmount': 100.0,
        'hsnSac': '1234567890',
        'itemPrice': 1000.0,
        'lastModifiedTime': testDateTime.toIso8601String(),
        'placeOfSupply': 'Tamil Nadu',
        'productCategory': 'Feed',
        'source': 'direct',
        'subTotal': 2000.0,
        'total': 1900.0,
      };

      testInvoiceItemModel = InvoiceItemModel.fromJson(testItemJson);
    });

    group('Constructor Tests', () {
      test('should create InvoiceItemModel with all required fields', () {
        expect(testInvoiceItemModel.id, equals('item_123'));
        expect(testInvoiceItemModel.productId, equals('product_123'));
        expect(testInvoiceItemModel.itemName, equals('Test Product'));
        expect(testInvoiceItemModel.quantity, equals(2.0));
        expect(testInvoiceItemModel.invoiceId, equals('invoice_123'));
        expect(testInvoiceItemModel.createdTime, equals(testDateTime));
        expect(testInvoiceItemModel.discountAmount, equals(100.0));
        expect(testInvoiceItemModel.hsnSac, equals('1234567890'));
        expect(testInvoiceItemModel.itemPrice, equals(1000.0));
        expect(testInvoiceItemModel.lastModifiedTime, equals(testDateTime));
        expect(testInvoiceItemModel.placeOfSupply, equals('Tamil Nadu'));
        expect(testInvoiceItemModel.productCategory, equals('Feed'));
        expect(testInvoiceItemModel.source, equals('direct'));
        expect(testInvoiceItemModel.subTotal, equals(2000.0));
        expect(testInvoiceItemModel.total, equals(1900.0));
      });
    });

    group('JSON Serialization Tests', () {
      test('should convert InvoiceItemModel to JSON correctly', () {
        final json = testInvoiceItemModel.toJson();

        expect(json['_id'], equals('item_123'));
        expect(json['productId'], equals('product_123'));
        expect(json['itemName'], equals('Test Product'));
        expect(json['quantity'], equals(2.0));
        expect(json['invoiceId'], equals('invoice_123'));
        expect(json['createdTime'], equals(testDateTime.toIso8601String()));
        expect(json['discountAmount'], equals(100.0));
        expect(json['hsnSac'], equals('1234567890'));
        expect(json['itemPrice'], equals(1000.0));
        expect(
          json['lastModifiedTime'],
          equals(testDateTime.toIso8601String()),
        );
        expect(json['placeOfSupply'], equals('Tamil Nadu'));
        expect(json['productCategory'], equals('Feed'));
        expect(json['source'], equals('direct'));
        expect(json['subTotal'], equals(2000.0));
        expect(json['total'], equals(1900.0));
      });

      test('should handle missing JSON fields with defaults', () {
        final incompleteJson = <String, dynamic>{
          '_id': 'item_123',
          'productId': 'product_123',
        };

        final invoiceItemModel = InvoiceItemModel.fromJson(incompleteJson);

        expect(invoiceItemModel.id, equals('item_123'));
        expect(invoiceItemModel.productId, equals('product_123'));
        expect(invoiceItemModel.itemName, isEmpty);
        expect(invoiceItemModel.quantity, equals(0.0));
        expect(invoiceItemModel.invoiceId, isEmpty);
        expect(invoiceItemModel.discountAmount, equals(0.0));
        expect(invoiceItemModel.hsnSac, isEmpty);
        expect(invoiceItemModel.itemPrice, equals(0.0));
        expect(invoiceItemModel.placeOfSupply, isEmpty);
        expect(invoiceItemModel.productCategory, isEmpty);
        expect(invoiceItemModel.source, isEmpty);
        expect(invoiceItemModel.subTotal, equals(0.0));
        expect(invoiceItemModel.total, equals(0.0));
        expect(invoiceItemModel.createdTime, isA<DateTime>());
        expect(invoiceItemModel.lastModifiedTime, isA<DateTime>());
      });

      test('should handle round-trip JSON serialization', () {
        final originalJson = testInvoiceItemModel.toJson();
        final recreatedModel = InvoiceItemModel.fromJson(originalJson);
        final finalJson = recreatedModel.toJson();

        expect(finalJson, equals(originalJson));
      });
    });
  });
}
