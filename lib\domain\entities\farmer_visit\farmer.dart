import 'package:equatable/equatable.dart';
import 'visit.dart';

/// Represents a Farmer in the domain layer.
/// This is a pure Dart object, free from database or network annotations.
class Farmer extends Equatable {
  final int? id; // Optional ID from the data layer (e.g., ObjectBox ID)
  final String name;
  final String mobileNumber;
  final List<Visit>
  visits; // Relationship represented by a list of domain entities

  const Farmer({
    this.id,
    required this.name,
    required this.mobileNumber,
    this.visits = const [],
  });

  // Use Equatable to easily compare Farmer objects
  @override
  List<Object?> get props => [id, name, mobileNumber, visits];
}
