import 'package:bloc_test/bloc_test.dart';
import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'package:aquapartner/core/error/failures.dart';
import 'package:aquapartner/core/utils/logger.dart';
import 'package:aquapartner/domain/entities/payments/payment_request.dart';
import 'package:aquapartner/domain/entities/payments/payment_session.dart';
import 'package:aquapartner/domain/entities/payments/payment_transaction.dart';
import 'package:aquapartner/domain/usecases/payments/create_payment_session_usecase.dart';
import 'package:aquapartner/domain/usecases/payments/verify_payment_usecase.dart';
import 'package:aquapartner/presentation/cubit/payments/payment_cubit.dart';
import 'package:aquapartner/presentation/cubit/payments/payment_state.dart';

import 'payment_cubit_test.mocks.dart';

@GenerateMocks([
  CreatePaymentSessionUseCase,
  VerifyPaymentUseCase,
  CheckPaymentStatusUseCase,
  AppLogger,
])
void main() {
  late PaymentCubit cubit;
  late MockCreatePaymentSessionUseCase mockCreatePaymentSession;
  late MockVerifyPaymentUseCase mockVerifyPayment;
  late MockCheckPaymentStatusUseCase mockCheckPaymentStatus;
  late MockAppLogger mockLogger;

  setUp(() {
    mockCreatePaymentSession = MockCreatePaymentSessionUseCase();
    mockVerifyPayment = MockVerifyPaymentUseCase();
    mockCheckPaymentStatus = MockCheckPaymentStatusUseCase();
    mockLogger = MockAppLogger();

    cubit = PaymentCubit(
      createPaymentSession: mockCreatePaymentSession,
      verifyPayment: mockVerifyPayment,
      checkPaymentStatus: mockCheckPaymentStatus,
      logger: mockLogger,
    );
  });

  tearDown(() {
    cubit.close();
  });

  group('PaymentCubit', () {
    const tPaymentRequest = PaymentRequest(
      amount: 100.0,
      currency: 'INR',
      invoiceNumber: 'INV-001',
      customerId: 'CUST-001',
      description: 'Test payment',
    );

    final tPaymentSession = PaymentSession(
      sessionId: 'PS_123456789',
      paymentUrl: 'https://payments.zoho.in/checkout/PS_123456789',
      amount: 100.0,
      currency: 'INR',
      invoiceNumber: 'INV-001',
      customerId: 'CUST-001',
      description: 'Test payment',
      status: PaymentSessionStatus.created,
      createdAt: DateTime(2024, 1, 1),
    );

    final tPaymentTransaction = PaymentTransaction(
      transactionId: 'TXN_123456789',
      sessionId: 'PS_123456789',
      invoiceNumber: 'INV-001',
      customerId: 'CUST-001',
      amount: 100.0,
      currency: 'INR',
      status: PaymentTransactionStatus.success,
      paymentMethod: PaymentMethod.creditCard,
      transactionDate: DateTime(2024, 1, 1),
    );

    test('initial state should be PaymentInitial', () {
      expect(cubit.state, const PaymentInitial());
    });

    group('createPaymentSession', () {
      blocTest<PaymentCubit, PaymentState>(
        'should emit [PaymentLoading, PaymentSessionCreated] when payment session is created successfully',
        build: () {
          when(
            mockCreatePaymentSession(any),
          ).thenAnswer((_) async => Right(tPaymentSession));
          return cubit;
        },
        act: (cubit) => cubit.createPaymentSession(tPaymentRequest),
        expect:
            () => [
              const PaymentLoading(),
              PaymentSessionCreated(paymentSession: tPaymentSession),
            ],
        verify: (_) {
          verify(mockCreatePaymentSession(tPaymentRequest));
          verify(mockLogger.i('Creating payment session for invoice: INV-001'));
          verify(
            mockLogger.i('Payment session created successfully: PS_123456789'),
          );
        },
      );

      blocTest<PaymentCubit, PaymentState>(
        'should emit [PaymentLoading, PaymentError] when payment session creation fails',
        build: () {
          when(
            mockCreatePaymentSession(any),
          ).thenAnswer((_) async => Left(ServerFailure()));
          return cubit;
        },
        act: (cubit) => cubit.createPaymentSession(tPaymentRequest),
        expect:
            () => [
              const PaymentLoading(),
              PaymentError(
                message:
                    'Payment service is temporarily unavailable. Please try again later.',
                failure: ServerFailure(),
              ),
            ],
        verify: (_) {
          verify(mockCreatePaymentSession(tPaymentRequest));
          verify(mockLogger.i('Creating payment session for invoice: INV-001'));
          verify(mockLogger.e(any));
        },
      );

      blocTest<PaymentCubit, PaymentState>(
        'should emit [PaymentLoading, PaymentError] with network error message when network fails',
        build: () {
          when(
            mockCreatePaymentSession(any),
          ).thenAnswer((_) async => Left(NetworkFailure()));
          return cubit;
        },
        act: (cubit) => cubit.createPaymentSession(tPaymentRequest),
        expect:
            () => [
              const PaymentLoading(),
              PaymentError(
                message:
                    'Network error. Please check your internet connection and try again.',
                failure: NetworkFailure(),
              ),
            ],
        verify: (_) {
          verify(mockCreatePaymentSession(tPaymentRequest));
        },
      );

      blocTest<PaymentCubit, PaymentState>(
        'should emit [PaymentLoading, PaymentError] with validation error message when validation fails',
        build: () {
          when(
            mockCreatePaymentSession(any),
          ).thenAnswer((_) async => Left(ValidationFailure('Invalid amount')));
          return cubit;
        },
        act: (cubit) => cubit.createPaymentSession(tPaymentRequest),
        expect:
            () => [
              const PaymentLoading(),
              PaymentError(
                message: 'ValidationFailure(Invalid amount)',
                failure: ValidationFailure('Invalid amount'),
              ),
            ],
        verify: (_) {
          verify(mockCreatePaymentSession(tPaymentRequest));
        },
      );
    });

    group('checkPaymentStatus', () {
      const sessionId = 'PS_123456789';

      blocTest<PaymentCubit, PaymentState>(
        'should emit [PaymentLoading, PaymentProcessing] when payment status is pending',
        build: () {
          when(mockCheckPaymentStatus(any)).thenAnswer(
            (_) async => Right(
              PaymentSession(
                sessionId: sessionId,
                paymentUrl: 'https://payments.zoho.in/checkout/PS_123456789',
                amount: 100.0,
                currency: 'INR',
                invoiceNumber: 'INV-001',
                customerId: 'CUST-001',
                status: PaymentSessionStatus.pending,
                createdAt: DateTime(2024, 1, 1),
              ),
            ),
          );
          return cubit;
        },
        act: (cubit) => cubit.checkPaymentStatus(sessionId),
        expect:
            () => [
              const PaymentLoading(),
              isA<PaymentProcessing>()
                  .having((p) => p.sessionId, 'sessionId', sessionId)
                  .having(
                    (p) => p.currentStatus,
                    'currentStatus',
                    PaymentSessionStatus.pending,
                  ),
            ],
        verify: (_) {
          verify(mockCheckPaymentStatus(sessionId));
          verify(
            mockLogger.i(
              'Manually checking payment status for session: $sessionId',
            ),
          );
          verify(mockLogger.i('Payment status checked: pending'));
        },
      );

      blocTest<PaymentCubit, PaymentState>(
        'should emit [PaymentLoading, PaymentSuccess] when payment is completed and verified',
        build: () {
          when(mockCheckPaymentStatus(any)).thenAnswer(
            (_) async => Right(
              PaymentSession(
                sessionId: sessionId,
                paymentUrl: 'https://payments.zoho.in/checkout/PS_123456789',
                amount: 100.0,
                currency: 'INR',
                invoiceNumber: 'INV-001',
                customerId: 'CUST-001',
                status: PaymentSessionStatus.completed,
                createdAt: DateTime(2024, 1, 1),
              ),
            ),
          );
          when(
            mockVerifyPayment(any),
          ).thenAnswer((_) async => Right(tPaymentTransaction));
          return cubit;
        },
        act: (cubit) => cubit.checkPaymentStatus(sessionId),
        expect:
            () => [
              const PaymentLoading(),
              PaymentSuccess(transaction: tPaymentTransaction),
            ],
        verify: (_) {
          verify(mockCheckPaymentStatus(sessionId));
          verify(mockVerifyPayment(sessionId));
          verify(mockLogger.i('Payment verified successfully: TXN_123456789'));
        },
      );

      blocTest<PaymentCubit, PaymentState>(
        'should emit [PaymentLoading, PaymentFailed] when payment status is failed',
        build: () {
          when(mockCheckPaymentStatus(any)).thenAnswer(
            (_) async => Right(
              PaymentSession(
                sessionId: sessionId,
                paymentUrl: 'https://payments.zoho.in/checkout/PS_123456789',
                amount: 100.0,
                currency: 'INR',
                invoiceNumber: 'INV-001',
                customerId: 'CUST-001',
                status: PaymentSessionStatus.failed,
                createdAt: DateTime(2024, 1, 1),
              ),
            ),
          );
          return cubit;
        },
        act: (cubit) => cubit.checkPaymentStatus(sessionId),
        expect:
            () => [
              const PaymentLoading(),
              PaymentFailed(
                sessionId: sessionId,
                reason: 'Payment processing failed',
              ),
            ],
        verify: (_) {
          verify(mockCheckPaymentStatus(sessionId));
          verify(mockLogger.w('Payment failed for session: $sessionId'));
        },
      );

      blocTest<PaymentCubit, PaymentState>(
        'should emit [PaymentLoading, PaymentCancelled] when payment status is cancelled',
        build: () {
          when(mockCheckPaymentStatus(any)).thenAnswer(
            (_) async => Right(
              PaymentSession(
                sessionId: sessionId,
                paymentUrl: 'https://payments.zoho.in/checkout/PS_123456789',
                amount: 100.0,
                currency: 'INR',
                invoiceNumber: 'INV-001',
                customerId: 'CUST-001',
                status: PaymentSessionStatus.cancelled,
                createdAt: DateTime(2024, 1, 1),
              ),
            ),
          );
          return cubit;
        },
        act: (cubit) => cubit.checkPaymentStatus(sessionId),
        expect:
            () => [
              const PaymentLoading(),
              PaymentCancelled(sessionId: sessionId),
            ],
        verify: (_) {
          verify(mockCheckPaymentStatus(sessionId));
          verify(mockLogger.w('Payment cancelled for session: $sessionId'));
        },
      );

      blocTest<PaymentCubit, PaymentState>(
        'should emit [PaymentLoading, PaymentExpired] when payment status is expired',
        build: () {
          when(mockCheckPaymentStatus(any)).thenAnswer(
            (_) async => Right(
              PaymentSession(
                sessionId: sessionId,
                paymentUrl: 'https://payments.zoho.in/checkout/PS_123456789',
                amount: 100.0,
                currency: 'INR',
                invoiceNumber: 'INV-001',
                customerId: 'CUST-001',
                status: PaymentSessionStatus.expired,
                createdAt: DateTime(2024, 1, 1),
              ),
            ),
          );
          return cubit;
        },
        act: (cubit) => cubit.checkPaymentStatus(sessionId),
        expect:
            () => [
              const PaymentLoading(),
              PaymentExpired(sessionId: sessionId),
            ],
        verify: (_) {
          verify(mockCheckPaymentStatus(sessionId));
          verify(mockLogger.w('Payment expired for session: $sessionId'));
        },
      );
    });

    group('resetPayment', () {
      blocTest<PaymentCubit, PaymentState>(
        'should emit PaymentInitial when reset is called',
        build: () => cubit,
        act: (cubit) => cubit.resetPayment(),
        expect: () => [const PaymentInitial()],
        verify: (_) {
          verify(mockLogger.i('Resetting payment state'));
        },
      );
    });

    group('cancelPayment', () {
      const sessionId = 'PS_123456789';

      blocTest<PaymentCubit, PaymentState>(
        'should emit PaymentCancelled when cancel is called',
        build: () => cubit,
        act: (cubit) => cubit.cancelPayment(sessionId),
        expect: () => [PaymentCancelled(sessionId: sessionId)],
        verify: (_) {
          verify(mockLogger.i('Cancelling payment session: $sessionId'));
        },
      );
    });
  });
}
