import 'package:aqua_ui/aqua_ui.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../core/mixins/analytics_mixin.dart';
import '../../core/utils/string_utils.dart';
import '../../domain/entities/product.dart';
import '../cubit/interested_product/interested_product_cubit.dart';
import '../cubit/interested_product/interested_product_state.dart';
import '../widgets/custome_badge.dart';

class ProductDetailsScreen extends StatefulWidget {
  final Product productDetails;

  const ProductDetailsScreen({super.key, required this.productDetails});

  @override
  State<ProductDetailsScreen> createState() => _ProductDetailsScreenState();
}

class _ProductDetailsScreenState extends State<ProductDetailsScreen>
    with SingleTickerProviderStateMixin, AnalyticsMixin {
  late final WebViewController _controller;
  double _webViewHeight = 400; // Default height
  bool _isLoading = true;
  bool _isSubmitting = false;

  // Animation controller for the button
  late AnimationController _animationController;
  late Animation<Offset> _buttonAnimation;

  // Track scroll direction
  bool _isScrollingDown = false;

  // Track scroll position
  final ScrollController _scrollController = ScrollController();

  @override
  String get screenName => 'product_details';

  @override
  String get parentScreenName => 'products';

  @override
  void initState() {
    super.initState();
    _initWebView();

    // Track product view
    trackEvent(
      'product_details_viewed',
      params: {
        'product_name': widget.productDetails.productName,
        'product_tag': widget.productDetails.productTag,
        'has_content': widget.productDetails.content.isNotEmpty.toString(),
      },
    );

    // Initialize scroll controller
    _scrollController.addListener(_scrollListener);

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    // Create slide animation for the button
    _buttonAnimation = Tween<Offset>(
      begin: const Offset(0, 1), // Start from bottom (hidden)
      end: Offset.zero, // End at original position (visible)
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    // Hide button initially
    _animationController.reverse();
  }

  void _scrollListener() {
    // Check if we're at the bottom of the scroll
    if (_scrollController.position.atEdge) {
      if (_scrollController.position.pixels != 0) {
        // At the bottom, always show the button
        _animationController.forward();
      }
      return;
    }

    // Determine scroll direction - REVERSED LOGIC
    if (_scrollController.position.userScrollDirection ==
        ScrollDirection.reverse) {
      // Scrolling down - SHOW the button (reversed from original)
      if (!_isScrollingDown) {
        _isScrollingDown = true;
        _animationController.forward();
      }
    } else if (_scrollController.position.userScrollDirection ==
        ScrollDirection.forward) {
      // Scrolling up - HIDE the button (reversed from original)
      if (_isScrollingDown) {
        _isScrollingDown = false;
        _animationController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _initWebView() {
    _controller =
        WebViewController()
          ..setJavaScriptMode(JavaScriptMode.unrestricted)
          ..setNavigationDelegate(
            NavigationDelegate(
              onPageFinished: (String url) {
                // Execute JavaScript to force style changes after page load
                _controller.runJavaScript('''
              // Make content visible with background color for debugging
              document.body.style.backgroundColor = '#ffffff';

              // Style h2 elements
              document.querySelectorAll('h2').forEach(function(h2) {
                h2.style.color = '#949494';
                h2.style.fontSize = '18px';
                h2.style.fontWeight = '600';
                h2.style.margin = '0px 0 0px 0';
                h2.style.paddingLeft = '0';
                h2.style.paddingRight = '0';
                h2.style.width = '100%';
                h2.style.display = 'block'; // Ensure it's visible
              });

              // Style paragraph elements
              document.querySelectorAll('p').forEach(function(p) {
                p.style.lineHeight = '22px';
                p.style.fontSize = '16px';
                p.style.marginTop = '8px';
                p.style.marginBottom = '8px';
                p.style.paddingLeft = '0';
                p.style.paddingRight = '0';
                p.style.display = 'block'; // Ensure it's visible
              });

              // Make sure all divs are visible
              document.querySelectorAll('div').forEach(function(div) {
                div.style.display = 'block';
              });

              console.log('Styles applied via JavaScript');
            ''');

                // Wait a bit before calculating height to ensure content is rendered
                Future.delayed(const Duration(milliseconds: 300), () {
                  _updateWebViewHeight();
                });

                setState(() {
                  _isLoading = false;
                });
              },
              onWebResourceError: (WebResourceError error) {
                debugPrint('WebView error: ${error.description}');
              },
            ),
          )
          ..loadHtmlString('''
        <!DOCTYPE html>
        <html lang="en">
          <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <style>
              /* Reset all margins and padding */
              * {
                box-sizing: border-box;
              }

              body {
                line-height: 1.4;
                color: #000;
                padding: 0px; /* Add some padding */
                margin: 0;
                background-color: #ffffff;
                width: 100%;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
              }

              /* Remove all margin and padding from h2 */
              h2 {
                color: #949494 !important;
                font-size: 18px !important;
                font-weight: 600 !important;
                margin: 20px 0 12px 0 !important;
                padding-left: 0 !important;
                padding-right: 0 !important;
                width: 100% !important;
                display: block !important;
              }

              /* Style paragraphs with specified line height and font size */
              p {
                margin: 8px 0 !important;
                padding-left: 0 !important;
                padding-right: 0 !important;
                line-height: 22px !important;
                font-size: 16px !important;
                display: block !important;
              }

              /* Container for content without side margins */
              div {
                padding: 0;
                margin: 0;
                width: 100%;
                display: block !important;
              }

              ul {
                list-style-type: disc;
                padding-left: 16px;
                margin-left: 0;
                margin-right: 0;
                margin-bottom: 16px;
              }

              li {
                margin-bottom: 8px;
                line-height: 22px;
                font-size: 16px;
              }
            </style>
          </head>
          <body>
            <div id="content-container">
              ${widget.productDetails.content}
            </div>
          </body>
        </html>
      ''');
  }

  Future<void> _updateWebViewHeight() async {
    try {
      // Get the height as a dynamic value
      final dynamic result = await _controller.runJavaScriptReturningResult('''
      (function() {
        var height = 0;
        var body = document.body, html = document.documentElement;

        // Get content height
        var contentHeight = Math.max(
          body.scrollHeight, body.offsetHeight,
          html.clientHeight, html.scrollHeight, html.offsetHeight
        );

        // Log for debugging
        console.log("Content height calculation: " + contentHeight);

        // Add extra padding for safety
        return contentHeight + 50;
      })();
    ''');

      // Handle the result properly based on its type
      double calculatedHeight;
      if (result is int) {
        calculatedHeight = result.toDouble();
      } else if (result is double) {
        calculatedHeight = result;
      } else if (result is String) {
        calculatedHeight = double.parse(result);
      } else {
        // Fallback if the type is unexpected
        calculatedHeight = 880;
      }

      if (mounted && calculatedHeight > 0) {
        setState(() {
          _webViewHeight = calculatedHeight;
        });
      } else {
        // Fallback to a reasonable minimum height
        setState(() {
          _webViewHeight = 880; // Larger fallback height
        });
      }
    } catch (e) {
      debugPrint('Error getting height: $e');
      // Fallback height
      setState(() {
        _webViewHeight = 880;
      });
    }
  }

  Future<void> _handleInterested() async {
    if (_isSubmitting) return;

    // Track interest button tap
    trackUserInteraction(
      'interested_button_tapped',
      'button',
      elementId: widget.productDetails.productName,
      additionalParams: {
        'product_name': widget.productDetails.productName,
        'product_tag': widget.productDetails.productTag,
        'source': 'Product Details Screen',
      },
    );

    setState(() {
      _isSubmitting = true;
    });

    try {
      // Get the cubit
      final cubit = context.read<InterestedProductCubit>();

      // Call the method and await its completion
      await cubit.addInterestedProduct(
        productName: widget.productDetails.productName,
        // Pass source parameter explicitly
        source: "Product Details Screen",
      );

      // Check the state to determine success or failure
      if (cubit.state.status == InterestedProductStatus.success) {
        // Track successful interest registration
        trackEvent(
          'product_interest_registered',
          params: {
            'product_name': widget.productDetails.productName,
            'product_tag': widget.productDetails.productTag,
            'source': 'Product Details Screen',
            'status': 'success',
          },
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: AquaText.body(
                'Your dedicated AquaConnect officer will reach out to you with details',
              ),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 5),
            ),
          );
        }
      } else if (cubit.state.status == InterestedProductStatus.error) {
        // Track interest registration error
        trackError(
          'product_interest_registration_failed',
          cubit.state.errorMessage ?? 'Unknown error',
          additionalParams: {
            'product_name': widget.productDetails.productName,
            'source': 'Product Details Screen',
            'error_type': 'cubit_error',
          },
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: AquaText.body(
                cubit.state.errorMessage ??
                    'Failed to register interest. Please try again.',
              ),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 2),
            ),
          );
        }
      }
    } catch (e) {
      // Track exception error
      trackError(
        'product_interest_registration_exception',
        e.toString(),
        additionalParams: {
          'product_name': widget.productDetails.productName,
          'source': 'Product Details Screen',
          'error_type': 'exception',
        },
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: AquaText.body(
              'Failed to register interest. Please try again.',
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.white,
          statusBarIconBrightness: Brightness.dark,
        ),
        backgroundColor: acWhiteColor,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          color: acTextSecondaryColor,
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: AquaText.headline(
          "Product Details",
          weight: AquaFontWeight.semibold,
          color: acTextSecondaryColor,
        ),
        centerTitle: false,
      ),
      // Use a Stack to position the floating button at the bottom
      body: Stack(
        children: [
          // Main content with scroll controller
          SafeArea(
            child: SingleChildScrollView(
              controller: _scrollController,
              child: Container(
                color: acWhiteColor,
                padding: EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: 32),
                    SizedBox(
                      width: double.infinity,
                      height: 350,
                      child: CachedNetworkImage(
                        imageUrl: widget.productDetails.productImage,
                        fit: BoxFit.contain,
                        placeholder:
                            (context, url) => Center(
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color: Color(0xFF0C84FE),
                              ),
                            ),
                        errorWidget:
                            (context, url, error) =>
                                Icon(Icons.error, color: Colors.red),
                        memCacheWidth: 200,
                        memCacheHeight: 300,
                        maxWidthDiskCache: 200,
                        maxHeightDiskCache: 300,
                      ),
                    ),
                    SizedBox(height: 32),
                    widget.productDetails.productTag.isEmpty
                        ? Container()
                        : CustomBadge(
                          title: widget.productDetails.productTag,
                          titleColor:
                              widget.productDetails.productTag == 'NEW ARRIVAL'
                                  ? acPrimaryBlue
                                  : acGreenColor,
                        ),
                    SizedBox(height: 8),
                    AquaText.heading(
                      StringUtils.decodeUnicodeEscapes(
                        widget.productDetails.productName,
                      ),
                      weight: AquaFontWeight.semibold,
                      color: acBlackColor,
                    ),

                    // WebView container with more visible debugging indicators
                    Container(
                      padding: EdgeInsets.only(bottom: 0),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.transparent),
                      ),
                      height: _webViewHeight,
                      width: double.infinity,
                      child: Stack(
                        children: [
                          WebViewWidget(controller: _controller),
                          if (_isLoading)
                            const Center(child: CircularProgressIndicator()),
                        ],
                      ),
                    ),

                    // Add padding at the bottom to ensure content isn't hidden behind the button
                    SizedBox(height: 80),
                  ],
                ),
              ),
            ),
          ),

          // Animated floating button at the bottom
          Positioned(
            left: 0,
            right: 0,
            bottom: 0,
            child: SlideTransition(
              position: _buttonAnimation,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  color: acWhiteColor,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: Offset(0, -2),
                    ),
                  ],
                ),
                child: SafeArea(
                  top: false,
                  child: AquaButton(
                    title: _isSubmitting ? "Processing..." : "I'm Interested",
                    onTap: _isLoading ? null : _handleInterested,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
