import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:firebase_performance/firebase_performance.dart';
import '../services/performance_monitoring_service.dart';
import '../utils/logger.dart';

/// An interceptor for Dio HTTP client that automatically tracks
/// network request performance using Firebase Performance Monitoring.
class PerformanceInterceptor extends Interceptor {
  final PerformanceMonitoringService _performanceService;
  final AppLogger _logger;
  final Map<String, HttpMetric> _metrics = {};

  PerformanceInterceptor(this._performanceService, this._logger);

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    try {
      final url = options.uri.toString();
      final method = _getHttpMethod(options.method);

      // Start HTTP metric
      final metric = _performanceService.startHttpMetric(url, method);
      if (metric != null) {
        final requestId = _generateRequestId(options);
        _metrics[requestId] = metric;

        // Track request size if available
        if (options.data != null) {
          int? requestSize;
          if (options.data is String) {
            requestSize = (options.data as String).length;
          } else if (options.data is List) {
            requestSize = jsonEncode(options.data).length;
          } else if (options.data is Map) {
            requestSize = jsonEncode(options.data).length;
          }

          if (requestSize != null) {
            metric.requestPayloadSize = requestSize;
          }
        }
      }
    } catch (e) {
      _logger.e('Error in performance interceptor (request)', e);
    }

    handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    try {
      final requestId = _generateRequestId(response.requestOptions);
      final metric = _metrics.remove(requestId);

      if (metric != null) {
        // Set response information
        metric.httpResponseCode = response.statusCode;

        // Calculate response size
        if (response.data != null) {
          int? responseSize;
          if (response.data is String) {
            responseSize = (response.data as String).length;
          } else {
            try {
              final jsonString = jsonEncode(response.data);
              responseSize = jsonString.length;
            } catch (_) {
              // Ignore if data can't be encoded to JSON
            }
          }

          if (responseSize != null) {
            metric.responsePayloadSize = responseSize;
          }
        }

        // Set content type if available
        final contentType = response.headers.value('content-type');
        if (contentType != null) {
          metric.responseContentType = contentType;
        }

        // Stop the metric
        metric.stop();
      }
    } catch (e) {
      _logger.e('Error in performance interceptor (response)', e);
    }

    handler.next(response);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    try {
      final requestId = _generateRequestId(err.requestOptions);
      final metric = _metrics.remove(requestId);

      if (metric != null) {
        // Set error information
        if (err.response != null) {
          metric.httpResponseCode = err.response!.statusCode;
        }

        // Stop the metric
        metric.stop();
      }
    } catch (e) {
      _logger.e('Error in performance interceptor (error)', e);
    }

    handler.next(err);
  }

  /// Convert string HTTP method to HttpMethod enum
  HttpMethod _getHttpMethod(String method) {
    switch (method.toUpperCase()) {
      case 'GET':
        return HttpMethod.Get;
      case 'POST':
        return HttpMethod.Post;
      case 'PUT':
        return HttpMethod.Put;
      case 'DELETE':
        return HttpMethod.Delete;
      case 'PATCH':
        return HttpMethod.Patch;
      case 'OPTIONS':
        return HttpMethod.Options;
      default:
        return HttpMethod.Get;
    }
  }

  /// Generate a unique ID for a request to track it through the request/response cycle
  String _generateRequestId(RequestOptions options) {
    // Use a combination of URL, method, and hashCode for consistent ID
    return '${options.uri.toString()}_${options.method}_${options.hashCode}';
  }
}
