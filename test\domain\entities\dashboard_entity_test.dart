import 'package:flutter_test/flutter_test.dart';
import 'package:aquapartner/domain/entities/dashboard/dashboard_entity.dart';

void main() {
  group('DashboardEntity', () {
    group('Constructor and Properties', () {
      test('should create dashboard entity with all required properties', () {
        // arrange & act
        final dashboard = DashboardEntity(
          customerId: 'CUST001',
          sales: const SalesEntity(yearlyData: {}),
          payments: const PaymentsEntity(yearlyData: {}),
          dues: const [],
          salesReturn: 1500.0,
          categoryTypeSales: const [],
          liquidation: const LiquidationEntity(
            totalLiquidation: 0.0,
            liquidationByYear: [],
          ),
          myFarmers: const MyFarmersEntity(
            totalFarmers: [],
            potentialFarmers: 0,
          ),
        );

        // assert
        expect(dashboard.customerId, 'CUST001');
        expect(dashboard.sales, isA<SalesEntity>());
        expect(dashboard.payments, isA<PaymentsEntity>());
        expect(dashboard.dues, isEmpty);
        expect(dashboard.salesReturn, 1500.0);
        expect(dashboard.categoryTypeSales, isEmpty);
        expect(dashboard.liquidation, isA<LiquidationEntity>());
        expect(dashboard.myFarmers, isA<MyFarmersEntity>());
      });

      test('should create dashboard entity with complex data', () {
        // arrange
        final salesData = {
          '2024': [
            MonthSalesEntity(
              month: 'January',
              totalSales: 10000.0,
              weeks: [
                WeekSalesEntity(week: 'Week 1', amount: 2500.0),
                WeekSalesEntity(week: 'Week 2', amount: 2500.0),
              ],
            ),
          ],
        };
        final paymentsData = {
          '2024': [
            MonthPaymentEntity(
              month: 'January',
              totalPayment: 8000.0,
              weeks: [WeekPaymentEntity(week: 'Week 1', amount: 4000.0)],
            ),
          ],
        };
        final dues = [
          DueTierEntity(ageTier: '0-30 days', totalAmount: 5000.0),
          DueTierEntity(ageTier: '31-60 days', totalAmount: 3000.0),
        ];
        final categoryTypeSales = [
          CategorySalesEntity(
            categoryType: 'Feed',
            yearlySales: {
              '2024': [
                MonthCategorySalesEntity(
                  month: 'January',
                  totalPayableAmount: 5000.0,
                  weeks: [],
                ),
              ],
            },
          ),
        ];
        final liquidation = LiquidationEntity(
          totalLiquidation: 2000.0,
          liquidationByYear: [
            YearLiquidationEntity(
              year: '2024',
              months: {'January': 1000.0, 'February': 1000.0},
            ),
          ],
        );
        final myFarmers = MyFarmersEntity(
          totalFarmers: [
            FarmerEntity(
              farmerName: 'John Doe',
              visits: [
                FarmerVisitEntity(
                  createdDateTime: DateTime(2024, 1, 15),
                  doc: 1,
                  pondId: 'POND001',
                  farmerId: 'FARMER001',
                  mobileNumber: '+************',
                  productUsed: 'Fish Feed',
                ),
              ],
            ),
          ],
          potentialFarmers: 25,
        );

        // act
        final dashboard = DashboardEntity(
          customerId: 'CUST001',
          sales: SalesEntity(yearlyData: salesData),
          payments: PaymentsEntity(yearlyData: paymentsData),
          dues: dues,
          salesReturn: 1500.0,
          categoryTypeSales: categoryTypeSales,
          liquidation: liquidation,
          myFarmers: myFarmers,
        );

        // assert
        expect(dashboard.customerId, 'CUST001');
        expect(dashboard.sales.yearlyData, salesData);
        expect(dashboard.payments.yearlyData, paymentsData);
        expect(dashboard.dues.length, 2);
        expect(dashboard.dues.first.ageTier, '0-30 days');
        expect(dashboard.salesReturn, 1500.0);
        expect(dashboard.categoryTypeSales.length, 1);
        expect(dashboard.categoryTypeSales.first.categoryType, 'Feed');
        expect(dashboard.liquidation.totalLiquidation, 2000.0);
        expect(dashboard.myFarmers.totalFarmers.length, 1);
        expect(dashboard.myFarmers.potentialFarmers, 25);
      });
    });

    group('Equality', () {
      test('should be equal when all properties are the same', () {
        // arrange
        final dashboard1 = DashboardEntity(
          customerId: 'CUST001',
          sales: const SalesEntity(yearlyData: {}),
          payments: const PaymentsEntity(yearlyData: {}),
          dues: const [],
          salesReturn: 1500.0,
          categoryTypeSales: const [],
          liquidation: const LiquidationEntity(
            totalLiquidation: 0.0,
            liquidationByYear: [],
          ),
          myFarmers: const MyFarmersEntity(
            totalFarmers: [],
            potentialFarmers: 0,
          ),
        );

        final dashboard2 = DashboardEntity(
          customerId: 'CUST001',
          sales: const SalesEntity(yearlyData: {}),
          payments: const PaymentsEntity(yearlyData: {}),
          dues: const [],
          salesReturn: 1500.0,
          categoryTypeSales: const [],
          liquidation: const LiquidationEntity(
            totalLiquidation: 0.0,
            liquidationByYear: [],
          ),
          myFarmers: const MyFarmersEntity(
            totalFarmers: [],
            potentialFarmers: 0,
          ),
        );

        // act & assert
        expect(dashboard1, equals(dashboard2));
        expect(dashboard1.hashCode, equals(dashboard2.hashCode));
      });

      test('should not be equal when customer IDs are different', () {
        // arrange
        final dashboard1 = DashboardEntity(
          customerId: 'CUST001',
          sales: const SalesEntity(yearlyData: {}),
          payments: const PaymentsEntity(yearlyData: {}),
          dues: const [],
          salesReturn: 1500.0,
          categoryTypeSales: const [],
          liquidation: const LiquidationEntity(
            totalLiquidation: 0.0,
            liquidationByYear: [],
          ),
          myFarmers: const MyFarmersEntity(
            totalFarmers: [],
            potentialFarmers: 0,
          ),
        );

        final dashboard2 = DashboardEntity(
          customerId: 'CUST002',
          sales: const SalesEntity(yearlyData: {}),
          payments: const PaymentsEntity(yearlyData: {}),
          dues: const [],
          salesReturn: 1500.0,
          categoryTypeSales: const [],
          liquidation: const LiquidationEntity(
            totalLiquidation: 0.0,
            liquidationByYear: [],
          ),
          myFarmers: const MyFarmersEntity(
            totalFarmers: [],
            potentialFarmers: 0,
          ),
        );

        // act & assert
        expect(dashboard1, isNot(equals(dashboard2)));
      });

      test('should not be equal when sales return is different', () {
        // arrange
        final dashboard1 = DashboardEntity(
          customerId: 'CUST001',
          sales: const SalesEntity(yearlyData: {}),
          payments: const PaymentsEntity(yearlyData: {}),
          dues: const [],
          salesReturn: 1500.0,
          categoryTypeSales: const [],
          liquidation: const LiquidationEntity(
            totalLiquidation: 0.0,
            liquidationByYear: [],
          ),
          myFarmers: const MyFarmersEntity(
            totalFarmers: [],
            potentialFarmers: 0,
          ),
        );

        final dashboard2 = DashboardEntity(
          customerId: 'CUST001',
          sales: const SalesEntity(yearlyData: {}),
          payments: const PaymentsEntity(yearlyData: {}),
          dues: const [],
          salesReturn: 2000.0,
          categoryTypeSales: const [],
          liquidation: const LiquidationEntity(
            totalLiquidation: 0.0,
            liquidationByYear: [],
          ),
          myFarmers: const MyFarmersEntity(
            totalFarmers: [],
            potentialFarmers: 0,
          ),
        );

        // act & assert
        expect(dashboard1, isNot(equals(dashboard2)));
      });
    });

    group('Edge Cases', () {
      test('should handle empty customer ID', () {
        // arrange & act
        final dashboard = DashboardEntity(
          customerId: '',
          sales: const SalesEntity(yearlyData: {}),
          payments: const PaymentsEntity(yearlyData: {}),
          dues: const [],
          salesReturn: 0.0,
          categoryTypeSales: const [],
          liquidation: const LiquidationEntity(
            totalLiquidation: 0.0,
            liquidationByYear: [],
          ),
          myFarmers: const MyFarmersEntity(
            totalFarmers: [],
            potentialFarmers: 0,
          ),
        );

        // assert
        expect(dashboard.customerId, '');
      });

      test('should handle negative sales return', () {
        // arrange & act
        final dashboard = DashboardEntity(
          customerId: 'CUST001',
          sales: const SalesEntity(yearlyData: {}),
          payments: const PaymentsEntity(yearlyData: {}),
          dues: const [],
          salesReturn: -500.0,
          categoryTypeSales: const [],
          liquidation: const LiquidationEntity(
            totalLiquidation: 0.0,
            liquidationByYear: [],
          ),
          myFarmers: const MyFarmersEntity(
            totalFarmers: [],
            potentialFarmers: 0,
          ),
        );

        // assert
        expect(dashboard.salesReturn, -500.0);
      });

      test('should handle very large sales return', () {
        // arrange & act
        final dashboard = DashboardEntity(
          customerId: 'CUST001',
          sales: const SalesEntity(yearlyData: {}),
          payments: const PaymentsEntity(yearlyData: {}),
          dues: const [],
          salesReturn: 999999999.99,
          categoryTypeSales: const [],
          liquidation: const LiquidationEntity(
            totalLiquidation: 0.0,
            liquidationByYear: [],
          ),
          myFarmers: const MyFarmersEntity(
            totalFarmers: [],
            potentialFarmers: 0,
          ),
        );

        // assert
        expect(dashboard.salesReturn, 999999999.99);
      });

      test('should handle special characters in customer ID', () {
        // arrange & act
        final dashboard = DashboardEntity(
          customerId: 'CUST@#\$%001',
          sales: const SalesEntity(yearlyData: {}),
          payments: const PaymentsEntity(yearlyData: {}),
          dues: const [],
          salesReturn: 1500.0,
          categoryTypeSales: const [],
          liquidation: const LiquidationEntity(
            totalLiquidation: 0.0,
            liquidationByYear: [],
          ),
          myFarmers: const MyFarmersEntity(
            totalFarmers: [],
            potentialFarmers: 0,
          ),
        );

        // assert
        expect(dashboard.customerId, 'CUST@#\$%001');
      });
    });
  });

  group('SalesEntity', () {
    test('should create sales entity with yearly data', () {
      // arrange
      final yearlyData = {
        '2024': [
          MonthSalesEntity(
            month: 'January',
            totalSales: 10000.0,
            weeks: [WeekSalesEntity(week: 'Week 1', amount: 2500.0)],
          ),
        ],
      };

      // act
      final sales = SalesEntity(yearlyData: yearlyData);

      // assert
      expect(sales.yearlyData, yearlyData);
      expect(sales.yearlyData['2024']?.length, 1);
      expect(sales.yearlyData['2024']?.first.month, 'January');
    });

    test('should be equal when yearly data is the same', () {
      // arrange
      final yearlyData = {
        '2024': [
          MonthSalesEntity(month: 'January', totalSales: 10000.0, weeks: []),
        ],
      };

      final sales1 = SalesEntity(yearlyData: yearlyData);
      final sales2 = SalesEntity(yearlyData: yearlyData);

      // act & assert
      expect(sales1, equals(sales2));
    });
  });

  group('MonthSalesEntity', () {
    test('should create month sales entity with all properties', () {
      // arrange
      final weeks = [
        WeekSalesEntity(week: 'Week 1', amount: 2500.0),
        WeekSalesEntity(week: 'Week 2', amount: 3000.0),
      ];

      // act
      final monthSales = MonthSalesEntity(
        month: 'January',
        totalSales: 5500.0,
        weeks: weeks,
      );

      // assert
      expect(monthSales.month, 'January');
      expect(monthSales.totalSales, 5500.0);
      expect(monthSales.weeks.length, 2);
      expect(monthSales.weeks.first.week, 'Week 1');
    });

    test('should be equal when all properties are the same', () {
      // arrange
      final weeks = [WeekSalesEntity(week: 'Week 1', amount: 2500.0)];
      final monthSales1 = MonthSalesEntity(
        month: 'January',
        totalSales: 2500.0,
        weeks: weeks,
      );
      final monthSales2 = MonthSalesEntity(
        month: 'January',
        totalSales: 2500.0,
        weeks: weeks,
      );

      // act & assert
      expect(monthSales1, equals(monthSales2));
    });
  });

  group('WeekSalesEntity', () {
    test('should create week sales entity with all properties', () {
      // arrange & act
      final weekSales = WeekSalesEntity(week: 'Week 1', amount: 2500.0);

      // assert
      expect(weekSales.week, 'Week 1');
      expect(weekSales.amount, 2500.0);
    });

    test('should be equal when all properties are the same', () {
      // arrange
      final weekSales1 = WeekSalesEntity(week: 'Week 1', amount: 2500.0);
      final weekSales2 = WeekSalesEntity(week: 'Week 1', amount: 2500.0);

      // act & assert
      expect(weekSales1, equals(weekSales2));
    });

    test('should handle zero amount', () {
      // arrange & act
      final weekSales = WeekSalesEntity(week: 'Week 1', amount: 0.0);

      // assert
      expect(weekSales.amount, 0.0);
    });

    test('should handle negative amount', () {
      // arrange & act
      final weekSales = WeekSalesEntity(week: 'Week 1', amount: -100.0);

      // assert
      expect(weekSales.amount, -100.0);
    });
  });

  group('DueTierEntity', () {
    test('should create due tier entity with all properties', () {
      // arrange & act
      final dueTier = DueTierEntity(ageTier: '0-30 days', totalAmount: 5000.0);

      // assert
      expect(dueTier.ageTier, '0-30 days');
      expect(dueTier.totalAmount, 5000.0);
    });

    test('should be equal when all properties are the same', () {
      // arrange
      final dueTier1 = DueTierEntity(ageTier: '0-30 days', totalAmount: 5000.0);
      final dueTier2 = DueTierEntity(ageTier: '0-30 days', totalAmount: 5000.0);

      // act & assert
      expect(dueTier1, equals(dueTier2));
    });

    test('should handle different age tiers', () {
      // arrange
      final dueTiers = [
        DueTierEntity(ageTier: '0-30 days', totalAmount: 5000.0),
        DueTierEntity(ageTier: '31-60 days', totalAmount: 3000.0),
        DueTierEntity(ageTier: '61-90 days', totalAmount: 2000.0),
        DueTierEntity(ageTier: '90+ days', totalAmount: 1000.0),
      ];

      // act & assert
      expect(dueTiers.length, 4);
      expect(dueTiers[0].ageTier, '0-30 days');
      expect(dueTiers[1].ageTier, '31-60 days');
      expect(dueTiers[2].ageTier, '61-90 days');
      expect(dueTiers[3].ageTier, '90+ days');
    });
  });
}
