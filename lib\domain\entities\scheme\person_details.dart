class PersonDetails {
  final String rowId;
  final String customerName;
  final String email;
  final String status;
  final String mobileNumber;
  final String userId;
  final DateTime? modifiedTime;
  final String empId;
  final String profile;

  PersonDetails({
    required this.rowId,
    required this.customerName,
    required this.email,
    required this.status,
    required this.mobileNumber,
    required this.userId,
    this.modifiedTime,
    required this.empId,
    required this.profile,
  });
}
