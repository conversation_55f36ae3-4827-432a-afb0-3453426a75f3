import '../../domain/entities/payments/payment_request.dart';
import 'zoho_payment_service.dart';

/// Legacy payment service - now delegates to ZohoPaymentService
/// Maintained for backward compatibility
class PaymentService {
  final String baseUrl;

  PaymentService({required this.baseUrl});

  // Create a payment session using your backend API
  Future<Map<String, dynamic>> createPaymentSession({
    required double amount,
    required String invoiceNumber,
    required String customerId,
    String? description,
    String? customerName,
    String? customerEmail,
  }) async {
    try {
      // Use the new ZohoPaymentService for better error handling and validation
      final request = PaymentRequest(
        amount: amount,
        currency: 'INR',
        invoiceNumber: invoiceNumber,
        customerId: customerId,
        description: description ?? 'Payment for $invoiceNumber',
        customerName: customerName,
        customerEmail: customerEmail,
      );

      final response = await ZohoPaymentService.createPaymentSession(request);

      // Convert to legacy format for backward compatibility
      return {
        'success': response.success,
        'message': response.message,
        'data': {
          'payment_session_id': response.data.paymentSessionId,
          'amount': response.data.amount,
          'currency': response.data.currency,
          'description': response.data.description,
          'invoice_number': response.data.invoiceNumber,
          'created_time': response.data.createdTime,
          'transaction_id': response.data.transactionId,
          'expires_in': response.data.expiresIn,
        },
      };
    } catch (e) {
      if (e is PaymentException) {
        throw Exception('Payment error: ${e.userFriendlyMessage}');
      }
      throw Exception('Error creating payment session: $e');
    }
  }

  // Check payment status
  Future<Map<String, dynamic>> checkPaymentStatus(
    String paymentSessionId,
  ) async {
    try {
      final response = await ZohoPaymentService.getPaymentStatus(
        paymentSessionId,
      );

      // Convert to legacy format for backward compatibility
      return {
        'success': response.success,
        'message': response.message,
        'data': {
          'transaction_id': response.data.transactionId,
          'payment_session_id': response.data.paymentSessionId,
          'status': response.data.status,
          'amount': response.data.amount,
          'currency': response.data.currency,
          'description': response.data.description,
          'invoice_number': response.data.invoiceNumber,
          'customer_id': response.data.customerId,
          'customer_name': response.data.customerName,
          'customer_email': response.data.customerEmail,
          'payment_id': response.data.paymentId,
          'payment_method': response.data.paymentMethod,
          'session_created_time':
              response.data.sessionCreatedTime?.toIso8601String(),
          'payment_completed_time':
              response.data.paymentCompletedTime?.toIso8601String(),
          'session_expires_at':
              response.data.sessionExpiresAt?.toIso8601String(),
          'error_code': response.data.errorCode,
          'error_message': response.data.errorMessage,
        },
      };
    } catch (e) {
      if (e is PaymentException) {
        throw Exception('Payment error: ${e.userFriendlyMessage}');
      }
      throw Exception('Error checking payment status: $e');
    }
  }
}
