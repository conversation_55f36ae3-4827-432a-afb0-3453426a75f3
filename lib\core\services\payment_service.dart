import 'dart:convert';
import 'package:http/http.dart' as http;

class PaymentService {
  final String baseUrl;

  PaymentService({required this.baseUrl});

  // Create a payment session using your backend API
  Future<Map<String, dynamic>> createPaymentSession({
    required double amount,
    required String invoiceNumber,
    required String customerId,
    String? description,
    String? customerName,
    String? customerEmail,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/api/zoho/payments/create-session'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'amount': amount,
          'invoice_number': invoiceNumber,
          'customer_id': customerId,
          'description': description ?? 'Payment for $invoiceNumber',
          'customer_name': customerName,
          'customer_email': customerEmail,
        }),
      );

      if (response.statusCode == 201) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to create payment session: ${response.body}');
      }
    } catch (e) {
      throw Exception('Error creating payment session: $e');
    }
  }

  // Check payment status
  Future<Map<String, dynamic>> checkPaymentStatus(
    String paymentSessionId,
  ) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/zoho/payments/status/$paymentSessionId'),
      );

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to check payment status: ${response.body}');
      }
    } catch (e) {
      throw Exception('Error checking payment status: $e');
    }
  }
}
