import 'dart:io';
import 'package:package_info_plus/package_info_plus.dart';

abstract class DeviceInfoDataSource {
  Future<String> getCurrentAppVersion();
  Future<String> getAppStoreUrl();
}

class DeviceInfoDataSourceImpl implements DeviceInfoDataSource {
  @override
  Future<String> getCurrentAppVersion() async {
    final packageInfo = await PackageInfo.fromPlatform();
    // Include build number in the version string (e.g., "1.1.1+1")
    return "${packageInfo.version}+${packageInfo.buildNumber}";
  }

  @override
  Future<String> getAppStoreUrl() async {
    final packageInfo = await PackageInfo.fromPlatform();
    final packageName = packageInfo.packageName;

    if (Platform.isAndroid) {
      return 'market://details?id=$packageName';
    } else if (Platform.isIOS) {
      // Using the actual App Store ID for AquaPartner
      return 'https://apps.apple.com/app/id6744552719';
    }

    throw UnsupportedError('Platform not supported for app store URL');
  }
}
