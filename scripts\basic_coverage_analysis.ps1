# Basic LCOV Coverage Analysis for Windows
param([string]$LcovPath = "coverage/lcov.info")

Write-Host "Flutter Test Coverage Analysis" -ForegroundColor Green
Write-Host "==============================" -ForegroundColor Green
Write-Host ""

if (!(Test-Path $LcovPath)) {
    Write-Host "LCOV file not found: $LcovPath" -ForegroundColor Red
    Write-Host "Please run 'flutter test --coverage' first" -ForegroundColor Yellow
    exit 1
}

Write-Host "Analyzing coverage data from: $LcovPath" -ForegroundColor Cyan
Write-Host ""

# Initialize counters
$totalFiles = 0
$totalLines = 0
$coveredLines = 0
$totalFunctions = 0
$coveredFunctions = 0

# Read and parse LCOV file
$lcovContent = Get-Content $LcovPath

foreach ($line in $lcovContent) {
    if ($line.StartsWith("SF:")) {
        $totalFiles++
    }
    elseif ($line.StartsWith("LF:")) {
        $totalLines += [int]($line.Split(":")[1])
    }
    elseif ($line.StartsWith("LH:")) {
        $coveredLines += [int]($line.Split(":")[1])
    }
    elseif ($line.StartsWith("FNF:")) {
        $totalFunctions += [int]($line.Split(":")[1])
    }
    elseif ($line.StartsWith("FNH:")) {
        $coveredFunctions += [int]($line.Split(":")[1])
    }
}

# Calculate coverage percentages
$lineCoverage = if ($totalLines -gt 0) { ($coveredLines / $totalLines) * 100 } else { 0 }
$functionCoverage = if ($totalFunctions -gt 0) { ($coveredFunctions / $totalFunctions) * 100 } else { 0 }

# Display results
Write-Host "OVERALL COVERAGE STATISTICS" -ForegroundColor Green
Write-Host "===========================" -ForegroundColor Green
Write-Host ""

Write-Host "Files Analyzed: $totalFiles" -ForegroundColor White
Write-Host ""

# Line Coverage
$lineColor = "Red"
if ($lineCoverage -ge 70) { $lineColor = "Green" }
elseif ($lineCoverage -ge 50) { $lineColor = "Yellow" }

Write-Host "Line Coverage: $($lineCoverage.ToString('F2'))% ($coveredLines/$totalLines)" -ForegroundColor $lineColor

# Function Coverage
if ($totalFunctions -gt 0) {
    $funcColor = "Red"
    if ($functionCoverage -ge 80) { $funcColor = "Green" }
    elseif ($functionCoverage -ge 60) { $funcColor = "Yellow" }
    
    Write-Host "Function Coverage: $($functionCoverage.ToString('F2'))% ($coveredFunctions/$totalFunctions)" -ForegroundColor $funcColor
}

Write-Host ""

# Production Readiness Assessment
Write-Host "PRODUCTION READINESS ASSESSMENT" -ForegroundColor Cyan
Write-Host "===============================" -ForegroundColor Cyan
Write-Host ""

$readinessScore = 0

# Line coverage scoring (50 points max)
if ($lineCoverage -ge 70) { $readinessScore += 50 }
elseif ($lineCoverage -ge 50) { $readinessScore += 30 }
elseif ($lineCoverage -ge 30) { $readinessScore += 15 }

# Function coverage scoring (30 points max)
if ($totalFunctions -gt 0) {
    if ($functionCoverage -ge 80) { $readinessScore += 30 }
    elseif ($functionCoverage -ge 60) { $readinessScore += 20 }
    elseif ($functionCoverage -ge 40) { $readinessScore += 10 }
} else {
    $readinessScore += 15
}

# File coverage scoring (20 points max)
if ($totalFiles -ge 50) { $readinessScore += 20 }
elseif ($totalFiles -ge 30) { $readinessScore += 15 }
elseif ($totalFiles -ge 15) { $readinessScore += 10 }

$readinessColor = "Red"
if ($readinessScore -ge 85) { $readinessColor = "Green" }
elseif ($readinessScore -ge 70) { $readinessColor = "Yellow" }

Write-Host "Production Readiness Score: $readinessScore%" -ForegroundColor $readinessColor
Write-Host ""

if ($readinessScore -ge 85) {
    Write-Host "READY FOR PRODUCTION" -ForegroundColor Green
    Write-Host "Your app has excellent test coverage and is ready for CI/CD implementation." -ForegroundColor Green
}
elseif ($readinessScore -ge 70) {
    Write-Host "APPROACHING PRODUCTION READINESS" -ForegroundColor Yellow
    Write-Host "Your app has good coverage but needs improvement in some areas." -ForegroundColor Yellow
}
else {
    Write-Host "NOT READY FOR PRODUCTION" -ForegroundColor Red
    Write-Host "Your app needs significant improvement in test coverage." -ForegroundColor Red
}

Write-Host ""

# Recommendations
Write-Host "RECOMMENDATIONS" -ForegroundColor Magenta
Write-Host "===============" -ForegroundColor Magenta
Write-Host ""

if ($lineCoverage -lt 70) {
    Write-Host "- Increase line coverage to at least 70% for production readiness" -ForegroundColor Yellow
}
if ($totalFunctions -gt 0 -and $functionCoverage -lt 80) {
    Write-Host "- Improve function coverage to at least 80%" -ForegroundColor Yellow
}
if ($totalFiles -lt 30) {
    Write-Host "- Consider adding more comprehensive test files" -ForegroundColor Yellow
}

Write-Host "- Use VS Code Coverage Gutters extension for visual coverage" -ForegroundColor Cyan
Write-Host "- Upload LCOV file to https://lcov-viewer.netlify.app/ for detailed analysis" -ForegroundColor Cyan
Write-Host "- Set up CI/CD pipeline with coverage gates" -ForegroundColor Cyan

Write-Host ""
Write-Host "Coverage analysis complete!" -ForegroundColor Green
Write-Host ""
Write-Host "LCOV file location: $LcovPath" -ForegroundColor Gray
Write-Host "Upload to: https://lcov-viewer.netlify.app/" -ForegroundColor Gray
