import 'package:flutter_test/flutter_test.dart';
import 'package:aquapartner/data/models/account_statement/account_statement_models.dart';
import 'package:aquapartner/domain/entities/account_statement/account_statement.dart';
import 'package:aquapartner/domain/entities/account_statement/account_statement_entity.dart';

void main() {
  group('AccountStatementModel Tests', () {
    late AccountStatementModel testAccountStatementModel;
    late AccountStatementEntryModel testAccountStatementEntryModel;
    late Map<String, dynamic> testJson;
    late DateTime testDateTime;

    setUp(() {
      testDateTime = DateTime(2024, 1, 15, 10, 30, 0);
      
      testAccountStatementEntryModel = AccountStatementEntryModel(
        txnDate: testDateTime,
        vchType: 'Invoice',
        invoiceNumber: 'INV-2024-001',
        particulars: 'Product Sale',
        debit: 0.0,
        credit: 1000.0,
        balance: 5000.0,
        amount: 1000.0,
      );

      testAccountStatementModel = AccountStatementModel(
        customerId: 'customer_123',
        lastSyncTime: testDateTime,
      );
      testAccountStatementModel.entries.add(testAccountStatementEntryModel);

      testJson = {
        'customerId': 'customer_123',
        'lastSyncTime': testDateTime.toIso8601String(),
        'results': [
          {
            'txnDate': testDateTime.toIso8601String(),
            'vchType': 'Invoice',
            'invoiceNumber': 'INV-2024-001',
            'particulars': 'Product Sale',
            'debit': 0.0,
            'credit': 1000.0,
            'balance': 5000.0,
            'amount': 1000.0,
          }
        ],
      };
    });

    group('Constructor Tests', () {
      test('should create AccountStatementModel with all required fields', () {
        expect(testAccountStatementModel.customerId, equals('customer_123'));
        expect(testAccountStatementModel.lastSyncTime, equals(testDateTime));
        expect(testAccountStatementModel.id, equals(0));
        expect(testAccountStatementModel.entries, hasLength(1));
        expect(testAccountStatementModel.entries.first, isA<AccountStatementEntryModel>());
      });

      test('should create AccountStatementModel with empty entries', () {
        final emptyModel = AccountStatementModel(
          customerId: 'customer_123',
          lastSyncTime: testDateTime,
        );

        expect(emptyModel.customerId, equals('customer_123'));
        expect(emptyModel.lastSyncTime, equals(testDateTime));
        expect(emptyModel.entries, isEmpty);
      });
    });

    group('JSON Serialization Tests', () {
      test('should convert AccountStatementModel to JSON correctly', () {
        final json = testAccountStatementModel.toJson();

        expect(json['customerId'], equals('customer_123'));
        expect(json['lastSyncTime'], equals(testDateTime.toIso8601String()));
        expect(json['results'], isA<List>());
        expect(json['results'], hasLength(1));
        expect(json['results'][0]['txnDate'], equals(testDateTime.toIso8601String()));
        expect(json['results'][0]['vchType'], equals('Invoice'));
        expect(json['results'][0]['invoiceNumber'], equals('INV-2024-001'));
        expect(json['results'][0]['particulars'], equals('Product Sale'));
        expect(json['results'][0]['debit'], equals(0.0));
        expect(json['results'][0]['credit'], equals(1000.0));
        expect(json['results'][0]['balance'], equals(5000.0));
        expect(json['results'][0]['amount'], equals(1000.0));
      });

      test('should create AccountStatementModel from JSON correctly', () {
        final accountStatementModel = AccountStatementModel.fromJson(testJson, 'customer_123');

        expect(accountStatementModel.customerId, equals('customer_123'));
        expect(accountStatementModel.lastSyncTime, isA<DateTime>());
        expect(accountStatementModel.entries, hasLength(1));
        expect(accountStatementModel.entries.first.txnDate, equals(testDateTime));
        expect(accountStatementModel.entries.first.vchType, equals('Invoice'));
        expect(accountStatementModel.entries.first.invoiceNumber, equals('INV-2024-001'));
        expect(accountStatementModel.entries.first.particulars, equals('Product Sale'));
        expect(accountStatementModel.entries.first.debit, equals(0.0));
        expect(accountStatementModel.entries.first.credit, equals(1000.0));
        expect(accountStatementModel.entries.first.balance, equals(5000.0));
        expect(accountStatementModel.entries.first.amount, equals(1000.0));
      });

      test('should handle missing results field in JSON', () {
        final jsonWithoutResults = <String, dynamic>{
          'customerId': 'customer_123',
          'lastSyncTime': testDateTime.toIso8601String(),
        };

        final accountStatementModel = AccountStatementModel.fromJson(jsonWithoutResults, 'customer_123');

        expect(accountStatementModel.customerId, equals('customer_123'));
        expect(accountStatementModel.entries, isEmpty);
      });

      test('should handle null results field in JSON', () {
        final jsonWithNullResults = <String, dynamic>{
          'customerId': 'customer_123',
          'lastSyncTime': testDateTime.toIso8601String(),
          'results': null,
        };

        final accountStatementModel = AccountStatementModel.fromJson(jsonWithNullResults, 'customer_123');

        expect(accountStatementModel.customerId, equals('customer_123'));
        expect(accountStatementModel.entries, isEmpty);
      });

      test('should handle empty results array in JSON', () {
        final jsonWithEmptyResults = <String, dynamic>{
          'customerId': 'customer_123',
          'lastSyncTime': testDateTime.toIso8601String(),
          'results': [],
        };

        final accountStatementModel = AccountStatementModel.fromJson(jsonWithEmptyResults, 'customer_123');

        expect(accountStatementModel.customerId, equals('customer_123'));
        expect(accountStatementModel.entries, isEmpty);
      });
    });

    group('Entity Conversion Tests', () {
      test('should convert AccountStatementModel to AccountStatement entity correctly', () {
        final accountStatementEntity = testAccountStatementModel.toEntity();

        expect(accountStatementEntity, isA<AccountStatement>());
        expect(accountStatementEntity.lastSyncTime, equals(testAccountStatementModel.lastSyncTime));
        expect(accountStatementEntity.entries, hasLength(1));
        expect(accountStatementEntity.entries.first, isA<AccountStatementEntity>());
        expect(accountStatementEntity.entries.first.txnDate, equals(testDateTime));
        expect(accountStatementEntity.entries.first.vchType, equals('Invoice'));
        expect(accountStatementEntity.entries.first.invoiceNumber, equals('INV-2024-001'));
        expect(accountStatementEntity.entries.first.particulars, equals('Product Sale'));
        expect(accountStatementEntity.entries.first.debit, equals(0.0));
        expect(accountStatementEntity.entries.first.credit, equals(1000.0));
        expect(accountStatementEntity.entries.first.balance, equals(5000.0));
        expect(accountStatementEntity.entries.first.amount, equals(1000.0));
      });

      test('should create AccountStatementModel from AccountStatement entity correctly', () {
        final accountStatementEntity = AccountStatement(
          entries: [
            AccountStatementEntity(
              txnDate: testDateTime,
              vchType: 'Payment',
              invoiceNumber: 'PAY-2024-001',
              particulars: 'Payment Received',
              debit: 500.0,
              credit: 0.0,
              balance: 4500.0,
              amount: 500.0,
            )
          ],
          lastSyncTime: testDateTime,
        );

        final accountStatementModel = AccountStatementModel.fromEntity(accountStatementEntity, 'customer_456');

        expect(accountStatementModel.customerId, equals('customer_456'));
        expect(accountStatementModel.lastSyncTime, equals(testDateTime));
        expect(accountStatementModel.entries, hasLength(1));
        expect(accountStatementModel.entries.first.txnDate, equals(testDateTime));
        expect(accountStatementModel.entries.first.vchType, equals('Payment'));
        expect(accountStatementModel.entries.first.invoiceNumber, equals('PAY-2024-001'));
        expect(accountStatementModel.entries.first.particulars, equals('Payment Received'));
        expect(accountStatementModel.entries.first.debit, equals(500.0));
        expect(accountStatementModel.entries.first.credit, equals(0.0));
        expect(accountStatementModel.entries.first.balance, equals(4500.0));
        expect(accountStatementModel.entries.first.amount, equals(500.0));
      });

      test('should maintain data integrity during entity conversion', () {
        final originalEntity = testAccountStatementModel.toEntity();
        final backToModel = AccountStatementModel.fromEntity(originalEntity, 'customer_123');
        final finalEntity = backToModel.toEntity();

        expect(finalEntity.lastSyncTime, equals(originalEntity.lastSyncTime));
        expect(finalEntity.entries.length, equals(originalEntity.entries.length));
        expect(finalEntity.entries.first.txnDate, equals(originalEntity.entries.first.txnDate));
        expect(finalEntity.entries.first.vchType, equals(originalEntity.entries.first.vchType));
        expect(finalEntity.entries.first.amount, equals(originalEntity.entries.first.amount));
      });
    });

    group('Edge Cases and Validation Tests', () {
      test('should handle multiple entries', () {
        final multiEntryModel = AccountStatementModel(
          customerId: 'customer_123',
          lastSyncTime: testDateTime,
        );

        final entry1 = AccountStatementEntryModel(
          txnDate: testDateTime,
          vchType: 'Invoice',
          invoiceNumber: 'INV-2024-001',
          particulars: 'Product Sale 1',
          debit: 0.0,
          credit: 1000.0,
          balance: 5000.0,
          amount: 1000.0,
        );

        final entry2 = AccountStatementEntryModel(
          txnDate: testDateTime.add(Duration(days: 1)),
          vchType: 'Payment',
          invoiceNumber: 'PAY-2024-001',
          particulars: 'Payment Received',
          debit: 500.0,
          credit: 0.0,
          balance: 4500.0,
          amount: 500.0,
        );

        multiEntryModel.entries.addAll([entry1, entry2]);

        expect(multiEntryModel.entries, hasLength(2));
        expect(multiEntryModel.entries[0].vchType, equals('Invoice'));
        expect(multiEntryModel.entries[1].vchType, equals('Payment'));
      });

      test('should handle special characters in string fields', () {
        final specialCharEntry = AccountStatementEntryModel(
          txnDate: testDateTime,
          vchType: 'Invoice & Credit',
          invoiceNumber: 'INV-2024-001 (revised)',
          particulars: 'Product Sale & Service',
          debit: 0.0,
          credit: 1000.0,
          balance: 5000.0,
          amount: 1000.0,
        );

        expect(specialCharEntry.vchType, equals('Invoice & Credit'));
        expect(specialCharEntry.invoiceNumber, equals('INV-2024-001 (revised)'));
        expect(specialCharEntry.particulars, equals('Product Sale & Service'));
      });

      test('should handle extreme numeric values', () {
        final extremeEntry = AccountStatementEntryModel(
          txnDate: testDateTime,
          vchType: 'Invoice',
          invoiceNumber: 'INV-2024-001',
          particulars: 'Large Transaction',
          debit: double.maxFinite,
          credit: double.maxFinite,
          balance: double.maxFinite,
          amount: double.maxFinite,
        );

        expect(extremeEntry.debit, equals(double.maxFinite));
        expect(extremeEntry.credit, equals(double.maxFinite));
        expect(extremeEntry.balance, equals(double.maxFinite));
        expect(extremeEntry.amount, equals(double.maxFinite));
      });

      test('should handle negative numeric values', () {
        final negativeEntry = AccountStatementEntryModel(
          txnDate: testDateTime,
          vchType: 'Adjustment',
          invoiceNumber: 'ADJ-2024-001',
          particulars: 'Negative Adjustment',
          debit: -500.0,
          credit: -1000.0,
          balance: -2000.0,
          amount: -500.0,
        );

        expect(negativeEntry.debit, equals(-500.0));
        expect(negativeEntry.credit, equals(-1000.0));
        expect(negativeEntry.balance, equals(-2000.0));
        expect(negativeEntry.amount, equals(-500.0));
      });

      test('should handle round-trip JSON serialization', () {
        final originalJson = testAccountStatementModel.toJson();
        final recreatedModel = AccountStatementModel.fromJson(originalJson, 'customer_123');
        final finalJson = recreatedModel.toJson();

        expect(finalJson['customerId'], equals(originalJson['customerId']));
        expect(finalJson['results'].length, equals(originalJson['results'].length));
        expect(finalJson['results'][0]['vchType'], equals(originalJson['results'][0]['vchType']));
        expect(finalJson['results'][0]['amount'], equals(originalJson['results'][0]['amount']));
      });
    });
  });

  group('AccountStatementEntryModel Tests', () {
    late AccountStatementEntryModel testAccountStatementEntryModel;
    late Map<String, dynamic> testEntryJson;
    late DateTime testDateTime;

    setUp(() {
      testDateTime = DateTime(2024, 1, 15, 10, 30, 0);
      
      testEntryJson = {
        'txnDate': testDateTime.toIso8601String(),
        'vchType': 'Invoice',
        'invoiceNumber': 'INV-2024-001',
        'particulars': 'Product Sale',
        'debit': 0.0,
        'credit': 1000.0,
        'balance': 5000.0,
        'amount': 1000.0,
      };

      testAccountStatementEntryModel = AccountStatementEntryModel.fromJson(testEntryJson);
    });

    group('Constructor Tests', () {
      test('should create AccountStatementEntryModel with all required fields', () {
        expect(testAccountStatementEntryModel.txnDate, equals(testDateTime));
        expect(testAccountStatementEntryModel.vchType, equals('Invoice'));
        expect(testAccountStatementEntryModel.invoiceNumber, equals('INV-2024-001'));
        expect(testAccountStatementEntryModel.particulars, equals('Product Sale'));
        expect(testAccountStatementEntryModel.debit, equals(0.0));
        expect(testAccountStatementEntryModel.credit, equals(1000.0));
        expect(testAccountStatementEntryModel.balance, equals(5000.0));
        expect(testAccountStatementEntryModel.amount, equals(1000.0));
        expect(testAccountStatementEntryModel.id, equals(0));
      });
    });

    group('JSON Serialization Tests', () {
      test('should convert AccountStatementEntryModel to JSON correctly', () {
        final json = testAccountStatementEntryModel.toJson();

        expect(json['txnDate'], equals(testDateTime.toIso8601String()));
        expect(json['vchType'], equals('Invoice'));
        expect(json['invoiceNumber'], equals('INV-2024-001'));
        expect(json['particulars'], equals('Product Sale'));
        expect(json['debit'], equals(0.0));
        expect(json['credit'], equals(1000.0));
        expect(json['balance'], equals(5000.0));
        expect(json['amount'], equals(1000.0));
      });

      test('should handle missing JSON fields with defaults', () {
        final incompleteJson = <String, dynamic>{
          'txnDate': testDateTime.toIso8601String(),
        };

        final entryModel = AccountStatementEntryModel.fromJson(incompleteJson);

        expect(entryModel.txnDate, equals(testDateTime));
        expect(entryModel.vchType, isEmpty);
        expect(entryModel.invoiceNumber, isEmpty);
        expect(entryModel.particulars, isEmpty);
        expect(entryModel.debit, equals(0.0));
        expect(entryModel.credit, equals(0.0));
        expect(entryModel.balance, equals(0.0));
        expect(entryModel.amount, equals(0.0));
      });

      test('should handle total field as amount for backward compatibility', () {
        final jsonWithTotal = <String, dynamic>{
          'txnDate': testDateTime.toIso8601String(),
          'vchType': 'Invoice',
          'invoiceNumber': 'INV-2024-001',
          'particulars': 'Product Sale',
          'debit': 0.0,
          'credit': 1000.0,
          'balance': 5000.0,
          'total': 1500.0, // Using 'total' instead of 'amount'
        };

        final entryModel = AccountStatementEntryModel.fromJson(jsonWithTotal);

        expect(entryModel.amount, equals(1500.0));
      });

      test('should prefer amount over total when both are present', () {
        final jsonWithBoth = <String, dynamic>{
          'txnDate': testDateTime.toIso8601String(),
          'vchType': 'Invoice',
          'invoiceNumber': 'INV-2024-001',
          'particulars': 'Product Sale',
          'debit': 0.0,
          'credit': 1000.0,
          'balance': 5000.0,
          'amount': 1000.0,
          'total': 1500.0,
        };

        final entryModel = AccountStatementEntryModel.fromJson(jsonWithBoth);

        expect(entryModel.amount, equals(1000.0));
      });

      test('should handle round-trip JSON serialization', () {
        final originalJson = testAccountStatementEntryModel.toJson();
        final recreatedModel = AccountStatementEntryModel.fromJson(originalJson);
        final finalJson = recreatedModel.toJson();

        expect(finalJson, equals(originalJson));
      });
    });

    group('Entity Conversion Tests', () {
      test('should convert AccountStatementEntryModel to AccountStatementEntity correctly', () {
        final entryEntity = testAccountStatementEntryModel.toEntity();

        expect(entryEntity, isA<AccountStatementEntity>());
        expect(entryEntity.txnDate, equals(testAccountStatementEntryModel.txnDate));
        expect(entryEntity.vchType, equals(testAccountStatementEntryModel.vchType));
        expect(entryEntity.invoiceNumber, equals(testAccountStatementEntryModel.invoiceNumber));
        expect(entryEntity.particulars, equals(testAccountStatementEntryModel.particulars));
        expect(entryEntity.debit, equals(testAccountStatementEntryModel.debit));
        expect(entryEntity.credit, equals(testAccountStatementEntryModel.credit));
        expect(entryEntity.balance, equals(testAccountStatementEntryModel.balance));
        expect(entryEntity.amount, equals(testAccountStatementEntryModel.amount));
      });

      test('should create AccountStatementEntryModel from AccountStatementEntity correctly', () {
        final entryEntity = AccountStatementEntity(
          txnDate: testDateTime,
          vchType: 'Payment',
          invoiceNumber: 'PAY-2024-001',
          particulars: 'Payment Received',
          debit: 500.0,
          credit: 0.0,
          balance: 4500.0,
          amount: 500.0,
        );

        final entryModel = AccountStatementEntryModel.fromEntity(entryEntity);

        expect(entryModel.txnDate, equals(testDateTime));
        expect(entryModel.vchType, equals('Payment'));
        expect(entryModel.invoiceNumber, equals('PAY-2024-001'));
        expect(entryModel.particulars, equals('Payment Received'));
        expect(entryModel.debit, equals(500.0));
        expect(entryModel.credit, equals(0.0));
        expect(entryModel.balance, equals(4500.0));
        expect(entryModel.amount, equals(500.0));
      });

      test('should maintain data integrity during entity conversion', () {
        final originalEntity = testAccountStatementEntryModel.toEntity();
        final backToModel = AccountStatementEntryModel.fromEntity(originalEntity);
        final finalEntity = backToModel.toEntity();

        expect(finalEntity.txnDate, equals(originalEntity.txnDate));
        expect(finalEntity.vchType, equals(originalEntity.vchType));
        expect(finalEntity.invoiceNumber, equals(originalEntity.invoiceNumber));
        expect(finalEntity.particulars, equals(originalEntity.particulars));
        expect(finalEntity.debit, equals(originalEntity.debit));
        expect(finalEntity.credit, equals(originalEntity.credit));
        expect(finalEntity.balance, equals(originalEntity.balance));
        expect(finalEntity.amount, equals(originalEntity.amount));
      });
    });
  });
}
