import UIKit
import Flutter
import Firebase
import FirebaseCore
import Firebase<PERSON>uth
import UserNotifications

@main
@objc class AppDelegate: FlutterAppDelegate {
    
    override func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
    ) -> Bool {
        FirebaseApp.configure()
        
        // Configure Firebase Auth settings
        configureFirebaseAuth()
        
        // Clear keychain items that might persist after app uninstall
        clearKeychainOnReinstall()
        
        // Setup push notifications with proper permissions
        setupPushNotifications(application)
        
        GeneratedPluginRegistrant.register(with: self)
        return super.application(application, didFinishLaunchingWithOptions: launchOptions)
    }
    
    private func configureFirebaseAuth() {
        let auth = Auth.auth()
        
        // Disable app verification for testing (enable for production)
        auth.settings?.isAppVerificationDisabledForTesting = false
        
        // Set language code
        auth.languageCode = "en"
        
        // Configure for iOS 14+ compatibility
        if #available(iOS 14.0, *) {
            // Enable additional logging for debugging
            auth.settings?.isAppVerificationDisabledForTesting = false
        }
    }
    
    private func clearKeychainOnReinstall() {
        let bundleId = Bundle.main.bundleIdentifier ?? ""
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: bundleId
        ]
        SecItemDelete(query as CFDictionary)
    }
    
    private func setupPushNotifications(_ application: UIApplication) {
        if #available(iOS 10.0, *) {
            UNUserNotificationCenter.current().delegate = self
            
            // Request authorization with all necessary options
            let authOptions: UNAuthorizationOptions = [.alert, .badge, .sound, .provisional]
            UNUserNotificationCenter.current().requestAuthorization(
                options: authOptions
            ) { granted, error in
                if let error = error {
                    print("❌ Push notification authorization error: \(error)")
                } else {
                    print("✅ Push notification authorization granted: \(granted)")
                }
                
                DispatchQueue.main.async {
                    application.registerForRemoteNotifications()
                }
            }
        } else {
            // Fallback for iOS 9
            let settings = UIUserNotificationSettings(
                types: [.alert, .badge, .sound],
                categories: nil
            )
            application.registerUserNotificationSettings(settings)
            application.registerForRemoteNotifications()
        }
    }
    
    // CRITICAL: Enhanced APNs token handling
    override func application(
        _ application: UIApplication,
        didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data
    ) {
        print("📱 APNs token received: \(deviceToken.map { String(format: "%02.2hhx", $0) }.joined())")
        
        // Determine correct token type based on build configuration
        let tokenType = getAPNSTokenType()
        
        // Set APNs token with retry mechanism
        setAPNSTokenWithRetry(deviceToken: deviceToken, tokenType: tokenType)
    }
    
    private func getAPNSTokenType() -> AuthAPNSTokenType {
        #if DEBUG
            return .sandbox
        #else
            return .prod
        #endif
    }
    
    private func setAPNSTokenWithRetry(deviceToken: Data, tokenType: AuthAPNSTokenType, retryCount: Int = 0) {
        let auth = Auth.auth()
        
        // Set the APNs token
        auth.setAPNSToken(deviceToken, type: tokenType)
        
        // Verify token was set successfully (iOS 14+ validation)
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            // Additional validation for iOS 14+
            if #available(iOS 14.0, *) {
                self.validateAPNSTokenRegistration(retryCount: retryCount)
            }
        }
        
        print("✅ APNs token set successfully with type: \(tokenType == .sandbox ? "sandbox" : "production")")
    }
    
    @available(iOS 14.0, *)
    private func validateAPNSTokenRegistration(retryCount: Int) {
        // Check if we can receive notifications
        UNUserNotificationCenter.current().getNotificationSettings { settings in
            if settings.authorizationStatus != .authorized && retryCount < 3 {
                print("⚠️ Notification authorization not granted, retrying...")
                DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                    UIApplication.shared.registerForRemoteNotifications()
                }
            }
        }
    }
    
    // Handle APNs registration failure
    override func application(
        _ application: UIApplication,
        didFailToRegisterForRemoteNotificationsWithError error: Error
    ) {
        print("❌ Failed to register for remote notifications: \(error)")
        
        // Fallback: Disable silent verification for this session
        Auth.auth().settings?.isAppVerificationDisabledForTesting = true
        
        // Retry registration after delay
        DispatchQueue.main.asyncAfter(deadline: .now() + 5.0) {
            application.registerForRemoteNotifications()
        }
    }
    
    // CRITICAL: Enhanced silent notification handling
    override func application(
        _ application: UIApplication,
        didReceiveRemoteNotification userInfo: [AnyHashable : Any],
        fetchCompletionHandler completionHandler: @escaping (UIBackgroundFetchResult) -> Void
    ) {
        print("📨 Received remote notification: \(userInfo)")
        
        // Check if this is a Firebase Auth notification
        if Auth.auth().canHandleNotification(userInfo) {
            print("✅ Firebase Auth handled the notification")
            completionHandler(.noData)
            return
        }
        
        // Handle other notifications
        print("📱 Handling non-Firebase notification")
        completionHandler(.newData)
    }
    
    // iOS 14+ specific notification handling
    @available(iOS 10.0, *)
    override func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        willPresent notification: UNNotification,
        withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void
    ) {
        let userInfo = notification.request.content.userInfo
        
        // Check if this is a Firebase Auth notification
        if Auth.auth().canHandleNotification(userInfo) {
            print("✅ Firebase Auth handled foreground notification")
            completionHandler([])
            return
        }
        
        // Handle other foreground notifications
        if #available(iOS 14.0, *) {
            completionHandler([.banner, .sound, .badge])
        } else {
            completionHandler([.alert, .sound, .badge])
        }
    }
}
