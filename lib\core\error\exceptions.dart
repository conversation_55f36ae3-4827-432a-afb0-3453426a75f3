/// Exception thrown when there is a server error
class ServerException implements Exception {}

/// Exception thrown when there is a database error
class DatabaseException implements Exception {}

/// Exception thrown when there is a cache error
class CacheException implements Exception {}

/// Exception thrown when there is a network error
class NetworkException implements Exception {}

/// Exception thrown when there is an authentication error
class AuthException implements Exception {}

/// Exception thrown when there is an authentication error
class NotFoundException implements Exception {}

/// Exception thrown when there is a validation error
class ValidationException implements Exception {
  final String message;

  ValidationException(this.message);
}

/// Exception thrown when there is an unexpected error
class UnexpectedException implements Exception {
  final String message;

  UnexpectedException(this.message);
}
