import 'package:mongo_dart/mongo_dart.dart';
import '../../domain/entities/interested_product.dart';

class InterestedProductModel extends InterestedProduct {
  InterestedProductModel({
    super.id,
    required super.customerId,
    required super.mobile,
    required super.productName,
    required super.datetime,
    required super.source,
    super.isSynced = false,
  });

  factory InterestedProductModel.fromJson(Map<String, dynamic> json) {
    // Handle ObjectId conversion to String
    String? idString;
    if (json['_id'] != null) {
      if (json['_id'] is ObjectId) {
        idString = (json['_id'] as ObjectId).oid;
      } else if (json['_id'] is String) {
        idString = json['_id'];
      }
    }

    return InterestedProductModel(
      id: idString,
      customerId: json['customerId'] ?? '',
      mobile: json['mobile'] ?? '',
      productName: json['productName'] ?? '',
      datetime:
          json['datetime'] != null
              ? DateTime.parse(json['datetime'])
              : DateTime.now(),
      source: json['source'] ?? '',
      isSynced: json['isSynced'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    final map = {
      'customerId': customerId,
      'mobile': mobile,
      'productName': productName,
      'datetime': datetime.toIso8601String(),
      'source': source,
      'isSynced': isSynced,
    };

    // Only include _id if it exists
    if (id != null) {
      map['_id'] = id!;
    }

    return map;
  }

  // Convert to MongoDB document (without isSynced field)
  Map<String, dynamic> toMongoDocument() {
    final map = <String, dynamic>{
      'mobile': mobile,
      'productName': productName,
      'datetime': datetime.toIso8601String(),
      'source': source,
    };

    // Handle customerId - try to convert to ObjectId if it's in the correct format
    try {
      // Check if the customerId is a valid 24-character hex string (ObjectId format)
      if (customerId.length == 24 &&
          RegExp(r'^[0-9a-fA-F]{24}$').hasMatch(customerId)) {
        // Explicitly set as dynamic to avoid type issues
        map['customerId'] = ObjectId.parse(customerId);
      } else {
        map['customerId'] = customerId;
      }
    } catch (e) {
      // If conversion fails, use the string customerId as is
      map['customerId'] = customerId;
    }

    return map;
  }

  InterestedProductModel copyWith({
    String? id,
    String? customerId,
    String? name,
    String? mobile,
    String? productId,
    String? productName,
    DateTime? datetime,
    String? source,
    bool? isSynced,
  }) {
    return InterestedProductModel(
      id: id ?? this.id,
      customerId: customerId ?? this.customerId,
      mobile: mobile ?? this.mobile,
      productName: productName ?? this.productName,
      datetime: datetime ?? this.datetime,
      source: source ?? this.source,
      isSynced: isSynced ?? this.isSynced,
    );
  }
}
