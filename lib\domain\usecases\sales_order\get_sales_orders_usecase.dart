import 'package:dartz/dartz.dart';

import '../../../core/error/failures.dart';
import '../../entities/sales_order/sales_order.dart';
import '../../repositories/sales_order_repository.dart';
import '../../../core/utils/logger.dart';

class GetSalesOrdersUseCase {
  final SalesOrderRepository repository;
  final AppLogger logger;

  GetSalesOrdersUseCase({required this.repository, required this.logger});

  Future<Either<Failure, List<SalesOrder>>> call(String customerId) async {
    logger.i('GetSalesOrdersUseCase called with customerId: $customerId');
    return await repository.getSalesOrders(customerId);
  }
}
