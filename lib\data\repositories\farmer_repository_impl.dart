import 'package:dartz/dartz.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/utils/logger.dart';
import '../../domain/entities/farmer_visit/farmer.dart';
import '../../domain/repositories/farmer_repository.dart';
import '../datasources/local/farmer_local_datasource.dart';
import '../datasources/remote/farmer_remote_datasource.dart';
import '../entities/farmer_visits/farmer_entity.dart';
import '../entities/farmer_visits/visit_entity.dart';
import '../models/farmer_visits/farmer_data_model.dart';

/// Implementation of the [FarmerRepository] interface.
/// This class orchestrates data flow between data sources.
class FarmerRepositoryImpl implements FarmerRepository {
  final FarmerLocalDataSource localDataSource;
  final FarmerRemoteDataSource remoteDataSource; // Inject remote data source
  final AppLogger logger;

  FarmerRepositoryImpl({
    required this.localDataSource,
    required this.remoteDataSource,
    required this.logger,
  });

  @override
  Future<Either<Failure, List<Farmer>>> getAllFarmers(String customerId) async {
    // Data Strategy: Try local first, if fails, try remote, then cache remote.
    try {
      // 1. Try to get data from the local data source (ObjectBox)
      final farmerEntities = await localDataSource.getAllFarmers();

      if (farmerEntities.isNotEmpty) {
        // If local data exists, map and return it
        final domainFarmers = farmerEntities.map((e) => e.toDomain()).toList();

        logger.i('Fetched ${domainFarmers.length} farmers from cache.');
        return Right(domainFarmers);
      } else {
        // 2. If local data is empty, try fetching from remote
        logger.i('Cache empty, fetching from remote...');
        try {
          final FarmerDataModel? remoteDataModel = await remoteDataSource
              .getFarmerData(customerId);
          // Map remote data models to domain entities
          final domainFarmersFromRemote = remoteDataModel?.toDomain() ?? [];

          // 3. Cache the remote data locally
          // Map domain entities back to local entities for storage
          final farmerEntitiesToCache =
              domainFarmersFromRemote.map((d) {
                final entity = FarmerEntity.fromDomain(d);
                // Map and add visits with proper relationship
                entity.visits.addAll(
                  d.visits
                      .map(
                        (v) => VisitEntity.fromDomain(v, farmerEntity: entity),
                      )
                      .toList(),
                );
                return entity;
              }).toList();

          await localDataSource.storeFarmersAndVisits(farmerEntitiesToCache);
          logger.i(
            'Fetched ${domainFarmersFromRemote.length} farmers from remote and cached.',
          );
          return Right(
            domainFarmersFromRemote,
          ); // Return data fetched from remote
        } on ServerException {
          // If remote fetch fails, return ServerFailure
          logger.e('Server error fetching data.');
          return Left(ServerFailure());
        }
      }
    } on CacheException {
      // If local data source fails unexpectedly, return CacheFailure
      logger.e('Cache error fetching data.');
      return Left(CacheFailure());
      // In a real app, you might still attempt remote fetch here
      // if the cache error is not critical (e.g., just read failure).
    }
  }

  @override
  Future<Either<Failure, void>> storeFarmerData(List<Farmer> farmers) async {
    try {
      // Map domain entities to data layer entities for storage
      final farmerEntitiesToStore =
          farmers.map((f) {
            final farmerEntity = FarmerEntity.fromDomain(f);
            // Also map and add visits to the entity's ToMany relation with proper relationship
            farmerEntity.visits.addAll(
              f.visits
                  .map(
                    (v) =>
                        VisitEntity.fromDomain(v, farmerEntity: farmerEntity),
                  )
                  .toList(),
            );
            return farmerEntity;
          }).toList();

      await localDataSource.storeFarmersAndVisits(farmerEntitiesToStore);
      logger.i('Stored farmer data locally.');
      return Right(null); // Indicate success
    } on CacheException catch (e) {
      // Return CacheFailure if local data source fails
      logger.e('Cache error storing data: $e');
      return Left(CacheFailure());
    }
    // You might add logic here to also send data to the remote data source
    // after successfully storing it locally.
    // try {
    //   await remoteDataSource.sendFarmerData(farmers); // Assuming a method to send data
    //   return Right(null); // Success if both local and remote succeed
    // } on ServerException {
    //   // If remote fails after local success, decide how to handle (e.g., return ServerFailure,
    //   // but local data is still saved, maybe flag for later sync).
    //   return Left(ServerFailure(message: 'Data stored locally but failed to sync remotely.'));
    // }
  }

  @override
  Future<Either<Failure, List<Farmer>>> syncFarmers(String customerId) async {
    try {
      logger.i('Starting one-way sync for farmers data...');

      try {
        // 1. First fetch from remote to ensure we have data before clearing local
        final FarmerDataModel? remoteDataModel = await remoteDataSource
            .getFarmerData(customerId);

        // 2. Map remote data models to domain entities
        final domainFarmersFromRemote = remoteDataModel?.toDomain() ?? [];

        // 3. Only proceed with clearing and storing if we got data from remote
        if (domainFarmersFromRemote.isNotEmpty) {
          // 4. Clear all local data
          await localDataSource.clearAllFarmersAndVisits();
          logger.i('Cleared all local farmers data for one-way sync');

          // 5. Map domain entities back to local entities for storage
          final farmerEntitiesToCache =
              domainFarmersFromRemote.map((d) {
                final entity = FarmerEntity.fromDomain(d);
                // Map and add visits with proper relationship
                entity.visits.addAll(
                  d.visits
                      .map(
                        (v) => VisitEntity.fromDomain(v, farmerEntity: entity),
                      )
                      .toList(),
                );
                return entity;
              }).toList();

          // 6. Store the remote data locally
          await localDataSource.storeFarmersAndVisits(farmerEntitiesToCache);
          logger.i(
            'One-way sync completed: Fetched ${domainFarmersFromRemote.length} farmers from remote and cached.',
          );
        } else {
          logger.i(
            'One-way sync completed: No farmers data found on remote. Local data preserved.',
          );
        }

        // 7. Return the remote data
        return Right(domainFarmersFromRemote);
      } on ServerException {
        // If remote fetch fails, try to get local data instead of returning failure
        logger.e(
          'Server error during one-way sync. Attempting to load local data.',
        );
        try {
          final localFarmers = await localDataSource.getAllFarmers();
          final domainFarmers = localFarmers.map((e) => e.toDomain()).toList();

          if (domainFarmers.isNotEmpty) {
            logger.i(
              'Recovered ${domainFarmers.length} farmers from local storage after sync failure.',
            );
            return Right(domainFarmers);
          } else {
            return Left(ServerFailure());
          }
        } catch (e) {
          logger.e('Failed to recover local data after sync failure: $e');
          return Left(ServerFailure());
        }
      }
    } on CacheException {
      // If local data source operations fail, return CacheFailure
      logger.e('Cache error during one-way sync.');
      return Left(CacheFailure());
    } catch (e) {
      // Handle any other unexpected errors
      logger.e('Unexpected error during one-way sync: $e');
      return Left(UnexpectedFailure('Unexpected error during sync: $e'));
    }
  }
}
