import 'package:aqua_ui/aqua_ui.dart';
import 'package:aquapartner/core/mixins/analytics_mixin.dart';
import 'package:aquapartner/core/utils/currency_formatter.dart';
import 'package:aquapartner/presentation/widgets/loading_widget.dart';
import 'package:aquapartner/presentation/widgets/styled_generic_table.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';

import '../../domain/entities/credit_notes/credit_note.dart';
import '../cubit/credit_notes/credit_notes_cubit.dart';
import '../cubit/credit_notes/credit_notes_state.dart';

class CreditNotesPage extends StatefulWidget {
  const CreditNotesPage({super.key});

  @override
  State<CreditNotesPage> createState() => _CreditNotesPageState();
}

class _CreditNotesPageState extends State<CreditNotesPage> with AnalyticsMixin {
  @override
  String get screenName => 'CreditNotesPage';

  @override
  void initState() {
    super.initState();
    context.read<CreditNotesCubit>().loadCreditNotes();

    // Track page initialization
    trackEvent('credit_notes_page_initialized');
  }

  void _trackCreditNoteTap(CreditNote creditNote) {
    trackUserInteraction(
      'view_credit_note_details',
      'table_row',
      elementId: creditNote.creditNoteNumber,
      additionalParams: {
        'credit_note_number': creditNote.creditNoteNumber,
        'credit_note_date':
            creditNote.date.toIso8601String(), // Convert DateTime to string
        'credit_note_amount': creditNote.amount.toString(),
        'credit_note_status': creditNote.category ?? 'N/A',
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<CreditNotesCubit, CreditNotesState>(
      builder: (context, state) {
        if (state is CreditNotesLoaded) {
          return SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 16),
                  StyledGenericTable<CreditNote>(
                    items: state.creditNotes,
                    showDividers: true,
                    onRowTap: (creditNote) {
                      _trackCreditNoteTap(creditNote);
                      // Navigate to credit note details or show details
                    },
                    columns: [
                      ColumnConfig<CreditNote>(
                        title: 'Date',
                        width: 100,
                        cellBuilder:
                            (creditNote) => AquaText.body(
                              DateFormat('dd-MM-yyyy').format(creditNote.date),
                            ),
                      ),
                      ColumnConfig<CreditNote>(
                        title: 'Credit Note Number',
                        width: 150,
                        cellBuilder:
                            (creditNote) => AquaText.body(
                              creditNote.creditNoteNumber,
                              color: acPrimaryBlue,
                              weight: AquaFontWeight.semibold,
                            ),
                      ),
                      ColumnConfig<CreditNote>(
                        title: 'Total',
                        width: 100,
                        titleAlignment: Alignment.center,
                        bodyAlignment: Alignment.centerRight,
                        cellBuilder:
                            (creditNote) => AquaText.body(
                              CurrencyFormatter.formatAsINR(
                                creditNote.amount,
                                decimalPlaces: 0,
                              ),
                              weight: AquaFontWeight.bold,
                            ),
                      ),
                      ColumnConfig<CreditNote>(
                        title: 'Category',
                        width: 100,
                        cellBuilder:
                            (creditNote) =>
                                AquaText.body(creditNote.category ?? 'N/A'),
                      ),
                      /* ColumnConfig<CreditNote>(
                        title: 'Type',
                        width: 80,
                        cellBuilder:
                            (creditNote) => AquaText.body(
                              creditNote.categoryType ?? 'N/A',
                            ),
                      ), */
                    ],
                  ),
                ],
              ),
            ),
          );
        }
        return const LoadingWidget(message: "Credit Notes Loading...");
      },
      listener: (context, state) {
        // Track state changes for analytics
        if (state is CreditNotesLoading) {
          trackEvent('credit_notes_loading');
        } else if (state is CreditNotesLoaded) {
          trackEvent(
            'credit_notes_loaded',
            params: {
              'credit_note_count': state.creditNotes.length,
              'is_from_cache': state.isFromCache ? 'true' : 'false',
            },
          );
        } else if (state is CreditNotesError) {
          trackEvent(
            'credit_notes_error',
            params: {'error_message': state.message},
          );
        }
      },
    );
  }
}
