import 'package:dartz/dartz.dart';

import '../../../core/error/failures.dart';
import '../../entities/invoices/invoice.dart';
import '../../repositories/invoice_repository.dart';

class GetInvoices {
  final InvoiceRepository repository;

  GetInvoices(this.repository);

  Future<Either<Failure, List<Invoice>>> call(String customerId) async {
    return await repository.getInvoices(customerId);
  }
}
