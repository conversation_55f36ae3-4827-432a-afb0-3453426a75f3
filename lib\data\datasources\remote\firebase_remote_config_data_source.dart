import 'dart:io';
import 'package:firebase_remote_config/firebase_remote_config.dart';

abstract class RemoteConfigDataSource {
  Future<void> initialize();
  String getMinRequiredVersion();
  String getUpdateMessage();
  String getUpdatePolicy();
}

class FirebaseRemoteConfigDataSource implements RemoteConfigDataSource {
  final FirebaseRemoteConfig _remoteConfig;

  FirebaseRemoteConfigDataSource(this._remoteConfig);

  @override
  Future<void> initialize() async {
    await _remoteConfig.setConfigSettings(
      RemoteConfigSettings(
        fetchTimeout: const Duration(minutes: 1),
        minimumFetchInterval: Duration.zero,
      ),
    );

    await _remoteConfig.setDefaults({
      // Platform-specific version requirements
      'aquapartner_required_version_android': '0.0.0+0',
      'aquapartner_required_version_ios': '0.0.0+0',
      'aquapartner_required_version':
          '0.0.0+0', // Fallback for backward compatibility
      // Update messages - can be platform-specific if needed
      'update_message_android': 'Please update to continue using the app.',
      'update_message_ios': 'Please update to continue using the app.',
      'update_message': 'Please update to continue using the app.', // Fallback
      // Update policies - set to 'all' to make all updates mandatory
      'update_policy_android': 'all',
      'update_policy_ios': 'all',
      'update_policy': 'all', // Fallback
    });

    await _remoteConfig.fetchAndActivate();
  }

  @override
  String getMinRequiredVersion() {
    if (Platform.isAndroid) {
      return _remoteConfig.getString('aquapartner_required_version_android');
    } else if (Platform.isIOS) {
      return _remoteConfig.getString('aquapartner_required_version_ios');
    }
    // Fallback for other platforms or backward compatibility
    return _remoteConfig.getString('aquapartner_required_version');
  }

  @override
  String getUpdateMessage() {
    if (Platform.isAndroid) {
      return _remoteConfig.getString('update_message_android');
    } else if (Platform.isIOS) {
      return _remoteConfig.getString('update_message_ios');
    }
    // Fallback for other platforms or backward compatibility
    return _remoteConfig.getString('update_message');
  }

  @override
  String getUpdatePolicy() {
    // Always return "all" to make all updates mandatory
    return "all";

    // Original implementation (commented out)
    /*
    if (Platform.isAndroid) {
      return _remoteConfig.getString('update_policy_android');
    } else if (Platform.isIOS) {
      return _remoteConfig.getString('update_policy_ios');
    }
    // Fallback for other platforms or backward compatibility
    return _remoteConfig.getString('update_policy');
    */
  }
}
