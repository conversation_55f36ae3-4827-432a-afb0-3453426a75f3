// lib/data/models/credit_note_model.dart
import 'package:intl/intl.dart';
import 'package:objectbox/objectbox.dart';

import '../../../domain/entities/credit_notes/credit_note.dart';

@Entity()
class CreditNoteModel {
  @Id()
  int dbId = 0; // ObjectBox internal ID

  @Index() // Index for faster querying by customerId
  final String customerId;

  @Unique(
    onConflict: ConflictStrategy.replace,
  ) // Use API's ID as unique identifier
  final String id; // Using _id from JSON

  final String customerName;
  @Property(type: PropertyType.date) // Store as DateTime timestamp
  final DateTime date;
  final String creditNoteId;
  final String creditNoteNumber;
  final String? invoiceId;
  final String? productId;
  final String itemName;
  final String? brand;
  final String? itemCategory;
  final String? categoryType;
  final int quantity;
  final double amount;
  final String? tag;
  final String? category;

  // Add fields from JSON that are not in the entity if needed for ObjectBox querying
  // For example:
  // final String dateString; // Store original date string if needed

  CreditNoteModel({
    required this.id,
    required this.customerId,
    required this.customerName,
    required this.date,
    required this.creditNoteId,
    required this.creditNoteNumber,
    this.invoiceId,
    this.productId,
    required this.itemName,
    this.brand,
    this.itemCategory,
    this.categoryType,
    required this.quantity,
    required this.amount,
    this.tag,
    this.category,
    // required this.dateString,
  });

  factory CreditNoteModel.fromJson(Map<String, dynamic> json) {
    // Robust date parsing for "Fri Sep 01 2023 00:00:00 GMT+0000 (Coordinated Universal Time)"
    DateTime parsedDate;
    try {
      // Attempt parsing common formats, adjust if needed
      // This specific format might be tricky with standard DateFormat.
      // Consider splitting or a more specific parsing logic if this fails.
      // Let's try a pattern that might capture the core part.
      final dateString = json['date1'] as String? ?? '';
      // A simpler approach might be to extract the core part if timezone is complex/inconsistent
      // Example: "Fri Sep 01 2023 00:00:00"
      final coreDateString = dateString.split(' GMT')[0];
      final inputFormat = DateFormat(
        'E MMM dd yyyy HH:mm:ss',
      ); // Adjust pattern if needed
      parsedDate = inputFormat.parse(coreDateString);
    } catch (e) {
      print("Error parsing date string: ${json['date1']} - $e");
      parsedDate = DateTime.now(); // Fallback or handle error appropriately
    }

    return CreditNoteModel(
      id: json['_id'] ?? '',
      customerId: json['customerId'] ?? '',
      customerName: json['customerName'] ?? '',
      date: parsedDate,
      creditNoteId: json['creditNoteId'] ?? '',
      creditNoteNumber: json['creditNoteNumber'] ?? '',
      invoiceId: json['invoiceId'],
      productId: json['productId'],
      itemName: json['itemName'] ?? '',
      brand: json['brand'],
      itemCategory: json['itemCategory'],
      categoryType: json['categoryType'],
      quantity: int.tryParse(json['quantity']?.toString() ?? '0') ?? 0,
      amount: double.tryParse(json['amount']?.toString() ?? '0.0') ?? 0.0,
      tag: json['tag'],
      category: json['category'],
      // dateString: json['date1'] ?? '', // Store original if needed
    );
  }

  CreditNote toEntity() {
    return CreditNote(
      id: id,
      customerId: customerId,
      customerName: customerName,
      date: date,
      creditNoteId: creditNoteId,
      creditNoteNumber: creditNoteNumber,
      invoiceId: invoiceId,
      productId: productId,
      itemName: itemName,
      brand: brand,
      itemCategory: itemCategory,
      categoryType: categoryType,
      quantity: quantity,
      amount: amount,
      tag: tag,
      category: category,
    );
  }
}
