import 'package:aqua_ui/aqua_ui.dart';
import 'package:aquapartner/core/mixins/analytics_mixin.dart';
import 'package:aquapartner/core/routes/app_router.dart';
import 'package:aquapartner/core/utils/currency_formatter.dart';
import 'package:aquapartner/presentation/widgets/loading_widget.dart';
import 'package:aquapartner/presentation/widgets/styled_generic_table.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/entities/sales_order/sales_order.dart';
import '../cubit/sales_order/sales_order_cubit.dart';
import '../cubit/sales_order/sales_order_state.dart';

class SalesOrdersPage extends StatefulWidget {
  const SalesOrdersPage({super.key});

  @override
  State<SalesOrdersPage> createState() => _SalesOrdersPageState();
}

class _SalesOrdersPageState extends State<SalesOrdersPage> with AnalyticsMixin {
  @override
  String get screenName => 'SalesOrdersPage';

  // Track screen view time
  DateTime? _screenViewStartTime;
  DateTime? _lastInteractionTime;

  // Track user interactions
  int _orderTapCount = 0;
  int _scrollCount = 0;

  // Track scroll position
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _screenViewStartTime = DateTime.now();
    _lastInteractionTime = _screenViewStartTime;

    // Add scroll listener for analytics
    _scrollController.addListener(_onScroll);

    context.read<SalesOrderCubit>().loadSalesOrders();

    // Track page initialization
    trackEvent('sales_orders_page_initialized');
  }

  @override
  void dispose() {
    // Track total time spent on screen
    _trackScreenDuration();

    // Track final engagement metrics
    _trackEngagementMetrics();

    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _trackScreenDuration() {
    if (_screenViewStartTime != null) {
      final duration = DateTime.now().difference(_screenViewStartTime!);
      trackEvent(
        'sales_orders_screen_duration',
        params: {
          'duration_ms': duration.inMilliseconds.toString(),
          'duration_seconds': duration.inSeconds.toString(),
        },
      );
    }
  }

  void _trackEngagementMetrics() {
    trackEvent(
      'sales_orders_engagement',
      params: {
        'order_tap_count': _orderTapCount.toString(),
        'scroll_count': _scrollCount.toString(),
        'time_since_last_interaction':
            _lastInteractionTime != null
                ? DateTime.now()
                    .difference(_lastInteractionTime!)
                    .inSeconds
                    .toString()
                : '0',
      },
    );
  }

  void _onScroll() {
    // Don't track every tiny scroll event - only significant ones
    if (!_scrollController.hasClients) return;

    // Track scroll position as percentage
    final scrollPosition = _scrollController.position.pixels;
    final maxScroll = _scrollController.position.maxScrollExtent;

    if (maxScroll <= 0) return;

    final scrollPercentage = (scrollPosition / maxScroll * 100).round();

    // Only track at 25%, 50%, 75%, and 100% scroll positions
    if (scrollPercentage == 25 ||
        scrollPercentage == 50 ||
        scrollPercentage == 75 ||
        scrollPercentage == 100) {
      _scrollCount++;
      _lastInteractionTime = DateTime.now();

      trackEvent(
        'sales_orders_scrolled',
        params: {
          'scroll_percentage': scrollPercentage.toString(),
          'scroll_position': scrollPosition.toString(),
        },
      );
    }
  }

  void _trackSalesOrderTap(SalesOrder salesOrder) {
    _orderTapCount++;
    _lastInteractionTime = DateTime.now();

    // Check if saleOrderDate is already a string or convert it if it's a DateTime
    final orderDateStr =
        salesOrder.saleOrderDate is DateTime
            ? (salesOrder.saleOrderDate as DateTime).toIso8601String()
            : salesOrder.saleOrderDate.toString();

    trackUserInteraction(
      'view_sales_order_details',
      'table_row',
      elementId: salesOrder.salesOrderNumber,
      additionalParams: {
        'order_number': salesOrder.salesOrderNumber,
        'order_date': orderDateStr,
        'order_total': salesOrder.total.toString(),
        'order_status':
            salesOrder.paidStatus.isEmpty ? 'draft' : salesOrder.paidStatus,
        'order_tap_count': _orderTapCount.toString(),
        'time_on_screen_before_tap':
            _screenViewStartTime != null
                ? DateTime.now()
                    .difference(_screenViewStartTime!)
                    .inSeconds
                    .toString()
                : '0',
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<SalesOrderCubit, SalesOrderState>(
      builder: (context, state) {
        if (state is SalesOrdersLoaded) {
          return SingleChildScrollView(
            controller: _scrollController, // Add controller for scroll tracking
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 16),
                  StyledGenericTable<SalesOrder>(
                    items: state.salesOrders,
                    showDividers: true,
                    onRowTap: (salesOrder) {
                      _trackSalesOrderTap(salesOrder);
                      AppRouter.navigateToSalesOrderDetails(
                        salesOrder: salesOrder,
                      );
                    },
                    columns: [
                      ColumnConfig<SalesOrder>(
                        title: 'Date',
                        width: 100,
                        cellBuilder:
                            (salesOrder) => Align(
                              alignment: Alignment.center,
                              child: AquaText.body(salesOrder.saleOrderDate),
                            ),
                      ),
                      ColumnConfig<SalesOrder>(
                        title: 'Total',
                        width: 100,
                        titleAlignment: Alignment.center,
                        bodyAlignment: Alignment.centerRight,
                        cellBuilder:
                            (salesOrder) => AquaText.body(
                              CurrencyFormatter.formatAsINR(
                                salesOrder.total,
                                decimalPlaces: 0,
                              ),
                              weight: AquaFontWeight.bold,
                            ),
                      ),
                      ColumnConfig<SalesOrder>(
                        title: 'Order Number',
                        width: 150,
                        cellBuilder:
                            (salesOrder) => AquaText.body(
                              salesOrder.salesOrderNumber,
                              weight: AquaFontWeight.semibold,
                              color: acPrimaryBlue,
                            ),
                      ),
                      ColumnConfig<SalesOrder>(
                        title: 'Status',
                        width: 100,
                        cellBuilder:
                            (salesOrder) => AquaText.body(
                              salesOrder.paidStatus.isEmpty
                                  ? 'draft'
                                  : salesOrder.paidStatus,
                            ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
        }
        return const LoadingWidget(message: "Sales Order Loading...");
      },
      listener: (context, state) {
        // Track state changes for analytics
        if (state is SalesOrderLoading) {
          trackEvent('sales_orders_loading');
        } else if (state is SalesOrdersLoaded) {
          _lastInteractionTime =
              DateTime.now(); // Update interaction time when data loads
          trackEvent(
            'sales_orders_loaded',
            params: {
              'order_count': state.salesOrders.length.toString(),
              'is_from_cache': state.isFromCache ? 'true' : 'false',
              'time_to_load':
                  _screenViewStartTime != null
                      ? DateTime.now()
                          .difference(_screenViewStartTime!)
                          .inMilliseconds
                          .toString()
                      : '0',
            },
          );
        } else if (state is SalesOrderError) {
          trackEvent(
            'sales_orders_error',
            params: {'error_message': state.message},
          );
        }
      },
    );
  }
}
