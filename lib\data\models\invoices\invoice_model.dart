import '../../../domain/entities/invoices/invoice.dart';
import 'invoice_item_model.dart';

class InvoiceModel extends Invoice {
  const InvoiceModel({
    required super.invoiceId,
    required super.addressId,
    required super.ageInDays,
    required super.ageTier,
    required super.balance,
    required super.customerId,
    required super.deliveryMode,
    required super.deliveryStatus,
    required super.dueDate,
    required super.invoiceDate,
    required super.invoiceNumber,
    required super.invoiceStatus,
    required super.subTotal,
    required super.total,
    required super.items,
  });

  factory InvoiceModel.fromJson(Map<String, dynamic> json) {
    List<InvoiceItemModel> items = [];
    if (json.containsKey('items') && json['items'] != null) {
      items =
          (json['items'] as List)
              .map((item) => InvoiceItemModel.fromJson(item))
              .toList();
    }

    return InvoiceModel(
      invoiceId: json['invoiceId'] ?? '',
      addressId: json['addressId'] ?? '',
      ageInDays: json['ageInDays'] ?? 0,
      ageTier: json['ageTier'] ?? '',
      balance: (json['balance'] ?? 0).toDouble(),
      customerId: json['customerId'] ?? '',
      deliveryMode: json['deliveryMode'] ?? '',
      deliveryStatus: json['deliveryStatus'] ?? '',
      dueDate:
          json['dueDate'] != null
              ? DateTime.parse(json['dueDate'])
              : DateTime.now(),
      invoiceDate:
          json['invoiceDate'] != null
              ? DateTime.parse(json['invoiceDate'])
              : DateTime.now(),
      invoiceNumber: json['invoiceNumber'] ?? '',
      invoiceStatus: json['invoiceStatus'] ?? '',
      subTotal: (json['subTotal'] ?? 0).toDouble(),
      total: (json['total'] ?? 0).toDouble(),
      items: items,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'invoiceId': invoiceId,
      'addressId': addressId,
      'ageInDays': ageInDays,
      'ageTier': ageTier,
      'balance': balance,
      'customerId': customerId,
      'deliveryMode': deliveryMode,
      'deliveryStatus': deliveryStatus,
      'dueDate': dueDate.toIso8601String(),
      'invoiceDate': invoiceDate.toIso8601String(),
      'invoiceNumber': invoiceNumber,
      'invoiceStatus': invoiceStatus,
      'subTotal': subTotal,
      'total': total,
      'items':
          (items as List<InvoiceItemModel>)
              .map((item) => item.toJson())
              .toList(),
    };
  }
}
