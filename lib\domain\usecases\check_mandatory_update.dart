import 'dart:io';
import '../../core/utils/logger.dart';
import '../entities/update_info.dart';
import '../repositories/version_repository.dart';

class CheckMandatoryUpdate {
  final VersionRepository repository;
  final AppLogger logger;

  CheckMandatoryUpdate(this.repository, this.logger);

  Future<MandatoryUpdateResult> execute() async {
    final currentVersion = await repository.getCurrentAppVersion();
    final minRequiredVersion = await repository.getMinRequiredVersion();
    final updatePolicy = await repository.getUpdatePolicy();

    final platform =
        Platform.isAndroid
            ? "Android"
            : Platform.isIOS
            ? "iOS"
            : "Unknown";
    logger.d(
      "[$platform] Current Version: ${currentVersion.version}, Required Version: ${minRequiredVersion.version}, Policy: $updatePolicy",
    );

    // Check if any update is needed
    if (currentVersion.isLowerThan(minRequiredVersion)) {
      // All updates are mandatory, but we'll still log the policy for debugging
      logger.i(
        "[$platform] Mandatory update required (all updates are mandatory). Policy: $updatePolicy",
      );

      final updateInfo = await repository.getUpdateInfo();

      // Always return mandatory update result
      return MandatoryUpdateResult(
        updateRequired: true,
        updateInfo: updateInfo,
        isRecommendedOnly: false,
      );
    }

    return MandatoryUpdateResult(
      updateRequired: false,
      isRecommendedOnly: false,
    );
  }
}

class MandatoryUpdateResult {
  final bool updateRequired;
  final UpdateInfo? updateInfo;
  final bool isRecommendedOnly;

  MandatoryUpdateResult({
    required this.updateRequired,
    this.updateInfo,
    this.isRecommendedOnly = false,
  });
}
