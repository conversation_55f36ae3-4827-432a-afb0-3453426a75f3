import 'package:equatable/equatable.dart';

import '../../../domain/entities/dues/dues.dart';

abstract class DuesState extends Equatable {
  const DuesState();

  @override
  List<Object?> get props => [];
}

class DuesInitial extends DuesState {}

class DuesLoading extends DuesState {}

class DuesLoaded extends DuesState {
  final DuesSummary duesSummary;
  final List<String> selectedAgingFilters;
  final bool isFromCache;
  final bool isCacheStale;
  final bool isBackgroundSyncInProgress;

  const DuesLoaded(
    this.duesSummary, {
    this.selectedAgingFilters = const [],
    this.isFromCache = false,
    this.isCacheStale = false,
    this.isBackgroundSyncInProgress = false,
  });

  // Create a copy with updated filters
  DuesLoaded copyWith({
    DuesSummary? duesSummary,
    List<String>? selectedAgingFilters,
    bool? isFromCache,
    bool? isCacheStale,
    bool? isBackgroundSyncInProgress,
  }) {
    return DuesLoaded(
      duesSummary ?? this.duesSummary,
      selectedAgingFilters: selectedAgingFilters ?? this.selectedAgingFilters,
      isFromCache: isFromCache ?? this.isFromCache,
      isCacheStale: isCacheStale ?? this.isCacheStale,
      isBackgroundSyncInProgress:
          isBackgroundSyncInProgress ?? this.isBackgroundSyncInProgress,
    );
  }

  @override
  List<Object?> get props => [
    duesSummary,
    selectedAgingFilters,
    isFromCache,
    isCacheStale,
    isBackgroundSyncInProgress,
  ];
}

class DuesError extends DuesState {
  final String message;

  const DuesError(this.message);

  @override
  List<Object?> get props => [message];
}

class DuesSyncing extends DuesState {}

class DuesSyncSuccess extends DuesState {}

class DuesSyncError extends DuesState {
  final String message;

  const DuesSyncError(this.message);

  @override
  List<Object?> get props => [message];
}
