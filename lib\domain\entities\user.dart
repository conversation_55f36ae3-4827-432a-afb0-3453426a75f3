import 'package:equatable/equatable.dart';

// In domain/entities/user.dart
class User extends Equatable {
  final int id; // Local ObjectBox ID
  final String? mongoId; // MongoDB ID
  final String phoneNumber;
  final bool isVerified;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool needsSync;

  const User({
    required this.id,
    this.mongoId,
    required this.phoneNumber,
    this.isVerified = false,
    required this.createdAt,
    required this.updatedAt,
    this.needsSync = true,
  });

  User copyWith({
    int? id,
    String? mongoId,
    String? phoneNumber,
    bool? isVerified,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? needsSync,
  }) {
    return User(
      id: id ?? this.id,
      mongoId: mongoId ?? this.mongoId,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      isVerified: isVerified ?? this.isVerified,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      needsSync: needsSync ?? this.needsSync,
    );
  }

  @override
  List<Object?> get props => [
    id,
    mongoId,
    phoneNumber,
    isVerified,
    createdAt,
    updatedAt,
    needsSync,
  ];
}
