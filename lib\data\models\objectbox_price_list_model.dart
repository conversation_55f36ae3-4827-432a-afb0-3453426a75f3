import 'dart:convert';
import 'package:objectbox/objectbox.dart';
import 'price_list_model.dart';

@Entity()
class ObjectBoxPriceListModel {
  @Id()
  int id = 0;
  
  @Index()
  final String state; // Use state as the primary identifier
  
  final String date;
  final String pricesJson; // Store prices as JSON string for ObjectBox compatibility

  ObjectBoxPriceListModel({
    this.id = 0,
    required this.state,
    required this.date,
    required this.pricesJson,
  });

  factory ObjectBoxPriceListModel.fromPriceListModel(PriceListModel model) {
    // Convert prices to JSON string
    final pricesJson = jsonEncode(
      (model.prices as List<PriceItemModel>).map((item) => item.toJson()).toList()
    );
    
    return ObjectBoxPriceListModel(
      state: model.state,
      date: model.date,
      pricesJson: pricesJson,
    );
  }

  PriceListModel toPriceListModel() {
    // Parse prices from JSON string
    final pricesData = jsonDecode(pricesJson) as List<dynamic>;
    final prices = pricesData
        .map((item) => PriceItemModel.fromJson(item))
        .toList();
    
    return PriceListModel(
      // No ID needed here
      state: state,
      date: date,
      prices: prices,
    );
  }

  ObjectBoxPriceListModel copyWith({
    int? id,
    String? state,
    String? date,
    String? pricesJson,
  }) {
    return ObjectBoxPriceListModel(
      id: id ?? this.id,
      state: state ?? this.state,
      date: date ?? this.date,
      pricesJson: pricesJson ?? this.pricesJson,
    );
  }
}