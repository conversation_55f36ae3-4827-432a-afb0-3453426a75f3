# Dashboard Cubit

## Overview
The `DashboardCubit` is responsible for managing the state of the dashboard screen in the application. It handles loading dashboard data, syncing with remote sources, and managing the UI state based on various conditions like connectivity status.

## Features

### State Management
- Manages different states: Initial, Loading, Loaded, and Error
- Preserves previous data during loading to prevent UI flashing
- Handles error states with appropriate messages

### Data Operations
- Loads dashboard data for a specific customer
- Syncs dashboard data with remote sources
- Refreshes dashboard data (sync + reload)
- Checks if data is stale and needs refreshing

### Connectivity Handling
- Checks for internet connectivity before performing operations
- Shows appropriate UI messages when offline
- Uses cached data when offline

### Auto-Refresh
- Supports automatic periodic refresh of dashboard data
- Configurable refresh interval
- Auto-cleanup of resources when cubit is closed

## Usage

### Basic Usage
```dart
// Initialize the cubit
final dashboardCubit = DashboardCubit(
  getDashboardUseCase: getIt<GetDashboardUseCase>(),
  syncDashboardUseCase: getIt<SyncDashboardUseCase>(),
  getSyncStatusUseCase: getIt<GetDashboardSyncStatusUseCase>(),
  dashboardService: getIt<DashboardService>(),
  logger: getIt<AppLogger>(),
  connectivityChecker: getIt<ConnectivityChecker>(),
);

// Load dashboard data
await dashboardCubit.loadDashboardData('customer123');

// Manually sync dashboard
await dashboardCubit.syncDashboard();

// Refresh dashboard (sync + reload)
await dashboardCubit.refreshDashboard();

// Enable auto-refresh every 10 minutes
dashboardCubit.enableAutoRefresh(interval: Duration(minutes: 10));

// Disable auto-refresh
dashboardCubit.disableAutoRefresh();

// Dismiss any error message
dashboardCubit.dismissError();
```

### Listening to State Changes
```dart
BlocBuilder<DashboardCubit, DashboardState>(
  builder: (context, state) {
    if (state is DashboardLoading) {
      return LoadingIndicator();
    } else if (state is DashboardLoaded) {
      return DashboardView(
        dashboard: state.dashboard,
        isSyncing: state.isSyncing,
        lastSyncTime: state.lastSyncTime,
        hasError: state.hasError,
        errorMessage: state.errorMessage,
      );
    } else if (state is DashboardError) {
      return ErrorView(message: state.message);
    } else {
      return EmptyView();
    }
  },
)
```

## Dependencies
- `flutter_bloc`: For state management
- `connectivity_plus`: For checking internet connectivity
- `logger`: For logging

## Error Handling
The cubit handles various error scenarios:
- Network connectivity issues
- API failures
- Unexpected exceptions

In most cases, it will preserve existing data and show an appropriate error message rather than completely failing.