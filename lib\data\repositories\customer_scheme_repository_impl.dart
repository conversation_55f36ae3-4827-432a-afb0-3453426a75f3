import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:logger/logger.dart'; // Add a proper logging package
import '../../domain/entities/scheme/customer_scheme.dart';
import '../../domain/repositories/customer_scheme_repository.dart';
import '../datasources/local/customer_scheme_local_data_source.dart';
import '../datasources/remote/customer_scheme_remote_data_source.dart';

class CustomerSchemeRepositoryImpl implements CustomerSchemeRepository {
  final CustomerSchemeLocalDataSource localDataSource;
  final CustomerSchemeRemoteDataSource remoteDataSource;
  final Connectivity connectivity;
  final Logger logger = Logger(); // Add logger

  CustomerSchemeRepositoryImpl({
    required this.localDataSource,
    required this.remoteDataSource,
    required this.connectivity,
  });

  /// Retrieves customer scheme data for a specific customer ID.
  /// Uses an offline-first approach, fetching from local storage before remote.
  @override
  Future<CustomerScheme?> getCustomerScheme(String customerId) async {
    try {
      // Try to get from local cache first
      final localScheme = await localDataSource.getCustomerScheme(customerId);

      if (localScheme != null) {
        logger.i('Retrieved customer scheme from local storage: $customerId');
        return localScheme.toEntity();
      }

      // If not found locally, check connectivity
      final connectivityResults = await connectivity.checkConnectivity();
      final hasConnection = connectivityResults.any(
        (result) => result != ConnectivityResult.none,
      );

      if (!hasConnection) {
        logger.w('No internet connection available to fetch customer scheme');
        return null;
      }

      // Fetch from remote
      final remoteScheme = await remoteDataSource.getCustomerScheme(customerId);

      if (remoteScheme == null) {
        logger.w('Remote data source returned null for customer: $customerId');
        return null;
      }

      // Cache the fetched data
      await localDataSource.cacheCustomerScheme(remoteScheme);
      logger.i('Cached customer scheme from remote: $customerId');

      return remoteScheme.toEntity();
    } catch (e, stackTrace) {
      logger.e(
        'Error retrieving customer scheme: $e',
        error: e,
        stackTrace: stackTrace,
      );
      return null; // Return null on error to maintain the method's contract
    }
  }

  /// Synchronizes customer scheme data between remote and local storage.
  /// Requires internet connectivity.
  @override
  Future<void> syncCustomerScheme(String customerId) async {
    try {
      final connectivityResults = await connectivity.checkConnectivity();
      final hasConnection = connectivityResults.any(
        (result) => result != ConnectivityResult.none,
      );

      if (!hasConnection) {
        logger.w('No internet connection available for sync');
        throw Exception('No internet connection');
      }

      final remoteScheme = await remoteDataSource.getCustomerScheme(customerId);

      if (remoteScheme == null) {
        logger.w(
          'Remote data source returned null during sync for customer: $customerId',
        );
        throw Exception('Failed to retrieve data from server');
      }

      // Clear existing cache for this customer and store new data
      await localDataSource.clearCustomerScheme(customerId);
      await localDataSource.cacheCustomerScheme(remoteScheme);

      logger.i('Successfully synced customer scheme: $customerId');
    } catch (e, stackTrace) {
      logger.e('Sync failed: $e', error: e, stackTrace: stackTrace);
      throw Exception('Failed to sync data: $e');
    }
  }
}
