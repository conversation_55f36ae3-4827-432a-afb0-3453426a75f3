import 'package:aqua_ui/aqua_ui.dart';
import 'package:aquapartner/domain/entities/scheme/support_person.dart';
import 'package:aquapartner/presentation/cubit/scheme/scheme_cubit.dart';
import 'package:aquapartner/presentation/cubit/scheme/scheme_state.dart';
import 'package:aquapartner/presentation/widgets/loading_widget.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../core/mixins/analytics_mixin.dart';
import '../../domain/entities/scheme/person_details.dart';

class HelpAndSupportScreen extends StatefulWidget {
  const HelpAndSupportScreen({super.key});

  @override
  State<HelpAndSupportScreen> createState() => _HelpAndSupportState();
}

class _HelpAndSupportState extends State<HelpAndSupportScreen>
    with AnalyticsMixin<HelpAndSupportScreen> {
  List<SupportPerson> supportPersons = [];
  late SupportPerson so;
  late SupportPerson asm;

  @override
  String get screenName => 'help_and_support';

  @override
  String get parentScreenName => 'home';

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    final schemeState = context.watch<SchemeCubit>().state;
    if (schemeState is SchemeLoaded) {
      supportPersons = schemeState.scheme.supportPersons;
      so = supportPersons.firstWhere((persion) => persion.profile == "SO");
      asm = supportPersons.firstWhere((persion) => persion.profile == "ASM");
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,

      body:
          supportPersons.isNotEmpty
              ? Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    vSpace4,
                    AquaText.headline(
                      "Help and Support",
                      weight: AquaFontWeight.semibold,
                      color: acTextSecondaryColor,
                    ),
                    vSpace6,
                    AquaText.subheadline(
                      'Your dedicated Aquaconnect Officer',
                      weight: AquaFontWeight.bold,
                    ),
                    const SizedBox(height: 8),
                    _contactCart(so),
                    const SizedBox(height: 24),
                    AquaText.subheadline(
                      'For Escalations',
                      weight: AquaFontWeight.bold,
                    ),
                    const SizedBox(height: 8),
                    _contactCart(asm),
                    const SizedBox(height: 24),
                    AquaText.subheadline(
                      'For other Queries',
                      weight: AquaFontWeight.bold,
                    ),
                    const SizedBox(height: 8),

                    _contactCart(
                      SupportPerson(
                        profile: "support",
                        details: PersonDetails(
                          rowId: '',
                          customerName: '000 - Aquaconnect (Support)',
                          email: '',
                          empId: '',
                          mobileNumber: '1800 123 1263',
                          profile: "support",
                          status: '',
                          userId: '',
                          modifiedTime: DateTime.now(),
                        ),
                      ),
                    ),
                  ],
                ),
              )
              : const LoadingWidget(message: "Fetching Your Dedicated Parters"),
    );
  }

  Widget _contactCart(SupportPerson person) {
    return InkWell(
      onTap: () async {
        // Track contact interaction
        trackUserInteraction(
          'contact_support_person',
          'contact_card',
          elementId: person.details.empId,
          additionalParams: {
            'contact_type': person.profile,
            'contact_name': person.details.customerName,
            'phone_number': person.details.mobileNumber,
          },
        );

        final Uri phoneUri = Uri(
          scheme: 'tel',
          path: person.details.mobileNumber,
        );

        try {
          if (await canLaunchUrl(phoneUri)) {
            await launchUrl(phoneUri);

            // Track successful phone call launch
            trackEvent(
              'phone_call_launched',
              params: {
                'contact_type': person.profile,
                'contact_name': person.details.customerName,
              },
            );
          } else {
            // Track phone launch failure
            trackError(
              'phone_launch_failed',
              'Could not launch phone dialer',
              additionalParams: {
                'contact_type': person.profile,
                'phone_number': person.details.mobileNumber,
              },
            );

            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: AquaText.body('Could not launch phone dialer'),
                ),
              );
            }
          }
        } catch (e) {
          // Track phone launch error
          trackError(
            'phone_launch_error',
            e.toString(),
            additionalParams: {
              'contact_type': person.profile,
              'phone_number': person.details.mobileNumber,
            },
          );

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: AquaText.body('Error launching phone dialer')),
            );
          }
        }
      },
      child: Row(
        children: [
          person.profile == "support"
              ? const Icon(Icons.phone, size: 30, color: acPrimaryBlue)
              : ClipRRect(
                borderRadius: BorderRadius.circular(30),
                child: CachedNetworkImage(
                  imageUrl:
                      'https://firebasestorage.googleapis.com/v0/b/aquaconnect-partner-prod.appspot.com/o/users%2F${person.details.empId.toLowerCase().trim()}.jpg?alt=media',
                  width: 60,
                  height: 60,
                  fit: BoxFit.cover,
                  placeholder:
                      (context, url) =>
                          const Center(child: CircularProgressIndicator()),
                  errorWidget:
                      (context, url, error) =>
                          const Icon(Icons.person, size: 30),
                ),
              ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AquaText.subheadline(
                  person.details.customerName.split('-')[1].trim(),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                  weight: AquaFontWeight.semibold,
                ),
                AquaText.body(
                  person.details.mobileNumber,
                  color: acTextSecondaryColor,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
