import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';

import '../../../core/error/exceptions.dart';
import '../../../core/error/failures.dart';
import '../../../core/network/network_info.dart';

import '../../domain/entities/invoices/invoice.dart';
import '../../domain/entities/sync_status.dart';
import '../../domain/repositories/invoice_repository.dart';
import '../datasources/local/invoice_local_data_source.dart';
import '../datasources/remote/invoice_remote_data_source.dart';

class InvoiceRepositoryImpl implements InvoiceRepository {
  final InvoiceRemoteDataSource remoteDataSource;
  final InvoiceLocalDataSource localDataSource;
  final NetworkInfo networkInfo;

  InvoiceRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, List<Invoice>>> getInvoices(String customerId) async {
    try {
      final localInvoices = await localDataSource.getInvoices(customerId);
      return Right(localInvoices);
    } on CacheException {
      // Return an empty list instead of a failure when no invoices are found
      return const Right([]);
    }
  }

  @override
  Future<Either<Failure, Invoice>> getInvoiceById(String invoiceId) async {
    try {
      final localInvoice = await localDataSource.getInvoiceById(invoiceId);
      return Right(localInvoice);
    } on CacheException {
      return Left(CacheFailure());
    }
  }

  @override
  Future<Either<Failure, SyncStatus>> syncInvoices(String customerId) async {
    if (await networkInfo.isConnected) {
      try {
        final remoteInvoices = await remoteDataSource.getInvoices(customerId);
        // Even if we get an empty list, save it and mark sync as completed
        await localDataSource.saveInvoices(remoteInvoices);
        return Right(
          SyncStatus(customerId: customerId, status: SyncStatusType.completed),
        );
      } on DioException catch (_) {
        return Left(ServerFailure());
      } on ServerException {
        return Left(ServerFailure());
      } catch (e) {
        return Left(ServerFailure());
      }
    } else {
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, bool>> checkIfSyncNeeded() async {
    try {
      final lastSyncTime = await localDataSource.getLastSyncTime();
      final now = DateTime.now();
      final difference = now.difference(lastSyncTime);
      // Sync needed if last sync was more than 1 hour ago
      return Right(difference.inMinutes > 1);
    } on CacheException {
      return const Right(true); // If no sync time found, sync is needed
    }
  }
}
