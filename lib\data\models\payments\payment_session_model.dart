import '../../../domain/entities/payments/payment_session.dart';

/// Data model for payment session API responses
class PaymentSessionModel extends PaymentSession {
  const PaymentSessionModel({
    required super.sessionId,
    required super.paymentUrl,
    required super.amount,
    required super.currency,
    required super.invoiceNumber,
    required super.customerId,
    super.description,
    super.customerName,
    super.customerEmail,
    required super.status,
    required super.createdAt,
    super.expiresAt,
    super.metadata,
  });

  /// Creates a PaymentSessionModel from JSON response
  factory PaymentSessionModel.fromJson(Map<String, dynamic> json) {
    final paymentSession = json['payment_session'] ?? json;
    final data = json['data'] ?? json;

    return PaymentSessionModel(
      sessionId:
          paymentSession['payments_session_id'] ??
          paymentSession['payment_session_id'] ??
          paymentSession['sessionId'] ??
          '',
      paymentUrl: _constructPaymentUrl(
        paymentSession['payment_url'],
        paymentSession['payments_session_id'] ??
            paymentSession['payment_session_id'],
      ),
      amount: _parseAmount(paymentSession['amount']),
      currency: paymentSession['currency'] ?? 'INR',
      invoiceNumber:
          data['invoice_number'] ?? paymentSession['invoice_number'] ?? '',
      customerId: data['customer_id'] ?? paymentSession['customer_id'] ?? '',
      description: data['description'] ?? paymentSession['description'],
      customerName: data['customer_name'] ?? paymentSession['customer_name'],
      customerEmail: data['customer_email'] ?? paymentSession['customer_email'],
      status: _parseStatus(paymentSession['status'] ?? 'created'),
      createdAt: _parseDateTime(paymentSession['created_time']),
      expiresAt: _parseDateTime(paymentSession['expires_at']),
      metadata: _parseMetadata(
        data['meta_data'] ?? paymentSession['meta_data'],
      ),
    );
  }

  /// Creates a PaymentSessionModel from legacy API response
  factory PaymentSessionModel.fromLegacyJson(Map<String, dynamic> json) {
    final paymentSession = json['paymentSession'] ?? json;

    return PaymentSessionModel(
      sessionId:
          paymentSession['payments_session_id'] ??
          json['payment_session_id'] ??
          json['paymentSessionId'] ??
          '',
      paymentUrl: _constructPaymentUrl(
        paymentSession['payment_url'],
        paymentSession['payments_session_id'] ?? json['payment_session_id'],
      ),
      amount: _parseAmount(paymentSession['amount']),
      currency: paymentSession['currency'] ?? 'INR',
      invoiceNumber: json['invoiceNo'] ?? json['invoice_number'] ?? '',
      customerId: json['customerId'] ?? json['customer_id'] ?? '',
      description: json['description'],
      customerName: json['customerName'] ?? json['customer_name'],
      customerEmail: json['customerEmail'] ?? json['customer_email'],
      status: _parseStatus(paymentSession['status'] ?? 'created'),
      createdAt: _parseDateTime(paymentSession['created_time']),
      expiresAt: _parseDateTime(paymentSession['expires_at']),
      metadata: null,
    );
  }

  /// Converts to JSON for API requests
  Map<String, dynamic> toJson() {
    return {
      'payments_session_id': sessionId,
      'payment_url': paymentUrl,
      'amount': amount,
      'currency': currency,
      'invoice_number': invoiceNumber,
      'customer_id': customerId,
      if (description != null) 'description': description,
      if (customerName != null) 'customer_name': customerName,
      if (customerEmail != null) 'customer_email': customerEmail,
      'status': status.name,
      'created_time': createdAt.millisecondsSinceEpoch ~/ 1000,
      if (expiresAt != null)
        'expires_at': expiresAt!.millisecondsSinceEpoch ~/ 1000,
      if (metadata != null) 'meta_data': metadata,
    };
  }

  static PaymentSessionStatus _parseStatus(String status) {
    switch (status.toLowerCase()) {
      case 'created':
        return PaymentSessionStatus.created;
      case 'pending':
        return PaymentSessionStatus.pending;
      case 'succeeded':
      case 'completed':
        return PaymentSessionStatus.completed;
      case 'failed':
        return PaymentSessionStatus.failed;
      case 'cancelled':
        return PaymentSessionStatus.cancelled;
      case 'expired':
        return PaymentSessionStatus.expired;
      default:
        return PaymentSessionStatus.created;
    }
  }

  static DateTime _parseDateTime(dynamic timestamp) {
    if (timestamp == null) return DateTime.now();

    if (timestamp is int) {
      // Unix timestamp (seconds)
      return DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
    }

    if (timestamp is String) {
      return DateTime.tryParse(timestamp) ?? DateTime.now();
    }

    return DateTime.now();
  }

  static Map<String, dynamic>? _parseMetadata(dynamic metaData) {
    if (metaData == null) return null;

    if (metaData is List) {
      final Map<String, dynamic> result = {};
      for (final item in metaData) {
        if (item is Map<String, dynamic> &&
            item.containsKey('key') &&
            item.containsKey('value')) {
          result[item['key']] = item['value'];
        }
      }
      return result.isEmpty ? null : result;
    }

    if (metaData is Map<String, dynamic>) {
      return metaData;
    }

    return null;
  }

  /// Safely parse amount from various formats (string, int, double)
  static double _parseAmount(dynamic amount) {
    if (amount == null) return 0.0;

    if (amount is double) return amount;
    if (amount is int) return amount.toDouble();

    if (amount is String) {
      return double.tryParse(amount) ?? 0.0;
    }

    return 0.0;
  }

  /// Construct payment URL from API response or session ID
  static String _constructPaymentUrl(dynamic paymentUrl, dynamic sessionId) {
    // If payment_url is provided in the response, use it
    if (paymentUrl != null && paymentUrl.toString().isNotEmpty) {
      return paymentUrl.toString();
    }

    // If no payment_url but we have a session_id, construct the Zoho checkout URL
    if (sessionId != null && sessionId.toString().isNotEmpty) {
      return 'https://checkout.zoho.com/v2/payment/${sessionId.toString()}';
    }

    // Fallback: return empty string (will be handled by WebView validation)
    return '';
  }
}
