class AppVersion {
  final String version;
  final int major;
  final int minor;
  final int patch;
  final int buildNumber;

  AppVersion(String versionString)
    : version = versionString,
      major = _extractMajor(versionString),
      minor = _extractMinor(versionString),
      patch = _extractPatch(versionString),
      buildNumber = _extractBuildNumber(versionString) {
    if (!_isValidVersion(versionString)) {
      throw FormatException(
        'Invalid version format: $versionString. Expected format: x.y.z or x.y.z+b',
      );
    }
  }

  static int _extractMajor(String version) {
    final parts = version.split('.');
    if (parts.isEmpty) return 0;
    return int.tryParse(parts[0]) ?? 0;
  }

  static int _extractMinor(String version) {
    final parts = version.split('.');
    if (parts.length < 2) return 0;
    return int.tryParse(parts[1]) ?? 0;
  }

  static int _extractPatch(String version) {
    final parts = version.split('.');
    if (parts.length < 3) return 0;

    // Handle build number if present
    final patchPart = parts[2].split('+').first;
    return int.tryParse(patchPart) ?? 0;
  }

  static int _extractBuildNumber(String version) {
    if (!version.contains('+')) return 0;

    final buildPart = version.split('+').last;
    return int.tryParse(buildPart) ?? 0;
  }

  bool _isValidVersion(String version) {
    if (version.isEmpty) return false;

    // Handle version with build number (x.y.z+b)
    if (version.contains('+')) {
      final parts = version.split('+');
      if (parts.length != 2) return false;

      // Validate build number
      if (int.tryParse(parts[1]) == null) return false;

      // Continue validation with the version part
      version = parts[0];
    }

    final parts = version.split('.');
    if (parts.length < 2 || parts.length > 3) return false;

    return parts.every((part) {
      try {
        final number = int.parse(part);
        return number >= 0;
      } catch (_) {
        return false;
      }
    });
  }

  bool isLowerThan(AppVersion other) {
    // Compare major version
    if (major < other.major) return true;
    if (major > other.major) return false;

    // Compare minor version
    if (minor < other.minor) return true;
    if (minor > other.minor) return false;

    // Compare patch version
    if (patch < other.patch) return true;
    if (patch > other.patch) return false;

    // Compare build number
    return buildNumber < other.buildNumber;
  }

  @override
  String toString() => version;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AppVersion &&
          runtimeType == other.runtimeType &&
          version == other.version;

  @override
  int get hashCode => version.hashCode;
}
