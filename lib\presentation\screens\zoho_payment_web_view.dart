import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';

class ZohoPaymentWebView extends StatefulWidget {
  final String paymentUrl;
  final Function(bool success, String? transactionId) onPaymentComplete;

  const ZohoPaymentWebView({
    super.key,
    required this.paymentUrl,
    required this.onPaymentComplete,
  });

  @override
  State<ZohoPaymentWebView> createState() => _ZohoPaymentWebViewState();
}

class _ZohoPaymentWebViewState extends State<ZohoPaymentWebView> {
  late WebViewController _controller;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _controller =
        WebViewController()
          ..setJavaScriptMode(JavaScriptMode.unrestricted)
          ..setNavigationDelegate(
            NavigationDelegate(
              onPageStarted: (String url) {
                setState(() {
                  _isLoading = true;
                });
              },
              onPageFinished: (String url) {
                setState(() {
                  _isLoading = false;
                });
              },
              onNavigationRequest: (NavigationRequest request) {
                // Check for success or failure redirects
                if (request.url.contains('payment/success')) {
                  // Extract transaction ID if available in URL
                  final uri = Uri.parse(request.url);
                  final transactionId = uri.queryParameters['transaction_id'];
                  widget.onPaymentComplete(true, transactionId);
                  return NavigationDecision.prevent;
                } else if (request.url.contains('payment/failure')) {
                  widget.onPaymentComplete(false, null);
                  return NavigationDecision.prevent;
                }
                return NavigationDecision.navigate;
              },
            ),
          )
          ..loadRequest(Uri.parse(widget.paymentUrl));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Payment'),
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () {
            widget.onPaymentComplete(false, null);
            Navigator.of(context).pop();
          },
        ),
      ),
      body: Stack(
        children: [
          WebViewWidget(controller: _controller),
          if (_isLoading) const Center(child: CircularProgressIndicator()),
        ],
      ),
    );
  }
}
