import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;

import 'package:aquapartner/core/error/failures.dart';
import 'package:aquapartner/core/network/network_info.dart';
import 'package:aquapartner/core/utils/logger.dart';
import 'package:aquapartner/domain/repositories/user_repository.dart';
import 'package:aquapartner/domain/usecases/sync_usecases.dart';
import 'package:aquapartner/domain/entities/user.dart';

// Mock classes
class MockUserRepository extends Mock implements UserRepository {}

class MockNetworkInfo extends Mock implements NetworkInfo {}

class MockAppLogger extends Mock implements AppLogger {}

class MockFirebaseUser extends Mock implements firebase_auth.User {}

void main() {
  setUpAll(() {
    // Register fallback values for mocktail
    registerFallbackValue(
      User(
        id: 0,
        phoneNumber: '',
        isVerified: false,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        needsSync: false,
      ),
    );
  });

  group('SyncUserUseCase', () {
    late SyncUserUseCase useCase;
    late MockUserRepository mockRepository;
    late MockNetworkInfo mockNetworkInfo;
    late MockAppLogger mockLogger;

    setUp(() {
      mockRepository = MockUserRepository();
      mockNetworkInfo = MockNetworkInfo();
      mockLogger = MockAppLogger();
      useCase = SyncUserUseCase(
        repository: mockRepository,
        networkInfo: mockNetworkInfo,
        logger: mockLogger,
      );
    });

    group('Successful Sync', () {
      test('should sync user when network is connected', () async {
        // arrange
        when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        when(
          () => mockRepository.syncUser(),
        ).thenAnswer((_) async => const Right(null));

        // act
        final result = await useCase.call();

        // assert
        expect(result, const Right(null));
        verify(() => mockNetworkInfo.isConnected).called(1);
        verify(() => mockRepository.syncUser()).called(1);
      });

      test('should log info message when sync is successful', () async {
        // arrange
        when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        when(
          () => mockRepository.syncUser(),
        ).thenAnswer((_) async => const Right(null));

        // act
        await useCase.call();

        // assert
        verify(() => mockNetworkInfo.isConnected).called(1);
        verify(() => mockRepository.syncUser()).called(1);
      });
    });

    group('Network Connectivity', () {
      test('should skip sync when network is not connected', () async {
        // arrange
        when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => false);

        // act
        final result = await useCase.call();

        // assert
        expect(result, const Right(null));
        verify(() => mockNetworkInfo.isConnected).called(1);
        verifyNever(() => mockRepository.syncUser());
        verify(
          () => mockLogger.i("No internet connection, skipping sync"),
        ).called(1);
      });

      test('should handle network check failure gracefully', () async {
        // arrange
        when(
          () => mockNetworkInfo.isConnected,
        ).thenThrow(Exception('Network check failed'));

        // act
        final result = await useCase.call();

        // assert
        expect(result, Left(ServerFailure()));
        verify(() => mockLogger.e("Error in SyncUserUseCase", any())).called(1);
      });
    });

    group('Error Handling', () {
      test(
        'should return ServerFailure when repository throws exception',
        () async {
          // arrange
          when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
          when(
            () => mockRepository.syncUser(),
          ).thenThrow(Exception('Repository error'));

          // act
          final result = await useCase.call();

          // assert
          expect(result, Left(ServerFailure()));
          verify(
            () => mockLogger.e("Error in SyncUserUseCase", any()),
          ).called(1);
        },
      );

      test('should return repository failure when sync fails', () async {
        // arrange
        when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        when(
          () => mockRepository.syncUser(),
        ).thenAnswer((_) async => Left(NetworkFailure()));

        // act
        final result = await useCase.call();

        // assert
        expect(result, Left(NetworkFailure()));
        verify(() => mockNetworkInfo.isConnected).called(1);
        verify(() => mockRepository.syncUser()).called(1);
      });

      test('should handle timeout during sync', () async {
        // arrange
        when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        when(() => mockRepository.syncUser()).thenAnswer(
          (_) async =>
              Future.delayed(Duration(seconds: 1), () => Left(ServerFailure())),
        );

        // act
        final result = await useCase.call();

        // assert
        expect(result, Left(ServerFailure()));
      });
    });
  });

  group('GetSyncStatusUseCase', () {
    late GetSyncStatusUseCase useCase;
    late MockUserRepository mockRepository;
    late MockAppLogger mockLogger;

    setUp(() {
      mockRepository = MockUserRepository();
      mockLogger = MockAppLogger();
      useCase = GetSyncStatusUseCase(
        repository: mockRepository,
        logger: mockLogger,
      );
    });

    group('Stream Operations', () {
      test('should return sync status stream', () async {
        // arrange
        final testStream = Stream.value(true);
        when(
          () => mockRepository.getSyncStatus(),
        ).thenAnswer((_) => testStream);

        // act
        final result = useCase.call();

        // assert
        expect(result, testStream);
        verify(() => mockLogger.i("Getting sync status stream")).called(1);
      });

      test('should return multiple sync status updates', () async {
        // arrange
        final testStream = Stream.fromIterable([false, true, false]);
        when(
          () => mockRepository.getSyncStatus(),
        ).thenAnswer((_) => testStream);

        // act
        final result = useCase.call();

        // assert
        expect(result, emitsInOrder([false, true, false]));
        verify(() => mockLogger.i("Getting sync status stream")).called(1);
      });

      test('should handle empty stream', () async {
        // arrange
        final testStream = Stream<bool>.empty();
        when(
          () => mockRepository.getSyncStatus(),
        ).thenAnswer((_) => testStream);

        // act
        final result = useCase.call();

        // assert
        expect(result, emitsDone);
        verify(() => mockLogger.i("Getting sync status stream")).called(1);
      });
    });

    group('Error Handling', () {
      test('should return error stream when exception occurs', () async {
        // arrange
        when(
          () => mockRepository.getSyncStatus(),
        ).thenThrow(Exception('Test exception'));

        // act
        final result = useCase.call();

        // assert
        expect(result, emitsError(isA<ServerFailure>()));
        verify(
          () => mockLogger.e("Error setting up sync status stream", any()),
        ).called(1);
      });

      test('should handle repository stream errors', () async {
        // arrange
        final testStream = Stream<bool>.error(Exception('Stream error'));
        when(
          () => mockRepository.getSyncStatus(),
        ).thenAnswer((_) => testStream);

        // act
        final result = useCase.call();

        // assert
        expect(result, emitsError(isA<Exception>()));
        verify(() => mockLogger.i("Getting sync status stream")).called(1);
      });
    });
  });

  group('CheckAuthStatusUseCase', () {
    late CheckAuthStatusUseCase useCase;
    late MockUserRepository mockRepository;
    late MockAppLogger mockLogger;

    setUp(() {
      mockRepository = MockUserRepository();
      mockLogger = MockAppLogger();
      useCase = CheckAuthStatusUseCase(
        repository: mockRepository,
        logger: mockLogger,
      );
    });

    group('User Exists and Verified', () {
      test('should return true when user exists and is verified', () async {
        // arrange
        final testUser = User(
          id: 1,
          phoneNumber: '+919999999999',
          isVerified: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          needsSync: false,
        );
        when(
          () => mockRepository.hasUser(),
        ).thenAnswer((_) async => const Right(true));
        when(
          () => mockRepository.getUser(),
        ).thenAnswer((_) async => Right(testUser));

        // act
        final result = await useCase.call();

        // assert
        expect(result, const Right(true));
        verify(() => mockLogger.i("Checking authentication status")).called(1);
        verify(() => mockLogger.i("User found, authenticated: true")).called(1);
      });

      test(
        'should return false when user exists but is not verified',
        () async {
          // arrange
          final testUser = User(
            id: 1,
            phoneNumber: '+919999999999',
            isVerified: false,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
            needsSync: false,
          );
          when(
            () => mockRepository.hasUser(),
          ).thenAnswer((_) async => const Right(true));
          when(
            () => mockRepository.getUser(),
          ).thenAnswer((_) async => Right(testUser));

          // act
          final result = await useCase.call();

          // assert
          expect(result, const Right(false));
          verify(
            () => mockLogger.i("User found, authenticated: false"),
          ).called(1);
        },
      );
    });

    group('No User Exists', () {
      test('should return false when no user exists', () async {
        // arrange
        when(
          () => mockRepository.hasUser(),
        ).thenAnswer((_) async => const Right(false));

        // act
        final result = await useCase.call();

        // assert
        expect(result, const Right(false));
        verify(
          () => mockLogger.i("No user found, not authenticated"),
        ).called(1);
        verifyNever(() => mockRepository.getUser());
      });

      test('should handle hasUser returning failure', () async {
        // arrange
        when(
          () => mockRepository.hasUser(),
        ).thenAnswer((_) async => Left(CacheFailure()));

        // act
        final result = await useCase.call();

        // assert
        expect(result, Left(CacheFailure()));
        verifyNever(() => mockRepository.getUser());
      });
    });

    group('Error Handling', () {
      test('should return failure when getUser fails', () async {
        // arrange
        when(
          () => mockRepository.hasUser(),
        ).thenAnswer((_) async => const Right(true));
        when(
          () => mockRepository.getUser(),
        ).thenAnswer((_) async => Left(ServerFailure()));

        // act
        final result = await useCase.call();

        // assert
        expect(result, Left(ServerFailure()));
        verify(() => mockLogger.e("Error getting user", any())).called(1);
      });

      test('should handle unexpected exceptions', () async {
        // arrange
        when(
          () => mockRepository.hasUser(),
        ).thenThrow(Exception('Unexpected error'));

        // act
        final result = await useCase.call();

        // assert
        expect(result, Left(ServerFailure()));
        verify(
          () =>
              mockLogger.e("Unexpected error in CheckAuthStatusUseCase", any()),
        ).called(1);
      });
    });

    group('Firebase User Operations', () {
      test('should return current Firebase user when available', () async {
        // arrange
        final mockFirebaseUser = MockFirebaseUser();
        when(() => mockFirebaseUser.uid).thenReturn('test_uid');

        // act
        final result = await useCase.getCurrentUser();

        // assert - This test would need Firebase Auth to be mocked at a higher level
        // For now, we test that the method exists and can be called
        expect(result, isA<firebase_auth.User?>());
      });

      test('should handle Firebase Auth errors gracefully', () async {
        // act
        final result = await useCase.getCurrentUser();

        // assert
        expect(result, isA<firebase_auth.User?>());
      });
    });

    group('Edge Cases', () {
      test('should handle null user gracefully', () async {
        // arrange
        when(
          () => mockRepository.hasUser(),
        ).thenAnswer((_) async => const Right(true));
        when(
          () => mockRepository.getUser(),
        ).thenAnswer((_) async => Left(NotFoundFailure()));

        // act
        final result = await useCase.call();

        // assert
        expect(result, Left(NotFoundFailure()));
      });

      test('should handle repository timeout', () async {
        // arrange
        when(() => mockRepository.hasUser()).thenAnswer(
          (_) async =>
              Future.delayed(Duration(seconds: 1), () => Left(ServerFailure())),
        );

        // act
        final result = await useCase.call();

        // assert
        expect(result, Left(ServerFailure()));
      });

      test('should handle concurrent auth checks', () async {
        // arrange
        final testUser = User(
          id: 1,
          phoneNumber: '+919999999999',
          isVerified: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          needsSync: false,
        );
        when(
          () => mockRepository.hasUser(),
        ).thenAnswer((_) async => const Right(true));
        when(
          () => mockRepository.getUser(),
        ).thenAnswer((_) async => Right(testUser));

        // act
        final results = await Future.wait([
          useCase.call(),
          useCase.call(),
          useCase.call(),
        ]);

        // assert
        for (final result in results) {
          expect(result, const Right(true));
        }
        verify(() => mockRepository.hasUser()).called(3);
        verify(() => mockRepository.getUser()).called(3);
      });
    });
  });
}
