import 'package:aqua_ui/aqua_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../core/constants/app_constants.dart';
import '../../core/services/analytics_service.dart';
import '../../domain/entities/price_list.dart';
import '../cubit/price_list/price_list_cubit.dart';
import '../cubit/price_list/price_list_state.dart';
import '../../injection_container.dart' as di;

class PriceListWidget extends StatefulWidget {
  const PriceListWidget({super.key});

  @override
  State<PriceListWidget> createState() => _PriceListWidgetState();
}

class _PriceListWidgetState extends State<PriceListWidget> {
  late final AnalyticsService _analyticsService;

  @override
  void initState() {
    super.initState();
    _analyticsService = di.sl<AnalyticsService>();

    // Track widget initialization
    _trackEvent('price_list_widget_initialized');

    // Track feature usage
    _analyticsService.logFeatureUsage('price_list_view');
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        BlocConsumer<PriceListCubit, PriceListState>(
          builder: (context, state) {
            if (state.isLoading) {
              return const Center(child: CircularProgressIndicator());
            }

            return Column(
              children: [
                // Location selector row
                Row(
                  children: [
                    Image.asset(AppConstants.iconLocationPointer),
                    const SizedBox(width: 4),
                    AquaText.body(
                      state.selectedState?.state ?? "State Is Empty",
                    ),
                    TextButton(
                      onPressed: () {
                        _trackEvent('change_state_button_clicked');
                        _showLocationDialog(context, state);
                      },
                      child: AquaText.body(
                        'Change',
                        color: acPrimaryBlue,
                        weight: AquaFontWeight.bold,
                      ),
                    ),
                  ],
                ),

                Container(
                  decoration: BoxDecoration(
                    color: acWhiteColor,
                    border: Border.all(color: Color(0xFFE2E2E2), width: 1.0),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    children: [
                      Padding(
                        padding: EdgeInsets.all(16),
                        child: Row(
                          children: [
                            Image.asset(AppConstants.iconShrimp),
                            const SizedBox(width: 8),
                            AquaText.headline(
                              'L. Vannamei',
                              weight: AquaFontWeight.semibold,
                            ),

                            const Spacer(),
                            AquaText.body(
                              state.selectedState?.date ?? "",
                              color: acBlackColor,
                            ),

                            const SizedBox(width: 8),
                          ],
                        ),
                      ),
                      // Price table
                      if (state.selectedState != null)
                        _buildPriceTable(state.selectedState!),
                    ],
                  ),
                ),
              ],
            );
          },
          listener: (context, state) {
            // Track state changes for analytics
            if (state.isLoading) {
              _trackEvent('price_list_loading');
            } else if (state.selectedState != null) {
              _trackEvent('price_list_loaded', {
                'state': state.selectedState!.state,
                'date': state.selectedState!.date,
                'price_count': state.selectedState!.prices.length.toString(),
                'is_from_cache': state.isFromCache ? 'true' : 'false',
              });
            } else if (state.errorMessage != null &&
                state.errorMessage!.isNotEmpty) {
              _trackEvent('price_list_error', {
                'error_message': state.errorMessage ?? 'Unknown error',
              });
            }
          },
        ),
      ],
    );
  }

  Widget _buildPriceTable(PriceList priceList) {
    // Track price table view event
    _trackEvent('price_table_viewed', {
      'state': priceList.state,
      'date': priceList.date,
      'price_count': priceList.prices.length.toString(),
    });

    return Row(
      children: [
        // Left column headers
        Table(
          defaultColumnWidth: const FixedColumnWidth(60),
          border: TableBorder.all(
            width: 0.2,
            color: const Color(0xFFE2E2E2),
            borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(8),
            ),
          ),
          children: [
            TableRow(
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: Color(0xFFE2E2E2), width: 0.2),
                ),
              ),
              children: [_tableCell(text: 'Count')],
            ),
            TableRow(children: [_tableCell(text: 'Price(₹)')]),
          ],
        ),

        // Right scrollable data columns
        Expanded(
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: IntrinsicWidth(
              child: Table(
                defaultColumnWidth: const FixedColumnWidth(60),
                border: TableBorder.all(
                  width: 0.2,
                  color: const Color(0xFFE2E2E2),
                  borderRadius: const BorderRadius.only(
                    bottomRight: Radius.circular(8),
                  ),
                ),
                children: [
                  TableRow(
                    decoration: BoxDecoration(
                      border: Border(
                        bottom: BorderSide(
                          color: Color(0xFFE2E2E2),
                          width: 0.2,
                        ),
                      ),
                    ),
                    children:
                        priceList.prices
                            .map((item) => _tableCell(text: item.count))
                            .toList(),
                  ),
                  TableRow(
                    children:
                        priceList.prices
                            .map((item) => _tableCell(text: item.price))
                            .toList(),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _tableCell({required String text}) {
    final leftSpace = text == "Count" || text == "Price(₹)" ? 8.0 : 0.0;
    return Container(
      padding: EdgeInsets.only(top: 10, bottom: 10, left: leftSpace),
      alignment:
          text == "Count" || text == "Price(₹)"
              ? Alignment.centerLeft
              : Alignment.center,
      child: AquaText.body(text, maxLines: 1, overflow: TextOverflow.ellipsis),
    );
  }

  void _showLocationDialog(BuildContext context, PriceListState state) {
    if (state.priceLists.isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: AquaText.body('No states available')));
      _trackEvent('state_selection_failed', {'reason': 'no_states_available'});
      return;
    }

    showDialog(
      context: context,
      builder: (BuildContext context) {
        final selectedStateValue = state.selectedState?.state;
        _trackEvent('state_selection_dialog_opened', {
          'current_state': selectedStateValue ?? 'none',
          'available_states': state.stateNames.length.toString(),
        });

        return AlertDialog(
          title: const Text(
            'Select State',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          titlePadding: const EdgeInsets.fromLTRB(24, 24, 24, 0),
          backgroundColor: acWhiteColor,
          contentPadding: const EdgeInsets.fromLTRB(8, 12, 8, 24),
          content: SizedBox(
            width: MediaQuery.of(context).size.width * 0.8,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: state.stateNames.length,
              itemBuilder: (BuildContext context, int index) {
                final stateName = state.stateNames[index];
                final priceList = state.priceLists.firstWhere(
                  (pl) => pl.state == stateName,
                  orElse: () => state.priceLists.first,
                );

                return RadioListTile<String>(
                  title: AquaText.subheadline(
                    stateName,
                    weight: AquaFontWeight.bold,
                  ),
                  value: stateName,
                  activeColor: acPrimaryBlue,
                  groupValue: selectedStateValue,
                  dense: true,
                  contentPadding: const EdgeInsets.symmetric(horizontal: 8),
                  visualDensity: const VisualDensity(
                    horizontal: -4,
                    vertical: -4,
                  ),
                  onChanged: (_) {
                    _trackEvent('state_selected', {
                      'previous_state': selectedStateValue ?? 'none',
                      'new_state': stateName,
                    });
                    context.read<PriceListCubit>().changeState(priceList);
                    Navigator.pop(context);
                  },
                );
              },
            ),
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        );
      },
    );
  }

  // Helper method to track analytics events
  void _trackEvent(
    String eventName, [
    Map<String, Object> parameters = const {},
  ]) {
    // Add common parameters
    final eventParams = {
      'screen': 'PriceListScreen',
      'component': 'PriceListWidget',
      'timestamp': DateTime.now().toIso8601String(),
      ...parameters,
    };

    _analyticsService.logEvent(
      name: 'price_list_' + eventName,
      parameters: eventParams,
    );
  }
}
