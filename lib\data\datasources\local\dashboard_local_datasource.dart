import '../../../core/error/exceptions.dart';
import '../../../core/utils/logger.dart';
import '../../../objectbox.g.dart';
import '../../models/dashboard/dashboard_model.dart';

abstract class DashboardLocalDataSource {
  Future<DashboardModel> getDashboard(String customerId);
  Future<void> cacheDashboard(DashboardModel dashboard);
  Future<bool> needsSync(String customerId);
  Future<DateTime?> getLastSyncTime(String customerId);
  Future<void> updateSyncStatus(String customerId, bool needsSync);
  Future<void> updateLastSyncTime(String customerId, DateTime time);
  Future<void> deleteDashboard();
  Future<void> clearDashboardForCustomer(String customerId);
}

class DashboardLocalDataSourceImpl implements DashboardLocalDataSource {
  final Box<DashboardModel> dashboardBox;
  final AppLogger logger;

  // Cache for faster access
  DashboardModel? _cachedDashboard;
  String? _cachedCustomerId;
  DateTime? _cachedLastSyncTime;

  DashboardLocalDataSourceImpl({
    required this.dashboardBox,
    required this.logger,
  });

  @override
  Future<DashboardModel> getDashboard(String customerId) async {
    try {
      // Check memory cache first for immediate response
      if (_cachedDashboard != null && _cachedCustomerId == customerId) {
        logger.i('Serving dashboard from memory cache for $customerId');
        return _cachedDashboard!;
      }

      // If not in memory, check database
      final query =
          dashboardBox
              .query(DashboardModel_.customerId.equals(customerId))
              .build();
      final result = query.findFirst();
      query.close();

      if (result == null) {
        throw CacheException();
      }

      // Update memory cache
      _cachedDashboard = result;
      _cachedCustomerId = customerId;

      return result;
    } catch (e) {
      logger.e('Error getting dashboard from local storage: $e');
      throw CacheException();
    }
  }

  @override
  Future<void> cacheDashboard(DashboardModel dashboardModel) async {
    try {
      // Check if dashboard already exists
      final query =
          dashboardBox
              .query(
                DashboardModel_.customerId.equals(dashboardModel.customerId),
              )
              .build();
      final existingDashboard = query.findFirst();
      query.close();

      if (existingDashboard != null) {
        // Remove only the existing dashboard for this customer, not all dashboards
        dashboardBox.remove(existingDashboard.id);
        logger.i(
          'Removed existing dashboard for customer: ${dashboardModel.customerId}',
        );
      }

      // Insert new dashboard
      dashboardBox.put(dashboardModel);
      logger.i(
        'Inserted new dashboard for customer: ${dashboardModel.customerId}',
      );

      // Update memory cache
      _cachedDashboard = dashboardModel;
      _cachedCustomerId = dashboardModel.customerId;
      _cachedLastSyncTime = dashboardModel.lastSyncedAt;
    } catch (e) {
      logger.e('Error caching dashboard: $e');
      throw CacheException();
    }
  }

  @override
  Future<void> updateSyncStatus(String customerId, bool isSynced) async {
    try {
      final query =
          dashboardBox
              .query(DashboardModel_.customerId.equals(customerId))
              .build();
      final dashboard = query.findFirst();
      query.close();

      if (dashboard != null) {
        final updatedDashboard = DashboardModel(
          id: dashboard.id,
          customerId: dashboard.customerId,
          salesJson: dashboard.salesJson,
          salesReturnJson: dashboard.salesReturnJson,
          paymentsJson: dashboard.paymentsJson,
          duesJson: dashboard.duesJson,
          categoryTypeSalesJson: dashboard.categoryTypeSalesJson,
          liquidationJson: dashboard.liquidationJson,
          myFarmersJson: dashboard.myFarmersJson,
          isSynced: isSynced,
          lastSyncedAt: isSynced ? DateTime.now() : dashboard.lastSyncedAt,
        );

        dashboardBox.put(updatedDashboard);

        // Update memory cache
        if (_cachedCustomerId == customerId) {
          _cachedDashboard = updatedDashboard;
          _cachedLastSyncTime = updatedDashboard.lastSyncedAt;
        }

        logger.i(
          'Dashboard sync status updated for customer: $customerId, isSynced: $isSynced',
        );
      } else {
        logger.w(
          'No dashboard found to update sync status for customer: $customerId',
        );
      }
    } catch (e) {
      logger.e('Error updating dashboard sync status: $e');
      throw CacheException();
    }
  }

  @override
  Future<bool> needsSync(String customerId) async {
    try {
      // Check memory cache first for immediate response
      if (_cachedDashboard != null && _cachedCustomerId == customerId) {
        return !_cachedDashboard!.isSynced;
      }

      final query =
          dashboardBox
              .query(DashboardModel_.customerId.equals(customerId))
              .build();
      final dashboard = query.findFirst();
      query.close();

      if (dashboard == null) {
        // If no dashboard exists, it needs to be synced
        return true;
      }

      return !dashboard.isSynced;
    } catch (e) {
      logger.e('Error checking if dashboard needs sync: $e');
      return true; // Default to needing sync if there's an error
    }
  }

  @override
  Future<DateTime?> getLastSyncTime(String customerId) async {
    try {
      // Check memory cache first for immediate response
      if (_cachedLastSyncTime != null && _cachedCustomerId == customerId) {
        return _cachedLastSyncTime;
      }

      final query =
          dashboardBox
              .query(DashboardModel_.customerId.equals(customerId))
              .build();
      final dashboard = query.findFirst();
      query.close();

      if (dashboard == null || !dashboard.isSynced) {
        return null;
      }

      // Update memory cache
      _cachedLastSyncTime = dashboard.lastSyncedAt;

      return dashboard.lastSyncedAt;
    } catch (e) {
      logger.e('Error getting dashboard last sync time: $e');
      return null;
    }
  }

  @override
  Future<void> updateLastSyncTime(String customerId, DateTime time) async {
    try {
      final query =
          dashboardBox
              .query(DashboardModel_.customerId.equals(customerId))
              .build();
      final dashboard = query.findFirst();
      query.close();

      if (dashboard != null) {
        final updatedDashboard = DashboardModel(
          id: dashboard.id,
          customerId: dashboard.customerId,
          salesJson: dashboard.salesJson,
          salesReturnJson: dashboard.salesReturnJson,
          paymentsJson: dashboard.paymentsJson,
          duesJson: dashboard.duesJson,
          categoryTypeSalesJson: dashboard.categoryTypeSalesJson,
          liquidationJson: dashboard.liquidationJson,
          myFarmersJson: dashboard.myFarmersJson,
          isSynced: true,
          lastSyncedAt: time,
        );

        dashboardBox.put(updatedDashboard);

        // Update memory cache
        if (_cachedCustomerId == customerId) {
          _cachedLastSyncTime = time;
          _cachedDashboard = updatedDashboard;
        }

        logger.i(
          'Dashboard last sync time updated for customer: $customerId to $time',
        );
      } else {
        logger.w(
          'No dashboard found to update last sync time for customer: $customerId',
        );
      }
    } catch (e) {
      logger.e('Error updating dashboard last sync time: $e');
      throw CacheException();
    }
  }

  @override
  Future<void> deleteDashboard() async {
    dashboardBox.removeAll();
    // Clear memory cache
    _cachedDashboard = null;
    _cachedCustomerId = null;
    _cachedLastSyncTime = null;
  }

  @override
  Future<void> clearDashboardForCustomer(String customerId) async {
    try {
      // Find the dashboard for this customer
      final query =
          dashboardBox
              .query(DashboardModel_.customerId.equals(customerId))
              .build();
      final dashboard = query.findFirst();
      query.close();

      if (dashboard != null) {
        // Remove the dashboard from the box
        dashboardBox.remove(dashboard.id);
        logger.i('Dashboard cleared for customer: $customerId');

        // Clear memory cache if it's for this customer
        if (_cachedCustomerId == customerId) {
          _cachedDashboard = null;
          _cachedCustomerId = null;
          _cachedLastSyncTime = null;
          logger.i('Memory cache cleared for customer: $customerId');
        }
      } else {
        logger.w('No dashboard found to clear for customer: $customerId');
      }
    } catch (e) {
      logger.e('Error clearing dashboard for customer $customerId: $e');
      throw CacheException();
    }
  }
}
