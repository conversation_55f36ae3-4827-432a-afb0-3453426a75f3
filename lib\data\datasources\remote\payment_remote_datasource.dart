import 'dart:convert';
import '../../../core/error/exceptions.dart';
import '../../../core/network/api_client.dart';
import '../../../core/utils/logger.dart';
import '../../../domain/entities/payments/payment_request.dart';
import '../../models/payments/payment_session_model.dart';
import '../../models/payments/payment_transaction_model.dart';

/// Abstract interface for payment remote data source
abstract class PaymentRemoteDataSource {
  Future<PaymentSessionModel> createPaymentSession(PaymentRequest request);
  Future<PaymentSessionModel> getPaymentSession(String sessionId);
  Future<PaymentTransactionModel> verifyPayment(String sessionId);
  Future<PaymentSessionModel> checkPaymentStatus(String sessionId);
  Future<void> cancelPaymentSession(String sessionId);
  Future<PaymentTransactionModel> getPaymentTransaction(String transactionId);
  Future<List<PaymentTransactionModel>> getCustomerTransactions(
    String customerId, {
    int? limit,
    int? offset,
  });
  Future<List<PaymentTransactionModel>> getInvoiceTransactions(
    String invoiceNumber,
  );
  Future<PaymentTransactionModel> processWebhookNotification(
    Map<String, dynamic> webhookData,
  );
}

/// Implementation of payment remote data source using ApiClient
class PaymentRemoteDataSourceImpl implements PaymentRemoteDataSource {
  final ApiClient apiClient;
  final AppLogger logger;

  PaymentRemoteDataSourceImpl({required this.apiClient, required this.logger});

  @override
  Future<PaymentSessionModel> createPaymentSession(
    PaymentRequest request,
  ) async {
    try {
      logger.i(
        'Creating payment session for invoice: ${request.invoiceNumber}',
      );
      logger.d('Payment request data: ${request.toJson()}');

      final response = await apiClient.post(
        '/zoho/payments/create-session/',
        data: request.toJson(),
      );

      logger.d('Payment session response status: ${response.statusCode}');
      logger.d('Payment session response data: ${response.data}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        final jsonData =
            response.data is String
                ? jsonDecode(response.data)
                : response.data as Map<String, dynamic>;

        logger.i('Payment session created successfully');
        return PaymentSessionModel.fromJson(jsonData['data'] ?? jsonData);
      } else {
        final errorMessage =
            'Failed to create payment session: ${response.statusCode}';
        final responseBody = response.data?.toString() ?? 'No response body';
        logger.e('$errorMessage - Response: $responseBody');

        // Provide more specific error based on status code
        if (response.statusCode == 400) {
          throw ValidationException('Invalid payment request data');
        } else if (response.statusCode == 401) {
          throw AuthException();
        } else if (response.statusCode == 403) {
          throw AuthException();
        } else if (response.statusCode == 404) {
          throw ServerException();
        } else if (response.statusCode == 500) {
          throw ServerException();
        } else {
          throw ServerException();
        }
      }
    } catch (e) {
      logger.e('Error creating payment session: ${e.toString()}');

      // Re-throw known exceptions
      if (e is ValidationException ||
          e is AuthException ||
          e is ServerException) {
        rethrow;
      }

      // Handle network and other errors
      if (e.toString().contains('SocketException') ||
          e.toString().contains('TimeoutException')) {
        throw NetworkException();
      }

      throw ServerException();
    }
  }

  @override
  Future<PaymentSessionModel> getPaymentSession(String sessionId) async {
    try {
      logger.i('Getting payment session: $sessionId');

      final response = await apiClient.get('/zoho/payments/status/$sessionId/');

      if (response.statusCode == 200) {
        final jsonData =
            response.data is String
                ? jsonDecode(response.data)
                : response.data as Map<String, dynamic>;

        logger.i('Payment session retrieved successfully');
        return PaymentSessionModel.fromJson(jsonData['data'] ?? jsonData);
      } else {
        logger.e('Failed to get payment session: ${response.statusCode}');
        throw ServerException();
      }
    } catch (e) {
      logger.e('Error getting payment session: ${e.toString()}');
      if (e is ServerException) rethrow;
      throw ServerException();
    }
  }

  @override
  Future<PaymentTransactionModel> verifyPayment(String sessionId) async {
    try {
      logger.i('Verifying payment for session: $sessionId');

      final response = await apiClient.get('/zoho/payments/status/$sessionId/');

      if (response.statusCode == 200) {
        final jsonData =
            response.data is String
                ? jsonDecode(response.data)
                : response.data as Map<String, dynamic>;

        logger.i('Payment verification completed');
        return PaymentTransactionModel.fromPaymentStatus(
          jsonData['data'] ?? jsonData,
        );
      } else {
        logger.e('Failed to verify payment: ${response.statusCode}');
        throw ServerException();
      }
    } catch (e) {
      logger.e('Error verifying payment: ${e.toString()}');
      if (e is ServerException) rethrow;
      throw ServerException();
    }
  }

  @override
  Future<PaymentSessionModel> checkPaymentStatus(String sessionId) async {
    try {
      logger.i('Checking payment status for session: $sessionId');

      final response = await apiClient.get('/zoho/payments/status/$sessionId/');

      if (response.statusCode == 200) {
        final jsonData =
            response.data is String
                ? jsonDecode(response.data)
                : response.data as Map<String, dynamic>;

        logger.i('Payment status checked successfully');
        return PaymentSessionModel.fromJson(jsonData['data'] ?? jsonData);
      } else {
        logger.e('Failed to check payment status: ${response.statusCode}');
        throw ServerException();
      }
    } catch (e) {
      logger.e('Error checking payment status: ${e.toString()}');
      if (e is ServerException) rethrow;
      throw ServerException();
    }
  }

  @override
  Future<void> cancelPaymentSession(String sessionId) async {
    try {
      logger.i('Cancelling payment session: $sessionId');

      final response = await apiClient.put(
        '/zoho/payments/status/$sessionId',
        data: {'status': 'cancelled'},
      );

      if (response.statusCode == 200) {
        logger.i('Payment session cancelled successfully');
      } else {
        logger.e('Failed to cancel payment session: ${response.statusCode}');
        throw ServerException();
      }
    } catch (e) {
      logger.e('Error cancelling payment session: ${e.toString()}');
      if (e is ServerException) rethrow;
      throw ServerException();
    }
  }

  @override
  Future<PaymentTransactionModel> getPaymentTransaction(
    String transactionId,
  ) async {
    try {
      logger.i('Getting payment transaction: $transactionId');

      // Note: This endpoint might need to be implemented on the backend
      final response = await apiClient.get(
        '/zoho/payments/transaction/$transactionId/',
      );

      if (response.statusCode == 200) {
        final jsonData =
            response.data is String
                ? jsonDecode(response.data)
                : response.data as Map<String, dynamic>;

        logger.i('Payment transaction retrieved successfully');
        return PaymentTransactionModel.fromJson(jsonData['data'] ?? jsonData);
      } else {
        logger.e('Failed to get payment transaction: ${response.statusCode}');
        throw ServerException();
      }
    } catch (e) {
      logger.e('Error getting payment transaction: ${e.toString()}');
      if (e is ServerException) rethrow;
      throw ServerException();
    }
  }

  @override
  Future<List<PaymentTransactionModel>> getCustomerTransactions(
    String customerId, {
    int? limit,
    int? offset,
  }) async {
    try {
      logger.i('Getting customer transactions for: $customerId');

      final queryParams = <String, dynamic>{
        'customer_id': customerId,
        if (limit != null) 'limit': limit.toString(),
        if (offset != null) 'offset': offset.toString(),
      };

      final response = await apiClient.get(
        '/zoho/payments/list/',
        queryParams: queryParams,
      );

      if (response.statusCode == 200) {
        final jsonData =
            response.data is String
                ? jsonDecode(response.data)
                : response.data as Map<String, dynamic>;

        final transactions = jsonData['data']['transactions'] as List? ?? [];
        logger.i('Retrieved ${transactions.length} customer transactions');

        return transactions
            .map((json) => PaymentTransactionModel.fromJson(json))
            .toList();
      } else {
        logger.e('Failed to get customer transactions: ${response.statusCode}');
        throw ServerException();
      }
    } catch (e) {
      logger.e('Error getting customer transactions: ${e.toString()}');
      if (e is ServerException) rethrow;
      throw ServerException();
    }
  }

  @override
  Future<List<PaymentTransactionModel>> getInvoiceTransactions(
    String invoiceNumber,
  ) async {
    try {
      logger.i('Getting invoice transactions for: $invoiceNumber');

      final queryParams = <String, dynamic>{'invoice_number': invoiceNumber};

      final response = await apiClient.get(
        '/zoho/payments/list',
        queryParams: queryParams,
      );

      if (response.statusCode == 200) {
        final jsonData =
            response.data is String
                ? jsonDecode(response.data)
                : response.data as Map<String, dynamic>;

        final transactions = jsonData['data']['transactions'] as List? ?? [];
        logger.i('Retrieved ${transactions.length} invoice transactions');

        return transactions
            .map((json) => PaymentTransactionModel.fromJson(json))
            .toList();
      } else {
        logger.e('Failed to get invoice transactions: ${response.statusCode}');
        throw ServerException();
      }
    } catch (e) {
      logger.e('Error getting invoice transactions: ${e.toString()}');
      if (e is ServerException) rethrow;
      throw ServerException();
    }
  }

  @override
  Future<PaymentTransactionModel> processWebhookNotification(
    Map<String, dynamic> webhookData,
  ) async {
    try {
      logger.i('Processing webhook notification');

      final response = await apiClient.post(
        '/zoho/webhooks/payment/',
        data: webhookData,
      );

      if (response.statusCode == 200) {
        final jsonData =
            response.data is String
                ? jsonDecode(response.data)
                : response.data as Map<String, dynamic>;

        logger.i('Webhook notification processed successfully');
        return PaymentTransactionModel.fromJson(jsonData['data'] ?? jsonData);
      } else {
        logger.e(
          'Failed to process webhook notification: ${response.statusCode}',
        );
        throw ServerException();
      }
    } catch (e) {
      logger.e('Error processing webhook notification: ${e.toString()}');
      if (e is ServerException) rethrow;
      throw ServerException();
    }
  }
}
