import 'package:dartz/dartz.dart';
import '../../../core/error/failures.dart';
import '../../entities/payments/payment_request.dart';
import '../../entities/payments/payment_session.dart';
import '../../repositories/payment_repository.dart';

/// Use case for creating a payment session
class CreatePaymentSessionUseCase {
  final PaymentRepository repository;

  CreatePaymentSessionUseCase(this.repository);

  /// Creates a payment session for the given request
  ///
  /// Returns [PaymentSession] on success or [Failure] on error
  Future<Either<Failure, PaymentSession>> call(PaymentRequest request) async {
    // Validate the payment request
    if (!request.isValid) {
      return Left(
        ValidationFailure(
          'Invalid payment request: ${request.validationErrors.join(', ')}',
        ),
      );
    }

    // Create the payment session
    return await repository.createPaymentSession(request);
  }
}
