import 'package:flutter/material.dart';
import '../services/analytics_service.dart';

class AnalyticsRouteObserver extends RouteObserver<PageRoute<dynamic>> {
  final AnalyticsService _analyticsService;
  DateTime? _lastScreenTime;
  String? _lastScreenName;

  AnalyticsRouteObserver(this._analyticsService);

  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPush(route, previousRoute);
    if (route is PageRoute) {
      final currentTime = DateTime.now();
      final screenName = route.settings.name ?? 'unknown';

      // Only log screen duration for routes that don't use AnalyticsMixin
      // This prevents duplicate tracking
      if (_lastScreenTime != null && _lastScreenName != null) {
        // Check if the previous route uses AnalyticsMixin
        // This is a simplified check - you may need a more robust solution
        final previousWidget = previousRoute?.settings.arguments;
        final usesMixin =
            previousWidget != null &&
            previousWidget.toString().contains('AnalyticsMixin');

        if (!usesMixin) {
          final duration = currentTime.difference(_lastScreenTime!);
          _analyticsService.logScreenDuration(
            screenName: _lastScreenName!,
            durationMs: duration.inMilliseconds,
          );
        }
      }

      // Track new screen
      _lastScreenTime = currentTime;
      _lastScreenName = screenName;

      _analyticsService.logScreenView(
        screenName: screenName,
        screenClass: route.runtimeType.toString(),
      );
    }
  }

  @override
  void didPop(Route<dynamic> route, Route<dynamic>? previousRoute) async {
    super.didPop(route, previousRoute);

    if (route is PageRoute && previousRoute is PageRoute) {
      final currentTime = DateTime.now();
      final screenName = route.settings.name ?? 'unknown';

      if (_lastScreenTime != null) {
        final duration = currentTime.difference(_lastScreenTime!);

        // Log screen duration
        _analyticsService.logScreenDuration(
          screenName: screenName,
          durationMs: duration.inMilliseconds,
        );

        _analyticsService.logUserEngagement(duration.inMilliseconds);
      }

      // Update for previous screen
      _lastScreenTime = currentTime;
      _lastScreenName = previousRoute.settings.name ?? 'unknown';
    }
  }
}
