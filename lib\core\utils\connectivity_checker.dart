import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';

/// A utility class to check network connectivity
class ConnectivityChecker {
  final Connectivity _connectivity;
  
  ConnectivityChecker({Connectivity? connectivity}) 
      : _connectivity = connectivity ?? Connectivity();

  /// Checks if the device has internet connectivity
  /// Returns true if connected, false otherwise
  Future<bool> hasConnectivity() async {
    try {
      final result = await _connectivity.checkConnectivity();
      return result != ConnectivityResult.none;
    } catch (e) {
      // If there's an error checking connectivity, assume no connection
      return false;
    }
  }

  /// Returns a stream of connectivity changes
  Stream<bool> get connectivityStream {
    return _connectivity.onConnectivityChanged.map(
      (result) => result != ConnectivityResult.none,
    );
  }
}