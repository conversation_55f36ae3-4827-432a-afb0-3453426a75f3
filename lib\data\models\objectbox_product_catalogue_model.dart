import 'dart:convert';
import 'package:objectbox/objectbox.dart';
import 'product_catalogue_model.dart';
import 'product_model.dart';

@Entity()
class ObjectBoxProductCatalogueModel {
  @Id()
  int id = 0;
  
  @Index()
  final String name; // Use name as the primary identifier
  
  final String image;
  final String sortOrder;
  final String status;
  final String productsJson; // Store products as JSON string for ObjectBox compatibility

  ObjectBoxProductCatalogueModel({
    this.id = 0,
    required this.name,
    required this.image,
    required this.sortOrder,
    required this.status,
    required this.productsJson,
  });

  factory ObjectBoxProductCatalogueModel.fromProductCatalogueModel(ProductCatalogueModel model) {
    // Convert products to JSON string
    final productsJson = jsonEncode(
      (model.products as List<ProductModel>).map((item) => item.toJson()).toList()
    );
    
    return ObjectBoxProductCatalogueModel(
      name: model.name,
      image: model.image,
      sortOrder: model.sortOrder,
      status: model.status,
      productsJson: productsJson,
    );
  }

  ProductCatalogueModel toProductCatalogueModel() {
    // Parse products from JSON string
    final productsData = jsonDecode(productsJson) as List<dynamic>;
    final products = productsData
        .map((item) => ProductModel.fromJson(item))
        .toList();
    
    return ProductCatalogueModel(
      name: name,
      image: image,
      sortOrder: sortOrder,
      status: status,
      products: products,
    );
  }

  ObjectBoxProductCatalogueModel copyWith({
    int? id,
    String? name,
    String? image,
    String? sortOrder,
    String? status,
    String? productsJson,
  }) {
    return ObjectBoxProductCatalogueModel(
      id: id ?? this.id,
      name: name ?? this.name,
      image: image ?? this.image,
      sortOrder: sortOrder ?? this.sortOrder,
      status: status ?? this.status,
      productsJson: productsJson ?? this.productsJson,
    );
  }
}