import '../../../core/error/exceptions.dart';
import '../../../core/utils/logger.dart';
import '../../../objectbox.g.dart';
import '../../models/interested_product_model.dart';
import '../../models/objectbox_interested_product_model.dart';

abstract class InterestedProductLocalDataSource {
  /// Gets all interested products from local storage
  Future<List<InterestedProductModel>> getInterestedProducts();

  /// Adds a new interested product to local storage
  Future<InterestedProductModel> addInterestedProduct(
    InterestedProductModel interestedProduct,
  );

  /// Gets all unsynced interested products
  Future<List<InterestedProductModel>> getUnsyncedInterestedProducts();

  /// Marks an interested product as synced
  Future<void> markAsSynced(InterestedProductModel interestedProduct);

  /// Gets the count of unsynced interested products
  Future<int> getUnsyncedCount();
}

class InterestedProductLocalDataSourceImpl
    implements InterestedProductLocalDataSource {
  final Box<ObjectBoxInterestedProductModel> interestedProductBox;
  final AppLogger logger;

  InterestedProductLocalDataSourceImpl({
    required this.interestedProductBox,
    required this.logger,
  });

  @override
  Future<List<InterestedProductModel>> getInterestedProducts() async {
    try {
      logger.i("Getting interested products from local storage");

      final objectBoxModels = interestedProductBox.getAll();

      if (objectBoxModels.isEmpty) {
        logger.w("No interested products found in local storage");
        return [];
      }

      final interestedProducts =
          objectBoxModels
              .map((model) => model.toInterestedProductModel())
              .toList();

      logger.i(
        "Found ${interestedProducts.length} interested products in local storage",
      );
      return interestedProducts;
    } catch (e) {
      logger.e("Error getting interested products from local storage", e);
      throw CacheException();
    }
  }

  @override
  Future<InterestedProductModel> addInterestedProduct(
    InterestedProductModel interestedProduct,
  ) async {
    try {
      logger.i(
        "Adding interested product to local storage: ${interestedProduct.productName}",
      );

      final objectBoxModel =
          ObjectBoxInterestedProductModel.fromInterestedProductModel(
            interestedProduct,
          );

      final id = interestedProductBox.put(objectBoxModel);

      // Get the saved model to return with the ObjectBox ID
      final savedModel = interestedProductBox.get(id)!;

      logger.i(
        "Successfully added interested product to local storage with ID: $id",
      );
      return savedModel.toInterestedProductModel();
    } catch (e) {
      logger.e("Error adding interested product to local storage", e);
      throw CacheException();
    }
  }

  @override
  Future<List<InterestedProductModel>> getUnsyncedInterestedProducts() async {
    try {
      logger.i("Getting unsynced interested products from local storage");

      final query =
          interestedProductBox
              .query(ObjectBoxInterestedProductModel_.isSynced.equals(false))
              .build();

      final results = query.find();
      query.close();

      if (results.isEmpty) {
        logger.w("No unsynced interested products found in local storage");
        return [];
      }

      final unsyncedProducts =
          results.map((model) => model.toInterestedProductModel()).toList();

      logger.i(
        "Found ${unsyncedProducts.length} unsynced interested products in local storage",
      );
      return unsyncedProducts;
    } catch (e) {
      logger.e(
        "Error getting unsynced interested products from local storage",
        e,
      );
      throw CacheException();
    }
  }

  @override
  Future<void> markAsSynced(InterestedProductModel interestedProduct) async {
    try {
      logger.i(
        "Marking interested product as synced: ${interestedProduct.productName}",
      );

      // Find the model by MongoDB ID if available, otherwise by product details
      ObjectBoxInterestedProductModel? existingModel;

      if (interestedProduct.id != null) {
        final query =
            interestedProductBox
                .query(
                  ObjectBoxInterestedProductModel_.mongoId.equals(
                    interestedProduct.id!,
                  ),
                )
                .build();

        final results = query.find();
        query.close();

        if (results.isNotEmpty) {
          existingModel = results.first;
        }
      }

      // If not found by MongoDB ID, try to find by other details
      if (existingModel == null) {
        final query =
            interestedProductBox
                .query(
                  ObjectBoxInterestedProductModel_.customerId.equals(
                        interestedProduct.customerId,
                      ) &
                      ObjectBoxInterestedProductModel_.datetimeString.equals(
                        interestedProduct.datetime.toIso8601String(),
                      ),
                )
                .build();

        final results = query.find();
        query.close();

        if (results.isNotEmpty) {
          existingModel = results.first;
        }
      }

      if (existingModel != null) {
        // Update the model with synced status and MongoDB ID if available
        final updatedModel = existingModel.copyWith(
          mongoId: interestedProduct.id ?? existingModel.mongoId,
          isSynced: true,
        );

        interestedProductBox.put(updatedModel);
        logger.i("Successfully marked interested product as synced");
      } else {
        logger.w("Could not find interested product to mark as synced");
      }
    } catch (e) {
      logger.e("Error marking interested product as synced", e);
      throw CacheException();
    }
  }

  @override
  Future<int> getUnsyncedCount() async {
    try {
      logger.i("Getting unsynced interested products count");

      final query =
          interestedProductBox
              .query(ObjectBoxInterestedProductModel_.isSynced.equals(false))
              .build();

      final count = query.count();
      query.close();

      logger.i("Unsynced interested products count: $count");
      return count;
    } catch (e) {
      logger.e("Error getting unsynced interested products count", e);
      throw CacheException();
    }
  }
}
