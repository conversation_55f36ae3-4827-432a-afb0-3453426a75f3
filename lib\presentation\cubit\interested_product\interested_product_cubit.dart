import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../core/utils/logger.dart';
import '../../../domain/entities/interested_product.dart';
import '../../../domain/usecases/interested_product_usecases.dart';
import '../../../domain/usecases/product_catalogue_usecases.dart';
import '../../../domain/usecases/user_usecases.dart';
import 'interested_product_state.dart';

class InterestedProductCubit extends Cubit<InterestedProductState> {
  final GetInterestedProductsUseCase getInterestedProductsUseCase;
  final AddInterestedProductUseCase addInterestedProductUseCase;
  final SyncInterestedProductsUseCase syncInterestedProductsUseCase;
  final GetUserUseCase getUserUseCase;
  final GetUnsyncedCountUseCase getUnsyncedCountUseCase;
  final AppLogger logger;

  InterestedProductCubit({
    required this.getInterestedProductsUseCase,
    required this.addInterestedProductUseCase,
    required this.syncInterestedProductsUseCase,
    required this.getUserUseCase,
    required this.getUnsyncedCountUseCase,
    required this.logger,
  }) : super(const InterestedProductState());

  Future<void> loadInterestedProducts() async {
    emit(state.copyWith(status: InterestedProductStatus.loading));

    final result = await getInterestedProductsUseCase();

    result.fold(
      (failure) {
        logger.e("Failed to load interested products", failure);
        emit(
          state.copyWith(
            status: InterestedProductStatus.error,
            errorMessage: "try again later",
          ),
        );
      },
      (interestedProducts) {
        logger.i(
          "Successfully loaded ${interestedProducts.length} interested products",
        );
        _updateUnsyncedCount();
        emit(
          state.copyWith(
            status: InterestedProductStatus.success,
            interestedProducts: interestedProducts,
            errorMessage: null,
          ),
        );
      },
    );
  }

  Future<void> addInterestedProduct({
    required String productName,
    String source = "Flutter Farmer App",
  }) async {
    emit(state.copyWith(status: InterestedProductStatus.adding));

    final userResult = await getUserUseCase();

    final userOrNull = userResult.fold((l) => null, (r) => r);

    // Check if user is null or if mongoId is null
    if (userOrNull == null || userOrNull.mongoId == null) {
      logger.e("User or mongoId is null, cannot add interested product");
      emit(
        state.copyWith(
          status: InterestedProductStatus.error,
          errorMessage: "User information not available. Please log in again.",
        ),
      );
      return;
    }

    final user = userOrNull; // Now we know user is not null

    final interestedProduct = InterestedProduct(
      customerId: user.mongoId!,
      mobile: user.phoneNumber,
      productName: productName,
      datetime: DateTime.now(),
      source: source, // Use the provided source parameter
    );

    final result = await addInterestedProductUseCase(interestedProduct);

    result.fold(
      (failure) {
        logger.e("Failed to add interested product", failure);
        emit(
          state.copyWith(
            status: InterestedProductStatus.error,
            errorMessage:
                "Failed to register interest. Please try again later.",
          ),
        );
      },
      (addedProduct) {
        logger.i(
          "Successfully added interested product: ${addedProduct.productName}",
        );

        // Update the list with the new product
        final updatedList = List<InterestedProduct>.from(
          state.interestedProducts,
        )..add(addedProduct);

        _updateUnsyncedCount();

        emit(
          state.copyWith(
            status: InterestedProductStatus.success,
            interestedProducts: updatedList,
            errorMessage: null,
          ),
        );
      },
    );
  }

  Future<void> syncInterestedProducts() async {
    emit(state.copyWith(status: InterestedProductStatus.syncing));

    final result = await syncInterestedProductsUseCase();

    result.fold(
      (failure) {
        logger.e("Failed to sync interested products", failure);
        emit(
          state.copyWith(
            status: InterestedProductStatus.error,
            errorMessage: "try again later",
          ),
        );

        // Reload the list after sync failure
        loadInterestedProducts();
      },
      (syncedCount) {
        logger.i("Successfully synced $syncedCount interested products");

        // Reload the list to get updated sync status
        loadInterestedProducts();
      },
    );
  }

  Future<void> _updateUnsyncedCount() async {
    final result = await getUnsyncedCountUseCase();

    result.fold(
      (failure) {
        logger.e("Failed to get unsynced count", failure);
        // Don't update the state for this failure
      },
      (count) {
        logger.i("Unsynced count: $count");
        emit(state.copyWith(unsyncedCount: count));
      },
    );
  }

  Future<void> refreshUnsyncedCount() async {
    await _updateUnsyncedCount();
  }
}
