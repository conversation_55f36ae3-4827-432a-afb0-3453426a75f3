import 'dart:async';
import 'package:aquapartner/core/services/analytics_service.dart';
import 'package:aquapartner/domain/services/auth_service.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';

import '../../../core/utils/logger.dart';
import '../../../domain/entities/dashboard/dashboard_entity.dart';
import '../../../domain/entities/sync_status.dart';
import '../../../domain/services/dashboard_service.dart';
import '../../../domain/usecases/dashboard_usecases.dart';
import 'dashboard_state.dart';

class DashboardCubit extends Cubit<DashboardState> {
  final GetDashboardUseCase getDashboardUseCase;
  final SyncDashboardUseCase syncDashboardUseCase;
  final GetDashboardSyncStatusUseCase getSyncStatusUseCase;
  final DashboardService dashboardService;
  final AppLogger logger;
  final InternetConnectionChecker connectivityChecker;
  final AuthService authService;
  final AnalyticsService analyticsService;

  StreamSubscription<SyncStatus>? _syncStatusSubscription;
  String? _currentCustomerId;
  Timer? _autoRefreshTimer;
  bool _isPrefetchInProgress = false;

  DashboardCubit({
    required this.getDashboardUseCase,
    required this.syncDashboardUseCase,
    required this.getSyncStatusUseCase,
    required this.logger,
    required this.dashboardService,
    required this.connectivityChecker,
    required this.authService,
    required this.analyticsService,
  }) : super(DashboardInitial()) {
    // Subscribe to sync status updates
    _syncStatusSubscription = dashboardService.syncStatusStream.listen(
      _handleSyncStatusUpdate,
    );

    // Start prefetching data immediately
    _prefetchDashboardData();
  }

  /// Prefetches dashboard data in the background to speed up initial load
  Future<void> _prefetchDashboardData() async {
    if (_isPrefetchInProgress) return;
    _isPrefetchInProgress = true;

    try {
      logger.i('Starting dashboard data prefetch');
      final authResult = await authService.getCurrentCustomer();

      authResult.fold(
        (failure) {
          logger.w('Prefetch: Failed to get customer data: $failure');
          _isPrefetchInProgress = false;
        },
        (customerData) async {
          if (customerData == null) {
            logger.w('Prefetch: No customer data available');
            _isPrefetchInProgress = false;
            return;
          }

          analyticsService.setCurrentUser(customerData);

          _currentCustomerId = customerData.customerId;

          // Try to get data from local cache first
          try {
            final dashboardResult = await getDashboardUseCase(
              _currentCustomerId!,
            );

            dashboardResult.fold(
              (failure) {
                logger.w('Prefetch: Failed to get dashboard data: $failure');
                _isPrefetchInProgress = false;
              },
              (dashboardData) async {
                logger.i('Prefetch: Dashboard data loaded successfully');

                // If we're in initial state, update to loaded state
                if (state is DashboardInitial) {
                  await _updateStateWithLoadedData(dashboardData);
                }

                // Check if we need to sync in background
                _checkAndTriggerBackgroundSyncIfNeeded();
                _isPrefetchInProgress = false;
              },
            );
          } catch (e) {
            logger.e('Prefetch: Error loading dashboard data: $e');
            _isPrefetchInProgress = false;
          }
        },
      );
    } catch (e) {
      logger.e('Prefetch: Unexpected error: $e');
      _isPrefetchInProgress = false;
    }
  }

  /// Checks if the device has internet connectivity
  /// Returns true if connected, false otherwise
  Future<bool> _checkConnectivity() async {
    final isConnected = await connectivityChecker.hasConnection;
    if (!isConnected && state is DashboardLoaded) {
      final currentState = state as DashboardLoaded;
      emit(
        DashboardLoaded(
          dashboard: currentState.dashboard,
          isSyncing: false,
          lastSyncTime: currentState.lastSyncTime,
          hasError: true,
          errorMessage: 'No internet connection',
        ),
      );
    }
    return isConnected;
  }

  /// Loads dashboard data for a specific customer
  Future<void> loadDashboardData() async {
    // If prefetch already loaded data, don't show loading state
    if (state is DashboardLoaded) {
      // Data already loaded, just check for updates in background
      _checkAndTriggerBackgroundSyncIfNeeded();
      return;
    }

    // Don't show loading state if we already have data - this prevents flickering
    bool isInitialLoad = state is DashboardInitial;
    DashboardEntity? previousDashboard;
    DateTime? previousLastSyncTime;
    bool wasSyncing = false;

    // Preserve previous data if available
    if (state is DashboardLoaded) {
      final currentState = state as DashboardLoaded;
      previousDashboard = currentState.dashboard;
      previousLastSyncTime = currentState.lastSyncTime;
      wasSyncing = currentState.isSyncing;
    } else if (isInitialLoad) {
      emit(DashboardLoading());
    }

    final authServiceResult = await authService.getCurrentCustomer();

    authServiceResult.fold(
      (failure) {
        logger.e('Failed to load customer data: $failure');
        // If we have previous data, keep showing it instead of error
        if (previousDashboard != null) {
          emit(
            DashboardLoaded(
              dashboard: previousDashboard,
              isSyncing: wasSyncing,
              lastSyncTime: previousLastSyncTime,
              hasError: true,
              errorMessage: "Customer data unavailable",
            ),
          );
        } else {
          emit(DashboardError("No customer data available"));
        }
      },
      (customerData) async {
        if (customerData == null) {
          if (previousDashboard != null) {
            emit(
              DashboardLoaded(
                dashboard: previousDashboard,
                isSyncing: wasSyncing,
                lastSyncTime: previousLastSyncTime,
                hasError: true,
                errorMessage: "Customer data unavailable",
              ),
            );
          } else {
            emit(DashboardError("No customer data available"));
          }
          return;
        }

        _currentCustomerId = customerData.customerId;
        logger.i('Customer data loaded successfully');

        try {
          // Get dashboard data - prioritize local cache for speed
          final dashboardResult = await getDashboardUseCase(
            _currentCustomerId!,
          );

          dashboardResult.fold(
            (failure) {
              logger.e('Failed to load dashboard data: ${failure.toString()}');

              // If we have previous data, keep showing it instead of error
              if (previousDashboard != null) {
                emit(
                  DashboardLoaded(
                    dashboard: previousDashboard,
                    isSyncing: wasSyncing,
                    lastSyncTime: previousLastSyncTime,
                    hasError: true,
                    errorMessage: "Please try again later",
                  ),
                );
              } else {
                emit(DashboardError("Please try again later"));
              }
            },
            (dashboardData) async {
              await _updateStateWithLoadedData(dashboardData);

              // Check if we need to sync in background
              _checkAndTriggerBackgroundSyncIfNeeded();
            },
          );
        } catch (e, stackTrace) {
          logger.e(
            'Unexpected error loading dashboard: $e\nStackTrace: $stackTrace',
          );

          // If we have previous data, keep showing it instead of error
          if (previousDashboard != null) {
            emit(
              DashboardLoaded(
                dashboard: previousDashboard,
                isSyncing: false,
                lastSyncTime: previousLastSyncTime,
                hasError: true,
                errorMessage: "An unexpected error occurred",
              ),
            );
          } else {
            emit(DashboardError('An unexpected error occurred: $e'));
          }
        }
      },
    );
  }

  /// Updates state with loaded dashboard data
  Future<void> _updateStateWithLoadedData(DashboardEntity dashboardData) async {
    logger.i("Dashboard data loaded for customer: ${dashboardData.customerId}");

    // Get last sync time
    final lastSyncTime = await dashboardService.getLastSyncTime(
      _currentCustomerId!,
    );

    // Check if sync is in progress
    final isSyncing = dashboardService.isSyncing(_currentCustomerId!);
    final isConnected = await connectivityChecker.hasConnection;

    emit(
      DashboardLoaded(
        dashboard: dashboardData,
        isSyncing: isSyncing,
        lastSyncTime: lastSyncTime,
        hasError: !isConnected,
        errorMessage: !isConnected ? 'Offline mode - using cached data' : null,
      ),
    );
  }

  /// Checks if data is stale and triggers background sync if needed
  void _checkAndTriggerBackgroundSyncIfNeeded() async {
    if (_currentCustomerId == null) return;

    try {
      final isConnected = await connectivityChecker.hasConnection;
      if (!isConnected) return;

      if (isDataStale() && !dashboardService.isSyncing(_currentCustomerId!)) {
        // Trigger background sync without waiting
        syncDashboard();
      }
    } catch (e) {
      logger.w('Error checking for background sync: $e');
    }
  }

  /// Manually triggers a sync operation
  Future<void> syncDashboard() async {
    if (_currentCustomerId == null) {
      logger.w('Cannot sync: No customer ID set');
      return;
    }

    // Check connectivity before proceeding
    final isConnected = await _checkConnectivity();
    if (!isConnected) {
      logger.w('Cannot sync: No internet connection');
      return;
    }

    try {
      // Update current state to show syncing
      if (state is DashboardLoaded) {
        final currentState = state as DashboardLoaded;
        emit(
          DashboardLoaded(
            dashboard: currentState.dashboard,
            isSyncing: true,
            lastSyncTime: currentState.lastSyncTime,
          ),
        );
      }

      // Trigger sync
      final syncResult = await syncDashboardUseCase(_currentCustomerId!);

      syncResult.fold(
        (failure) {
          logger.e('Sync failed: ${failure.toString()}');

          // Update state to show sync error but keep existing data
          if (state is DashboardLoaded) {
            final currentState = state as DashboardLoaded;
            emit(
              DashboardLoaded(
                dashboard: currentState.dashboard,
                isSyncing: false,
                lastSyncTime: currentState.lastSyncTime,
                hasError: true,
                errorMessage: 'Sync failed: try again later',
              ),
            );
          }
        },
        (success) {
          logger.i('Sync triggered successfully');
          // The actual state update will happen via the sync status stream
        },
      );
    } catch (e, stackTrace) {
      logger.e('Error during sync: $e\nStackTrace: $stackTrace');
      // Update state to show error but keep existing data
      if (state is DashboardLoaded) {
        final currentState = state as DashboardLoaded;
        emit(
          DashboardLoaded(
            dashboard: currentState.dashboard,
            isSyncing: false,
            lastSyncTime: currentState.lastSyncTime,
            hasError: true,
            errorMessage: 'Sync error: $e',
          ),
        );
      }
    }
  }

  /// Refresh dashboard with minimal UI disruption
  Future<void> refreshDashboard() async {
    if (_currentCustomerId == null) {
      logger.w('Cannot refresh: No customer ID set');
      return;
    }

    try {
      // Check connectivity before proceeding with sync
      final isConnected = await _checkConnectivity();

      // Don't show loading state during refresh to prevent flickering
      // Just update the sync indicator if we're online
      if (isConnected && state is DashboardLoaded) {
        final currentState = state as DashboardLoaded;
        if (!currentState.isSyncing) {
          emit(currentState.copyWith(isSyncing: true));
        }

        // Trigger sync in background
        syncDashboardUseCase(_currentCustomerId!).then((result) {
          result.fold(
            (failure) {
              logger.e('Background sync failed: ${failure.toString()}');
              // State will be updated via sync status stream
            },
            (success) {
              logger.i('Background sync triggered successfully');
              // State will be updated via sync status stream
            },
          );
        });
      } else {
        // If offline, just reload from cache without showing loading state
        await loadDashboardData();
      }
    } catch (e, stackTrace) {
      logger.e(
        'Error refreshing dashboard: ${e.toString()}, stackTrace: ${stackTrace.toString()}',
      );
      // Don't change UI state for background refresh errors
    }
  }

  /// Enables auto-refresh of dashboard data at specified interval
  void enableAutoRefresh({Duration interval = const Duration(minutes: 15)}) {
    // Cancel any existing timer
    _autoRefreshTimer?.cancel();

    // Set up new timer
    _autoRefreshTimer = Timer.periodic(interval, (_) {
      if (_currentCustomerId != null) {
        logger.i('Auto-refreshing dashboard data');
        refreshDashboard();
      }
    });

    logger.i(
      'Auto-refresh enabled with interval: ${interval.inMinutes} minutes',
    );
  }

  /// Disables auto-refresh of dashboard data
  void disableAutoRefresh() {
    _autoRefreshTimer?.cancel();
    _autoRefreshTimer = null;
    logger.i('Auto-refresh disabled');
  }

  /// Dismisses any current error message
  void dismissError() {
    if (state is DashboardLoaded && (state as DashboardLoaded).hasError) {
      final currentState = state as DashboardLoaded;
      emit(
        DashboardLoaded(
          dashboard: currentState.dashboard,
          isSyncing: currentState.isSyncing,
          lastSyncTime: currentState.lastSyncTime,
          hasError: false,
        ),
      );
    }
  }

  /// Handles sync status updates from the stream
  void _handleSyncStatusUpdate(SyncStatus status) {
    // Only process updates for the current customer
    if (status.customerId != _currentCustomerId) return;

    logger.i('Sync status update: ${status.status} - ${status.message}');

    if (state is DashboardLoaded) {
      final currentState = state as DashboardLoaded;

      switch (status.status) {
        case SyncStatusType.inProgress:
          emit(
            DashboardLoaded(
              dashboard: currentState.dashboard,
              isSyncing: true,
              lastSyncTime: currentState.lastSyncTime,
              hasError: false,
            ),
          );
          break;

        case SyncStatusType.completed:
          // When sync completes, reload the data to get the latest version
          // but preserve the current UI state to prevent flickering
          _loadLatestDataWithoutFlickering();
          break;

        case SyncStatusType.failed:
          emit(
            DashboardLoaded(
              dashboard: currentState.dashboard,
              isSyncing: false,
              lastSyncTime: currentState.lastSyncTime,
              hasError: true,
              errorMessage: status.message ?? 'Sync failed',
            ),
          );
          break;

        default:
          // No action needed for other status types
          break;
      }
    }
  }

  /// Loads latest data without showing loading state
  Future<void> _loadLatestDataWithoutFlickering() async {
    if (_currentCustomerId == null) return;

    try {
      // Get dashboard data without changing UI state
      final dashboardResult = await getDashboardUseCase(_currentCustomerId!);

      dashboardResult.fold(
        (failure) {
          logger.e('Failed to load latest data: ${failure.toString()}');
          // If we fail to load latest data, just update sync status
          if (state is DashboardLoaded) {
            final currentState = state as DashboardLoaded;
            emit(currentState.copyWith(isSyncing: false));
          }
        },
        (dashboardData) async {
          // Get last sync time
          final lastSyncTime = await dashboardService.getLastSyncTime(
            _currentCustomerId!,
          );

          if (state is DashboardLoaded) {
            emit(
              DashboardLoaded(
                dashboard: dashboardData,
                isSyncing: false,
                lastSyncTime: lastSyncTime,
                hasError: false,
              ),
            );
          }
        },
      );
    } catch (e) {
      logger.e('Error loading latest data: $e');
      // Just update sync status on error
      if (state is DashboardLoaded) {
        final currentState = state as DashboardLoaded;
        emit(currentState.copyWith(isSyncing: false));
      }
    }
  }

  /// Checks if data is stale and needs refresh
  bool isDataStale() {
    if (state is DashboardLoaded) {
      final currentState = state as DashboardLoaded;
      final lastSync = currentState.lastSyncTime;

      if (lastSync == null) return true;

      // Consider data stale if last sync was more than 1 hour ago
      final now = DateTime.now();
      return now.difference(lastSync).inHours >= 1;
    }
    return true;
  }

  @override
  Future<void> close() {
    _syncStatusSubscription?.cancel();
    _autoRefreshTimer?.cancel();
    return super.close();
  }
}
