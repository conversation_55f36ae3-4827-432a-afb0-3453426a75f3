// lib/domain/repositories/customer_payments_repository.dart
import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../entities/payments/payments_summary.dart';
import '../entities/payments/customer_payment.dart';

abstract class CustomerPaymentsRepository {
  Future<Either<Failure, PaymentsSummary>> getCustomerPayments(
    String customerId,
  );
  Future<Either<Failure, void>> syncCustomerPayments(String customerId);
  Future<Either<Failure, List<CustomerPayment>>> getLocalCustomerPayments(
    String customerId,
  );
}
