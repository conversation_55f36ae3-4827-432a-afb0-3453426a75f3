import 'package:aquapartner/core/error/failures.dart';
import 'package:aquapartner/domain/repositories/auth_repository.dart';
import 'package:aquapartner/domain/usecases/auth_usecases.dart';
import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockAuthRepository extends Mock implements AuthRepository {}

void main() {
  group('Authentication Use Cases Tests', () {
    late MockAuthRepository mockAuthRepository;

    setUp(() {
      mockAuthRepository = MockAuthRepository();
    });

    group('SendOtpUseCase', () {
      late SendOtpUseCase sendOtpUseCase;

      setUp(() {
        sendOtpUseCase = SendOtpUseCase(mockAuthRepository);
      });

      test(
        'should return verification ID when OTP is sent successfully',
        () async {
          // Arrange
          const phoneNumber = '+919999999999';
          const verificationId = 'test_verification_id';
          when(
            () => mockAuthRepository.sendOtp(phoneNumber),
          ).thenAnswer((_) async => const Right(verificationId));

          // Act
          final result = await sendOtpUseCase(phoneNumber);

          // Assert
          expect(result, const Right(verificationId));
          verify(() => mockAuthRepository.sendOtp(phoneNumber)).called(1);
        },
      );

      test(
        'should return NetworkFailure when no internet connection',
        () async {
          // Arrange
          const phoneNumber = '+919999999999';
          when(
            () => mockAuthRepository.sendOtp(phoneNumber),
          ).thenAnswer((_) async => Left(NetworkFailure()));

          // Act
          final result = await sendOtpUseCase(phoneNumber);

          // Assert
          expect(result, Left(NetworkFailure()));
          verify(() => mockAuthRepository.sendOtp(phoneNumber)).called(1);
        },
      );

      test('should return ServerFailure when server error occurs', () async {
        // Arrange
        const phoneNumber = '+919999999999';
        when(
          () => mockAuthRepository.sendOtp(phoneNumber),
        ).thenAnswer((_) async => Left(ServerFailure()));

        // Act
        final result = await sendOtpUseCase(phoneNumber);

        // Assert
        expect(result, Left(ServerFailure()));
        verify(() => mockAuthRepository.sendOtp(phoneNumber)).called(1);
      });

      test(
        'should return ValidationFailure for invalid phone number',
        () async {
          // Arrange
          const phoneNumber = 'invalid_phone';
          when(() => mockAuthRepository.sendOtp(phoneNumber)).thenAnswer(
            (_) async => Left(ValidationFailure('Invalid phone number')),
          );

          // Act
          final result = await sendOtpUseCase(phoneNumber);

          // Assert
          expect(result, Left(ValidationFailure('Invalid phone number')));
          verify(() => mockAuthRepository.sendOtp(phoneNumber)).called(1);
        },
      );

      test('should return ValidationFailure when too many requests', () async {
        // Arrange
        const phoneNumber = '+919999999999';
        when(
          () => mockAuthRepository.sendOtp(phoneNumber),
        ).thenAnswer((_) async => Left(ValidationFailure('Too many requests')));

        // Act
        final result = await sendOtpUseCase(phoneNumber);

        // Assert
        expect(result, Left(ValidationFailure('Too many requests')));
        verify(() => mockAuthRepository.sendOtp(phoneNumber)).called(1);
      });
    });

    group('VerifyOtpUseCase', () {
      late VerifyOtpUseCase verifyOtpUseCase;

      setUp(() {
        verifyOtpUseCase = VerifyOtpUseCase(mockAuthRepository);
      });

      test('should return true when OTP is verified successfully', () async {
        // Arrange
        const verificationId = 'test_verification_id';
        const otp = '123456';
        when(
          () => mockAuthRepository.verifyOtp(verificationId, otp),
        ).thenAnswer((_) async => const Right(true));

        // Act
        final result = await verifyOtpUseCase(verificationId, otp);

        // Assert
        expect(result, const Right(true));
        verify(
          () => mockAuthRepository.verifyOtp(verificationId, otp),
        ).called(1);
      });

      test('should return ValidationFailure when OTP is invalid', () async {
        // Arrange
        const verificationId = 'test_verification_id';
        const otp = '000000';
        when(
          () => mockAuthRepository.verifyOtp(verificationId, otp),
        ).thenAnswer((_) async => Left(ValidationFailure('Invalid OTP')));

        // Act
        final result = await verifyOtpUseCase(verificationId, otp);

        // Assert
        expect(result, Left(ValidationFailure('Invalid OTP')));
        verify(
          () => mockAuthRepository.verifyOtp(verificationId, otp),
        ).called(1);
      });

      test('should return ValidationFailure when OTP is expired', () async {
        // Arrange
        const verificationId = 'test_verification_id';
        const otp = '123456';
        when(
          () => mockAuthRepository.verifyOtp(verificationId, otp),
        ).thenAnswer((_) async => Left(ValidationFailure('OTP expired')));

        // Act
        final result = await verifyOtpUseCase(verificationId, otp);

        // Assert
        expect(result, Left(ValidationFailure('OTP expired')));
        verify(
          () => mockAuthRepository.verifyOtp(verificationId, otp),
        ).called(1);
      });

      test(
        'should return NetworkFailure when no internet connection',
        () async {
          // Arrange
          const verificationId = 'test_verification_id';
          const otp = '123456';
          when(
            () => mockAuthRepository.verifyOtp(verificationId, otp),
          ).thenAnswer((_) async => Left(NetworkFailure()));

          // Act
          final result = await verifyOtpUseCase(verificationId, otp);

          // Assert
          expect(result, Left(NetworkFailure()));
          verify(
            () => mockAuthRepository.verifyOtp(verificationId, otp),
          ).called(1);
        },
      );

      test(
        'should return ValidationFailure when verification ID is invalid',
        () async {
          // Arrange
          const verificationId = 'invalid_verification_id';
          const otp = '123456';
          when(
            () => mockAuthRepository.verifyOtp(verificationId, otp),
          ).thenAnswer(
            (_) async => Left(ValidationFailure('Invalid verification ID')),
          );

          // Act
          final result = await verifyOtpUseCase(verificationId, otp);

          // Assert
          expect(result, Left(ValidationFailure('Invalid verification ID')));
          verify(
            () => mockAuthRepository.verifyOtp(verificationId, otp),
          ).called(1);
        },
      );
    });

    group('SignOutUseCase', () {
      late SignOutUseCase signOutUseCase;

      setUp(() {
        signOutUseCase = SignOutUseCase(mockAuthRepository);
      });

      test('should complete successfully when sign out succeeds', () async {
        // Arrange
        when(() => mockAuthRepository.signOut()).thenAnswer((_) async {});

        // Act
        await signOutUseCase();

        // Assert
        verify(() => mockAuthRepository.signOut()).called(1);
      });

      test('should handle sign out failure gracefully', () async {
        // Arrange
        when(
          () => mockAuthRepository.signOut(),
        ).thenThrow(Exception('Sign out failed'));

        // Act & Assert
        expect(() => signOutUseCase(), throwsException);
        verify(() => mockAuthRepository.signOut()).called(1);
      });

      test('should handle network error during sign out', () async {
        // Arrange
        when(() => mockAuthRepository.signOut()).thenThrow(NetworkFailure());

        // Act & Assert
        expect(() => signOutUseCase(), throwsA(isA<NetworkFailure>()));
        verify(() => mockAuthRepository.signOut()).called(1);
      });
    });
  });
}
