import 'package:aqua_ui/aqua_ui.dart';
import 'package:aquapartner/core/utils/currency_formatter.dart';
import 'package:aquapartner/presentation/cubit/customer/customer_cubit.dart';
import 'package:aquapartner/presentation/widgets/styled_generic_table.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:provider/provider.dart';

import '../../core/constants/app_constants.dart';
import '../../core/mixins/analytics_mixin.dart';
import '../../domain/entities/sales_order/sales_order.dart';
import '../../domain/entities/sales_order/sales_order_item.dart';

class SalesOrderDetailsScreen extends StatefulWidget {
  final SalesOrder salesOrder;
  const SalesOrderDetailsScreen({super.key, required this.salesOrder});

  @override
  State<SalesOrderDetailsScreen> createState() =>
      _SalesOrderDetailsScreenState();
}

class _SalesOrderDetailsScreenState extends State<SalesOrderDetailsScreen>
    with AnalyticsMixin<SalesOrderDetailsScreen> {
  CustomerLoaded? customerState;

  @override
  String get screenName => 'sales_order_details';

  @override
  String get parentScreenName => 'billing_and_payments';

  @override
  void initState() {
    final currentState = context.read<CustomerCubit>().state;
    if (currentState is CustomerLoaded) {
      customerState = currentState;
    }
    super.initState();

    // Track sales order details view
    trackEvent(
      'sales_order_details_viewed',
      params: {
        'order_number': widget.salesOrder.salesOrderNumber,
        'order_total': widget.salesOrder.total.toString(),
        'order_date': widget.salesOrder.saleOrderDate.toString(),
        'order_status': widget.salesOrder.invoicedStatus,
        'item_count': widget.salesOrder.items.length.toString(),
        'customer_name': customerState?.customer.companyName ?? 'Unknown',
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.white,
          statusBarIconBrightness: Brightness.dark,
        ),
        backgroundColor: acWhiteColor,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          color: acTextSecondaryColor,
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: AquaText.headline(
          '#${widget.salesOrder.salesOrderNumber}',
          weight: AquaFontWeight.semibold,
          color: acTextSecondaryColor,
        ),
        centerTitle: false,
      ),
      // Use a Stack to position the floating button at the bottom
      body: Container(
        color: acWhiteColor,
        height: double.infinity,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SvgPicture.asset(
                  AppConstants.imgAquaconnectLogo,
                  height: 35,
                  colorFilter: ColorFilter.mode(acPrimaryBlue, BlendMode.srcIn),
                ),
                SizedBox(height: 16),
                AquaText.subheadline(
                  'Coastal Aquaculture Research Institute Private Limited',
                  weight: AquaFontWeight.bold,
                ),
                SizedBox(height: 8),
                AquaText.body(
                  'Type II/17, Dr.VSI Estate, Thiruvanmiyur, Chennai, Tamilnadu - 600041',
                ),
                SizedBox(height: 8),
                AquaText.body(widget.salesOrder.saleOrderDate),
                SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: AquaText.caption(widget.salesOrder.invoicedStatus),
                ),
                SizedBox(height: 16),
                AquaText.subheadline(
                  "DELIVERY ADDRESS:",
                  weight: AquaFontWeight.bold,
                ),
                SizedBox(height: 16),
                AquaText.subheadline(
                  customerState?.customer.companyName ?? 'Unknown Company',
                  color: acTextSecondaryColor,
                ),
                SizedBox(height: 8),
                AquaText.subheadline(
                  customerState?.customer.billingAddress ?? 'Unknown Address',
                  color: acTextSecondaryColor,
                ),
                StyledGenericTable<SalesOrderItem>(
                  items: widget.salesOrder.items,
                  showDividers: true,
                  columns: [
                    ColumnConfig<SalesOrderItem>(
                      title: 'Item',
                      width: 160,
                      cellBuilder:
                          (orderItem) => AquaText.body(
                            orderItem.itemName,
                            weight: AquaFontWeight.bold,
                          ),
                    ),
                    ColumnConfig<SalesOrderItem>(
                      title: 'Item Price',
                      width: 100,
                      cellBuilder:
                          (orderItem) => AquaText.body(
                            CurrencyFormatter.formatAsINR(orderItem.itemPrice),
                          ),
                    ),
                    ColumnConfig<SalesOrderItem>(
                      title: 'Discount',
                      width: 90,
                      cellBuilder:
                          (orderItem) =>
                              AquaText.body(orderItem.entityDiscountPercent),
                    ),
                    ColumnConfig<SalesOrderItem>(
                      title: 'Sub Total',
                      width: 120,
                      cellBuilder:
                          (orderItem) => AquaText.body(
                            CurrencyFormatter.formatAsINR(orderItem.total),
                          ),
                    ),
                    ColumnConfig<SalesOrderItem>(
                      title: 'Total',
                      width: 120,
                      cellBuilder:
                          (orderItem) => AquaText.body(
                            CurrencyFormatter.formatAsINR(
                              CurrencyFormatter.calculateDiscountedPrice(
                                subTotal: orderItem.total,
                                discount: orderItem.entityDiscountPercent,
                              ),
                            ),
                          ),
                    ),
                  ],
                ),
                SizedBox(height: 16),
                _buildPriceRow(
                  isBold: true,
                  label: "PRICE",
                  value: CurrencyFormatter.formatAsINR(
                    widget.salesOrder.subTotal,
                  ),
                ),
                SizedBox(height: 16),
                _buildPriceRow(
                  isBold: false,
                  label: "TAX RATE",
                  value:
                      "${CurrencyFormatter.calculateOffer(subTotal: widget.salesOrder.total, total: widget.salesOrder.subTotal)}%",
                ),
                SizedBox(height: 16),
                _buildPriceRow(
                  isBold: false,
                  label: "DISCOUNT",
                  value:
                      "${CurrencyFormatter.calculateOffer(subTotal: widget.salesOrder.subTotal, total: widget.salesOrder.total).toStringAsFixed(2)}",
                ),
                SizedBox(height: 16),
                _buildPriceRow(
                  isBold: true,
                  label: "TOTAL",
                  value: CurrencyFormatter.formatAsINR(widget.salesOrder.total),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPriceRow({
    required String label,
    required String value,
    required bool isBold,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        AquaText.subheadline(
          label,
          weight: isBold ? AquaFontWeight.bold : AquaFontWeight.regular,
        ),
        AquaText.subheadline(
          value,
          weight: isBold ? AquaFontWeight.bold : AquaFontWeight.regular,
        ),
      ],
    );
  }
}
