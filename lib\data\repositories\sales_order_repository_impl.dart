import 'package:dartz/dartz.dart';

import '../../core/error/exceptions.dart';
import '../../core/error/failures.dart';
import '../../core/network/network_info.dart';
import '../../core/utils/logger.dart';
import '../../domain/entities/sales_order/sales_order.dart';
import '../../domain/repositories/sales_order_repository.dart';
import '../datasources/local/sales_order_local_datasource.dart';
import '../datasources/remote/sales_order_remote_datasource.dart';

class SalesOrderRepositoryImpl implements SalesOrderRepository {
  final SalesOrderRemoteDataSource remoteDataSource;
  final SalesOrderLocalDataSource localDataSource;
  final NetworkInfo networkInfo;
  final AppLogger logger;

  SalesOrderRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
    required this.networkInfo,
    required this.logger,
  });

  @override
  Future<Either<Failure, List<SalesOrder>>> getSalesOrders(
    String customerId,
  ) async {
    try {
      logger.i('Getting sales orders for customer: $customerId');

      // Try to get data from local cache first (offline-first approach)
      try {
        final localSalesOrders = await localDataSource.getSalesOrders(
          customerId,
        );
        logger.i(
          'Retrieved ${localSalesOrders.length} sales orders from local cache',
        );
        return Right(
          localSalesOrders.map((model) => model.toEntity()).toList(),
        );
      } on CacheException {
        logger.w('No sales orders found in local cache, checking network');

        // If no local data, check if we're online and fetch from remote
        if (await networkInfo.isConnected) {
          return await syncSalesOrders(customerId);
        } else {
          logger.e('Device is offline and no local data available');
          return Left(CacheFailure());
        }
      }
    } catch (e) {
      logger.e('Error getting sales orders: $e');
      return Left(ServerFailure());
    }
  }

  @override
  Future<Either<Failure, List<SalesOrder>>> syncSalesOrders(
    String customerId,
  ) async {
    try {
      logger.i('Syncing sales orders for customer: $customerId');

      if (await networkInfo.isConnected) {
        try {
          // Fetch from remote
          final remoteSalesOrders = await remoteDataSource.getSalesOrders(
            customerId,
          );
          logger.i(
            'Retrieved ${remoteSalesOrders.length} sales orders from remote',
          );

          // Cache the data locally
          await localDataSource.cacheSalesOrders(remoteSalesOrders, customerId);
          logger.i('Cached sales orders locally');

          // Update last sync time
          await localDataSource.updateLastSyncTime(customerId);

          return Right(
            remoteSalesOrders.map((model) => model.toEntity()).toList(),
          );
        } on ServerException {
          logger.e('Server exception while syncing sales orders');
          return Left(ServerFailure());
        }
      } else {
        logger.w('Device is offline, cannot sync sales orders');
        return Left(NetworkFailure());
      }
    } catch (e) {
      logger.e('Error syncing sales orders: $e');
      return Left(ServerFailure());
    }
  }

  @override
  Future<Either<Failure, bool>> checkIfSyncNeeded(String customerId) async {
    try {
      logger.i(
        'Checking if sales orders sync is needed for customer: $customerId',
      );
      final syncNeeded = await localDataSource.checkIfSyncNeeded(customerId);
      return Right(syncNeeded);
    } catch (e) {
      logger.e('Error checking if sync is needed: $e');
      return Left(CacheFailure());
    }
  }
}
