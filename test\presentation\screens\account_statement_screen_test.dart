import 'package:aquapartner/presentation/cubit/account_statement/account_statement_cubit.dart';
import 'package:aquapartner/presentation/cubit/account_statement/account_statement_state.dart'
    as account_state;
import 'package:aquapartner/presentation/screens/account_statement_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../helpers/test_helpers.dart';

// Mock classes
class MockAccountStatementCubit extends Mock implements AccountStatementCubit {}

void main() {
  group('AccountStatementScreen Tests', () {
    late MockAccountStatementCubit mockAccountStatementCubit;

    setUp(() {
      mockAccountStatementCubit = MockAccountStatementCubit();

      // Set up default state
      when(
        () => mockAccountStatementCubit.state,
      ).thenReturn(account_state.AccountStatementInitial());
      when(() => mockAccountStatementCubit.stream).thenAnswer(
        (_) => Stream.value(account_state.AccountStatementInitial()),
      );
      when(
        () => mockAccountStatementCubit.loadAccountStatement(),
      ).thenAnswer((_) async {});

      // Set up GetIt services for testing
      TestHelpers.setupGetItServices();
    });

    tearDown(() {
      // Clean up GetIt services after testing
      TestHelpers.cleanupGetItServices();
    });

    Widget createAccountStatementScreen() {
      return TestHelpers.createTestApp(
        home: BlocProvider<AccountStatementCubit>.value(
          value: mockAccountStatementCubit,
          child: AccountStatementScreen(),
        ),
      );
    }

    group('Widget Rendering', () {
      testWidgets(
        'should render account statement screen with main components',
        (tester) async {
          // Set up loaded state
          final loadedState = account_state.AccountStatementLoaded(
            customer: TestHelpers.createTestCustomer(),
            statement: TestHelpers.createTestAccountStatement(),
            isSyncing: false,
            selectedFilter: 'FY',
            periods: ['FY', 'Q1', 'Q2'],
            dateRange: account_state.DateTimeRange(
              startDate: DateTime(2024, 1, 1),
              endDate: DateTime(2024, 12, 31),
            ),
          );

          when(() => mockAccountStatementCubit.state).thenReturn(loadedState);
          when(
            () => mockAccountStatementCubit.stream,
          ).thenAnswer((_) => Stream.value(loadedState));

          await TestHelpers.pumpAndSettle(
            tester,
            createAccountStatementScreen(),
          );

          // Verify main structure
          expect(find.byType(Scaffold), findsOneWidget);
          expect(find.byType(AccountStatementScreen), findsOneWidget);
        },
      );

      testWidgets('should display account statement data when loaded', (
        tester,
      ) async {
        // Set up loaded state
        final loadedState = account_state.AccountStatementLoaded(
          customer: TestHelpers.createTestCustomer(),
          statement: TestHelpers.createTestAccountStatement(),
          isSyncing: false,
          selectedFilter: 'FY',
          periods: ['FY', 'Q1', 'Q2'],
          dateRange: account_state.DateTimeRange(
            startDate: DateTime(2024, 1, 1),
            endDate: DateTime(2024, 12, 31),
          ),
        );

        when(() => mockAccountStatementCubit.state).thenReturn(loadedState);
        when(
          () => mockAccountStatementCubit.stream,
        ).thenAnswer((_) => Stream.value(loadedState));

        await TestHelpers.pumpAndSettle(tester, createAccountStatementScreen());

        // Verify account statement content is displayed
        expect(find.byType(AccountStatementScreen), findsOneWidget);
      });

      testWidgets('should show loading state', (tester) async {
        // Set loading state
        final initialState = account_state.AccountStatementInitial();
        when(() => mockAccountStatementCubit.state).thenReturn(initialState);
        when(
          () => mockAccountStatementCubit.stream,
        ).thenAnswer((_) => Stream.fromIterable([initialState]));

        // Mock the loadAccountStatement method to prevent it from changing state
        when(
          () => mockAccountStatementCubit.loadAccountStatement(),
        ).thenAnswer((_) async {});

        await tester.pumpWidget(createAccountStatementScreen());
        await tester.pump(); // Just pump once instead of pumpAndSettle

        // Verify loading indicator
        expect(find.byType(CircularProgressIndicator), findsOneWidget);
      });

      testWidgets('should show error state', (tester) async {
        // Set error state
        final errorState = account_state.AccountStatementError(
          'Failed to load account statement',
        );
        when(() => mockAccountStatementCubit.state).thenReturn(errorState);
        when(
          () => mockAccountStatementCubit.stream,
        ).thenAnswer((_) => Stream.value(errorState));

        await TestHelpers.pumpAndSettle(tester, createAccountStatementScreen());

        // Verify error is displayed (AquaText.subheading renders RichText)
        // Use a custom finder for RichText content
        final errorTextFinder = find.byWidgetPredicate((widget) {
          if (widget is RichText) {
            final textSpan = widget.text;
            if (textSpan is TextSpan && textSpan.text != null) {
              return textSpan.text!.contains(
                'Failed to load account statement',
              );
            }
          }
          return false;
        });

        final tryAgainFinder = find.byWidgetPredicate((widget) {
          if (widget is RichText) {
            final textSpan = widget.text;
            if (textSpan is TextSpan && textSpan.text != null) {
              return textSpan.text! == 'Try Again';
            }
          }
          return false;
        });

        expect(errorTextFinder, findsOneWidget);
        expect(tryAgainFinder, findsOneWidget);
      });

      testWidgets('should show empty state when no transactions', (
        tester,
      ) async {
        // Set empty state
        final emptyState = account_state.AccountStatementLoaded(
          customer: TestHelpers.createTestCustomer(),
          statement: TestHelpers.createEmptyAccountStatement(),
          isSyncing: false,
          selectedFilter: 'FY',
          periods: ['FY', 'Q1', 'Q2'],
          dateRange: account_state.DateTimeRange(
            startDate: DateTime(2024, 1, 1),
            endDate: DateTime(2024, 12, 31),
          ),
        );

        when(() => mockAccountStatementCubit.state).thenReturn(emptyState);
        when(
          () => mockAccountStatementCubit.stream,
        ).thenAnswer((_) => Stream.value(emptyState));

        await TestHelpers.pumpAndSettle(tester, createAccountStatementScreen());

        // Verify empty state is displayed (this will depend on the actual screen implementation)
        expect(find.byType(AccountStatementScreen), findsOneWidget);
      });
    });

    group('Data Display', () {
      testWidgets('should display transaction details correctly', (
        tester,
      ) async {
        // Set up loaded state with data
        final loadedState = account_state.AccountStatementLoaded(
          customer: TestHelpers.createTestCustomer(),
          statement: TestHelpers.createTestAccountStatement(),
          isSyncing: false,
          selectedFilter: 'FY',
          periods: ['FY', 'Q1', 'Q2'],
          dateRange: account_state.DateTimeRange(
            startDate: DateTime(2024, 1, 1),
            endDate: DateTime(2024, 12, 31),
          ),
        );

        when(() => mockAccountStatementCubit.state).thenReturn(loadedState);
        when(
          () => mockAccountStatementCubit.stream,
        ).thenAnswer((_) => Stream.value(loadedState));

        await TestHelpers.pumpAndSettle(tester, createAccountStatementScreen());

        // Verify transaction data is displayed
        expect(find.byType(AccountStatementScreen), findsOneWidget);
      });

      testWidgets('should display balance information', (tester) async {
        // Set up loaded state with data
        final loadedState = account_state.AccountStatementLoaded(
          customer: TestHelpers.createTestCustomer(),
          statement: TestHelpers.createTestAccountStatement(),
          isSyncing: false,
          selectedFilter: 'FY',
          periods: ['FY', 'Q1', 'Q2'],
          dateRange: account_state.DateTimeRange(
            startDate: DateTime(2024, 1, 1),
            endDate: DateTime(2024, 12, 31),
          ),
        );

        when(() => mockAccountStatementCubit.state).thenReturn(loadedState);
        when(
          () => mockAccountStatementCubit.stream,
        ).thenAnswer((_) => Stream.value(loadedState));

        await TestHelpers.pumpAndSettle(tester, createAccountStatementScreen());

        // Verify balance information is shown
        expect(find.byType(AccountStatementScreen), findsOneWidget);
      });

      testWidgets('should format dates correctly', (tester) async {
        // Set up loaded state with data
        final loadedState = account_state.AccountStatementLoaded(
          customer: TestHelpers.createTestCustomer(),
          statement: TestHelpers.createTestAccountStatement(),
          isSyncing: false,
          selectedFilter: 'FY',
          periods: ['FY', 'Q1', 'Q2'],
          dateRange: account_state.DateTimeRange(
            startDate: DateTime(2024, 1, 1),
            endDate: DateTime(2024, 12, 31),
          ),
        );

        when(() => mockAccountStatementCubit.state).thenReturn(loadedState);
        when(
          () => mockAccountStatementCubit.stream,
        ).thenAnswer((_) => Stream.value(loadedState));

        await TestHelpers.pumpAndSettle(tester, createAccountStatementScreen());

        // Verify date formatting
        expect(find.byType(AccountStatementScreen), findsOneWidget);
      });
    });

    group('User Interactions', () {
      testWidgets('should handle transaction item taps', (tester) async {
        // Set up loaded state with data
        final loadedState = account_state.AccountStatementLoaded(
          customer: TestHelpers.createTestCustomer(),
          statement: TestHelpers.createTestAccountStatement(),
          isSyncing: false,
          selectedFilter: 'FY',
          periods: ['FY', 'Q1', 'Q2'],
          dateRange: account_state.DateTimeRange(
            startDate: DateTime(2024, 1, 1),
            endDate: DateTime(2024, 12, 31),
          ),
        );

        when(() => mockAccountStatementCubit.state).thenReturn(loadedState);
        when(
          () => mockAccountStatementCubit.stream,
        ).thenAnswer((_) => Stream.value(loadedState));

        await TestHelpers.pumpAndSettle(tester, createAccountStatementScreen());

        // Verify screen is displayed
        expect(find.byType(AccountStatementScreen), findsOneWidget);
      });

      testWidgets('should handle filter actions', (tester) async {
        // Set up loaded state with data
        final loadedState = account_state.AccountStatementLoaded(
          customer: TestHelpers.createTestCustomer(),
          statement: TestHelpers.createTestAccountStatement(),
          isSyncing: false,
          selectedFilter: 'FY',
          periods: ['FY', 'Q1', 'Q2'],
          dateRange: account_state.DateTimeRange(
            startDate: DateTime(2024, 1, 1),
            endDate: DateTime(2024, 12, 31),
          ),
        );

        when(() => mockAccountStatementCubit.state).thenReturn(loadedState);
        when(
          () => mockAccountStatementCubit.stream,
        ).thenAnswer((_) => Stream.value(loadedState));

        await TestHelpers.pumpAndSettle(tester, createAccountStatementScreen());

        // Verify screen is displayed
        expect(find.byType(AccountStatementScreen), findsOneWidget);
      });

      testWidgets('should handle export actions', (tester) async {
        // Set up loaded state with data
        final loadedState = account_state.AccountStatementLoaded(
          customer: TestHelpers.createTestCustomer(),
          statement: TestHelpers.createTestAccountStatement(),
          isSyncing: false,
          selectedFilter: 'FY',
          periods: ['FY', 'Q1', 'Q2'],
          dateRange: account_state.DateTimeRange(
            startDate: DateTime(2024, 1, 1),
            endDate: DateTime(2024, 12, 31),
          ),
        );

        when(() => mockAccountStatementCubit.state).thenReturn(loadedState);
        when(
          () => mockAccountStatementCubit.stream,
        ).thenAnswer((_) => Stream.value(loadedState));

        await TestHelpers.pumpAndSettle(tester, createAccountStatementScreen());

        // Verify screen is displayed
        expect(find.byType(AccountStatementScreen), findsOneWidget);
      });
    });

    group('State Management', () {
      testWidgets('should respond to account statement state changes', (
        tester,
      ) async {
        // Set initial state
        final loadedState = account_state.AccountStatementLoaded(
          customer: TestHelpers.createTestCustomer(),
          statement: TestHelpers.createTestAccountStatement(),
          isSyncing: false,
          selectedFilter: 'FY',
          periods: ['FY', 'Q1', 'Q2'],
          dateRange: account_state.DateTimeRange(
            startDate: DateTime(2024, 1, 1),
            endDate: DateTime(2024, 12, 31),
          ),
        );

        when(() => mockAccountStatementCubit.state).thenReturn(loadedState);
        when(
          () => mockAccountStatementCubit.stream,
        ).thenAnswer((_) => Stream.value(loadedState));

        await TestHelpers.pumpAndSettle(tester, createAccountStatementScreen());

        // Verify state change is reflected
        expect(find.byType(AccountStatementScreen), findsOneWidget);
      });

      testWidgets('should handle data refresh', (tester) async {
        // Set initial state
        final loadedState = account_state.AccountStatementLoaded(
          customer: TestHelpers.createTestCustomer(),
          statement: TestHelpers.createTestAccountStatement(),
          isSyncing: false,
          selectedFilter: 'FY',
          periods: ['FY', 'Q1', 'Q2'],
          dateRange: account_state.DateTimeRange(
            startDate: DateTime(2024, 1, 1),
            endDate: DateTime(2024, 12, 31),
          ),
        );

        when(() => mockAccountStatementCubit.state).thenReturn(loadedState);
        when(
          () => mockAccountStatementCubit.stream,
        ).thenAnswer((_) => Stream.value(loadedState));

        await TestHelpers.pumpAndSettle(tester, createAccountStatementScreen());

        // Trigger refresh action
        final refreshButtons = find.byIcon(Icons.refresh);
        if (refreshButtons.evaluate().isNotEmpty) {
          await tester.tap(refreshButtons.first);
          await tester.pumpAndSettle();

          // Verify refresh was triggered
          verify(
            () => mockAccountStatementCubit.loadAccountStatement(),
          ).called(1);
        }
      });
    });

    group('Analytics Tracking', () {
      testWidgets('should track screen view on load', (tester) async {
        // Set up loaded state with data
        final loadedState = account_state.AccountStatementLoaded(
          customer: TestHelpers.createTestCustomer(),
          statement: TestHelpers.createTestAccountStatement(),
          isSyncing: false,
          selectedFilter: 'FY',
          periods: ['FY', 'Q1', 'Q2'],
          dateRange: account_state.DateTimeRange(
            startDate: DateTime(2024, 1, 1),
            endDate: DateTime(2024, 12, 31),
          ),
        );

        when(() => mockAccountStatementCubit.state).thenReturn(loadedState);
        when(
          () => mockAccountStatementCubit.stream,
        ).thenAnswer((_) => Stream.value(loadedState));

        await TestHelpers.pumpAndSettle(tester, createAccountStatementScreen());

        // Verify screen view tracking
        expect(find.byType(AccountStatementScreen), findsOneWidget);
      });

      testWidgets('should track transaction interactions', (tester) async {
        // Set up loaded state with data
        final loadedState = account_state.AccountStatementLoaded(
          customer: TestHelpers.createTestCustomer(),
          statement: TestHelpers.createTestAccountStatement(),
          isSyncing: false,
          selectedFilter: 'FY',
          periods: ['FY', 'Q1', 'Q2'],
          dateRange: account_state.DateTimeRange(
            startDate: DateTime(2024, 1, 1),
            endDate: DateTime(2024, 12, 31),
          ),
        );

        when(() => mockAccountStatementCubit.state).thenReturn(loadedState);
        when(
          () => mockAccountStatementCubit.stream,
        ).thenAnswer((_) => Stream.value(loadedState));

        await TestHelpers.pumpAndSettle(tester, createAccountStatementScreen());

        // Verify interaction tracking
        expect(find.byType(AccountStatementScreen), findsOneWidget);
      });

      testWidgets('should track filter usage', (tester) async {
        // Set up loaded state with data
        final loadedState = account_state.AccountStatementLoaded(
          customer: TestHelpers.createTestCustomer(),
          statement: TestHelpers.createTestAccountStatement(),
          isSyncing: false,
          selectedFilter: 'FY',
          periods: ['FY', 'Q1', 'Q2'],
          dateRange: account_state.DateTimeRange(
            startDate: DateTime(2024, 1, 1),
            endDate: DateTime(2024, 12, 31),
          ),
        );

        when(() => mockAccountStatementCubit.state).thenReturn(loadedState);
        when(
          () => mockAccountStatementCubit.stream,
        ).thenAnswer((_) => Stream.value(loadedState));

        await TestHelpers.pumpAndSettle(tester, createAccountStatementScreen());

        // Verify filter usage tracking
        expect(find.byType(AccountStatementScreen), findsOneWidget);
      });
    });

    group('Error Handling', () {
      testWidgets('should handle network errors gracefully', (tester) async {
        // Set network error state
        final errorState = account_state.AccountStatementError(
          'Network connection failed',
        );
        when(() => mockAccountStatementCubit.state).thenReturn(errorState);
        when(
          () => mockAccountStatementCubit.stream,
        ).thenAnswer((_) => Stream.value(errorState));

        await TestHelpers.pumpAndSettle(tester, createAccountStatementScreen());

        // Verify error is handled gracefully
        final networkErrorFinder = find.byWidgetPredicate((widget) {
          if (widget is RichText) {
            final textSpan = widget.text;
            if (textSpan is TextSpan && textSpan.text != null) {
              return textSpan.text!.contains('Network connection failed');
            }
          }
          return false;
        });

        final tryAgainFinder = find.byWidgetPredicate((widget) {
          if (widget is RichText) {
            final textSpan = widget.text;
            if (textSpan is TextSpan && textSpan.text != null) {
              return textSpan.text! == 'Try Again';
            }
          }
          return false;
        });

        expect(networkErrorFinder, findsOneWidget);
        expect(tryAgainFinder, findsOneWidget);
      });

      testWidgets('should handle data parsing errors', (tester) async {
        // Set data error state
        final errorState = account_state.AccountStatementError(
          'Invalid data format',
        );
        when(() => mockAccountStatementCubit.state).thenReturn(errorState);
        when(
          () => mockAccountStatementCubit.stream,
        ).thenAnswer((_) => Stream.value(errorState));

        await TestHelpers.pumpAndSettle(tester, createAccountStatementScreen());

        // Verify error is handled
        final dataErrorFinder = find.byWidgetPredicate((widget) {
          if (widget is RichText) {
            final textSpan = widget.text;
            if (textSpan is TextSpan && textSpan.text != null) {
              return textSpan.text!.contains('Invalid data format');
            }
          }
          return false;
        });

        expect(dataErrorFinder, findsOneWidget);
      });
    });

    group('Accessibility', () {
      testWidgets('should have proper accessibility labels', (tester) async {
        // Set up loaded state with data
        final loadedState = account_state.AccountStatementLoaded(
          customer: TestHelpers.createTestCustomer(),
          statement: TestHelpers.createTestAccountStatement(),
          isSyncing: false,
          selectedFilter: 'FY',
          periods: ['FY', 'Q1', 'Q2'],
          dateRange: account_state.DateTimeRange(
            startDate: DateTime(2024, 1, 1),
            endDate: DateTime(2024, 12, 31),
          ),
        );

        when(() => mockAccountStatementCubit.state).thenReturn(loadedState);
        when(
          () => mockAccountStatementCubit.stream,
        ).thenAnswer((_) => Stream.value(loadedState));

        await TestHelpers.pumpAndSettle(tester, createAccountStatementScreen());

        // Verify semantic labels exist
        expect(find.byType(Semantics), findsAtLeastNWidgets(1));
      });

      testWidgets('should support screen readers', (tester) async {
        // Set up loaded state with data
        final loadedState = account_state.AccountStatementLoaded(
          customer: TestHelpers.createTestCustomer(),
          statement: TestHelpers.createTestAccountStatement(),
          isSyncing: false,
          selectedFilter: 'FY',
          periods: ['FY', 'Q1', 'Q2'],
          dateRange: account_state.DateTimeRange(
            startDate: DateTime(2024, 1, 1),
            endDate: DateTime(2024, 12, 31),
          ),
        );

        when(() => mockAccountStatementCubit.state).thenReturn(loadedState);
        when(
          () => mockAccountStatementCubit.stream,
        ).thenAnswer((_) => Stream.value(loadedState));

        await TestHelpers.pumpAndSettle(tester, createAccountStatementScreen());

        // Verify screen reader support
        final semanticsFinder = find.byType(Semantics);
        expect(semanticsFinder, findsAtLeastNWidgets(1));
      });
    });

    group('Performance', () {
      testWidgets('should handle large transaction lists efficiently', (
        tester,
      ) async {
        // Set large data state
        final largeDataState = account_state.AccountStatementLoaded(
          customer: TestHelpers.createTestCustomer(),
          statement: TestHelpers.createLargeAccountStatement(),
          isSyncing: false,
          selectedFilter: 'FY',
          periods: ['FY', 'Q1', 'Q2'],
          dateRange: account_state.DateTimeRange(
            startDate: DateTime(2024, 1, 1),
            endDate: DateTime(2024, 12, 31),
          ),
        );

        when(() => mockAccountStatementCubit.state).thenReturn(largeDataState);
        when(
          () => mockAccountStatementCubit.stream,
        ).thenAnswer((_) => Stream.value(largeDataState));

        await TestHelpers.pumpAndSettle(tester, createAccountStatementScreen());

        // Verify large list is handled efficiently
        expect(find.byType(AccountStatementScreen), findsOneWidget);
      });
    });
  });
}
