import 'dart:async';
import 'package:mongo_dart/mongo_dart.dart';
import '../config/mongodb_config.dart';
import '../error/exceptions.dart';
import '../network/network_info.dart';
import '../utils/logger.dart';

class MongoDBService {
  Db? _db;
  DbCollection? _userCollection;
  DbCollection? _productCatalogCollection;
  DbCollection? _priceListCollection;
  DbCollection? _interestedProductsCollection;

  bool _isConnected = false;
  bool _isConnecting = false;
  DateTime? _lastConnectionAttempt;
  int _connectionAttempts = 0;

  // Connection settings
  static const int maxReconnectAttempts = 5;
  static const Duration minReconnectDelay = Duration(seconds: 1);
  static const Duration maxReconnectDelay = Duration(seconds: 30);
  static const Duration connectionTimeout = Duration(seconds: 15);
  static const Duration connectionThrottleWindow = Duration(seconds: 5);

  final NetworkInfo networkInfo;
  final AppLogger logger;

  MongoDBService({required this.networkInfo, required this.logger});

  Future<void> connect() async {
    // Prevent multiple simultaneous connection attempts
    if (_isConnecting) {
      logger.i("Connection attempt already in progress, waiting...");
      // Wait for the current connection attempt to complete
      for (int i = 0; i < 10; i++) {
        await Future.delayed(Duration(milliseconds: 500));
        if (_isConnected || !_isConnecting) break;
      }
      if (_isConnected) return;
    }

    // Prevent connection throttling (too many attempts in a short time)
    if (_lastConnectionAttempt != null) {
      final timeSinceLastAttempt = DateTime.now().difference(
        _lastConnectionAttempt!,
      );
      if (timeSinceLastAttempt < connectionThrottleWindow) {
        logger.w("Connection attempts throttled, waiting before retry");
        await Future.delayed(connectionThrottleWindow - timeSinceLastAttempt);
      }
    }

    // If already connected, just return
    if (_isConnected && _db != null && _db!.isConnected) {
      logger.i("Already connected to MongoDB");
      return;
    }

    _isConnecting = true;
    _lastConnectionAttempt = DateTime.now();
    _connectionAttempts++;

    // Check network connectivity first
    if (!await networkInfo.isConnected) {
      logger.w("Cannot connect to MongoDB: No internet connection");
      _isConnecting = false;
      return; // Don't throw, just return - this allows offline operation
    }

    try {
      logger.i("Connecting to MongoDB (attempt $_connectionAttempts)...");

      // Close any existing connection first to avoid socket leaks
      await _safeCloseConnection();

      // Create a new connection with timeout
      _db = await Db.create(MongoDBConfig.connectionString);

      // No need to set additional options - the connection string already includes the database

      // Open the connection with timeout
      await _db?.open().timeout(
        connectionTimeout,
        onTimeout: () {
          throw TimeoutException(
            "Connection timed out after ${connectionTimeout.inSeconds} seconds",
          );
        },
      );

      // Initialize collections
      _userCollection = _db?.collection(MongoDBConfig.userCollection);
      _productCatalogCollection = _db?.collection(
        MongoDBConfig.productCatalogueCollection,
      );
      _priceListCollection = _db?.collection(MongoDBConfig.priceListCollection);
      _interestedProductsCollection = _db?.collection(
        MongoDBConfig.interestedProductsCollection,
      );

      // Verify connection by checking the state
      if (_db != null && _db!.isConnected) {
        // Connection is established, no need for additional verification
        // The isConnected property already tells us the socket is open
        logger.i("MongoDB connection established successfully");
      } else {
        throw Exception("MongoDB connection failed to open properly");
      }

      _isConnected = true;
      _connectionAttempts = 0; // Reset counter on successful connection
      logger.i("Successfully connected to MongoDB");
    } catch (e) {
      _isConnected = false;

      // Check for specific error types
      String errorMessage = e.toString();
      if (errorMessage.contains("not authorized") ||
          errorMessage.contains("not allowed") ||
          errorMessage.contains("Authentication failed")) {
        logger.e("MongoDB authentication error: $errorMessage");
        // This is a credentials issue - we should notify the user
        // But still allow offline operation
      } else if (errorMessage.contains("SocketException") ||
          errorMessage.contains("connection failed") ||
          errorMessage.contains("timed out")) {
        logger.e("MongoDB connection error: $errorMessage");
        // This is a network issue - we should retry later
      } else {
        logger.e("Failed to connect to MongoDB: $errorMessage");
      }

      // Don't throw, just log - this allows offline operation
      // But close any partially open connection
      await _safeCloseConnection();
    } finally {
      _isConnecting = false;
    }
  }

  Future<void> disconnect() async {
    try {
      await _safeCloseConnection();
      _isConnected = false;
      logger.i("Disconnected from MongoDB");
    } catch (e) {
      logger.e("Error disconnecting from MongoDB", e);
      // Don't throw, just log the error
    }
  }

  // Safely close the connection without throwing exceptions
  Future<void> _safeCloseConnection() async {
    try {
      if (_db != null) {
        if (_db!.isConnected) {
          await _db!.close().timeout(
            Duration(seconds: 5),
            onTimeout: () {
              logger.w("Timeout while closing MongoDB connection");
              return;
            },
          );
        }
        _db = null;
      }
    } catch (e) {
      logger.w(
        "Error while safely closing MongoDB connection: ${e.toString()}",
      );
      // Don't rethrow, we're just cleaning up
    }
  }

  Future<void> ensureConnected() async {
    if (!_isConnected || _db == null || !_db!.isConnected) {
      // Calculate exponential backoff delay based on connection attempts
      if (_connectionAttempts > 0) {
        final backoffMs =
            minReconnectDelay.inMilliseconds *
            (1 << (_connectionAttempts - 1).clamp(0, 10)); // Prevent overflow
        final delayMs = backoffMs.clamp(
          minReconnectDelay.inMilliseconds,
          maxReconnectDelay.inMilliseconds,
        );

        logger.i(
          "Reconnection attempt $_connectionAttempts, waiting ${delayMs}ms before retry",
        );
        await Future.delayed(Duration(milliseconds: delayMs));
      }

      await connect();

      // If still not connected after max attempts, throw an exception
      if (!_isConnected && _connectionAttempts >= maxReconnectAttempts) {
        logger.e(
          "Failed to connect to MongoDB after $_connectionAttempts attempts",
        );
        throw Exception("Failed to connect to MongoDB after multiple attempts");
      }
    }
  }

  // Helper method to check collection and connection status
  Future<DbCollection> _getCollection(
    DbCollection? collection,
    String collectionName,
  ) async {
    // Check if we need to reconnect
    if (!_isConnected ||
        _db == null ||
        !_db!.isConnected ||
        collection == null) {
      logger.w(
        "MongoDB connection lost or collection $collectionName not initialized, attempting to reconnect",
      );
      try {
        await ensureConnected();

        // Re-initialize the specific collection if needed
        if (collection == null) {
          switch (collectionName) {
            case 'user':
              _userCollection = _db?.collection(MongoDBConfig.userCollection);
              collection = _userCollection;
              break;
            case 'priceList':
              _priceListCollection = _db?.collection(
                MongoDBConfig.priceListCollection,
              );
              collection = _priceListCollection;
              break;
            case 'productCatalogue':
              _productCatalogCollection = _db?.collection(
                MongoDBConfig.productCatalogueCollection,
              );
              collection = _productCatalogCollection;
              break;
            case 'interestedProducts':
              _interestedProductsCollection = _db?.collection(
                MongoDBConfig.interestedProductsCollection,
              );
              collection = _interestedProductsCollection;
              break;
          }
        }
      } catch (e) {
        logger.e(
          "Failed to reconnect to MongoDB for collection $collectionName: $e",
        );
        throw DatabaseException();
      }
    }

    if (collection == null) {
      logger.e("Collection $collectionName is null after reconnection attempt");
      throw DatabaseException();
    }

    return collection;
  }

  Future<DbCollection> get userCollection async =>
      await _getCollection(_userCollection, 'user');

  Future<DbCollection> get priceListCollection async =>
      await _getCollection(_priceListCollection, 'priceList');

  Future<DbCollection> get productCatalogueCollection async =>
      await _getCollection(_productCatalogCollection, 'productCatalogue');

  Future<DbCollection> get interestedProductsCollection async =>
      await _getCollection(_interestedProductsCollection, 'interestedProducts');

  // Check if currently connected
  bool get isConnected => _db != null && _db!.isConnected && _isConnected;

  // Check if connection attempt is in progress
  bool get isConnecting => _isConnecting;
}
