import 'package:shared_preferences/shared_preferences.dart';
import '../../../core/error/exceptions.dart';
import '../../../core/utils/logger.dart';
import '../../../objectbox.g.dart';
import '../../models/account_statement/account_statement_models.dart';

abstract class AccountStatementLocalDataSource {
  /// Get account statement from local storage
  Future<AccountStatementModel?> getAccountStatement(String customerId);

  /// Cache account statement in local storage
  Future<void> cacheAccountStatement(AccountStatementModel statement);
  
  /// Cache an empty statement for a customer with no data
  Future<void> cacheEmptyStatement(String customerId);

  /// Clear account statement for a specific customer
  Future<void> clearAccountStatement(String customerId);

  /// Get last sync time for account statement
  Future<DateTime?> getLastSyncTime(String customerId);

  /// Save last sync time for account statement
  Future<void> saveLastSyncTime(String customerId, DateTime time);
}

class AccountStatementLocalDataSourceImpl
    implements AccountStatementLocalDataSource {
  final Box<AccountStatementModel> accountStatementBox;
  final SharedPreferences sharedPreferences;
  final AppLogger logger;

  // Key prefix for storing sync timestamps in SharedPreferences
  static const String _lastSyncTimeKeyPrefix = 'account_statement_last_sync_';

  AccountStatementLocalDataSourceImpl({
    required this.accountStatementBox,
    required this.sharedPreferences,
    required this.logger,
  });

  @override
  Future<AccountStatementModel?> getAccountStatement(String customerId) async {
    try {
      logger.i(
        'Getting account statement from local storage for customer: $customerId',
      );

      final query =
          accountStatementBox
              .query(AccountStatementModel_.customerId.equals(customerId))
              .build();

      final result = query.findFirst();
      query.close();

      if (result != null) {
        logger.i(
          'Found account statement in local storage with ${result.entries.length} entries',
        );
      } else {
        logger.i('No account statement found in local storage');
      }

      return result;
    } catch (e) {
      logger.e(
        'Error getting account statement from local storage: ${e.toString()}',
      );
      throw CacheException();
    }
  }

  @override
  Future<void> cacheAccountStatement(AccountStatementModel statement) async {
    try {
      logger.i(
        'Caching account statement for customer: ${statement.customerId} with ${statement.entries.length} entries',
      );

      // First clear any existing data
      await clearAccountStatement(statement.customerId);

      // Then save the new data
      accountStatementBox.put(statement);

      // Update last sync time
      await saveLastSyncTime(statement.customerId, statement.lastSyncTime);

      logger.i('Account statement cached successfully');
    } catch (e) {
      logger.e('Error caching account statement: ${e.toString()}');
      throw CacheException();
    }
  }
  
  @override
  Future<void> cacheEmptyStatement(String customerId) async {
    try {
      logger.i('Caching empty account statement for customer: $customerId');
      
      // Create an empty statement model
      final emptyStatement = AccountStatementModel(
        customerId: customerId,
        lastSyncTime: DateTime.now(),
      );
      // Note: entries is a ToMany relationship that's initialized empty by default
      // so we don't need to pass it to the constructor
      
      // Cache it
      await cacheAccountStatement(emptyStatement);
      
      logger.i('Empty account statement cached successfully');
    } catch (e) {
      logger.e('Error caching empty account statement: ${e.toString()}');
      throw CacheException();
    }
  }

  @override
  Future<void> clearAccountStatement(String customerId) async {
    try {
      logger.i('Clearing account statement for customer: $customerId');

      final query =
          accountStatementBox
              .query(AccountStatementModel_.customerId.equals(customerId))
              .build();

      final entities = query.find();
      query.close();

      for (final entity in entities) {
        accountStatementBox.remove(entity.id);
      }

      logger.i('Account statement cleared successfully');
    } catch (e) {
      logger.e('Error clearing account statement: ${e.toString()}');
      throw CacheException();
    }
  }

  @override
  Future<DateTime?> getLastSyncTime(String customerId) async {
    try {
      final key = _lastSyncTimeKeyPrefix + customerId;
      final timestamp = sharedPreferences.getInt(key);

      if (timestamp != null) {
        return DateTime.fromMillisecondsSinceEpoch(timestamp);
      }

      return null;
    } catch (e) {
      logger.e('Error getting last sync time: ${e.toString()}');
      return null;
    }
  }

  @override
  Future<void> saveLastSyncTime(String customerId, DateTime time) async {
    try {
      final key = _lastSyncTimeKeyPrefix + customerId;
      await sharedPreferences.setInt(key, time.millisecondsSinceEpoch);
    } catch (e) {
      logger.e('Error saving last sync time: ${e.toString()}');
    }
  }
}