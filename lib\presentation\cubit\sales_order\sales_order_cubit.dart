import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/utils/logger.dart';
import '../../../domain/services/auth_service.dart';
import '../../../domain/usecases/sales_order/check_if_sync_needed_usecase.dart';
import '../../../domain/usecases/sales_order/get_sales_orders_usecase.dart';
import '../../../domain/usecases/sales_order/sync_sales_orders_usecase.dart';
import 'sales_order_state.dart';

class SalesOrderCubit extends Cubit<SalesOrderState> {
  final GetSalesOrdersUseCase getSalesOrdersUseCase;
  final SyncSalesOrdersUseCase syncSalesOrdersUseCase;
  final CheckIfSalesOrdersSyncNeededUseCase checkIfSyncNeededUseCase;
  final AuthService authService;
  final AppLogger logger;

  bool _isBackgroundSyncing = false;

  SalesOrderCubit({
    required this.getSalesOrdersUseCase,
    required this.syncSalesOrdersUseCase,
    required this.checkIfSyncNeededUseCase,
    required this.authService,
    required this.logger,
  }) : super(const SalesOrderInitial());

  /// Load sales orders for the current customer
  /// Implements a caching-first approach - returns cached data immediately if available
  /// and triggers a background sync if needed
  Future<void> loadSalesOrders() async {
    try {
      emit(const SalesOrderLoading());

      // Get the current customer ID from auth service
      final customerIdResult = await authService.getCurrentCustomer();
      final customer = customerIdResult.fold(
        (failure) => throw Exception('Failed to get customer ID'),
        (customer) => customer,
      );

      logger.i('Loading sales orders for customer: ${customer!.customerId}');

      // CACHING-FIRST APPROACH:
      // 1. Always try to get data from local cache first
      final result = await getSalesOrdersUseCase(customer.customerId);

      result.fold(
        (failure) {
          logger.e('Error loading sales orders from cache: $failure');

          // If cache fetch fails, try to sync from remote
          emit(
            SalesOrderError(
              'Failed to load sales orders: ${failure.toString()}',
            ),
          );

          // Try to sync if local fetch fails
          syncSalesOrders();
        },
        (salesOrders) {
          logger.i(
            'Successfully loaded ${salesOrders.length} sales orders from cache',
          );

          // 2. Emit loaded state with cached data immediately
          emit(
            SalesOrdersLoaded(
              salesOrders,
              isFromCache: true,
              isCacheStale: false, // We don't know yet, will update if needed
              isBackgroundSyncInProgress: false,
            ),
          );

          // 3. Check if sync is needed in the background
          _checkAndSyncIfNeeded(customer.customerId);
        },
      );
    } catch (e) {
      logger.e('Exception in loadSalesOrders: $e');
      emit(SalesOrderError('An unexpected error occurred: ${e.toString()}'));
    }
  }

  /// Check if sync is needed and perform background sync if necessary
  Future<void> _checkAndSyncIfNeeded(String customerId) async {
    try {
      // Check if sync is needed
      final syncNeededResult = await checkIfSyncNeededUseCase(customerId);
      final syncNeeded = syncNeededResult.fold((failure) {
        logger.w('Error checking if sync needed: $failure');
        return false; // Don't sync on error
      }, (needed) => needed);

      if (syncNeeded && !_isBackgroundSyncing) {
        logger.i('Cache is stale, performing background sync');

        // Update state to indicate cache is stale and background sync is in progress
        if (state is SalesOrdersLoaded) {
          emit(
            (state as SalesOrdersLoaded).copyWith(
              isCacheStale: true,
              isBackgroundSyncInProgress: true,
            ),
          );
        }

        // Perform background sync
        _isBackgroundSyncing = true;
        await _backgroundSync(customerId);
        _isBackgroundSyncing = false;

        // Update state to indicate background sync is complete
        if (state is SalesOrdersLoaded) {
          emit(
            (state as SalesOrdersLoaded).copyWith(
              isBackgroundSyncInProgress: false,
            ),
          );
        }
      }
    } catch (e) {
      logger.e('Error in _checkAndSyncIfNeeded: $e');
      _isBackgroundSyncing = false;
    }
  }

  /// Perform a background sync without changing the UI state
  Future<void> _backgroundSync(String customerId) async {
    try {
      logger.i('Performing background sync for customer: $customerId');

      // Sync sales orders
      final result = await syncSalesOrdersUseCase(customerId);

      result.fold(
        (failure) {
          logger.e('Error in background sync: $failure');
          // Don't emit error state as this is a background operation
        },
        (salesOrders) {
          logger.i(
            'Background sync completed successfully with ${salesOrders.length} sales orders',
          );

          // Update the state with new data but keep isFromCache=false since it's from remote
          if (state is SalesOrdersLoaded) {
            emit(
              SalesOrdersLoaded(
                salesOrders,
                isFromCache: false,
                isCacheStale: false,
                isBackgroundSyncInProgress: false,
              ),
            );
          }
        },
      );
    } catch (e) {
      logger.e('Error in background sync: $e');
      // Don't emit error state as this is a background operation
    }
  }

  /// Sync sales orders for the current customer
  /// This is a manual sync that shows loading indicators
  Future<void> syncSalesOrders() async {
    try {
      emit(const SalesOrderSyncing());

      // Get the current customer ID from auth service
      final customerIdResult = await authService.getCurrentCustomer();
      final customer = customerIdResult.fold(
        (failure) => throw Exception('Failed to get customer ID'),
        (customer) => customer,
      );

      logger.i('Syncing sales orders for customer: ${customer!.customerId}');

      // Sync sales orders
      final result = await syncSalesOrdersUseCase(customer.customerId);
      result.fold(
        (failure) {
          logger.e('Error syncing sales orders: $failure');
          emit(
            SalesOrderSyncError(
              'Failed to sync sales orders: ${failure.toString()}',
            ),
          );

          // Try to load from cache if sync fails
          _loadFromCacheAfterSyncFailure(customer.customerId);
        },
        (salesOrders) {
          logger.i(
            'Successfully synced ${salesOrders.length} sales orders with ${salesOrders.fold(0, (sum, order) => sum + order.items.length)} items',
          );

          // Show success message
          emit(SalesOrderSyncSuccess(salesOrders));

          // Update the state with new data
          emit(
            SalesOrdersLoaded(
              salesOrders,
              isFromCache: false,
              isCacheStale: false,
              isBackgroundSyncInProgress: false,
            ),
          );
        },
      );
    } catch (e) {
      logger.e('Exception in syncSalesOrders: $e');
      emit(
        SalesOrderSyncError('An unexpected error occurred: ${e.toString()}'),
      );
    }
  }

  /// Load from cache after sync failure
  Future<void> _loadFromCacheAfterSyncFailure(String customerId) async {
    try {
      final result = await getSalesOrdersUseCase(customerId);
      result.fold(
        (failure) {
          logger.e('Error loading from cache after sync failure: $failure');
          // Already in error state, no need to emit again
        },
        (salesOrders) {
          logger.i(
            'Loaded ${salesOrders.length} sales orders from cache after sync failure',
          );
          emit(
            SalesOrdersLoaded(
              salesOrders,
              isFromCache: true,
              isCacheStale: true, // Mark as stale since sync failed
              isBackgroundSyncInProgress: false,
            ),
          );
        },
      );
    } catch (e) {
      logger.e('Exception loading from cache after sync failure: $e');
      // Already in error state, no need to emit again
    }
  }

  /// Force invalidate the cache and trigger a sync
  Future<void> invalidateAndSync() async {
    try {
      // Get the current customer ID from auth service
      final customerIdResult = await authService.getCurrentCustomer();
      final customer = customerIdResult.fold(
        (failure) => throw Exception('Failed to get customer ID'),
        (customer) => customer,
      );

      logger.i(
        'Invalidating cache and syncing for customer: ${customer!.customerId}',
      );

      // Mark cache as stale if we have loaded data
      if (state is SalesOrdersLoaded) {
        emit((state as SalesOrdersLoaded).copyWith(isCacheStale: true));
      }

      // Perform a full sync
      await syncSalesOrders();
    } catch (e) {
      logger.e('Error invalidating cache: $e');
      emit(SalesOrderError('Failed to invalidate cache: ${e.toString()}'));
    }
  }
}
