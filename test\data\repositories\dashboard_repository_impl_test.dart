import 'dart:async';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:dartz/dartz.dart';
import 'package:aquapartner/data/repositories/dashboard_repository_impl.dart';
import 'package:aquapartner/data/datasources/local/dashboard_local_datasource.dart';
import 'package:aquapartner/data/datasources/remote/dashboard_remote_datasource.dart';
import 'package:aquapartner/core/network/network_info.dart';
import 'package:aquapartner/core/utils/logger.dart';
import 'package:aquapartner/core/error/failures.dart';
import 'package:aquapartner/core/error/exceptions.dart';
import 'package:aquapartner/domain/entities/dashboard/dashboard_entity.dart';
import 'package:aquapartner/domain/entities/sync_status.dart';
import 'package:aquapartner/data/models/dashboard/dashboard_model.dart';

// Mock classes
class MockDashboardRemoteDataSource extends Mock
    implements DashboardRemoteDataSource {}

class MockDashboardLocalDataSource extends Mock
    implements DashboardLocalDataSource {}

class MockNetworkInfo extends Mock implements NetworkInfo {}

class MockAppLogger extends Mock implements AppLogger {}

void main() {
  group('DashboardRepositoryImpl Tests', () {
    late DashboardRepositoryImpl repository;
    late MockDashboardRemoteDataSource mockRemoteDataSource;
    late MockDashboardLocalDataSource mockLocalDataSource;
    late MockNetworkInfo mockNetworkInfo;
    late MockAppLogger mockLogger;

    late DashboardModel testDashboardModel;
    const testCustomerId = 'customer_123';

    setUpAll(() {
      // Register fallback values for mocktail
      registerFallbackValue(
        DashboardModel(
          id: 0,
          customerId: 'test',
          salesJson: '{}',
          paymentsJson: '{}',
          duesJson: '[]',
          salesReturnJson: 0.0,
          categoryTypeSalesJson: '[]',
          liquidationJson: '{}',
          myFarmersJson: '{}',
          isSynced: false,
          lastSyncedAt: DateTime.now(),
        ),
      );
    });

    setUp(() {
      mockRemoteDataSource = MockDashboardRemoteDataSource();
      mockLocalDataSource = MockDashboardLocalDataSource();
      mockNetworkInfo = MockNetworkInfo();
      mockLogger = MockAppLogger();

      repository = DashboardRepositoryImpl(
        remoteDataSource: mockRemoteDataSource,
        localDataSource: mockLocalDataSource,
        networkInfo: mockNetworkInfo,
        logger: mockLogger,
        dataFreshnessThreshold: const Duration(seconds: 1),
        maxRetries: 2,
        initialRetryDelay: const Duration(milliseconds: 100),
      );

      testDashboardModel = DashboardModel(
        id: 1,
        customerId: testCustomerId,
        salesJson:
            '{"2024": [{"month": "January", "totalSales": 10000.0, "weeks": []}]}',
        paymentsJson:
            '{"2024": [{"month": "January", "totalPayments": 8000.0, "weeks": []}]}',
        duesJson: '[{"ageTier": "0-30 days", "totalAmount": 2000.0}]',
        salesReturnJson: 500.0,
        categoryTypeSalesJson: '[{"categoryType": "Feed", "yearlySales": {}}]',
        liquidationJson: '{"totalLiquidation": 1500.0, "details": []}',
        myFarmersJson: '{"totalFarmers": 25, "activeFarmers": 20}',
        isSynced: true,
        lastSyncedAt: DateTime.now(),
      );

      // testDashboardEntity = testDashboardModel.toEntity(); // Not used in tests
    });

    group('getDashboard Tests', () {
      test('should return dashboard from local cache when available', () async {
        // Arrange
        when(
          () => mockLocalDataSource.getDashboard(testCustomerId),
        ).thenAnswer((_) async => testDashboardModel);

        // Act
        final result = await repository.getDashboard(testCustomerId);

        // Assert
        expect(result, isA<Right<Failure, DashboardEntity>>());
        result.fold(
          (failure) => fail('Expected Right but got Left: $failure'),
          (entity) {
            expect(entity.customerId, equals(testCustomerId));
            expect(entity.salesReturn, equals(500.0));
          },
        );
        verify(
          () => mockLocalDataSource.getDashboard(testCustomerId),
        ).called(1);
        verify(
          () => mockLogger.i(
            'Serving dashboard from local cache for $testCustomerId',
          ),
        ).called(1);
      });

      test(
        'should fetch from remote when no local data and network available',
        () async {
          // Arrange
          when(
            () => mockLocalDataSource.getDashboard(testCustomerId),
          ).thenThrow(CacheException());
          when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
          when(
            () => mockRemoteDataSource.getDashboard(testCustomerId),
          ).thenAnswer((_) async => testDashboardModel);
          when(
            () => mockLocalDataSource.cacheDashboard(any()),
          ).thenAnswer((_) async {});
          when(
            () => mockLocalDataSource.updateLastSyncTime(testCustomerId, any()),
          ).thenAnswer((_) async {});

          // Act
          final result = await repository.getDashboard(testCustomerId);

          // Assert
          expect(result, isA<Right<Failure, DashboardEntity>>());
          verify(
            () => mockLocalDataSource.getDashboard(testCustomerId),
          ).called(1);
          verify(() => mockNetworkInfo.isConnected).called(1);
          verify(
            () => mockRemoteDataSource.getDashboard(testCustomerId),
          ).called(1);
          verify(() => mockLocalDataSource.cacheDashboard(any())).called(1);
          verify(
            () => mockLocalDataSource.updateLastSyncTime(testCustomerId, any()),
          ).called(1);
          verify(
            () => mockLogger.i('No local data for $testCustomerId'),
          ).called(1);
        },
      );

      test(
        'should return CacheFailure when no local data and no network',
        () async {
          // Arrange
          when(
            () => mockLocalDataSource.getDashboard(testCustomerId),
          ).thenThrow(CacheException());
          when(
            () => mockNetworkInfo.isConnected,
          ).thenAnswer((_) async => false);

          // Act
          final result = await repository.getDashboard(testCustomerId);

          // Assert
          expect(result, equals(Left(CacheFailure())));
          verify(
            () => mockLocalDataSource.getDashboard(testCustomerId),
          ).called(1);
          verify(() => mockNetworkInfo.isConnected).called(1);
          verify(
            () => mockLogger.w(
              'No network connection and no local data for $testCustomerId',
            ),
          ).called(1);
          verifyNever(() => mockRemoteDataSource.getDashboard(any()));
        },
      );

      test('should return ServerFailure when remote fetch fails', () async {
        // Arrange
        when(
          () => mockLocalDataSource.getDashboard(testCustomerId),
        ).thenThrow(CacheException());
        when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        when(
          () => mockRemoteDataSource.getDashboard(testCustomerId),
        ).thenThrow(ServerException());

        // Act
        final result = await repository.getDashboard(testCustomerId);

        // Assert
        expect(result, equals(Left(ServerFailure())));
        // The implementation retries up to maxRetries (2) times, so expect 2 calls
        verify(
          () => mockRemoteDataSource.getDashboard(testCustomerId),
        ).called(2);
      });

      test('should trigger background sync when local data is stale', () async {
        // Arrange
        final staleModel = DashboardModel(
          id: testDashboardModel.id,
          customerId: testDashboardModel.customerId,
          salesJson: testDashboardModel.salesJson,
          paymentsJson: testDashboardModel.paymentsJson,
          duesJson: testDashboardModel.duesJson,
          salesReturnJson: testDashboardModel.salesReturnJson,
          categoryTypeSalesJson: testDashboardModel.categoryTypeSalesJson,
          liquidationJson: testDashboardModel.liquidationJson,
          myFarmersJson: testDashboardModel.myFarmersJson,
          isSynced: testDashboardModel.isSynced,
          lastSyncedAt: DateTime.now().subtract(const Duration(hours: 1)),
        );

        when(
          () => mockLocalDataSource.getDashboard(testCustomerId),
        ).thenAnswer((_) async => staleModel);
        when(
          () => mockLocalDataSource.getLastSyncTime(testCustomerId),
        ).thenAnswer(
          (_) async => DateTime.now().subtract(const Duration(hours: 1)),
        );
        when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);

        // Act
        final result = await repository.getDashboard(testCustomerId);

        // Assert
        expect(result, isA<Right<Failure, DashboardEntity>>());
        verify(
          () => mockLocalDataSource.getDashboard(testCustomerId),
        ).called(1);
        // Background sync should be triggered but not awaited
      });
    });

    group('syncDashboard Tests', () {
      test(
        'should sync dashboard successfully when network available',
        () async {
          // Arrange
          when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
          when(
            () => mockRemoteDataSource.getDashboard(testCustomerId),
          ).thenAnswer((_) async => testDashboardModel);
          when(
            () => mockLocalDataSource.clearDashboardForCustomer(testCustomerId),
          ).thenAnswer((_) async {});
          when(
            () => mockLocalDataSource.cacheDashboard(any()),
          ).thenAnswer((_) async {});
          when(
            () => mockLocalDataSource.updateLastSyncTime(testCustomerId, any()),
          ).thenAnswer((_) async {});

          // Act
          final result = await repository.syncDashboard(testCustomerId);

          // Assert
          expect(result, equals(const Right(true)));
          verify(() => mockNetworkInfo.isConnected).called(1);
          verify(
            () => mockRemoteDataSource.getDashboard(testCustomerId),
          ).called(1);
          verify(
            () => mockLocalDataSource.clearDashboardForCustomer(testCustomerId),
          ).called(1);
          verify(() => mockLocalDataSource.cacheDashboard(any())).called(1);
          verify(
            () => mockLocalDataSource.updateLastSyncTime(testCustomerId, any()),
          ).called(1);
          verify(
            () => mockLogger.i(
              'Starting explicit one-way sync for $testCustomerId...',
            ),
          ).called(1);
        },
      );

      test(
        'should return NetworkFailure when no internet connection',
        () async {
          // Arrange
          when(
            () => mockNetworkInfo.isConnected,
          ).thenAnswer((_) async => false);

          // Act
          final result = await repository.syncDashboard(testCustomerId);

          // Assert
          expect(result, equals(Left(NetworkFailure())));
          verify(() => mockNetworkInfo.isConnected).called(1);
          verify(
            () => mockLogger.w(
              'Explicit sync failed: No network connection for $testCustomerId',
            ),
          ).called(1);
          verifyNever(() => mockRemoteDataSource.getDashboard(any()));
        },
      );

      test('should return ServerFailure when remote sync fails', () async {
        // Arrange
        when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        when(
          () => mockRemoteDataSource.getDashboard(testCustomerId),
        ).thenThrow(ServerException());

        // Act
        final result = await repository.syncDashboard(testCustomerId);

        // Assert
        expect(result, equals(Left(ServerFailure())));
        // The implementation retries up to maxRetries (2) times, so expect 2 calls
        verify(
          () => mockRemoteDataSource.getDashboard(testCustomerId),
        ).called(2);
      });

      test(
        'should implement one-way synchronization with data clearing',
        () async {
          // Arrange
          when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
          when(
            () => mockRemoteDataSource.getDashboard(testCustomerId),
          ).thenAnswer((_) async => testDashboardModel);
          when(
            () => mockLocalDataSource.clearDashboardForCustomer(testCustomerId),
          ).thenAnswer((_) async {});
          when(
            () => mockLocalDataSource.cacheDashboard(any()),
          ).thenAnswer((_) async {});
          when(
            () => mockLocalDataSource.updateLastSyncTime(testCustomerId, any()),
          ).thenAnswer((_) async {});

          // Act
          final result = await repository.syncDashboard(testCustomerId);

          // Assert
          expect(result, equals(const Right(true)));

          // Verify one-way sync: clear local data first, then replace with remote data
          verifyInOrder([
            () => mockLocalDataSource.clearDashboardForCustomer(testCustomerId),
            () => mockLocalDataSource.cacheDashboard(any()),
          ]);
        },
      );

      test('should retry on transient failures', () async {
        // Arrange
        when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        // Mock the retry behavior: first call throws, second call succeeds
        var callCount = 0;
        when(
          () => mockRemoteDataSource.getDashboard(testCustomerId),
        ).thenAnswer((_) async {
          callCount++;
          if (callCount == 1) {
            throw ServerException();
          }
          return testDashboardModel;
        });
        when(
          () => mockLocalDataSource.clearDashboardForCustomer(testCustomerId),
        ).thenAnswer((_) async {});
        when(
          () => mockLocalDataSource.cacheDashboard(any()),
        ).thenAnswer((_) async {});
        when(
          () => mockLocalDataSource.updateLastSyncTime(testCustomerId, any()),
        ).thenAnswer((_) async {});

        // Act
        final result = await repository.syncDashboard(testCustomerId);

        // Assert
        expect(result, equals(const Right(true)));
        verify(
          () => mockRemoteDataSource.getDashboard(testCustomerId),
        ).called(2);
      });
    });

    group('getSyncStatus Tests', () {
      test('should return sync status stream', () {
        // Act
        final stream = repository.syncStatusStream;

        // Assert
        expect(stream, isA<Stream<SyncStatus>>());
      });

      test('should emit sync status updates during sync', () async {
        // Arrange
        when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        when(
          () => mockRemoteDataSource.getDashboard(testCustomerId),
        ).thenAnswer((_) async => testDashboardModel);
        when(
          () => mockLocalDataSource.clearDashboardForCustomer(testCustomerId),
        ).thenAnswer((_) async {});
        when(
          () => mockLocalDataSource.cacheDashboard(any()),
        ).thenAnswer((_) async {});
        when(
          () => mockLocalDataSource.updateLastSyncTime(testCustomerId, any()),
        ).thenAnswer((_) async {});

        // Act
        final streamValues = <SyncStatus>[];
        final subscription = repository.syncStatusStream.listen(
          streamValues.add,
        );

        await repository.syncDashboard(testCustomerId);
        await Future.delayed(const Duration(milliseconds: 100));

        subscription.cancel();

        // Assert
        expect(streamValues, isNotEmpty);
        expect(
          streamValues.any(
            (status) => status.status == SyncStatusType.inProgress,
          ),
          isTrue,
        );
      });
    });

    group('Edge Cases and Error Handling', () {
      test('should handle null dashboard from remote source', () async {
        // Arrange
        when(
          () => mockLocalDataSource.getDashboard(testCustomerId),
        ).thenThrow(CacheException());
        when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        when(
          () => mockRemoteDataSource.getDashboard(testCustomerId),
        ).thenAnswer((_) async => null);

        // Act
        final result = await repository.getDashboard(testCustomerId);

        // Assert
        expect(result, equals(Left(ServerFailure())));
      });

      test('should handle cache failures during sync', () async {
        // Arrange
        when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        when(
          () => mockRemoteDataSource.getDashboard(testCustomerId),
        ).thenAnswer((_) async => testDashboardModel);
        when(
          () => mockLocalDataSource.clearDashboardForCustomer(testCustomerId),
        ).thenThrow(CacheException());

        // Act
        final result = await repository.syncDashboard(testCustomerId);

        // Assert
        // The implementation wraps cache exceptions in UnexpectedFailure
        expect(result, isA<Left<Failure, bool>>());
        result.fold(
          (failure) => expect(failure, isA<UnexpectedFailure>()),
          (_) => fail('Expected Left but got Right'),
        );
        verify(
          () => mockLogger.e(
            any(
              that: contains(
                'Error fetching/caching remote data for $testCustomerId',
              ),
            ),
          ),
        ).called(1);
      });

      test('should handle concurrent sync operations', () async {
        // Arrange
        when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        when(
          () => mockRemoteDataSource.getDashboard(testCustomerId),
        ).thenAnswer((_) async => testDashboardModel);
        when(
          () => mockLocalDataSource.clearDashboardForCustomer(testCustomerId),
        ).thenAnswer((_) async {});
        when(
          () => mockLocalDataSource.cacheDashboard(any()),
        ).thenAnswer((_) async {});
        when(
          () => mockLocalDataSource.updateLastSyncTime(testCustomerId, any()),
        ).thenAnswer((_) async {});

        // Act
        final futures = [
          repository.syncDashboard(testCustomerId),
          repository.syncDashboard(testCustomerId),
        ];

        final results = await Future.wait(futures);

        // Assert
        expect(results[0], equals(const Right(true)));
        expect(
          results[1],
          equals(const Right(false)),
        ); // Second call should return false due to concurrency
      });

      test('should handle empty customer ID', () async {
        // Arrange
        const emptyCustomerId = '';
        when(
          () => mockLocalDataSource.getDashboard(emptyCustomerId),
        ).thenThrow(CacheException());

        // Act
        final result = await repository.getDashboard(emptyCustomerId);

        // Assert
        // The implementation wraps exceptions in UnexpectedFailure
        expect(result, isA<Left<Failure, DashboardEntity>>());
        result.fold(
          (failure) => expect(failure, isA<UnexpectedFailure>()),
          (_) => fail('Expected Left but got Right'),
        );
      });

      test('should handle very large dashboard data', () async {
        // Arrange
        final largeDashboardModel = DashboardModel(
          id: 1,
          customerId: testCustomerId,
          salesJson: '{"data": "${List.filled(10000, 'x').join()}"}',
          paymentsJson: '{"data": "${List.filled(10000, 'y').join()}"}',
          duesJson:
              '[${List.filled(1000, '{"ageTier": "test", "totalAmount": 100.0}').join(',')}]',
          salesReturnJson: 999999999.99,
          categoryTypeSalesJson:
              '[${List.filled(1000, '{"categoryType": "test", "yearlySales": {}}').join(',')}]',
          liquidationJson: '{"data": "${List.filled(10000, 'z').join()}"}',
          myFarmersJson: '{"data": "${List.filled(10000, 'a').join()}"}',
          isSynced: true,
          lastSyncedAt: DateTime.now(),
        );

        when(
          () => mockLocalDataSource.getDashboard(testCustomerId),
        ).thenAnswer((_) async => largeDashboardModel);

        // Act
        final result = await repository.getDashboard(testCustomerId);

        // Assert
        expect(result, isA<Right<Failure, DashboardEntity>>());
      });

      test(
        'should handle network connectivity changes during operations',
        () async {
          // Arrange
          when(
            () => mockLocalDataSource.getDashboard(testCustomerId),
          ).thenThrow(CacheException());
          when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
          when(
            () => mockNetworkInfo.isConnected,
          ).thenAnswer((_) async => false); // Network goes down

          // Act
          final result = await repository.getDashboard(testCustomerId);

          // Assert - Should still attempt the operation based on initial network check
          expect(result, isA<Left<Failure, DashboardEntity>>());
        },
      );

      test('should handle malformed JSON in dashboard model', () async {
        // Arrange
        final malformedDashboardModel = DashboardModel(
          id: 1,
          customerId: testCustomerId,
          salesJson: 'invalid json',
          paymentsJson: '{incomplete json',
          duesJson: '[invalid array',
          salesReturnJson: 500.0,
          categoryTypeSalesJson: 'not json',
          liquidationJson: '{broken: json}',
          myFarmersJson: 'invalid',
          isSynced: true,
          lastSyncedAt: DateTime.now(),
        );

        when(
          () => mockLocalDataSource.getDashboard(testCustomerId),
        ).thenAnswer((_) async => malformedDashboardModel);

        // Act & Assert - Should not throw exception during entity conversion
        expect(() => repository.getDashboard(testCustomerId), returnsNormally);
      });
    });
  });
}
