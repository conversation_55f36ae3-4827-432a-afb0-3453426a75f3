import 'package:flutter_test/flutter_test.dart';
import 'package:aquapartner/data/models/dues/dues_model.dart';
import 'package:aquapartner/domain/entities/dues/dues.dart';

void main() {
  group('DuesInvoiceModel Tests', () {
    late DuesInvoiceModel testDuesInvoiceModel;
    late Map<String, dynamic> testJson;

    setUp(() {
      testJson = {
        '_id': 'invoice_123',
        'invoiceId': 'inv_123',
        'invoiceNumber': 'INV-2024-001',
        'invoiceDate': '2024-01-15',
        'customerCode': 'CUST001',
        'customerName': 'Test Customer',
        'customerDistrict': 'Chennai',
        'customerState': 'Tamil Nadu',
        'customerType': 'Retailer',
        'onBoardedTime': '2023-01-01',
        'categoryType': 'Feed',
        'invoiceType': 'Sales',
        'retailType': 'Direct',
        'invoiceStatus': 'Pending',
        'invoiceRaisedBy': 'Sales Team',
        'mode': 'Online',
        'businessVertical': 'Aquaculture',
        'feedCreditLimit': '100000',
        'nonFeedCreditLimit': '50000',
        'harvestCreditLimit': '25000',
        'totalSalesInclTax': 11800.0,
        'totalSalesExclTax': 10000.0,
        'tcsAmount': 100.0,
        'tdsAmount': 200.0,
        'amountAfterTcs': 11700.0,
        'shippingCharges': 500.0,
        'feedAmountAfterTcs': '10000',
        'nonFeedAmountAfterTcs': 1700.0,
        'creditNoteAmountWithTcs': 0.0,
        'payableAmount': 11700.0,
        'paidAmount': 5000.0,
        'due': 6700.0,
        'dueDate': '2024-02-15',
        'dueDays': 30,
        'aging': '0-30 days',
        'aging1': 'Current',
        'paymentCredibility': 'Good',
        'totalPurchase': 50000.0,
        'totalSales': 100000.0,
        'salesTier': 'Tier 1',
        'healthcareSales': 5000.0,
        'feedSales': 80000.0,
        'chemicalSales': 10000.0,
        'equipmentSales': 3000.0,
        'harvestSales': 2000.0,
        'grossMargin': 15000.0,
        'lastPaidDate': {'date': '2024-01-10'},
      };

      testDuesInvoiceModel = DuesInvoiceModel.fromJson(
        testJson,
        '0-30 days',
        'customer_123',
      );
    });

    group('Constructor Tests', () {
      test('should create DuesInvoiceModel from JSON with all fields', () {
        expect(testDuesInvoiceModel.agingGroup, equals('0-30 days'));
        expect(testDuesInvoiceModel.customerId, equals('customer_123'));
        expect(testDuesInvoiceModel.id, equals('invoice_123'));
        expect(testDuesInvoiceModel.invoiceId, equals('inv_123'));
        expect(testDuesInvoiceModel.invoiceNumber, equals('INV-2024-001'));
        expect(testDuesInvoiceModel.invoiceDate, equals('2024-01-15'));
        expect(testDuesInvoiceModel.customerCode, equals('CUST001'));
        expect(testDuesInvoiceModel.customerName, equals('Test Customer'));
        expect(testDuesInvoiceModel.customerDistrict, equals('Chennai'));
        expect(testDuesInvoiceModel.customerState, equals('Tamil Nadu'));
        expect(testDuesInvoiceModel.customerType, equals('Retailer'));
        expect(testDuesInvoiceModel.onBoardedTime, equals('2023-01-01'));
        expect(testDuesInvoiceModel.categoryType, equals('Feed'));
        expect(testDuesInvoiceModel.invoiceType, equals('Sales'));
        expect(testDuesInvoiceModel.retailType, equals('Direct'));
        expect(testDuesInvoiceModel.invoiceStatus, equals('Pending'));
        expect(testDuesInvoiceModel.invoiceRaisedBy, equals('Sales Team'));
        expect(testDuesInvoiceModel.mode, equals('Online'));
        expect(testDuesInvoiceModel.businessVertical, equals('Aquaculture'));
        expect(testDuesInvoiceModel.feedCreditLimit, equals('100000'));
        expect(testDuesInvoiceModel.nonFeedCreditLimit, equals('50000'));
        expect(testDuesInvoiceModel.harvestCreditLimit, equals('25000'));
        expect(testDuesInvoiceModel.totalSalesInclTax, equals(11800.0));
        expect(testDuesInvoiceModel.totalSalesExclTax, equals(10000.0));
        expect(testDuesInvoiceModel.tcsAmount, equals(100.0));
        expect(testDuesInvoiceModel.tdsAmount, equals(200.0));
        expect(testDuesInvoiceModel.amountAfterTcs, equals(11700.0));
        expect(testDuesInvoiceModel.shippingCharges, equals(500.0));
        expect(testDuesInvoiceModel.feedAmountAfterTcs, equals('10000'));
        expect(testDuesInvoiceModel.nonFeedAmountAfterTcs, equals(1700.0));
        expect(testDuesInvoiceModel.creditNoteAmountWithTcs, equals(0.0));
        expect(testDuesInvoiceModel.payableAmount, equals(11700.0));
        expect(testDuesInvoiceModel.paidAmount, equals(5000.0));
        expect(testDuesInvoiceModel.due, equals(6700.0));
        expect(testDuesInvoiceModel.dueDate, equals('2024-02-15'));
        expect(testDuesInvoiceModel.dueDays, equals(30));
        expect(testDuesInvoiceModel.aging, equals('0-30 days'));
        expect(testDuesInvoiceModel.aging1, equals('Current'));
        expect(testDuesInvoiceModel.paymentCredibility, equals('Good'));
        expect(testDuesInvoiceModel.totalPurchase, equals(50000.0));
        expect(testDuesInvoiceModel.totalSales, equals(100000.0));
        expect(testDuesInvoiceModel.salesTier, equals('Tier 1'));
        expect(testDuesInvoiceModel.healthcareSales, equals(5000.0));
        expect(testDuesInvoiceModel.feedSales, equals(80000.0));
        expect(testDuesInvoiceModel.chemicalSales, equals(10000.0));
        expect(testDuesInvoiceModel.equipmentSales, equals(3000.0));
        expect(testDuesInvoiceModel.harvestSales, equals(2000.0));
        expect(testDuesInvoiceModel.grossMargin, equals(15000.0));
        expect(testDuesInvoiceModel.lastPaidDate, isNotNull);
        expect(testDuesInvoiceModel.lastSyncTimestamp, isA<int>());
      });

      test('should handle missing JSON fields with defaults', () {
        final incompleteJson = <String, dynamic>{
          '_id': 'invoice_123',
          'invoiceId': 'inv_123',
        };

        final duesInvoiceModel = DuesInvoiceModel.fromJson(
          incompleteJson,
          '0-30 days',
          'customer_123',
        );

        expect(duesInvoiceModel.id, equals('invoice_123'));
        expect(duesInvoiceModel.invoiceId, equals('inv_123'));
        expect(duesInvoiceModel.agingGroup, equals('0-30 days'));
        expect(duesInvoiceModel.customerId, equals('customer_123'));
        expect(duesInvoiceModel.invoiceNumber, isEmpty);
        expect(duesInvoiceModel.invoiceDate, isEmpty);
        expect(duesInvoiceModel.customerCode, isEmpty);
        expect(duesInvoiceModel.customerName, isEmpty);
        expect(duesInvoiceModel.totalSalesInclTax, equals(0.0));
        expect(duesInvoiceModel.totalSalesExclTax, equals(0.0));
        expect(duesInvoiceModel.tcsAmount, equals(0.0));
        expect(duesInvoiceModel.tdsAmount, equals(0.0));
        expect(duesInvoiceModel.amountAfterTcs, equals(0.0));
        expect(duesInvoiceModel.shippingCharges, equals(0.0));
        expect(duesInvoiceModel.feedAmountAfterTcs, isEmpty);
        expect(duesInvoiceModel.nonFeedAmountAfterTcs, equals(0.0));
        expect(duesInvoiceModel.creditNoteAmountWithTcs, equals(0.0));
        expect(duesInvoiceModel.payableAmount, equals(0.0));
        expect(duesInvoiceModel.paidAmount, equals(0.0));
        expect(duesInvoiceModel.due, equals(0.0));
        expect(duesInvoiceModel.dueDate, isEmpty);
        expect(duesInvoiceModel.dueDays, equals(0));
        expect(duesInvoiceModel.aging, isEmpty);
        expect(duesInvoiceModel.aging1, isEmpty);
        expect(duesInvoiceModel.paymentCredibility, isEmpty);
        expect(duesInvoiceModel.feedCreditLimit, equals('0'));
        expect(duesInvoiceModel.nonFeedCreditLimit, equals('0'));
        expect(duesInvoiceModel.harvestCreditLimit, equals('0'));
      });

      test('should handle null JSON values with defaults', () {
        final nullJson = <String, dynamic>{
          '_id': null,
          'invoiceId': null,
          'totalSalesInclTax': null,
          'totalSalesExclTax': null,
          'tcsAmount': null,
          'tdsAmount': null,
          'amountAfterTcs': null,
          'shippingCharges': null,
          'nonFeedAmountAfterTcs': null,
          'creditNoteAmountWithTcs': null,
          'payableAmount': null,
          'paidAmount': null,
          'due': null,
          'dueDays': null,
        };

        final duesInvoiceModel = DuesInvoiceModel.fromJson(
          nullJson,
          '0-30 days',
          'customer_123',
        );

        expect(duesInvoiceModel.id, isEmpty);
        expect(duesInvoiceModel.invoiceId, isEmpty);
        expect(duesInvoiceModel.totalSalesInclTax, equals(0.0));
        expect(duesInvoiceModel.totalSalesExclTax, equals(0.0));
        expect(duesInvoiceModel.tcsAmount, equals(0.0));
        expect(duesInvoiceModel.tdsAmount, equals(0.0));
        expect(duesInvoiceModel.amountAfterTcs, equals(0.0));
        expect(duesInvoiceModel.shippingCharges, equals(0.0));
        expect(duesInvoiceModel.nonFeedAmountAfterTcs, equals(0.0));
        expect(duesInvoiceModel.creditNoteAmountWithTcs, equals(0.0));
        expect(duesInvoiceModel.payableAmount, equals(0.0));
        expect(duesInvoiceModel.paidAmount, equals(0.0));
        expect(duesInvoiceModel.due, equals(0.0));
        expect(duesInvoiceModel.dueDays, equals(0));
      });

      test('should handle lastPaidDate as empty object', () {
        final jsonWithEmptyLastPaidDate = <String, dynamic>{
          '_id': 'invoice_123',
          'invoiceId': 'inv_123',
          'lastPaidDate': {},
        };

        final duesInvoiceModel = DuesInvoiceModel.fromJson(
          jsonWithEmptyLastPaidDate,
          '0-30 days',
          'customer_123',
        );

        expect(duesInvoiceModel.lastPaidDate, isNull);
      });

      test('should handle lastPaidDate as non-empty object', () {
        final jsonWithLastPaidDate = <String, dynamic>{
          '_id': 'invoice_123',
          'invoiceId': 'inv_123',
          'lastPaidDate': {'date': '2024-01-10'},
        };

        final duesInvoiceModel = DuesInvoiceModel.fromJson(
          jsonWithLastPaidDate,
          '0-30 days',
          'customer_123',
        );

        expect(duesInvoiceModel.lastPaidDate, isNotNull);
        expect(duesInvoiceModel.lastPaidDate, contains('date'));
      });
    });

    group('Entity Conversion Tests', () {
      test(
        'should convert DuesInvoiceModel to DuesInvoice entity correctly',
        () {
          final duesInvoiceEntity = testDuesInvoiceModel.toEntity();

          expect(duesInvoiceEntity, isA<DuesInvoice>());
          expect(duesInvoiceEntity.id, equals(testDuesInvoiceModel.id));
          expect(
            duesInvoiceEntity.invoiceId,
            equals(testDuesInvoiceModel.invoiceId),
          );
          expect(
            duesInvoiceEntity.invoiceNumber,
            equals(testDuesInvoiceModel.invoiceNumber),
          );
          expect(
            duesInvoiceEntity.invoiceDate,
            equals(testDuesInvoiceModel.invoiceDate),
          );
          expect(
            duesInvoiceEntity.customerId,
            equals(testDuesInvoiceModel.customerId),
          );
          expect(
            duesInvoiceEntity.customerCode,
            equals(testDuesInvoiceModel.customerCode),
          );
          expect(
            duesInvoiceEntity.customerName,
            equals(testDuesInvoiceModel.customerName),
          );
          expect(
            duesInvoiceEntity.customerDistrict,
            equals(testDuesInvoiceModel.customerDistrict),
          );
          expect(
            duesInvoiceEntity.customerState,
            equals(testDuesInvoiceModel.customerState),
          );
          expect(
            duesInvoiceEntity.customerType,
            equals(testDuesInvoiceModel.customerType),
          );
          expect(
            duesInvoiceEntity.onBoardedTime,
            equals(testDuesInvoiceModel.onBoardedTime),
          );
          expect(
            duesInvoiceEntity.categoryType,
            equals(testDuesInvoiceModel.categoryType),
          );
          expect(
            duesInvoiceEntity.invoiceType,
            equals(testDuesInvoiceModel.invoiceType),
          );
          expect(
            duesInvoiceEntity.retailType,
            equals(testDuesInvoiceModel.retailType),
          );
          expect(
            duesInvoiceEntity.invoiceStatus,
            equals(testDuesInvoiceModel.invoiceStatus),
          );
          expect(
            duesInvoiceEntity.invoiceRaisedBy,
            equals(testDuesInvoiceModel.invoiceRaisedBy),
          );
          expect(duesInvoiceEntity.mode, equals(testDuesInvoiceModel.mode));
          expect(
            duesInvoiceEntity.businessVertical,
            equals(testDuesInvoiceModel.businessVertical),
          );
          expect(
            duesInvoiceEntity.feedCreditLimit,
            equals(testDuesInvoiceModel.feedCreditLimit),
          );
          expect(
            duesInvoiceEntity.nonFeedCreditLimit,
            equals(testDuesInvoiceModel.nonFeedCreditLimit),
          );
          expect(
            duesInvoiceEntity.harvestCreditLimit,
            equals(testDuesInvoiceModel.harvestCreditLimit),
          );
          expect(
            duesInvoiceEntity.totalSalesInclTax,
            equals(testDuesInvoiceModel.totalSalesInclTax),
          );
          expect(
            duesInvoiceEntity.totalSalesExclTax,
            equals(testDuesInvoiceModel.totalSalesExclTax),
          );
          expect(
            duesInvoiceEntity.tcsAmount,
            equals(testDuesInvoiceModel.tcsAmount),
          );
          expect(
            duesInvoiceEntity.tdsAmount,
            equals(testDuesInvoiceModel.tdsAmount),
          );
          expect(
            duesInvoiceEntity.amountAfterTcs,
            equals(testDuesInvoiceModel.amountAfterTcs),
          );
          expect(
            duesInvoiceEntity.shippingCharges,
            equals(testDuesInvoiceModel.shippingCharges),
          );
          expect(
            duesInvoiceEntity.feedAmountAfterTcs,
            equals(testDuesInvoiceModel.feedAmountAfterTcs),
          );
          expect(
            duesInvoiceEntity.nonFeedAmountAfterTcs,
            equals(testDuesInvoiceModel.nonFeedAmountAfterTcs),
          );
          expect(
            duesInvoiceEntity.creditNoteAmountWithTcs,
            equals(testDuesInvoiceModel.creditNoteAmountWithTcs),
          );
          expect(
            duesInvoiceEntity.payableAmount,
            equals(testDuesInvoiceModel.payableAmount),
          );
          expect(
            duesInvoiceEntity.paidAmount,
            equals(testDuesInvoiceModel.paidAmount),
          );
          expect(duesInvoiceEntity.due, equals(testDuesInvoiceModel.due));
          expect(
            duesInvoiceEntity.dueDate,
            equals(testDuesInvoiceModel.dueDate),
          );
          expect(
            duesInvoiceEntity.dueDays,
            equals(testDuesInvoiceModel.dueDays),
          );
          expect(duesInvoiceEntity.aging, equals(testDuesInvoiceModel.aging));
          expect(duesInvoiceEntity.aging1, equals(testDuesInvoiceModel.aging1));
          expect(
            duesInvoiceEntity.paymentCredibility,
            equals(testDuesInvoiceModel.paymentCredibility),
          );
          expect(
            duesInvoiceEntity.totalPurchase,
            equals(testDuesInvoiceModel.totalPurchase),
          );
          expect(
            duesInvoiceEntity.totalSales,
            equals(testDuesInvoiceModel.totalSales),
          );
          expect(
            duesInvoiceEntity.salesTier,
            equals(testDuesInvoiceModel.salesTier),
          );
          expect(
            duesInvoiceEntity.healthcareSales,
            equals(testDuesInvoiceModel.healthcareSales),
          );
          expect(
            duesInvoiceEntity.feedSales,
            equals(testDuesInvoiceModel.feedSales),
          );
          expect(
            duesInvoiceEntity.chemicalSales,
            equals(testDuesInvoiceModel.chemicalSales),
          );
          expect(
            duesInvoiceEntity.equipmentSales,
            equals(testDuesInvoiceModel.equipmentSales),
          );
          expect(
            duesInvoiceEntity.harvestSales,
            equals(testDuesInvoiceModel.harvestSales),
          );
          expect(
            duesInvoiceEntity.grossMargin,
            equals(testDuesInvoiceModel.grossMargin),
          );
          expect(
            duesInvoiceEntity.lastPaidDate,
            equals(testDuesInvoiceModel.lastPaidDate),
          );
        },
      );

      test('should maintain data integrity during entity conversion', () {
        final convertedEntity = testDuesInvoiceModel.toEntity();

        expect(convertedEntity.id, equals(testJson['_id']));
        expect(convertedEntity.invoiceId, equals(testJson['invoiceId']));
        expect(
          convertedEntity.invoiceNumber,
          equals(testJson['invoiceNumber']),
        );
        expect(convertedEntity.customerId, equals('customer_123'));
        expect(
          convertedEntity.totalSalesInclTax,
          equals(testJson['totalSalesInclTax']),
        );
        expect(
          convertedEntity.totalSalesExclTax,
          equals(testJson['totalSalesExclTax']),
        );
        expect(
          convertedEntity.payableAmount,
          equals(testJson['payableAmount']),
        );
        expect(convertedEntity.paidAmount, equals(testJson['paidAmount']));
        expect(convertedEntity.due, equals(testJson['due']));
        expect(convertedEntity.dueDays, equals(testJson['dueDays']));
      });
    });

    group('Edge Cases and Validation Tests', () {
      test('should handle special characters in string fields', () {
        final specialCharJson = {
          '_id': 'invoice_123 & special',
          'invoiceId': 'inv_123 (special)',
          'invoiceNumber': 'INV-2024-001 & revised',
          'customerCode': 'CUST001-special',
          'customerName': 'Test Customer & Co.',
          'customerDistrict': 'Chennai (South)',
          'customerState': 'Tamil Nadu & Region',
          'aging': '0-30 days (current)',
          'aging1': 'Current & Active',
          'paymentCredibility': 'Good & Reliable',
        };

        final duesInvoiceModel = DuesInvoiceModel.fromJson(
          specialCharJson,
          '0-30 days',
          'customer_123',
        );

        expect(duesInvoiceModel.id, equals('invoice_123 & special'));
        expect(duesInvoiceModel.invoiceId, equals('inv_123 (special)'));
        expect(
          duesInvoiceModel.invoiceNumber,
          equals('INV-2024-001 & revised'),
        );
        expect(duesInvoiceModel.customerCode, equals('CUST001-special'));
        expect(duesInvoiceModel.customerName, equals('Test Customer & Co.'));
        expect(duesInvoiceModel.customerDistrict, equals('Chennai (South)'));
        expect(duesInvoiceModel.customerState, equals('Tamil Nadu & Region'));
        expect(duesInvoiceModel.aging, equals('0-30 days (current)'));
        expect(duesInvoiceModel.aging1, equals('Current & Active'));
        expect(duesInvoiceModel.paymentCredibility, equals('Good & Reliable'));
      });

      test('should handle extreme numeric values', () {
        final extremeJson = {
          '_id': 'invoice_123',
          'invoiceId': 'inv_123',
          'totalSalesInclTax': double.maxFinite,
          'totalSalesExclTax': double.maxFinite,
          'tcsAmount': double.maxFinite,
          'tdsAmount': double.maxFinite,
          'amountAfterTcs': double.maxFinite,
          'shippingCharges': double.maxFinite,
          'nonFeedAmountAfterTcs': double.maxFinite,
          'creditNoteAmountWithTcs': double.maxFinite,
          'payableAmount': double.maxFinite,
          'paidAmount': double.maxFinite,
          'due': double.maxFinite,
          'dueDays': 999999,
        };

        final duesInvoiceModel = DuesInvoiceModel.fromJson(
          extremeJson,
          '0-30 days',
          'customer_123',
        );

        expect(duesInvoiceModel.totalSalesInclTax, equals(double.maxFinite));
        expect(duesInvoiceModel.totalSalesExclTax, equals(double.maxFinite));
        expect(duesInvoiceModel.tcsAmount, equals(double.maxFinite));
        expect(duesInvoiceModel.tdsAmount, equals(double.maxFinite));
        expect(duesInvoiceModel.amountAfterTcs, equals(double.maxFinite));
        expect(duesInvoiceModel.shippingCharges, equals(double.maxFinite));
        expect(
          duesInvoiceModel.nonFeedAmountAfterTcs,
          equals(double.maxFinite),
        );
        expect(
          duesInvoiceModel.creditNoteAmountWithTcs,
          equals(double.maxFinite),
        );
        expect(duesInvoiceModel.payableAmount, equals(double.maxFinite));
        expect(duesInvoiceModel.paidAmount, equals(double.maxFinite));
        expect(duesInvoiceModel.due, equals(double.maxFinite));
        expect(duesInvoiceModel.dueDays, equals(999999));
      });

      test('should handle negative numeric values', () {
        final negativeJson = {
          '_id': 'invoice_123',
          'invoiceId': 'inv_123',
          'totalSalesInclTax': -1000.0,
          'totalSalesExclTax': -900.0,
          'tcsAmount': -10.0,
          'tdsAmount': -20.0,
          'amountAfterTcs': -980.0,
          'shippingCharges': -50.0,
          'nonFeedAmountAfterTcs': -100.0,
          'creditNoteAmountWithTcs': -50.0,
          'payableAmount': -930.0,
          'paidAmount': -500.0,
          'due': -430.0,
          'dueDays': -30,
        };

        final duesInvoiceModel = DuesInvoiceModel.fromJson(
          negativeJson,
          '0-30 days',
          'customer_123',
        );

        expect(duesInvoiceModel.totalSalesInclTax, equals(-1000.0));
        expect(duesInvoiceModel.totalSalesExclTax, equals(-900.0));
        expect(duesInvoiceModel.tcsAmount, equals(-10.0));
        expect(duesInvoiceModel.tdsAmount, equals(-20.0));
        expect(duesInvoiceModel.amountAfterTcs, equals(-980.0));
        expect(duesInvoiceModel.shippingCharges, equals(-50.0));
        expect(duesInvoiceModel.nonFeedAmountAfterTcs, equals(-100.0));
        expect(duesInvoiceModel.creditNoteAmountWithTcs, equals(-50.0));
        expect(duesInvoiceModel.payableAmount, equals(-930.0));
        expect(duesInvoiceModel.paidAmount, equals(-500.0));
        expect(duesInvoiceModel.due, equals(-430.0));
        expect(duesInvoiceModel.dueDays, equals(-30));
      });

      test('should handle string to double conversion for numeric fields', () {
        final stringNumericJson = {
          '_id': 'invoice_123',
          'invoiceId': 'inv_123',
          'totalSalesInclTax': 11800.50,
          'totalSalesExclTax': 10000.25,
          'tcsAmount': 100.75,
          'tdsAmount': 200.25,
          'amountAfterTcs': 11700.00,
          'shippingCharges': 500.50,
          'nonFeedAmountAfterTcs': 1700.75,
          'creditNoteAmountWithTcs': 0.00,
          'payableAmount': 11700.00,
          'paidAmount': 5000.00,
          'due': 6700.00,
        };

        final duesInvoiceModel = DuesInvoiceModel.fromJson(
          stringNumericJson,
          '0-30 days',
          'customer_123',
        );

        expect(duesInvoiceModel.totalSalesInclTax, equals(11800.50));
        expect(duesInvoiceModel.totalSalesExclTax, equals(10000.25));
        expect(duesInvoiceModel.tcsAmount, equals(100.75));
        expect(duesInvoiceModel.tdsAmount, equals(200.25));
        expect(duesInvoiceModel.amountAfterTcs, equals(11700.00));
        expect(duesInvoiceModel.shippingCharges, equals(500.50));
        expect(duesInvoiceModel.nonFeedAmountAfterTcs, equals(1700.75));
        expect(duesInvoiceModel.creditNoteAmountWithTcs, equals(0.00));
        expect(duesInvoiceModel.payableAmount, equals(11700.00));
        expect(duesInvoiceModel.paidAmount, equals(5000.00));
        expect(duesInvoiceModel.due, equals(6700.00));
      });
    });
  });

  group('DuesAgingGroupModel Tests', () {
    test('should create DuesAgingGroupModel from JSON correctly', () {
      final json = {
        'totalPayableAmount': 50000.0,
        'aging': '0-30 days',
        'dueDays': 15,
      };

      final agingGroupModel = DuesAgingGroupModel.fromJson(
        json,
        'customer_123',
      );

      expect(agingGroupModel.customerId, equals('customer_123'));
      expect(agingGroupModel.totalPayableAmount, equals(50000.0));
      expect(agingGroupModel.aging, equals('0-30 days'));
      expect(agingGroupModel.dueDays, equals(15));
      expect(agingGroupModel.lastSyncTimestamp, isA<int>());
    });

    test('should handle missing JSON fields with defaults', () {
      final incompleteJson = <String, dynamic>{};

      final agingGroupModel = DuesAgingGroupModel.fromJson(
        incompleteJson,
        'customer_123',
      );

      expect(agingGroupModel.customerId, equals('customer_123'));
      expect(agingGroupModel.totalPayableAmount, equals(0.0));
      expect(agingGroupModel.aging, isEmpty);
      expect(agingGroupModel.dueDays, equals(0));
      expect(agingGroupModel.lastSyncTimestamp, isA<int>());
    });
  });

  group('DuesSummaryModel Tests', () {
    test('should create DuesSummaryModel with all required fields', () {
      final summaryModel = DuesSummaryModel(
        customerId: 'customer_123',
        totalDue: 75000.0,
        lastSyncTimestamp: DateTime.now().millisecondsSinceEpoch,
      );

      expect(summaryModel.customerId, equals('customer_123'));
      expect(summaryModel.totalDue, equals(75000.0));
      expect(summaryModel.lastSyncTimestamp, isA<int>());
    });

    test('should create DuesSummaryModel with default id', () {
      final summaryModel = DuesSummaryModel(
        customerId: 'customer_123',
        totalDue: 75000.0,
        lastSyncTimestamp: DateTime.now().millisecondsSinceEpoch,
      );

      expect(summaryModel.dbId, equals(0));
    });
  });
}
