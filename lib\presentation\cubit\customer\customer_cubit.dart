import 'package:aquapartner/core/utils/logger.dart';
import 'package:aquapartner/domain/services/auth_service.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../domain/entities/customer.dart';
import '../../../domain/usecases/customer_usercases.dart';

part 'customer_state.dart';

class CustomerCubit extends Cubit<CustomerState> {
  final AuthService authService;
  final CheckIfSyncNeeded checkIfSyncNeeded;
  final AppLogger logger;

  CustomerCubit({
    required this.authService,
    required this.checkIfSyncNeeded,
    required this.logger,
  }) : super(CustomerInitial());

  bool _isInitialLoadDone = false;

  Future<void> loadCustomer() async {
    if (_isInitialLoadDone) {
      // Return if data is already loaded
      return;
    }

    emit(CustomerLoading());

    final result = await authService.getCurrentCustomer();

    result.fold(
      (failure) async {
        logger.e("Failed to load product catalogues", failure);
        emit(CustomerError(message: "Please try again later"));
      },
      (Customer? customer) async {
        if (customer == null) {
          emit(CustomerError(message: "Customer Not Fount"));
          return;
        }
        _isInitialLoadDone = true;
        emit(CustomerLoaded(customer: customer));
      },
    );
  }

  /// Resets the customer state to initial
  void resetCustomerState() {
    emit(CustomerInitial());
  }
}
