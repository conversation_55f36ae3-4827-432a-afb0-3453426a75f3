import 'package:objectbox/objectbox.dart';
import '../../domain/entities/scheme/support_person.dart';
import 'customer_scheme_model.dart';
import 'person_details_model.dart';

@Entity()
class SupportPersonModel {
  @Id()
  int id = 0;

  String profile;

  final customerScheme = ToOne<CustomerSchemeModel>();

  // Changed from ToOne to ToMany and removed @Backlink
  final details = ToMany<PersonDetailsModel>();

  SupportPersonModel({this.id = 0, required this.profile});

  // Convert from domain entity to model
  factory SupportPersonModel.fromEntity(SupportPerson entity) {
    final model = SupportPersonModel(profile: entity.profile);

    final detailsModel = PersonDetailsModel.fromEntity(entity.details);
    detailsModel.supportPerson.target = model;
    model.details.add(detailsModel);

    return model;
  }

  // Convert from model to domain entity
  SupportPerson toEntity() {
    return SupportPerson(
      profile: profile, 
      details: details.isNotEmpty ? details.first.toEntity() : PersonDetailsModel(
        rowId: '',
        customerName: '',
        email: '',
        status: '',
        mobileNumber: '',
        userId: '',
        empId: '',
        profile: '',
      ).toEntity()
    );
  }

  // Convert from JSON to model
  factory SupportPersonModel.fromJson(Map<String, dynamic> json) {
    final model = SupportPersonModel(profile: json['profile'] ?? '');

    // Process details
    if (json['details'] != null) {
      final personDetails = PersonDetailsModel.fromJson(json['details']);
      personDetails.supportPerson.target = model;
      model.details.add(personDetails);
    }

    return model;
  }

  // Convert from model to JSON
  Map<String, dynamic> toJson() {
    return {
      'profile': profile, 
      'details': details.isNotEmpty ? details.first.toJson() : null
    };
  }
}