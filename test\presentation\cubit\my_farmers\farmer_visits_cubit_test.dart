import 'package:aquapartner/core/error/failures.dart';
import 'package:aquapartner/domain/entities/customer.dart';
import 'package:aquapartner/domain/entities/farmer_visit/farmer.dart';
import 'package:aquapartner/domain/entities/farmer_visit/visit.dart';
import 'package:aquapartner/domain/services/auth_service.dart';
import 'package:aquapartner/domain/usecases/get_all_farmers_usecase.dart';
import 'package:aquapartner/presentation/cubit/my_farmers/farmer_visits_cubit.dart';
import 'package:bloc_test/bloc_test.dart';
import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// Mock classes
class MockGetAllFarmersUseCase extends Mock implements GetAllFarmersUseCase {}

class MockAuthService extends Mock implements AuthService {}

// Test data
final testCustomer = Customer(
  customerId: 'test_customer_123',
  customerName: 'Test Customer',
  email: '<EMAIL>',
  companyName: 'Test Company',
  mobileNumber: '+919999999999',
  billingAddress: 'Test Address',
  gstNo: 'TEST123456789',
  businessVertical: 'Aquaculture',
  customerCode: 'TC001',
);

final testVisits = [
  Visit(
    id: 1,
    pondId: 'POND001',
    farmerId: 'FARMER001',
    farmerName: 'Test Farmer 1',
    doc: 30,
    createdDateTime: DateTime.now(),
    mobileNumber: '+919999999999',
    productUsed: 'Product A',
  ),
  Visit(
    id: 2,
    pondId: 'POND002',
    farmerId: 'FARMER001',
    farmerName: 'Test Farmer 1',
    doc: 45,
    createdDateTime: DateTime.now().subtract(Duration(days: 1)),
    mobileNumber: '+919999999999',
    productUsed: 'Product B',
  ),
];

final testFarmers = [
  Farmer(
    id: 1,
    name: 'Test Farmer 1',
    mobileNumber: '+919999999999',
    visits: testVisits,
  ),
  Farmer(
    id: 2,
    name: 'Test Farmer 2',
    mobileNumber: '+919876543211',
    visits: [testVisits.first],
  ),
];

void main() {
  setUpAll(() {
    registerFallbackValue(GetAllFarmersParams(customerId: 'test'));
  });

  group('FarmerVisitsCubit Tests', () {
    late FarmerVisitsCubit farmerVisitsCubit;
    late MockGetAllFarmersUseCase mockGetAllFarmersUseCase;
    late MockAuthService mockAuthService;

    setUp(() {
      mockGetAllFarmersUseCase = MockGetAllFarmersUseCase();
      mockAuthService = MockAuthService();

      farmerVisitsCubit = FarmerVisitsCubit(
        getAllFarmersUseCase: mockGetAllFarmersUseCase,
        authService: mockAuthService,
      );

      // Setup default mock behaviors
      when(
        () => mockAuthService.getCurrentCustomer(),
      ).thenAnswer((_) async => Right(testCustomer));
    });

    tearDown(() {
      farmerVisitsCubit.close();
    });

    group('Initial State', () {
      test('should have initial state as FarmerVisitsInitial', () {
        expect(farmerVisitsCubit.state, isA<FarmerVisitsInitial>());
      });
    });

    group('Get Farmer Visits', () {
      blocTest<FarmerVisitsCubit, FarmerVisitsState>(
        'should emit [FarmerVisitsLoading, FarmerVisitsLoaded] when getting farmers successfully',
        build: () {
          when(
            () => mockGetAllFarmersUseCase.call(any()),
          ).thenAnswer((_) async => Right(testFarmers));
          return farmerVisitsCubit;
        },
        act: (cubit) => cubit.getFarmerVisits(),
        expect:
            () => [
              isA<FarmerVisitsLoading>(),
              predicate<FarmerVisitsLoaded>(
                (state) => state.farmers == testFarmers,
              ),
            ],
        verify: (_) {
          verify(
            () => mockGetAllFarmersUseCase.call(
              GetAllFarmersParams(customerId: testCustomer.customerId),
            ),
          ).called(1);
        },
      );

      blocTest<FarmerVisitsCubit, FarmerVisitsState>(
        'should emit [FarmerVisitsLoading, FarmerVisitsEmpty] when no farmers found',
        build: () {
          when(
            () => mockGetAllFarmersUseCase.call(any()),
          ).thenAnswer((_) async => const Right([]));
          return farmerVisitsCubit;
        },
        act: (cubit) => cubit.getFarmerVisits(),
        expect: () => [isA<FarmerVisitsLoading>(), isA<FarmerVisitsEmpty>()],
      );

      blocTest<FarmerVisitsCubit, FarmerVisitsState>(
        'should emit [FarmerVisitsLoading, FarmerVisitsError] when use case fails',
        build: () {
          when(
            () => mockGetAllFarmersUseCase.call(any()),
          ).thenAnswer((_) async => Left(CacheFailure()));
          return farmerVisitsCubit;
        },
        act: (cubit) => cubit.getFarmerVisits(),
        expect:
            () => [
              isA<FarmerVisitsLoading>(),
              predicate<FarmerVisitsError>(
                (state) =>
                    state.message ==
                    'Failed to load cached data. Please try again.',
              ),
            ],
      );

      blocTest<FarmerVisitsCubit, FarmerVisitsState>(
        'should emit [FarmerVisitsLoading, FarmerVisitsError] when auth service fails',
        build: () {
          when(
            () => mockAuthService.getCurrentCustomer(),
          ).thenAnswer((_) async => Left(ServerFailure()));
          return farmerVisitsCubit;
        },
        act: (cubit) => cubit.getFarmerVisits(),
        expect:
            () => [
              isA<FarmerVisitsLoading>(),
              predicate<FarmerVisitsError>(
                (state) =>
                    state.message ==
                    'Failed to connect to the server. Please check your internet connection.',
              ),
            ],
      );

      blocTest<FarmerVisitsCubit, FarmerVisitsState>(
        'should emit [FarmerVisitsError] when customer is null',
        build: () {
          when(
            () => mockAuthService.getCurrentCustomer(),
          ).thenAnswer((_) async => const Right(null));
          return farmerVisitsCubit;
        },
        act: (cubit) => cubit.getFarmerVisits(),
        expect:
            () => [
              isA<FarmerVisitsLoading>(),
              predicate<FarmerVisitsError>(
                (state) => state.message == 'Customer information not found',
              ),
            ],
      );

      test('should not emit loading if already in loaded state', () async {
        // First set to loaded state
        farmerVisitsCubit.emit(FarmerVisitsLoaded(testFarmers));

        when(
          () => mockGetAllFarmersUseCase.call(any()),
        ).thenAnswer((_) async => Right(testFarmers));

        farmerVisitsCubit.getFarmerVisits();

        // Should not emit loading state
        expect(farmerVisitsCubit.state, isA<FarmerVisitsLoaded>());
      });
    });

    group('Sync Farmer Visits - One-way Sync', () {
      blocTest<FarmerVisitsCubit, FarmerVisitsState>(
        'should emit [FarmerVisitsSyncing, FarmerVisitsLoaded] when sync is successful',
        build: () {
          when(
            () => mockGetAllFarmersUseCase.syncFarmers(testCustomer.customerId),
          ).thenAnswer((_) async => Right(testFarmers));
          return farmerVisitsCubit;
        },
        act: (cubit) => cubit.syncFarmerVisits(),
        expect:
            () => [
              isA<FarmerVisitsSyncing>(),
              predicate<FarmerVisitsLoaded>(
                (state) => state.farmers == testFarmers,
              ),
            ],
        verify: (_) {
          verify(
            () => mockGetAllFarmersUseCase.syncFarmers(testCustomer.customerId),
          ).called(1);
        },
      );

      blocTest<FarmerVisitsCubit, FarmerVisitsState>(
        'should emit [FarmerVisitsSyncing, FarmerVisitsEmpty] when sync returns empty list',
        build: () {
          when(
            () => mockGetAllFarmersUseCase.syncFarmers(testCustomer.customerId),
          ).thenAnswer((_) async => const Right([]));
          return farmerVisitsCubit;
        },
        act: (cubit) => cubit.syncFarmerVisits(),
        expect: () => [isA<FarmerVisitsSyncing>(), isA<FarmerVisitsEmpty>()],
      );

      blocTest<FarmerVisitsCubit, FarmerVisitsState>(
        'should preserve current data when sync fails',
        build: () {
          // Start with loaded state
          farmerVisitsCubit.emit(FarmerVisitsLoaded(testFarmers));

          when(
            () => mockGetAllFarmersUseCase.syncFarmers(testCustomer.customerId),
          ).thenAnswer((_) async => Left(NetworkFailure()));
          return farmerVisitsCubit;
        },
        act: (cubit) => cubit.syncFarmerVisits(),
        expect:
            () => [
              predicate<FarmerVisitsSyncing>(
                (state) =>
                    state.farmers != null && state.farmers == testFarmers,
              ),
              predicate<FarmerVisitsLoaded>(
                (state) => state.farmers == testFarmers,
              ),
            ],
      );

      blocTest<FarmerVisitsCubit, FarmerVisitsState>(
        'should emit error when sync fails and no current data',
        build: () {
          when(
            () => mockGetAllFarmersUseCase.syncFarmers(testCustomer.customerId),
          ).thenAnswer((_) async => Left(NetworkFailure()));
          return farmerVisitsCubit;
        },
        act: (cubit) => cubit.syncFarmerVisits(),
        expect:
            () => [
              isA<FarmerVisitsSyncing>(),
              predicate<FarmerVisitsError>(
                (state) =>
                    state.message ==
                    'No internet connection. Please check your network settings.',
              ),
            ],
      );

      blocTest<FarmerVisitsCubit, FarmerVisitsState>(
        'should handle auth service failure during sync',
        build: () {
          when(
            () => mockAuthService.getCurrentCustomer(),
          ).thenAnswer((_) async => Left(ServerFailure()));
          return farmerVisitsCubit;
        },
        act: (cubit) => cubit.syncFarmerVisits(),
        expect:
            () => [
              isA<FarmerVisitsSyncing>(),
              predicate<FarmerVisitsError>(
                (state) =>
                    state.message ==
                    'Failed to connect to the server. Please check your internet connection.',
              ),
            ],
      );

      blocTest<FarmerVisitsCubit, FarmerVisitsState>(
        'should handle null customer during sync',
        build: () {
          when(
            () => mockAuthService.getCurrentCustomer(),
          ).thenAnswer((_) async => const Right(null));
          return farmerVisitsCubit;
        },
        act: (cubit) => cubit.syncFarmerVisits(),
        expect:
            () => [
              isA<FarmerVisitsSyncing>(),
              predicate<FarmerVisitsError>(
                (state) => state.message == 'Customer information not found',
              ),
            ],
      );
    });

    group('Error Mapping', () {
      blocTest<FarmerVisitsCubit, FarmerVisitsState>(
        'should map ServerFailure correctly',
        build: () {
          when(
            () => mockGetAllFarmersUseCase.call(any()),
          ).thenAnswer((_) async => Left(ServerFailure()));
          return farmerVisitsCubit;
        },
        act: (cubit) => cubit.getFarmerVisits(),
        expect:
            () => [
              isA<FarmerVisitsLoading>(),
              predicate<FarmerVisitsError>(
                (state) =>
                    state.message ==
                    'Failed to connect to the server. Please check your internet connection.',
              ),
            ],
      );

      blocTest<FarmerVisitsCubit, FarmerVisitsState>(
        'should map NetworkFailure correctly',
        build: () {
          when(
            () => mockGetAllFarmersUseCase.call(any()),
          ).thenAnswer((_) async => Left(NetworkFailure()));
          return farmerVisitsCubit;
        },
        act: (cubit) => cubit.getFarmerVisits(),
        expect:
            () => [
              isA<FarmerVisitsLoading>(),
              predicate<FarmerVisitsError>(
                (state) =>
                    state.message ==
                    'No internet connection. Please check your network settings.',
              ),
            ],
      );

      blocTest<FarmerVisitsCubit, FarmerVisitsState>(
        'should map unknown failure correctly',
        build: () {
          when(
            () => mockGetAllFarmersUseCase.call(any()),
          ).thenAnswer((_) async => Left(UnknownFailure()));
          return farmerVisitsCubit;
        },
        act: (cubit) => cubit.getFarmerVisits(),
        expect:
            () => [
              isA<FarmerVisitsLoading>(),
              predicate<FarmerVisitsError>(
                (state) =>
                    state.message ==
                    'An unexpected error occurred. Please try again later.',
              ),
            ],
      );
    });

    group('State Management', () {
      test('should maintain state equality correctly', () {
        final state1 = FarmerVisitsLoaded(testFarmers);
        final state2 = FarmerVisitsLoaded(testFarmers);
        final state3 = FarmerVisitsLoaded([]);

        expect(state1, equals(state2));
        expect(state1, isNot(equals(state3)));
      });

      test('should handle syncing state with farmers correctly', () {
        final syncingState = FarmerVisitsSyncing(testFarmers);
        expect(syncingState.farmers, equals(testFarmers));
        expect(syncingState.props, equals([testFarmers]));
      });

      test('should handle syncing state without farmers correctly', () {
        const syncingState = FarmerVisitsSyncing();
        expect(syncingState.farmers, isNull);
        expect(syncingState.props, equals([]));
      });
    });

    group('Edge Cases', () {
      blocTest<FarmerVisitsCubit, FarmerVisitsState>(
        'should handle multiple rapid sync calls',
        build: () {
          when(
            () => mockGetAllFarmersUseCase.syncFarmers(testCustomer.customerId),
          ).thenAnswer((_) async => Right(testFarmers));
          return farmerVisitsCubit;
        },
        act: (cubit) async {
          // Call sync multiple times rapidly
          cubit.syncFarmerVisits();
          await Future.delayed(Duration(milliseconds: 10));
          cubit.syncFarmerVisits();
        },
        expect:
            () => [
              isA<FarmerVisitsSyncing>(),
              isA<FarmerVisitsLoaded>(),
              isA<FarmerVisitsSyncing>(),
              isA<FarmerVisitsLoaded>(),
            ],
      );

      blocTest<FarmerVisitsCubit, FarmerVisitsState>(
        'should handle concurrent get and sync operations',
        build: () {
          when(
            () => mockGetAllFarmersUseCase.call(any()),
          ).thenAnswer((_) async => Right(testFarmers));
          when(
            () => mockGetAllFarmersUseCase.syncFarmers(testCustomer.customerId),
          ).thenAnswer((_) async => Right(testFarmers));
          return farmerVisitsCubit;
        },
        act: (cubit) async {
          cubit.getFarmerVisits();
          await Future.delayed(Duration(milliseconds: 50));
          cubit.syncFarmerVisits();
        },
        expect:
            () => [
              isA<FarmerVisitsLoading>(),
              isA<FarmerVisitsLoaded>(),
              isA<FarmerVisitsSyncing>(),
              isA<FarmerVisitsLoaded>(),
            ],
      );

      blocTest<FarmerVisitsCubit, FarmerVisitsState>(
        'should handle farmers with empty visits list',
        build: () {
          final farmersWithoutVisits = [
            const Farmer(
              id: 1,
              name: 'Farmer Without Visits',
              mobileNumber: '+919999999999',
              visits: [],
            ),
          ];

          when(
            () => mockGetAllFarmersUseCase.call(any()),
          ).thenAnswer((_) async => Right(farmersWithoutVisits));
          return farmerVisitsCubit;
        },
        act: (cubit) => cubit.getFarmerVisits(),
        expect:
            () => [
              isA<FarmerVisitsLoading>(),
              predicate<FarmerVisitsLoaded>(
                (state) =>
                    state.farmers.length == 1 &&
                    state.farmers.first.visits.isEmpty,
              ),
            ],
      );
    });
  });
}

// Custom failure for testing
class UnknownFailure extends Failure {
  @override
  List<Object> get props => [];
}
