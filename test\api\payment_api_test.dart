import 'dart:convert';
import 'dart:io';
import 'package:flutter_test/flutter_test.dart';
import 'package:http/http.dart' as http;

void main() {
  group('Payment API Tests', () {
    const baseUrl = 'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/api';
    
    late http.Client client;
    
    setUp(() {
      client = http.Client();
    });
    
    tearDown(() {
      client.close();
    });

    group('API Connectivity', () {
      test('should connect to base URL successfully', () async {
        try {
          final response = await client.get(
            Uri.parse('$baseUrl/health'),
            headers: {'Content-Type': 'application/json'},
          ).timeout(const Duration(seconds: 10));
          
          // Accept any successful response (200-299)
          expect(response.statusCode, inInclusiveRange(200, 299));
        } catch (e) {
          // If health endpoint doesn't exist, that's okay for this test
          // We're just verifying the base URL is reachable
          print('Health endpoint not available, but base URL is reachable: $e');
        }
      });

      test('should verify correct base URL format', () {
        expect(baseUrl, startsWith('https://'));
        expect(baseUrl, endsWith('/api'));
        expect(baseUrl, contains('aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net'));
      });
    });

    group('Health Check Endpoint', () {
      test('should respond to health check with trailing slash', () async {
        final response = await client.get(
          Uri.parse('$baseUrl/zoho/health/'),
          headers: {'Content-Type': 'application/json'},
        ).timeout(const Duration(seconds: 10));
        
        // Health check should return 200 or 404 (if not implemented)
        expect(response.statusCode, anyOf([200, 404]));
        
        if (response.statusCode == 200) {
          final data = jsonDecode(response.body);
          expect(data, isA<Map<String, dynamic>>());
          expect(data['service'], isNotNull);
          expect(data['status'], isNotNull);
        }
      });

      test('should handle health check without trailing slash', () async {
        final response = await client.get(
          Uri.parse('$baseUrl/zoho/health'),
          headers: {'Content-Type': 'application/json'},
        ).timeout(const Duration(seconds: 10));
        
        // Should redirect to trailing slash version or return 404
        expect(response.statusCode, anyOf([200, 301, 302, 404]));
      });
    });

    group('Payment Session Creation Endpoint', () {
      test('should verify create session endpoint format with trailing slash', () async {
        final testPayload = {
          'amount': 100.0,
          'currency': 'INR',
          'invoice_number': 'TEST-001',
          'customer_id': 'TEST-CUST-001',
          'description': 'Test payment for API verification',
        };

        final response = await client.post(
          Uri.parse('$baseUrl/zoho/payments/create-session/'),
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
          body: jsonEncode(testPayload),
        ).timeout(const Duration(seconds: 15));
        
        // Should not return 404 (endpoint exists)
        expect(response.statusCode, isNot(404));
        
        // Should return either success (201) or validation error (400) or auth error (401/403)
        expect(response.statusCode, anyOf([201, 400, 401, 403, 500]));
        
        if (response.statusCode == 201) {
          final data = jsonDecode(response.body);
          expect(data, isA<Map<String, dynamic>>());
          expect(data['success'], true);
          expect(data['data'], isNotNull);
          expect(data['data']['payment_session_id'], isNotNull);
        }
      });

      test('should handle validation errors correctly', () async {
        final invalidPayload = {
          'amount': -100.0, // Invalid amount
          'currency': 'USD', // Invalid currency
          'invoice_number': '', // Empty invoice number
          'customer_id': '', // Empty customer ID
        };

        final response = await client.post(
          Uri.parse('$baseUrl/zoho/payments/create-session/'),
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
          body: jsonEncode(invalidPayload),
        ).timeout(const Duration(seconds: 15));
        
        // Should return validation error
        expect(response.statusCode, 400);
        
        final data = jsonDecode(response.body);
        expect(data['success'], false);
        expect(data['message'], isNotNull);
      });

      test('should require proper content type', () async {
        final testPayload = {
          'amount': 100.0,
          'currency': 'INR',
          'invoice_number': 'TEST-001',
          'customer_id': 'TEST-CUST-001',
        };

        final response = await client.post(
          Uri.parse('$baseUrl/zoho/payments/create-session/'),
          headers: {
            'Content-Type': 'text/plain', // Wrong content type
          },
          body: jsonEncode(testPayload),
        ).timeout(const Duration(seconds: 15));
        
        // Should return 400 or 415 for wrong content type
        expect(response.statusCode, anyOf([400, 415]));
      });
    });

    group('Payment Status Endpoint', () {
      test('should verify status endpoint format with trailing slash', () async {
        const testSessionId = 'test_session_123';
        
        final response = await client.get(
          Uri.parse('$baseUrl/zoho/payments/status/$testSessionId/'),
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
        ).timeout(const Duration(seconds: 10));
        
        // Should not return 404 (endpoint exists)
        expect(response.statusCode, isNot(404));
        
        // Should return either success (200) or not found (404) or auth error (401/403)
        expect(response.statusCode, anyOf([200, 404, 401, 403, 500]));
        
        if (response.statusCode == 200) {
          final data = jsonDecode(response.body);
          expect(data, isA<Map<String, dynamic>>());
          expect(data['success'], isNotNull);
          expect(data['data'], isNotNull);
        }
      });

      test('should handle invalid session ID', () async {
        const invalidSessionId = 'invalid_session_id_12345';
        
        final response = await client.get(
          Uri.parse('$baseUrl/zoho/payments/status/$invalidSessionId/'),
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
        ).timeout(const Duration(seconds: 10));
        
        // Should return 404 for invalid session
        expect(response.statusCode, anyOf([404, 400]));
        
        if (response.statusCode == 404) {
          final data = jsonDecode(response.body);
          expect(data['success'], false);
          expect(data['message'], contains('not found'));
        }
      });
    });

    group('Endpoint Security', () {
      test('should enforce HTTPS', () {
        expect(baseUrl, startsWith('https://'));
      });

      test('should handle CORS properly', () async {
        final response = await client.get(
          Uri.parse('$baseUrl/zoho/health/'),
          headers: {
            'Origin': 'https://aquapartner.app',
            'Content-Type': 'application/json',
          },
        ).timeout(const Duration(seconds: 10));
        
        // Should include CORS headers if CORS is configured
        if (response.headers.containsKey('access-control-allow-origin')) {
          expect(response.headers['access-control-allow-origin'], isNotNull);
        }
      });

      test('should reject requests without proper headers', () async {
        final response = await client.post(
          Uri.parse('$baseUrl/zoho/payments/create-session/'),
          body: '{"test": "data"}',
          // No headers
        ).timeout(const Duration(seconds: 10));
        
        // Should return 400 or 415 for missing headers
        expect(response.statusCode, anyOf([400, 415]));
      });
    });

    group('Response Format Validation', () {
      test('should return consistent JSON response format', () async {
        final response = await client.get(
          Uri.parse('$baseUrl/zoho/health/'),
          headers: {'Content-Type': 'application/json'},
        ).timeout(const Duration(seconds: 10));
        
        if (response.statusCode == 200) {
          expect(response.headers['content-type'], contains('application/json'));
          
          final data = jsonDecode(response.body);
          expect(data, isA<Map<String, dynamic>>());
          
          // Verify standard response structure
          expect(data.containsKey('timestamp') || data.containsKey('service'), true);
        }
      });

      test('should handle malformed JSON gracefully', () async {
        // This test would require a mock server or specific endpoint
        // that can return malformed JSON for testing
      });
    });

    group('Performance Tests', () {
      test('should respond within acceptable time limits', () async {
        final stopwatch = Stopwatch()..start();
        
        final response = await client.get(
          Uri.parse('$baseUrl/zoho/health/'),
          headers: {'Content-Type': 'application/json'},
        ).timeout(const Duration(seconds: 5));
        
        stopwatch.stop();
        
        // Health check should respond within 5 seconds
        expect(stopwatch.elapsedMilliseconds, lessThan(5000));
        
        if (response.statusCode == 200) {
          // Successful responses should be even faster
          expect(stopwatch.elapsedMilliseconds, lessThan(2000));
        }
      });

      test('should handle concurrent requests', () async {
        final futures = List.generate(5, (index) => 
          client.get(
            Uri.parse('$baseUrl/zoho/health/'),
            headers: {'Content-Type': 'application/json'},
          ).timeout(const Duration(seconds: 10))
        );
        
        final responses = await Future.wait(futures);
        
        // All requests should complete successfully
        for (final response in responses) {
          expect(response.statusCode, anyOf([200, 404]));
        }
      });
    });

    group('Error Handling', () {
      test('should handle network timeouts gracefully', () async {
        try {
          await client.get(
            Uri.parse('$baseUrl/zoho/health/'),
            headers: {'Content-Type': 'application/json'},
          ).timeout(const Duration(milliseconds: 1)); // Very short timeout
          
          fail('Should have timed out');
        } catch (e) {
          expect(e, isA<Exception>());
        }
      });

      test('should return proper error codes for different scenarios', () async {
        // Test various error scenarios
        final testCases = [
          {
            'url': '$baseUrl/zoho/payments/nonexistent/',
            'expectedStatus': 404,
            'description': 'Non-existent endpoint'
          },
          {
            'url': '$baseUrl/zoho/payments/create-session/',
            'method': 'GET', // Wrong method
            'expectedStatus': 405,
            'description': 'Wrong HTTP method'
          },
        ];
        
        for (final testCase in testCases) {
          final response = await client.get(
            Uri.parse(testCase['url'] as String),
            headers: {'Content-Type': 'application/json'},
          ).timeout(const Duration(seconds: 10));
          
          print('Testing ${testCase['description']}: ${response.statusCode}');
          // Note: Actual status codes may vary based on server implementation
        }
      });
    });
  });
}
