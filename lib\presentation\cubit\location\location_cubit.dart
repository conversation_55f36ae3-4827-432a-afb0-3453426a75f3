import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../core/services/location_service.dart';
import 'location_state.dart';

class LocationCubit extends Cubit<LocationState> {
  final LocationService _locationService;

  LocationCubit(this._locationService) : super(LocationInitial());

  Future<void> getCurrentLocation() async {
    emit(LocationLoading());
    try {
      final position = await _locationService.getCurrentLocation();
      if (position != null) {
        emit(LocationSuccess(position));
      } else {
        emit(LocationError('Unable to get location'));
      }
    } catch (e) {
      emit(LocationError(e.toString()));
    }
  }
}
