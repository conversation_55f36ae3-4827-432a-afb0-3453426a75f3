import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../domain/entities/smr_report.dart';
import '../../../domain/services/auth_service.dart';
import '../../../domain/usecases/smr_reports_usecases.dart';
import 'smr_report_state.dart';

class SMRReportCubit extends Cubit<SMRReportState> {
  final GetSMRReportsUseCase getSMRReportsUseCase;
  final SyncSMRReportsUseCase syncSMRReportsUseCase;
  final AuthService authService;

  // Cache for SMR reports
  List<SMRReport>? _cachedReports;
  bool _hasLoadedOnce = false;

  SMRReportCubit({
    required this.getSMRReportsUseCase,
    required this.syncSMRReportsUseCase,
    required this.authService,
  }) : super(SMRReportInitial());

  Future<void> loadSMRReports({bool forceRefresh = false}) async {
    // If we have cached data and not forcing a refresh, use the cache
    if (_cachedReports != null && !forceRefresh && _hasLoadedOnce) {
      emit(SMRReportLoaded(reports: _cachedReports!, isFromCache: true));
      return;
    }

    emit(SMRReportLoading());
    _hasLoadedOnce = true;

    final customerResult = await authService.getCurrentCustomer();

    customerResult.fold((failure) => emit(SMRReportError("Please try again")), (
      customer,
    ) async {
      final result = await getSMRReportsUseCase(customer!.customerId);
      result.fold(
        (failure) => emit(
          SMRReportError("Check your internet connection , try again later"),
        ),
        (reports) {
          _cachedReports = reports; // Cache the reports
          emit(SMRReportLoaded(reports: reports, isFromCache: false));
        },
      );
    });
  }

  Future<void> syncReports(String customerId) async {
    emit(SMRReportLoading());
    final result = await syncSMRReportsUseCase(customerId);
    result.fold((failure) => emit(SMRReportError("Pleaase try again later")), (
      success,
    ) async {
      final reportsResult = await getSMRReportsUseCase(customerId);
      reportsResult.fold(
        (failure) => emit(SMRReportError("Please try again later")),
        (reports) {
          _cachedReports = reports; // Update the cache
          emit(SMRReportLoaded(reports: reports, isFromCache: false));
        },
      );
    });
  }

  // Method to clear the cache if needed
  void clearCache() {
    _cachedReports = null;
    _hasLoadedOnce = false;
  }
}
