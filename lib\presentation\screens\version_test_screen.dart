import 'package:flutter/material.dart';
import 'package:aqua_ui/aqua_ui.dart';
import '../../core/utils/update_test_helper.dart';
import '../../core/mixins/analytics_mixin.dart';

/// A screen for testing version update scenarios
class VersionTestScreen extends StatefulWidget {
  const VersionTestScreen({super.key});

  @override
  State<VersionTestScreen> createState() => _VersionTestScreenState();
}

class _VersionTestScreenState extends State<VersionTestScreen>
    with AnalyticsMixin {
  final TextEditingController _versionController = TextEditingController(
    text: '1.1.0+1',
  );
  String _testResult = '';
  String _configInfo = '';
  bool _isLoading = false;

  @override
  String get screenName => 'version_test';

  @override
  void initState() {
    super.initState();
    _loadConfigInfo();
  }

  @override
  void dispose() {
    _versionController.dispose();
    super.dispose();
  }

  Future<void> _loadConfigInfo() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final info = await UpdateTestHelper.getUpdateConfigInfo();
      setState(() {
        _configInfo = info;
      });
      trackEvent('config_info_loaded');
    } catch (e) {
      setState(() {
        _configInfo = 'Error: $e';
      });
      trackEvent('config_info_error', params: {'error': e.toString()});
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testVersion() async {
    final version = _versionController.text.trim();
    if (version.isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Please enter a version')));
      trackEvent('version_test_empty');
      return;
    }

    setState(() {
      _isLoading = true;
      _testResult = '';
    });

    try {
      final result = await UpdateTestHelper.testVersionScenario(version);
      setState(() {
        _testResult = result;
      });
      trackEvent(
        'version_test_completed',
        params: {'test_version': version, 'result_length': result.length},
      );
    } catch (e) {
      setState(() {
        _testResult = 'Error: $e';
      });
      trackEvent(
        'version_test_error',
        params: {'test_version': version, 'error': e.toString()},
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _checkForUpdates() async {
    trackUserInteraction(
      'check_for_updates',
      'button',
      elementId: 'check_updates_button',
    );
    await UpdateTestHelper.checkForUpdates(context);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Version Update Test'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              trackUserInteraction(
                'refresh_config',
                'icon_button',
                elementId: 'refresh_button',
              );
              _loadConfigInfo();
            },
          ),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    AquaText.headline('Current Configuration'),
                    const SizedBox(height: 8),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.grey[200],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'ALL UPDATES ARE MANDATORY',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.red,
                              fontSize: 16,
                            ),
                          ),
                          const SizedBox(height: 8),
                          SelectableText(_configInfo),
                        ],
                      ),
                    ),
                    const SizedBox(height: 24),
                    AquaText.headline('Test Version Scenario'),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Expanded(
                          child: TextField(
                            controller: _versionController,
                            decoration: const InputDecoration(
                              labelText: 'Version to test (e.g., 1.1.0+1)',
                              border: OutlineInputBorder(),
                            ),
                            onChanged: (value) {
                              trackUserInteraction(
                                'version_input_changed',
                                'text_field',
                                elementId: 'version_input',
                                additionalParams: {'value': value},
                              );
                            },
                          ),
                        ),
                        const SizedBox(width: 16),
                        ElevatedButton(
                          onPressed: () {
                            trackUserInteraction(
                              'test_version',
                              'button',
                              elementId: 'test_button',
                              additionalParams: {
                                'version': _versionController.text,
                              },
                            );
                            _testVersion();
                          },
                          child: const Text('Test'),
                        ),
                      ],
                    ),
                    if (_testResult.isNotEmpty) ...[
                      const SizedBox(height: 16),
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.grey[200],
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: SelectableText(_testResult),
                      ),
                    ],
                    const SizedBox(height: 24),
                    AquaText.headline('Test Update Dialog'),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: _checkForUpdates,
                      child: const Text('Check for Updates'),
                    ),
                    const SizedBox(height: 24),
                    AquaText.headline('Common Test Scenarios'),
                    const SizedBox(height: 8),
                    _buildTestScenarioButton(
                      '1.0.0+0',
                      'Same major, lower minor',
                    ),
                    _buildTestScenarioButton(
                      '1.1.0+0',
                      'Same major/minor, lower patch',
                    ),
                    _buildTestScenarioButton(
                      '1.1.1+0',
                      'Same version, lower build',
                    ),
                    _buildTestScenarioButton('1.1.1+1', 'Exact same version'),
                    _buildTestScenarioButton('1.1.1+2', 'Higher build number'),
                    _buildTestScenarioButton('1.1.2+0', 'Higher patch version'),
                    _buildTestScenarioButton('1.2.0+0', 'Higher minor version'),
                    _buildTestScenarioButton('2.0.0+0', 'Higher major version'),
                  ],
                ),
              ),
    );
  }

  Widget _buildTestScenarioButton(String version, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: ElevatedButton(
        onPressed: () {
          trackUserInteraction(
            'test_scenario_selected',
            'button',
            elementId: 'scenario_${version.replaceAll('.', '_')}',
            additionalParams: {'version': version, 'description': description},
          );
          _versionController.text = version;
          _testVersion();
        },
        child: Text('$version - $description'),
      ),
    );
  }
}
