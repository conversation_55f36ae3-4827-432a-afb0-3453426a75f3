import 'dart:async';
import 'package:firebase_auth/firebase_auth.dart' as firebase;
import 'package:dartz/dartz.dart';
import '../../../core/error/failures.dart';
import '../../../core/utils/logger.dart';

abstract class AuthRemoteDataSource {
  Future<Either<Failure, String>> sendOtp(String phoneNumber);
  Future<Either<Failure, bool>> verifyOtp(String verificationId, String otp);
  Future<void> signOut();
}

class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  final firebase.FirebaseAuth _auth;
  final AppLogger logger;

  AuthRemoteDataSourceImpl({
    required firebase.FirebaseAuth auth,
    required this.logger,
  }) : _auth = auth;

  @override
  Future<Either<Failure, String>> sendOtp(String phoneNumber) async {
    try {
      Completer<Either<Failure, String>> completer = Completer();

      // Ensure phone number has proper format
      final formattedPhoneNumber =
          phoneNumber.startsWith('+') ? phoneNumber : '+91$phoneNumber';

      logger.i("📱 Sending OTP to: $formattedPhoneNumber");

      await _auth.verifyPhoneNumber(
        phoneNumber: formattedPhoneNumber,
        verificationCompleted: (firebase.PhoneAuthCredential credential) async {
          logger.i("✅ Auto verification completed via silent notification");
          try {
            await _auth.signInWithCredential(credential);
            if (!completer.isCompleted) {
              completer.complete(const Right("auto-verified"));
            }
          } catch (e) {
            logger.e("Error in auto verification", e);
            if (!completer.isCompleted) {
              completer.complete(Left(UserAuthFailure()));
            }
          }
        },
        verificationFailed: (firebase.FirebaseAuthException e) {
          logger.e("❌ Verification failed: ${e.code} - ${e.message}", e);

          // Enhanced error handling for iOS 14+ issues
          String errorMessage = _getEnhancedErrorMessage(e);
          logger.w("Enhanced error message: $errorMessage");

          if (!completer.isCompleted) {
            completer.complete(Left(UserAuthFailure()));
          }
        },
        codeSent: (String verId, int? resendToken) {
          logger.i("OTP code sent, verification ID: $verId");
          if (!completer.isCompleted) {
            logger.d("Code Successfully Sent and Id: $verId");
            completer.complete(Right(verId));
          }
        },
        codeAutoRetrievalTimeout: (String verId) {
          logger.i("⏰ Auto retrieval timeout - falling back to manual entry");
          if (!completer.isCompleted) {
            completer.complete(Right(verId));
          }
        },
        timeout: const Duration(seconds: 120), // Increased timeout for iOS 14+
      );

      return await completer.future;
    } catch (e) {
      logger.e("Error sending OTP", e);
      return Left(UserAuthFailure());
    }
  }

  @override
  Future<Either<Failure, bool>> verifyOtp(
    String verificationId,
    String otp,
  ) async {
    try {
      logger.i("Verifying OTP: $otp for verification ID: $verificationId");

      firebase.PhoneAuthCredential credential = firebase
          .PhoneAuthProvider.credential(
        verificationId: verificationId,
        smsCode: otp,
      );

      final userCredential = await _auth.signInWithCredential(credential);
      logger.i(
        "OTP verified successfully for user: ${userCredential.user?.uid}",
      );

      return const Right(true);
    } catch (e) {
      logger.e("Error verifying OTP", e);
      return Left(UserAuthFailure());
    }
  }

  @override
  Future<void> signOut() async {
    logger.i("Signing out user");
    await _auth.signOut();
  }

  String _getEnhancedErrorMessage(firebase.FirebaseAuthException e) {
    switch (e.code) {
      case 'invalid-phone-number':
        return 'Invalid phone number format';
      case 'too-many-requests':
        return 'Too many requests. Please try again later';
      case 'app-not-authorized':
        return 'App not authorized for phone authentication';
      case 'network-request-failed':
        return 'Network error. Please check your connection';
      case 'session-expired':
        return 'Session expired. Please request a new OTP';
      case 'invalid-verification-code':
        return 'Invalid verification code';
      case 'missing-verification-code':
        return 'Please enter the verification code';
      default:
        return 'Authentication failed. Please try again';
    }
  }
}
