import 'package:aqua_ui/aqua_ui.dart';
import 'package:flutter/material.dart';

import '../widgets/price_list_widget.dart';

class PriceListScreen extends StatelessWidget {
  const PriceListScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Color(0xFFF9FAFC),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 24),
            AquaText.headline(
              "Price List",
              weight: AquaFontWeight.semibold,
              color: acTextSecondaryColor,
            ),
            PriceListWidget(),
          ],
        ),
      ),
    );
  }
}
