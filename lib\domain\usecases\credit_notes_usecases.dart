import 'package:dartz/dartz.dart';

import '../../core/error/failures.dart';
import '../entities/credit_notes/credit_note.dart';
import '../repositories/credit_notes_repository.dart';

class GetCreditNotes {
  final CreditNotesRepository repository;

  GetCreditNotes(this.repository);

  Future<Either<Failure, List<CreditNote>>> call(String customerId) {
    return repository.getCreditNotes(customerId);
  }
}

class SyncCreditNotes {
  final CreditNotesRepository repository;

  SyncCreditNotes(this.repository);

  Future<Either<Failure, void>> call(String customerId) {
    return repository.syncCreditNotes(customerId);
  }
}

class GetLocalCreditNotes {
  final CreditNotesRepository repository;

  GetLocalCreditNotes(this.repository);

  Future<Either<Failure, List<CreditNote>>> call(String customerId) {
    return repository.getLocalCreditNotes(customerId);
  }
}
