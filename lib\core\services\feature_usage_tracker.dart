import 'package:shared_preferences/shared_preferences.dart';
import 'analytics_service.dart';

class FeatureUsageTracker {
  final AnalyticsService _analyticsService;
  static const String _featureListKey = 'tracked_features';

  FeatureUsageTracker(this._analyticsService);

  // Track feature usage and update metrics
  Future<void> trackFeature(String featureName) async {
    final prefs = await SharedPreferences.getInstance();
    final now = DateTime.now();

    // Get list of all tracked features
    final featureList = prefs.getStringList(_featureListKey) ?? [];
    if (!featureList.contains(featureName)) {
      featureList.add(featureName);
      await prefs.setStringList(_featureListKey, featureList);
    }

    // Update usage count
    final countKey = 'feature_count_$featureName';
    final count = prefs.getInt(countKey) ?? 0;
    await prefs.setInt(countKey, count + 1);

    // Update last used timestamp
    final lastUsedKey = 'feature_last_used_$featureName';
    await prefs.setString(lastUsedKey, now.toIso8601String());

    // Update first used timestamp if not set
    final firstUsedKey = 'feature_first_used_$featureName';
    if (!prefs.containsKey(firstUsedKey)) {
      await prefs.setString(firstUsedKey, now.toIso8601String());
    }

    // Log to analytics
    _analyticsService.logFeatureUsage(featureName);

    // Update user properties for most/least used features
    await _updateMostLeastUsedFeatures();
  }

  // Calculate and update most/least used features
  Future<void> _updateMostLeastUsedFeatures() async {
    final prefs = await SharedPreferences.getInstance();
    final featureList = prefs.getStringList(_featureListKey) ?? [];

    if (featureList.isEmpty) return;

    // Build usage map
    final usageMap = <String, int>{};
    for (final feature in featureList) {
      final countKey = 'feature_count_$feature';
      final count = prefs.getInt(countKey) ?? 0;
      usageMap[feature] = count;
    }

    // Sort by usage
    final sortedFeatures =
        usageMap.entries.toList()..sort((a, b) => b.value.compareTo(a.value));

    // Get most and least used
    final mostUsed = sortedFeatures.first.key;
    final leastUsed = sortedFeatures.last.key;

    // Update user properties
    await _analyticsService.analytics.setUserProperty(
      name: 'most_used_feature',
      value: mostUsed,
    );

    await _analyticsService.analytics.setUserProperty(
      name: 'least_used_feature',
      value: leastUsed,
    );

    // Log top 3 features as a comma-separated list
    if (sortedFeatures.length >= 3) {
      final top3 = sortedFeatures.take(3).map((e) => e.key).join(',');
      await _analyticsService.analytics.setUserProperty(
        name: 'top_3_features',
        value: top3,
      );
    }
  }

  // Get report of feature usage
  Future<Map<String, dynamic>> getFeatureUsageReport() async {
    final prefs = await SharedPreferences.getInstance();
    final featureList = prefs.getStringList(_featureListKey) ?? [];

    final report = <String, dynamic>{};

    for (final feature in featureList) {
      final countKey = 'feature_count_$feature';
      final lastUsedKey = 'feature_last_used_$feature';
      final firstUsedKey = 'feature_first_used_$feature';

      final count = prefs.getInt(countKey) ?? 0;
      final lastUsed = prefs.getString(lastUsedKey);
      final firstUsed = prefs.getString(firstUsedKey);

      report[feature] = {
        'count': count,
        'last_used': lastUsed,
        'first_used': firstUsed,
      };
    }

    return report;
  }
}
