// Mocks generated by Mocki<PERSON> 5.4.6 from annotations
// in aquapartner/test/presentation/widgets/zoho_payment_button_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;

import 'package:aquapartner/core/services/analytics_service.dart' as _i9;
import 'package:aquapartner/core/utils/logger.dart' as _i8;
import 'package:aquapartner/domain/entities/customer.dart' as _i10;
import 'package:aquapartner/domain/entities/payments/payment_request.dart'
    as _i6;
import 'package:aquapartner/presentation/cubit/payments/payment_cubit.dart'
    as _i4;
import 'package:aquapartner/presentation/cubit/payments/payment_state.dart'
    as _i2;
import 'package:firebase_analytics/firebase_analytics.dart' as _i3;
import 'package:flutter_bloc/flutter_bloc.dart' as _i7;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakePaymentState_0 extends _i1.SmartFake implements _i2.PaymentState {
  _FakePaymentState_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeFirebaseAnalytics_1 extends _i1.SmartFake
    implements _i3.FirebaseAnalytics {
  _FakeFirebaseAnalytics_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [PaymentCubit].
///
/// See the documentation for Mockito's code generation for more information.
class MockPaymentCubit extends _i1.Mock implements _i4.PaymentCubit {
  MockPaymentCubit() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.PaymentState get state =>
      (super.noSuchMethod(
            Invocation.getter(#state),
            returnValue: _FakePaymentState_0(this, Invocation.getter(#state)),
          )
          as _i2.PaymentState);

  @override
  _i5.Stream<_i2.PaymentState> get stream =>
      (super.noSuchMethod(
            Invocation.getter(#stream),
            returnValue: _i5.Stream<_i2.PaymentState>.empty(),
          )
          as _i5.Stream<_i2.PaymentState>);

  @override
  bool get isClosed =>
      (super.noSuchMethod(Invocation.getter(#isClosed), returnValue: false)
          as bool);

  @override
  _i5.Future<void> createPaymentSession(_i6.PaymentRequest? request) =>
      (super.noSuchMethod(
            Invocation.method(#createPaymentSession, [request]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  void startPaymentStatusPolling(String? sessionId) => super.noSuchMethod(
    Invocation.method(#startPaymentStatusPolling, [sessionId]),
    returnValueForMissingStub: null,
  );

  @override
  _i5.Future<void> checkPaymentStatus(String? sessionId) =>
      (super.noSuchMethod(
            Invocation.method(#checkPaymentStatus, [sessionId]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> cancelPayment(String? sessionId) =>
      (super.noSuchMethod(
            Invocation.method(#cancelPayment, [sessionId]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  void resetPayment() => super.noSuchMethod(
    Invocation.method(#resetPayment, []),
    returnValueForMissingStub: null,
  );

  @override
  _i5.Future<void> close() =>
      (super.noSuchMethod(
            Invocation.method(#close, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  void emit(_i2.PaymentState? state) => super.noSuchMethod(
    Invocation.method(#emit, [state]),
    returnValueForMissingStub: null,
  );

  @override
  void onChange(_i7.Change<_i2.PaymentState>? change) => super.noSuchMethod(
    Invocation.method(#onChange, [change]),
    returnValueForMissingStub: null,
  );

  @override
  void addError(Object? error, [StackTrace? stackTrace]) => super.noSuchMethod(
    Invocation.method(#addError, [error, stackTrace]),
    returnValueForMissingStub: null,
  );

  @override
  void onError(Object? error, StackTrace? stackTrace) => super.noSuchMethod(
    Invocation.method(#onError, [error, stackTrace]),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [AppLogger].
///
/// See the documentation for Mockito's code generation for more information.
class MockAppLogger extends _i1.Mock implements _i8.AppLogger {
  MockAppLogger() {
    _i1.throwOnMissingStub(this);
  }

  @override
  void d(String? message) => super.noSuchMethod(
    Invocation.method(#d, [message]),
    returnValueForMissingStub: null,
  );

  @override
  void i(String? message) => super.noSuchMethod(
    Invocation.method(#i, [message]),
    returnValueForMissingStub: null,
  );

  @override
  void w(String? message) => super.noSuchMethod(
    Invocation.method(#w, [message]),
    returnValueForMissingStub: null,
  );

  @override
  void e(String? message, [dynamic error, StackTrace? stackTrace]) =>
      super.noSuchMethod(
        Invocation.method(#e, [message, error, stackTrace]),
        returnValueForMissingStub: null,
      );

  @override
  void enableFirebaseVerboseLogging() => super.noSuchMethod(
    Invocation.method(#enableFirebaseVerboseLogging, []),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [AnalyticsService].
///
/// See the documentation for Mockito's code generation for more information.
class MockAnalyticsService extends _i1.Mock implements _i9.AnalyticsService {
  MockAnalyticsService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.FirebaseAnalytics get analytics =>
      (super.noSuchMethod(
            Invocation.getter(#analytics),
            returnValue: _FakeFirebaseAnalytics_1(
              this,
              Invocation.getter(#analytics),
            ),
          )
          as _i3.FirebaseAnalytics);

  @override
  _i5.Future<void> logUserLogin(String? userId, String? userType) =>
      (super.noSuchMethod(
            Invocation.method(#logUserLogin, [userId, userType]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> logUserLogout(String? userId) =>
      (super.noSuchMethod(
            Invocation.method(#logUserLogout, [userId]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> logEvent({
    required String? name,
    required Map<String, Object>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logEvent, [], {
              #name: name,
              #parameters: parameters,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> logScreenView({
    required String? screenName,
    String? screenClass,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logScreenView, [], {
              #screenName: screenName,
              #screenClass: screenClass,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> setUserProperties(String? userId, String? userRole) =>
      (super.noSuchMethod(
            Invocation.method(#setUserProperties, [userId, userRole]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> logFeatureUsage(String? featureName) =>
      (super.noSuchMethod(
            Invocation.method(#logFeatureUsage, [featureName]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> logProductView(String? productId, String? productName) =>
      (super.noSuchMethod(
            Invocation.method(#logProductView, [productId, productName]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  void setCurrentUser(_i10.Customer? customer) => super.noSuchMethod(
    Invocation.method(#setCurrentUser, [customer]),
    returnValueForMissingStub: null,
  );

  @override
  _i5.Future<void> logScreenDuration({
    required String? screenName,
    required int? durationMs,
    String? screenClass,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logScreenDuration, [], {
              #screenName: screenName,
              #durationMs: durationMs,
              #screenClass: screenClass,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> logOrderCreated(String? orderId, double? amount) =>
      (super.noSuchMethod(
            Invocation.method(#logOrderCreated, [orderId, amount]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> logCustomerInteraction(
    String? customerId,
    String? interactionType,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#logCustomerInteraction, [
              customerId,
              interactionType,
            ]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> logError({
    required String? errorType,
    required String? errorMessage,
    String? screenName,
    Map<String, Object>? additionalParams = const {},
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logError, [], {
              #errorType: errorType,
              #errorMessage: errorMessage,
              #screenName: screenName,
              #additionalParams: additionalParams,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> logUserEngagement(int? durationMs) =>
      (super.noSuchMethod(
            Invocation.method(#logUserEngagement, [durationMs]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> logSessionStart() =>
      (super.noSuchMethod(
            Invocation.method(#logSessionStart, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> logUserInteraction({
    required String? screenName,
    required String? actionName,
    required String? elementType,
    String? elementId,
    Map<String, Object>? additionalParams = const {},
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logUserInteraction, [], {
              #screenName: screenName,
              #actionName: actionName,
              #elementType: elementType,
              #elementId: elementId,
              #additionalParams: additionalParams,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> logUserFlow({
    required String? flowName,
    required String? stepName,
    required String? status,
    Map<String, Object>? additionalParams = const {},
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logUserFlow, [], {
              #flowName: flowName,
              #stepName: stepName,
              #status: status,
              #additionalParams: additionalParams,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> updateUserBehaviorProperties() =>
      (super.noSuchMethod(
            Invocation.method(#updateUserBehaviorProperties, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);
}
