import 'dart:io';

void main() {
  final file = File('coverage/lcov.info');
  if (!file.existsSync()) {
    print('Coverage file not found');
    return;
  }

  final lines = file.readAsLinesSync();
  int totalLines = 0;
  int hitLines = 0;
  int totalFiles = 0;
  Map<String, Map<String, int>> fileStats = {};

  String? currentFile;
  
  for (final line in lines) {
    if (line.startsWith('SF:')) {
      currentFile = line.substring(3);
      totalFiles++;
      fileStats[currentFile] = {'LF': 0, 'LH': 0};
    } else if (line.startsWith('LF:')) {
      final lf = int.parse(line.substring(3));
      totalLines += lf;
      if (currentFile != null) {
        fileStats[currentFile]!['LF'] = lf;
      }
    } else if (line.startsWith('LH:')) {
      final lh = int.parse(line.substring(3));
      hitLines += lh;
      if (currentFile != null) {
        fileStats[currentFile]!['LH'] = lh;
      }
    }
  }

  final coverage = totalLines > 0 ? (hitLines / totalLines * 100) : 0.0;
  
  print('=== FLUTTER APP TEST COVERAGE ANALYSIS ===');
  print('');
  print('📊 OVERALL COVERAGE SUMMARY:');
  print('   Total Files: $totalFiles');
  print('   Total Lines: $totalLines');
  print('   Hit Lines: $hitLines');
  print('   Coverage: ${coverage.toStringAsFixed(2)}%');
  print('');
  
  // Categorize by coverage level
  final excellent = <String>[];
  final good = <String>[];
  final fair = <String>[];
  final poor = <String>[];
  final uncovered = <String>[];
  
  fileStats.forEach((file, stats) {
    final lf = stats['LF']!;
    final lh = stats['LH']!;
    final fileCoverage = lf > 0 ? (lh / lf * 100) : 0.0;
    
    if (fileCoverage >= 90) {
      excellent.add('$file (${fileCoverage.toStringAsFixed(1)}%)');
    } else if (fileCoverage >= 80) {
      good.add('$file (${fileCoverage.toStringAsFixed(1)}%)');
    } else if (fileCoverage >= 70) {
      fair.add('$file (${fileCoverage.toStringAsFixed(1)}%)');
    } else if (fileCoverage > 0) {
      poor.add('$file (${fileCoverage.toStringAsFixed(1)}%)');
    } else {
      uncovered.add('$file (0.0%)');
    }
  });
  
  print('🎯 COVERAGE BY QUALITY LEVEL:');
  print('');
  print('✅ EXCELLENT (90%+): ${excellent.length} files');
  excellent.take(5).forEach((f) => print('   $f'));
  if (excellent.length > 5) print('   ... and ${excellent.length - 5} more');
  print('');
  
  print('🟡 GOOD (80-89%): ${good.length} files');
  good.take(5).forEach((f) => print('   $f'));
  if (good.length > 5) print('   ... and ${good.length - 5} more');
  print('');
  
  print('🟠 FAIR (70-79%): ${fair.length} files');
  fair.take(5).forEach((f) => print('   $f'));
  if (fair.length > 5) print('   ... and ${fair.length - 5} more');
  print('');
  
  print('🔴 POOR (<70%): ${poor.length} files');
  poor.take(5).forEach((f) => print('   $f'));
  if (poor.length > 5) print('   ... and ${poor.length - 5} more');
  print('');
  
  print('⚫ UNCOVERED (0%): ${uncovered.length} files');
  uncovered.take(5).forEach((f) => print('   $f'));
  if (uncovered.length > 5) print('   ... and ${uncovered.length - 5} more');
  print('');
  
  // Production readiness assessment
  print('🚀 PRODUCTION READINESS ASSESSMENT:');
  print('');
  
  if (coverage >= 70) {
    print('✅ READY FOR PRODUCTION (${coverage.toStringAsFixed(1)}% >= 70%)');
  } else if (coverage >= 50) {
    print('🟡 APPROACHING PRODUCTION READY (${coverage.toStringAsFixed(1)}% >= 50%)');
  } else if (coverage >= 30) {
    print('🟠 FOUNDATION ESTABLISHED (${coverage.toStringAsFixed(1)}% >= 30%)');
  } else {
    print('🔴 NEEDS SIGNIFICANT WORK (${coverage.toStringAsFixed(1)}% < 30%)');
  }
  
  print('');
  print('📋 NEXT STEPS:');
  if (coverage < 30) {
    print('   1. Focus on data layer tests (repositories, models)');
    print('   2. Add basic cubit/bloc tests');
    print('   3. Implement core service tests');
  } else if (coverage < 50) {
    print('   1. Expand domain layer tests (use cases, services)');
    print('   2. Add more comprehensive cubit tests');
    print('   3. Include edge case testing');
  } else if (coverage < 70) {
    print('   1. Complete presentation layer tests (screens, widgets)');
    print('   2. Add integration tests');
    print('   3. Enhance error handling tests');
  } else {
    print('   1. Set up CI/CD pipeline');
    print('   2. Configure merge blocking on test failures');
    print('   3. Add performance and accessibility tests');
  }
}
