import 'package:dartz/dartz.dart';

import '../../core/error/failures.dart';
import '../../core/utils/logger.dart';
import '../entities/interested_product.dart';
import '../entities/product_catalogue.dart';
import '../repositories/interested_product_repository.dart';
import '../repositories/product_catalogue_repository.dart';

class GetProductCataloguesUseCase {
  final ProductCatalogueRepository repository;
  final AppLogger logger;

  GetProductCataloguesUseCase({required this.repository, required this.logger});

  Future<Either<Failure, List<ProductCatalogue>>> call() async {
    logger.i("GetProductCataloguesUseCase: Getting product catalogues");
    return await repository.getProductCatalogues();
  }
}

class SyncProductCataloguesUseCase {
  final ProductCatalogueRepository repository;
  final AppLogger logger;

  SyncProductCataloguesUseCase({
    required this.repository,
    required this.logger,
  });

  Future<Either<Failure, List<ProductCatalogue>>> call() async {
    logger.i("SyncProductCataloguesUseCase: Syncing product catalogues");
    return await repository.syncProductCatalogues();
  }
}

class GetProductCatalogueByNameUseCase {
  final ProductCatalogueRepository repository;
  final AppLogger logger;

  GetProductCatalogueByNameUseCase({
    required this.repository,
    required this.logger,
  });

  Future<Either<Failure, ProductCatalogue?>> call(String name) async {
    logger.i(
      "GetProductCatalogueByNameUseCase: Getting product catalogue by name: $name",
    );
    return await repository.getProductCatalogueByName(name);
  }
}

class GetInterestedProductsUseCase {
  final InterestedProductRepository repository;
  final AppLogger logger;

  GetInterestedProductsUseCase({
    required this.repository,
    required this.logger,
  });

  Future<Either<Failure, List<InterestedProduct>>> call() async {
    logger.i("GetInterestedProductsUseCase: Getting interested products");
    return await repository.getInterestedProducts();
  }
}
