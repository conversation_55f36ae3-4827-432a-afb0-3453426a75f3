import 'package:aquapartner/domain/entities/product.dart';
import 'package:aquapartner/presentation/screens/product_details_screen.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../helpers/test_helpers.dart';

void main() {
  group('ProductDetailsScreen Tests', () {
    late Product testProduct;

    setUpAll(() {
      TestHelpers.setupGetItServices();
    });

    tearDownAll(() {
      TestHelpers.cleanupGetItServices();
    });

    setUp(() {
      testProduct = Product(
        productName: 'Test Product',
        category: 'Test Category',
        categoryType: 'Type A',
        subCategory: 'Sub Category',
        tagLine: 'Test tagline',
        productTag: 'NEW ARRIVAL',
        productImage: 'https://example.com/image.jpg',
        content: '<p>Test product content with detailed description</p>',
        sortOrder: '1',
        status: 'active',
      );
    });

    // Note: ProductDetailsScreen uses WebView which requires platform-specific setup
    // These tests focus on basic widget instantiation and structure
    group('Widget Creation', () {
      test('should create ProductDetailsScreen widget', () {
        // Test that the widget can be instantiated without errors
        final widget = ProductDetailsScreen(productDetails: testProduct);
        expect(widget, isA<ProductDetailsScreen>());
        expect(widget.productDetails, equals(testProduct));
      });

      test('should have correct product details', () {
        final widget = ProductDetailsScreen(productDetails: testProduct);
        expect(widget.productDetails.productName, equals('Test Product'));
        expect(widget.productDetails.category, equals('Test Category'));
        expect(widget.productDetails.productTag, equals('NEW ARRIVAL'));
      });
    });

    group('Product Entity Tests', () {
      test('should have correct product properties', () {
        expect(testProduct.productName, equals('Test Product'));
        expect(testProduct.category, equals('Test Category'));
        expect(testProduct.categoryType, equals('Type A'));
        expect(testProduct.subCategory, equals('Sub Category'));
        expect(testProduct.tagLine, equals('Test tagline'));
        expect(testProduct.productTag, equals('NEW ARRIVAL'));
        expect(
          testProduct.productImage,
          equals('https://example.com/image.jpg'),
        );
        expect(testProduct.content, contains('Test product content'));
        expect(testProduct.sortOrder, equals('1'));
        expect(testProduct.status, equals('active'));
      });

      test('should create product with empty content', () {
        final emptyProduct = Product(
          productName: 'Empty Product',
          category: '',
          categoryType: '',
          subCategory: '',
          tagLine: '',
          productTag: '',
          productImage: '',
          content: '',
          sortOrder: '1',
          status: 'active',
        );

        expect(emptyProduct.productName, equals('Empty Product'));
        expect(emptyProduct.content, isEmpty);
      });

      test('should support product equality', () {
        final product1 = Product(
          productName: 'Test Product',
          category: 'Test Category',
          categoryType: 'Type A',
          subCategory: 'Sub Category',
          tagLine: 'Test tagline',
          productTag: 'NEW ARRIVAL',
          productImage: 'https://example.com/image.jpg',
          content: '<p>Test product content</p>',
          sortOrder: '1',
          status: 'active',
        );

        final product2 = Product(
          productName: 'Test Product',
          category: 'Test Category',
          categoryType: 'Type A',
          subCategory: 'Sub Category',
          tagLine: 'Test tagline',
          productTag: 'NEW ARRIVAL',
          productImage: 'https://example.com/image.jpg',
          content: '<p>Test product content</p>',
          sortOrder: '1',
          status: 'active',
        );

        expect(product1, equals(product2));
        expect(product1.hashCode, equals(product2.hashCode));
      });
    });

    group('Widget Properties', () {
      test('should validate required constructor parameters', () {
        expect(
          () => ProductDetailsScreen(productDetails: testProduct),
          returnsNormally,
        );
      });

      test('should handle product with HTML content', () {
        final productWithHtml = Product(
          productName: 'HTML Product',
          category: 'Test',
          categoryType: 'Type',
          subCategory: 'Sub',
          tagLine: 'Tag',
          productTag: 'TAG',
          productImage: 'image.jpg',
          content: '<h1>Title</h1><p>Description with <b>bold</b> text</p>',
          sortOrder: '1',
          status: 'active',
        );

        final widget = ProductDetailsScreen(productDetails: productWithHtml);
        expect(widget.productDetails.content, contains('<h1>'));
        expect(widget.productDetails.content, contains('<b>bold</b>'));
      });

      test('should handle product with special characters', () {
        final productWithSpecialChars = Product(
          productName: 'Product with quotes & symbols',
          category: 'Category & More',
          categoryType: 'Type',
          subCategory: 'Sub',
          tagLine: 'Tag with symbols',
          productTag: 'SPECIAL',
          productImage: 'image.jpg',
          content: 'Content with special chars',
          sortOrder: '1',
          status: 'active',
        );

        final widget = ProductDetailsScreen(
          productDetails: productWithSpecialChars,
        );
        expect(widget.productDetails.productName, contains('quotes'));
        expect(widget.productDetails.tagLine, contains('symbols'));
      });
    });
  });
}
