import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../core/services/analytics_service.dart';
import 'billing_and_payments_state.dart';

class BillingAndPaymentsPageCubit extends Cubit<BillingAndPaymentsPageState> {
  final AnalyticsService _analyticsService;
  DateTime? _subPageStartTime;
  String? _currentSubPage;

  BillingAndPaymentsPageCubit(this._analyticsService)
    : super(BillingAndPaymentsPageState.initial());

  void selectPage(String page) {
    // Log time spent on previous sub-page
    _logSubPageDuration();

    // Set new sub-page start time
    _subPageStartTime = DateTime.now();
    _currentSubPage = page;

    emit(state.copyWith(selectedPage: page));
  }

  void _logSubPageDuration() {
    if (_subPageStartTime != null && _currentSubPage != null) {
      final duration = DateTime.now().difference(_subPageStartTime!);

      _analyticsService.logEvent(
        name: 'billing_subpage_duration',
        parameters: {
          'sub_page': _currentSubPage!,
          'duration_ms': duration.inMilliseconds,
          'screen_name': 'BillingAndPaymentsScreen',
          'timestamp': DateTime.now().toIso8601String(),
        },
      );
    }
  }

  @override
  Future<void> close() {
    // Log duration when widget is disposed
    _logSubPageDuration();
    return super.close();
  }
}
