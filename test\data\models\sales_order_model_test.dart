import 'package:flutter_test/flutter_test.dart';
import 'package:aquapartner/data/models/sales_order/sales_order_model.dart';
import 'package:aquapartner/domain/entities/sales_order/sales_order.dart';
import 'package:aquapartner/domain/entities/sales_order/sales_order_item.dart';

void main() {
  group('SalesOrderModel Tests', () {
    late SalesOrderModel testSalesOrderModel;
    late Map<String, dynamic> testJson;
    late DateTime testDateTime;

    setUp(() {
      testDateTime = DateTime(2024, 1, 15, 10, 30, 0);
      testSalesOrderModel = SalesOrderModel(
        salesOrderId: 'so_123',
        addressId: 'addr_123',
        createdTime: testDateTime,
        customerId: 'customer_123',
        invoicedStatus: 'pending',
        lastModifiedTime: testDateTime,
        orderSource: 'mobile_app',
        paidStatus: 'unpaid',
        paymentTermsLabel: 'Net 30',
        saleOrderDate: '2024-01-15',
        salesChannel: 'direct',
        salesOrderNumber: 'SO-2024-001',
        subTotal: 1800.0,
        total: 1980.0,
        isSynced: true,
        lastSyncedAt: testDateTime,
      );

      testJson = {
        'salesOrderId': 'so_123',
        'addressId': 'addr_123',
        'createdTime': testDateTime.toIso8601String(),
        'customerId': 'customer_123',
        'invoicedStatus': 'pending',
        'lastModifiedTime': testDateTime.toIso8601String(),
        'orderSource': 'mobile_app',
        'paidStatus': 'unpaid',
        'paymentTermsLabel': 'Net 30',
        'saleOrderDate': '2024-01-15',
        'salesChannel': 'direct',
        'salesOrderNumber': 'SO-2024-001',
        'subTotal': 1800.0,
        'total': 1980.0,
        'isSynced': true,
        'lastSyncedAt': testDateTime.toIso8601String(),
        'items': [],
      };
    });

    group('Constructor Tests', () {
      test('should create SalesOrderModel with all required fields', () {
        expect(testSalesOrderModel.salesOrderId, equals('so_123'));
        expect(testSalesOrderModel.addressId, equals('addr_123'));
        expect(testSalesOrderModel.createdTime, equals(testDateTime));
        expect(testSalesOrderModel.customerId, equals('customer_123'));
        expect(testSalesOrderModel.invoicedStatus, equals('pending'));
        expect(testSalesOrderModel.lastModifiedTime, equals(testDateTime));
        expect(testSalesOrderModel.orderSource, equals('mobile_app'));
        expect(testSalesOrderModel.paidStatus, equals('unpaid'));
        expect(testSalesOrderModel.paymentTermsLabel, equals('Net 30'));
        expect(testSalesOrderModel.saleOrderDate, equals('2024-01-15'));
        expect(testSalesOrderModel.salesChannel, equals('direct'));
        expect(testSalesOrderModel.salesOrderNumber, equals('SO-2024-001'));
        expect(testSalesOrderModel.subTotal, equals(1800.0));
        expect(testSalesOrderModel.total, equals(1980.0));
        expect(testSalesOrderModel.isSynced, isTrue);
        expect(testSalesOrderModel.lastSyncedAt, equals(testDateTime));
      });

      test('should create SalesOrderModel with default values', () {
        final defaultModel = SalesOrderModel(
          salesOrderId: '',
          addressId: '',
          createdTime: testDateTime,
          customerId: '',
          invoicedStatus: '',
          lastModifiedTime: testDateTime,
          orderSource: '',
          paidStatus: '',
          paymentTermsLabel: '',
          saleOrderDate: '',
          salesChannel: '',
          salesOrderNumber: '',
          subTotal: 0.0,
          total: 0.0,
        );

        expect(defaultModel.salesOrderId, isEmpty);
        expect(defaultModel.addressId, isEmpty);
        expect(defaultModel.customerId, isEmpty);
        expect(defaultModel.invoicedStatus, isEmpty);
        expect(defaultModel.orderSource, isEmpty);
        expect(defaultModel.paidStatus, isEmpty);
        expect(defaultModel.paymentTermsLabel, isEmpty);
        expect(defaultModel.saleOrderDate, isEmpty);
        expect(defaultModel.salesChannel, isEmpty);
        expect(defaultModel.salesOrderNumber, isEmpty);
        expect(defaultModel.subTotal, equals(0.0));
        expect(defaultModel.total, equals(0.0));
        expect(defaultModel.isSynced, isFalse);
        expect(defaultModel.lastSyncedAt, isNull);
      });
    });

    group('JSON Serialization Tests', () {
      test('should convert SalesOrderModel to JSON correctly', () {
        final json = testSalesOrderModel.toJson();

        expect(json['salesOrderId'], equals('so_123'));
        expect(json['addressId'], equals('addr_123'));
        expect(json['createdTime'], equals(testDateTime.toIso8601String()));
        expect(json['customerId'], equals('customer_123'));
        expect(json['invoicedStatus'], equals('pending'));
        expect(
          json['lastModifiedTime'],
          equals(testDateTime.toIso8601String()),
        );
        expect(json['orderSource'], equals('mobile_app'));
        expect(json['paidStatus'], equals('unpaid'));
        expect(json['paymentTermsLabel'], equals('Net 30'));
        expect(json['saleOrderDate'], equals('2024-01-15'));
        expect(json['salesChannel'], equals('direct'));
        expect(json['salesOrderNumber'], equals('SO-2024-001'));
        expect(json['subTotal'], equals(1800.0));
        expect(json['total'], equals(1980.0));
        expect(json['isSynced'], isTrue);
        expect(json['lastSyncedAt'], equals(testDateTime.toIso8601String()));
        expect(json['items'], isA<List>());
      });

      test('should create SalesOrderModel from JSON correctly', () {
        final salesOrderModel = SalesOrderModel.fromJson(testJson);

        expect(salesOrderModel.salesOrderId, equals('so_123'));
        expect(salesOrderModel.addressId, equals('addr_123'));
        expect(salesOrderModel.createdTime, equals(testDateTime));
        expect(salesOrderModel.customerId, equals('customer_123'));
        expect(salesOrderModel.invoicedStatus, equals('pending'));
        expect(salesOrderModel.lastModifiedTime, equals(testDateTime));
        expect(salesOrderModel.orderSource, equals('mobile_app'));
        expect(salesOrderModel.paidStatus, equals('unpaid'));
        expect(salesOrderModel.paymentTermsLabel, equals('Net 30'));
        expect(salesOrderModel.saleOrderDate, equals('2024-01-15'));
        expect(salesOrderModel.salesChannel, equals('direct'));
        expect(salesOrderModel.salesOrderNumber, equals('SO-2024-001'));
        expect(salesOrderModel.subTotal, equals(1800.0));
        expect(salesOrderModel.total, equals(1980.0));
        expect(salesOrderModel.isSynced, isTrue);
        expect(salesOrderModel.lastSyncedAt, equals(testDateTime));
      });

      test('should handle missing JSON fields with defaults', () {
        final incompleteJson = <String, dynamic>{
          'salesOrderId': 'so_123',
          'customerId': 'customer_123',
        };

        final salesOrderModel = SalesOrderModel.fromJson(incompleteJson);

        expect(salesOrderModel.salesOrderId, equals('so_123'));
        expect(salesOrderModel.customerId, equals('customer_123'));
        expect(salesOrderModel.addressId, isEmpty);
        expect(salesOrderModel.invoicedStatus, isEmpty);
        expect(salesOrderModel.orderSource, isEmpty);
        expect(salesOrderModel.paidStatus, isEmpty);
        expect(salesOrderModel.paymentTermsLabel, isEmpty);
        expect(salesOrderModel.saleOrderDate, isEmpty);
        expect(salesOrderModel.salesChannel, isEmpty);
        expect(salesOrderModel.salesOrderNumber, isEmpty);
        expect(salesOrderModel.subTotal, equals(0.0));
        expect(salesOrderModel.total, equals(0.0));
        expect(salesOrderModel.isSynced, isFalse);
        expect(salesOrderModel.lastSyncedAt, isNull);
      });

      test('should handle _id field as salesOrderId', () {
        final jsonWithId = <String, dynamic>{
          '_id': 'so_from_id',
          'customerId': 'customer_123',
        };

        final salesOrderModel = SalesOrderModel.fromJson(jsonWithId);

        expect(salesOrderModel.salesOrderId, equals('so_from_id'));
        expect(salesOrderModel.customerId, equals('customer_123'));
      });

      test('should handle null date fields gracefully', () {
        final jsonWithNullDates = <String, dynamic>{
          'salesOrderId': 'so_123',
          'customerId': 'customer_123',
          'createdTime': null,
          'lastModifiedTime': null,
          'lastSyncedAt': null,
        };

        final salesOrderModel = SalesOrderModel.fromJson(jsonWithNullDates);

        expect(salesOrderModel.salesOrderId, equals('so_123'));
        expect(salesOrderModel.customerId, equals('customer_123'));
        expect(salesOrderModel.createdTime, isA<DateTime>());
        expect(salesOrderModel.lastModifiedTime, isA<DateTime>());
        expect(salesOrderModel.lastSyncedAt, isNull);
      });
    });

    group('Entity Conversion Tests', () {
      test('should convert SalesOrderModel to SalesOrder entity correctly', () {
        final salesOrderEntity = testSalesOrderModel.toEntity();

        expect(salesOrderEntity, isA<SalesOrder>());
        expect(
          salesOrderEntity.salesOrderId,
          equals(testSalesOrderModel.salesOrderId),
        );
        expect(
          salesOrderEntity.addressId,
          equals(testSalesOrderModel.addressId),
        );
        expect(
          salesOrderEntity.createdTime,
          equals(testSalesOrderModel.createdTime),
        );
        expect(
          salesOrderEntity.customerId,
          equals(testSalesOrderModel.customerId),
        );
        expect(
          salesOrderEntity.invoicedStatus,
          equals(testSalesOrderModel.invoicedStatus),
        );
        expect(
          salesOrderEntity.lastModifiedTime,
          equals(testSalesOrderModel.lastModifiedTime),
        );
        expect(
          salesOrderEntity.orderSource,
          equals(testSalesOrderModel.orderSource),
        );
        expect(
          salesOrderEntity.paidStatus,
          equals(testSalesOrderModel.paidStatus),
        );
        expect(
          salesOrderEntity.paymentTermsLabel,
          equals(testSalesOrderModel.paymentTermsLabel),
        );
        expect(
          salesOrderEntity.saleOrderDate,
          equals(testSalesOrderModel.saleOrderDate),
        );
        expect(
          salesOrderEntity.salesChannel,
          equals(testSalesOrderModel.salesChannel),
        );
        expect(
          salesOrderEntity.salesOrderNumber,
          equals(testSalesOrderModel.salesOrderNumber),
        );
        expect(salesOrderEntity.subTotal, equals(testSalesOrderModel.subTotal));
        expect(salesOrderEntity.total, equals(testSalesOrderModel.total));
        expect(salesOrderEntity.isSynced, equals(testSalesOrderModel.isSynced));
        expect(
          salesOrderEntity.lastSyncedAt,
          equals(testSalesOrderModel.lastSyncedAt),
        );
        expect(salesOrderEntity.items, isA<List<SalesOrderItem>>());
      });

      test(
        'should create SalesOrderModel from SalesOrder entity correctly',
        () {
          final salesOrderEntity = SalesOrder(
            salesOrderId: 'so_entity_123',
            addressId: 'addr_entity_123',
            createdTime: testDateTime,
            customerId: 'customer_entity_123',
            invoicedStatus: 'invoiced',
            lastModifiedTime: testDateTime,
            orderSource: 'web',
            paidStatus: 'paid',
            paymentTermsLabel: 'Net 15',
            saleOrderDate: '2024-01-16',
            salesChannel: 'online',
            salesOrderNumber: 'SO-2024-002',
            subTotal: 2000.0,
            total: 2200.0,
            isSynced: false,
            lastSyncedAt: null,
            items: [],
          );

          final salesOrderModel = SalesOrderModel.fromEntity(salesOrderEntity);

          expect(salesOrderModel.salesOrderId, equals('so_entity_123'));
          expect(salesOrderModel.addressId, equals('addr_entity_123'));
          expect(salesOrderModel.createdTime, equals(testDateTime));
          expect(salesOrderModel.customerId, equals('customer_entity_123'));
          expect(salesOrderModel.invoicedStatus, equals('invoiced'));
          expect(salesOrderModel.lastModifiedTime, equals(testDateTime));
          expect(salesOrderModel.orderSource, equals('web'));
          expect(salesOrderModel.paidStatus, equals('paid'));
          expect(salesOrderModel.paymentTermsLabel, equals('Net 15'));
          expect(salesOrderModel.saleOrderDate, equals('2024-01-16'));
          expect(salesOrderModel.salesChannel, equals('online'));
          expect(salesOrderModel.salesOrderNumber, equals('SO-2024-002'));
          expect(salesOrderModel.subTotal, equals(2000.0));
          expect(salesOrderModel.total, equals(2200.0));
          expect(salesOrderModel.isSynced, isFalse);
          expect(salesOrderModel.lastSyncedAt, isNull);
        },
      );

      test('should maintain data integrity during entity conversion', () {
        final originalJson = testSalesOrderModel.toJson();
        final convertedEntity = testSalesOrderModel.toEntity();
        final backToModel = SalesOrderModel.fromEntity(convertedEntity);
        final finalJson = backToModel.toJson();

        expect(finalJson['salesOrderId'], equals(originalJson['salesOrderId']));
        expect(finalJson['customerId'], equals(originalJson['customerId']));
        expect(finalJson['total'], equals(originalJson['total']));
        expect(finalJson['subTotal'], equals(originalJson['subTotal']));
        expect(finalJson['isSynced'], equals(originalJson['isSynced']));
      });
    });

    group('Edge Cases and Validation Tests', () {
      test('should handle special characters in string fields', () {
        final specialCharJson = {
          'salesOrderId': 'so_123 & special',
          'addressId': 'addr_123 (special)',
          'customerId': 'customer_123-special',
          'invoicedStatus': 'pending/partial',
          'orderSource': 'mobile_app & web',
          'paidStatus': 'unpaid/partial',
          'paymentTermsLabel': 'Net 30 (special terms)',
          'saleOrderDate': '2024-01-15',
          'salesChannel': 'direct & online',
          'salesOrderNumber': 'SO-2024-001 (revised)',
          'subTotal': 1800.0,
          'total': 1980.0,
          'createdTime': testDateTime.toIso8601String(),
          'lastModifiedTime': testDateTime.toIso8601String(),
        };

        final salesOrderModel = SalesOrderModel.fromJson(specialCharJson);

        expect(salesOrderModel.salesOrderId, equals('so_123 & special'));
        expect(salesOrderModel.addressId, equals('addr_123 (special)'));
        expect(salesOrderModel.customerId, equals('customer_123-special'));
        expect(salesOrderModel.invoicedStatus, equals('pending/partial'));
        expect(salesOrderModel.orderSource, equals('mobile_app & web'));
        expect(salesOrderModel.paidStatus, equals('unpaid/partial'));
        expect(
          salesOrderModel.paymentTermsLabel,
          equals('Net 30 (special terms)'),
        );
        expect(salesOrderModel.salesChannel, equals('direct & online'));
        expect(
          salesOrderModel.salesOrderNumber,
          equals('SO-2024-001 (revised)'),
        );
      });

      test('should handle round-trip JSON serialization', () {
        final originalJson = testSalesOrderModel.toJson();
        final recreatedModel = SalesOrderModel.fromJson(originalJson);
        final finalJson = recreatedModel.toJson();

        expect(finalJson['salesOrderId'], equals(originalJson['salesOrderId']));
        expect(finalJson['customerId'], equals(originalJson['customerId']));
        expect(finalJson['total'], equals(originalJson['total']));
        expect(finalJson['subTotal'], equals(originalJson['subTotal']));
        expect(finalJson['createdTime'], equals(originalJson['createdTime']));
        expect(
          finalJson['lastModifiedTime'],
          equals(originalJson['lastModifiedTime']),
        );
        expect(finalJson['lastSyncedAt'], equals(originalJson['lastSyncedAt']));
      });

      test('should handle extreme numeric values', () {
        final extremeJson = {
          'salesOrderId': 'so_123',
          'customerId': 'customer_123',
          'subTotal': double.maxFinite,
          'total': double.maxFinite,
          'createdTime': testDateTime.toIso8601String(),
          'lastModifiedTime': testDateTime.toIso8601String(),
        };

        final salesOrderModel = SalesOrderModel.fromJson(extremeJson);

        expect(salesOrderModel.subTotal, equals(double.maxFinite));
        expect(salesOrderModel.total, equals(double.maxFinite));
      });

      test('should handle negative numeric values', () {
        final negativeJson = {
          'salesOrderId': 'so_123',
          'customerId': 'customer_123',
          'subTotal': -1000.0,
          'total': -1100.0,
          'createdTime': testDateTime.toIso8601String(),
          'lastModifiedTime': testDateTime.toIso8601String(),
        };

        final salesOrderModel = SalesOrderModel.fromJson(negativeJson);

        expect(salesOrderModel.subTotal, equals(-1000.0));
        expect(salesOrderModel.total, equals(-1100.0));
      });
    });
  });
}
