import 'package:flutter_test/flutter_test.dart';
import 'package:aquapartner/data/models/user_model.dart';
import 'package:aquapartner/data/models/dashboard/dashboard_model.dart';
import 'package:aquapartner/data/models/objectbox_customer_model.dart';

void main() {
  group('ObjectBox Configuration Tests', () {
    setUpAll(() async {
      TestWidgetsFlutterBinding.ensureInitialized();
    });

    group('Model Creation Tests', () {
      test('should create UserModel successfully', () {
        // Arrange & Act
        final userModel = UserModel(
          phoneNumber: '+************',
          isVerified: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        // Assert
        expect(userModel, isA<UserModel>());
        expect(userModel.phoneNumber, equals('+************'));
        expect(userModel.isVerified, isTrue);
        expect(userModel.createdAt, isA<DateTime>());
        expect(userModel.updatedAt, isA<DateTime>());
      });

      test('should create DashboardModel successfully', () {
        // Arrange & Act
        final dashboardModel = DashboardModel(
          id: 0,
          customerId: 'customer_123',
          salesJson: '{"test": "data"}',
          paymentsJson: '{"test": "data"}',
          duesJson: '[]',
          salesReturnJson: 100.0,
          categoryTypeSalesJson: '[]',
          liquidationJson: '{}',
          myFarmersJson: '{}',
          isSynced: true,
          lastSyncedAt: DateTime.now(),
        );

        // Assert
        expect(dashboardModel, isA<DashboardModel>());
        expect(dashboardModel.customerId, equals('customer_123'));
        expect(dashboardModel.salesReturnJson, equals(100.0));
        expect(dashboardModel.isSynced, isTrue);
      });

      test('should create ObjectBoxCustomerModel successfully', () {
        // Arrange & Act
        final customerModel = ObjectBoxCustomerModel(
          customerId: 'customer_123',
          customerName: 'John Doe',
          email: '<EMAIL>',
          mobileNumber: '+************',
          companyName: 'Aqua Farms Ltd',
          gstNo: '29ABCDE1234F1Z5',
          businessVertical: 'Aquaculture',
          customerCode: 'CUST001',
          billingAddress: '123 Main St, City, State 12345',
          isSynced: true,
        );

        // Assert
        expect(customerModel, isA<ObjectBoxCustomerModel>());
        expect(customerModel.customerName, equals('John Doe'));
        expect(customerModel.email, equals('<EMAIL>'));
        expect(customerModel.isSynced, isTrue);
      });
    });

    group('Model Validation Tests', () {
      test('should validate UserModel fields correctly', () {
        // Arrange & Act
        final userModel = UserModel(
          phoneNumber: '+************',
          isVerified: false,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        // Assert
        expect(userModel.phoneNumber.startsWith('+91'), isTrue);
        expect(userModel.phoneNumber.length, equals(13));
        expect(userModel.isVerified, isFalse);
      });

      test('should handle UserModel copyWith correctly', () {
        // Arrange
        final originalUser = UserModel(
          phoneNumber: '+************',
          isVerified: false,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        // Act
        final updatedUser = originalUser.copyWith(isVerified: true);

        // Assert
        expect(updatedUser.phoneNumber, equals(originalUser.phoneNumber));
        expect(updatedUser.isVerified, isTrue);
        expect(updatedUser.createdAt, equals(originalUser.createdAt));
        // The updatedAt field should be the same unless explicitly changed
        expect(updatedUser.updatedAt, equals(originalUser.updatedAt));
      });

      test('should validate DashboardModel JSON fields', () {
        // Arrange & Act
        final dashboardModel = DashboardModel(
          id: 0,
          customerId: 'customer_123',
          salesJson: '{"total": 1000}',
          paymentsJson: '{"received": 800}',
          duesJson: '[{"amount": 200}]',
          salesReturnJson: 50.0,
          categoryTypeSalesJson: '[{"category": "feed"}]',
          liquidationJson: '{"status": "pending"}',
          myFarmersJson: '{"count": 5}',
          isSynced: false,
          lastSyncedAt: DateTime.now(),
        );

        // Assert
        expect(dashboardModel.salesJson.contains('total'), isTrue);
        expect(dashboardModel.paymentsJson.contains('received'), isTrue);
        expect(dashboardModel.duesJson.startsWith('['), isTrue);
        expect(dashboardModel.salesReturnJson, equals(50.0));
        expect(dashboardModel.isSynced, isFalse);
      });

      test('should validate ObjectBoxCustomerModel required fields', () {
        // Arrange & Act
        final customerModel = ObjectBoxCustomerModel(
          customerId: 'CUST_001',
          customerName: 'Test Customer',
          email: '<EMAIL>',
          mobileNumber: '+************',
          companyName: 'Test Company',
          gstNo: '29ABCDE1234F1Z5',
          businessVertical: 'Aquaculture',
          customerCode: 'TC001',
          billingAddress: '123 Test Street',
          isSynced: true,
        );

        // Assert
        expect(customerModel.customerId.isNotEmpty, isTrue);
        expect(customerModel.customerName.isNotEmpty, isTrue);
        expect(customerModel.email.contains('@'), isTrue);
        expect(customerModel.mobileNumber.startsWith('+91'), isTrue);
        expect(customerModel.gstNo.length, equals(15));
        expect(customerModel.isSynced, isTrue);
      });
    });

    group('Model Edge Cases Tests', () {
      test('should handle empty strings in DashboardModel', () {
        // Arrange & Act
        final dashboardModel = DashboardModel(
          id: 0,
          customerId: '',
          salesJson: '{}',
          paymentsJson: '{}',
          duesJson: '[]',
          salesReturnJson: 0.0,
          categoryTypeSalesJson: '[]',
          liquidationJson: '{}',
          myFarmersJson: '{}',
          isSynced: false,
          lastSyncedAt: DateTime.now(),
        );

        // Assert
        expect(dashboardModel.customerId, isEmpty);
        expect(dashboardModel.salesReturnJson, equals(0.0));
        expect(dashboardModel.isSynced, isFalse);
      });

      test('should handle special characters in customer data', () {
        // Arrange & Act
        final customerModel = ObjectBoxCustomerModel(
          customerId: 'CUST@#123',
          customerName: 'Test & Co. Ltd.',
          email: '<EMAIL>',
          mobileNumber: '+91-9876-543-210',
          companyName: 'Test & Associates Pvt. Ltd.',
          gstNo: '29ABCDE1234F1Z5',
          businessVertical: 'Aqua & Marine',
          customerCode: 'TC@001',
          billingAddress: '123, Test Street, City - 123456',
          isSynced: false,
        );

        // Assert
        expect(customerModel.customerId.contains('@'), isTrue);
        expect(customerModel.customerName.contains('&'), isTrue);
        expect(customerModel.email.contains('+'), isTrue);
        expect(customerModel.billingAddress.contains(','), isTrue);
      });
    });
  });
}
