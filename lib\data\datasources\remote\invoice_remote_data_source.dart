import 'dart:convert';

import 'package:aquapartner/core/network/api_client.dart';

import '../../../core/constants/app_constants.dart';
import '../../../core/error/exceptions.dart';
import '../../../core/utils/logger.dart';
import '../../models/invoices/invoice_model.dart';

abstract class InvoiceRemoteDataSource {
  Future<List<InvoiceModel>> getInvoices(String customerId);
}

class InvoiceRemoteDataSourceImpl implements InvoiceRemoteDataSource {
  final ApiClient apiClient;
  final AppLogger logger;

  InvoiceRemoteDataSourceImpl({required this.apiClient, required this.logger});

  @override
  Future<List<InvoiceModel>> getInvoices(String customerId) async {
    try {
      final response = await apiClient.get(
        '${AppConstants.baseUrl}/invoices/$customerId',
      );

      if (response.statusCode == 200) {
        final jsonData = jsonDecode(response.data) as Map<String, dynamic>;
        if (jsonData['results'] != null && jsonData['results'] is List) {
          return (jsonData['results'] as List)
              .map((invoice) => InvoiceModel.fromJson(invoice))
              .toList();
        } else {
          // Return empty list instead of throwing exception
          logger.e('No invoice results found for customer: $customerId');
          return [];
        }
      } else if (response.statusCode == 404) {
        // If 404, it might mean no invoices for this customer
        logger.e('No invoices found (404) for customer: $customerId');
        return [];
      } else {
        throw ServerException();
      }
    } catch (e) {
      logger.e('Error fetching invoices: ${e.toString()}');
      throw ServerException();
    }
  }
}
