import 'package:flutter_test/flutter_test.dart';
import 'package:aquapartner/domain/entities/app_version.dart';

void main() {
  group('AppVersion', () {
    group('Constructor validation', () {
      test('should create valid version with three components', () {
        expect(() => AppVersion('1.0.0'), returnsNormally);
      });

      test('should create valid version with two components', () {
        expect(() => AppVersion('1.0'), returnsNormally);
      });

      test('should throw FormatException for invalid versions', () {
        expect(() => AppVersion(''), throwsFormatException);
        expect(() => AppVersion('1'), throwsFormatException);
        expect(() => AppVersion('1.0.0.0'), throwsFormatException);
        expect(() => AppVersion('1.a.0'), throwsFormatException);
        expect(() => AppVersion('1.0.b'), throwsFormatException);
        expect(() => AppVersion('invalid'), throwsFormatException);
      });
    });

    group('Version comparison', () {
      test('should correctly compare major versions', () {
        expect(AppVersion('1.0.0').isLowerThan(AppVersion('2.0.0')), true);
        expect(AppVersion('2.0.0').isLowerThan(AppVersion('1.0.0')), false);
      });

      test('should correctly compare minor versions', () {
        expect(AppVersion('1.1.0').isLowerThan(AppVersion('1.2.0')), true);
        expect(AppVersion('1.2.0').isLowerThan(AppVersion('1.1.0')), false);
      });

      test('should correctly compare patch versions', () {
        expect(AppVersion('1.0.1').isLowerThan(AppVersion('1.0.2')), true);
        expect(AppVersion('1.0.2').isLowerThan(AppVersion('1.0.1')), false);
      });

      test('should handle equal versions', () {
        expect(AppVersion('1.0.0').isLowerThan(AppVersion('1.0.0')), false);
        expect(AppVersion('2.1.0').isLowerThan(AppVersion('2.1.0')), false);
      });

      test('should handle missing patch version', () {
        expect(AppVersion('1.0').isLowerThan(AppVersion('1.0.1')), true);
        expect(AppVersion('1.0.1').isLowerThan(AppVersion('1.0')), false);
        expect(AppVersion('1.0').isLowerThan(AppVersion('1.0')), false);
      });
    });

    group('Object methods', () {
      test('toString should return version string', () {
        final version = AppVersion('1.2.3');
        expect(version.toString(), '1.2.3');
      });

      test('equals should work correctly', () {
        final version1 = AppVersion('1.0.0');
        final version2 = AppVersion('1.0.0');
        final version3 = AppVersion('1.0.1');

        expect(version1 == version2, true);
        expect(version1 == version3, false);
      });

      test('hashCode should be consistent', () {
        final version1 = AppVersion('1.0.0');
        final version2 = AppVersion('1.0.0');

        expect(version1.hashCode == version2.hashCode, true);
      });
    });
  });
}
