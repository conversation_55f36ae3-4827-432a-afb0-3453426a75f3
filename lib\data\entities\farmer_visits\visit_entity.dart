import 'package:objectbox/objectbox.dart';
import '../../../domain/entities/farmer_visit/visit.dart';
import 'farmer_entity.dart'; // Import farmer entity

@Entity()
class VisitEntity {
  @Id()
  int id = 0;
  int createdDateTimeMillis;
  int doc;
  String pondId;
  String mobileNumber;
  String productUsed;
  // Remove the String farmerId property as it's now represented by the ToOne relation
  String farmerName;

  // Define a ToOne relationship to the FarmerEntity
  // This replaces the String farmerId property
  final farmer = ToOne<FarmerEntity>();

  VisitEntity({
    required this.createdDateTimeMillis,
    required this.doc,
    required this.pondId,
    required this.mobileNumber,
    required this.productUsed,
    required this.farmerName,
  });

  // Factory constructor to create a VisitEntity from a Visit domain entity
  factory VisitEntity.fromDomain(Visit visit, {FarmerEntity? farmerEntity}) {
    VisitEntity entity = VisitEntity(
      createdDateTimeMillis: visit.createdDateTime.millisecondsSinceEpoch,
      doc: visit.doc,
      pondId: visit.pondId,
      mobileNumber: visit.mobileNumber,
      productUsed: visit.productUsed,
      farmerName: visit.farmerName,
    );

    // Set the ID if it exists in the domain entity
    if (visit.id != null) {
      entity.id = visit.id!;
    }

    // Set the farmer relationship if a farmer entity is provided
    if (farmerEntity != null) {
      entity.farmer.target = farmerEntity;
    }

    return entity;
  }

  // Method to convert a VisitEntity back to a Visit domain entity
  Visit toDomain() {
    return Visit(
      id: id,
      createdDateTime: DateTime.fromMillisecondsSinceEpoch(
        createdDateTimeMillis,
      ),
      doc: doc,
      pondId: pondId,
      mobileNumber: mobileNumber,
      productUsed: productUsed,
      farmerId:
          farmer.target?.id.toString() ?? "", // Get farmerId from the relation
      farmerName: farmerName,
    );
  }
}
