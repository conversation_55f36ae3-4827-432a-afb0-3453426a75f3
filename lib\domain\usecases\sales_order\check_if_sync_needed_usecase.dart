import 'package:dartz/dartz.dart';

import '../../../core/error/failures.dart';
import '../../repositories/sales_order_repository.dart';
import '../../../core/utils/logger.dart';

class CheckIfSalesOrdersSyncNeededUseCase {
  final SalesOrderRepository repository;
  final AppLogger logger;

  CheckIfSalesOrdersSyncNeededUseCase({
    required this.repository,
    required this.logger,
  });

  Future<Either<Failure, bool>> call(String customerId) async {
    logger.i(
      'CheckIfSalesOrdersSyncNeededUseCase called with customerId: $customerId',
    );
    return await repository.checkIfSyncNeeded(customerId);
  }
}
