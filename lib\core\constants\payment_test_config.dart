/// Configuration for testing Zoho payment API
class PaymentTestConfig {
  // Zoho Payments Test Environment URLs
  static const String sandboxBaseUrl = 'https://payments-sandbox.zoho.in';
  static const String productionBaseUrl = 'https://payments.zoho.in';
  
  // Test API endpoints (replace with your backend test endpoints)
  static const String testApiBaseUrl = 'https://staging-partner.aquaconnect.blue/api';
  static const String devApiBaseUrl = 'https://dev-partner.aquaconnect.blue/api';
  
  // Test credentials (DO NOT use in production)
  static const String testOrganizationId = 'test_org_123456789';
  static const String testApiKey = 'test_api_key_123456789';
  
  // Test payment amounts (in INR)
  static const double testAmountSmall = 1.00;
  static const double testAmountMedium = 100.00;
  static const double testAmountLarge = 1000.00;
  
  // Test customer data
  static const String testCustomerId = 'TEST_CUSTOMER_001';
  static const String testCustomerName = 'Test Customer';
  static const String testCustomerEmail = '<EMAIL>';
  static const String testCustomerPhone = '+919999999999';
  
  // Test invoice data
  static const String testInvoiceNumber = 'TEST_INV_001';
  static const String testInvoiceDescription = 'Test Payment for Invoice';
  
  // Test card details (Zoho sandbox test cards)
  static const Map<String, dynamic> testCardSuccess = {
    'number': '****************',
    'expiry_month': '12',
    'expiry_year': '2025',
    'cvv': '123',
    'name': 'Test User',
  };
  
  static const Map<String, dynamic> testCardFailure = {
    'number': '****************',
    'expiry_month': '12',
    'expiry_year': '2025',
    'cvv': '123',
    'name': 'Test User Failure',
  };
  
  // Test UPI details
  static const String testUpiId = 'test@paytm';
  
  // Test scenarios
  static const List<Map<String, dynamic>> testScenarios = [
    {
      'name': 'Successful Credit Card Payment',
      'amount': testAmountMedium,
      'payment_method': 'card',
      'expected_status': 'success',
      'card_details': testCardSuccess,
    },
    {
      'name': 'Failed Credit Card Payment',
      'amount': testAmountMedium,
      'payment_method': 'card',
      'expected_status': 'failed',
      'card_details': testCardFailure,
    },
    {
      'name': 'UPI Payment',
      'amount': testAmountSmall,
      'payment_method': 'upi',
      'expected_status': 'success',
      'upi_id': testUpiId,
    },
    {
      'name': 'Large Amount Payment',
      'amount': testAmountLarge,
      'payment_method': 'card',
      'expected_status': 'success',
      'card_details': testCardSuccess,
    },
  ];
  
  // Test timeouts
  static const Duration testTimeout = Duration(minutes: 2);
  static const Duration pollingInterval = Duration(seconds: 1); // Faster for testing
  
  // Environment detection
  static bool get isTestEnvironment {
    const environment = String.fromEnvironment('ENVIRONMENT', defaultValue: 'dev');
    return environment == 'test' || environment == 'dev' || environment == 'staging';
  }
  
  static String get currentApiBaseUrl {
    const environment = String.fromEnvironment('ENVIRONMENT', defaultValue: 'dev');
    switch (environment) {
      case 'test':
      case 'dev':
        return devApiBaseUrl;
      case 'staging':
        return testApiBaseUrl;
      case 'production':
        return 'https://partner.aquaconnect.blue/api';
      default:
        return devApiBaseUrl;
    }
  }
  
  // Test data generators
  static String generateTestInvoiceNumber() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return 'TEST_INV_$timestamp';
  }
  
  static String generateTestCustomerId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return 'TEST_CUST_$timestamp';
  }
  
  // Validation helpers
  static bool isTestPaymentSession(String sessionId) {
    return sessionId.startsWith('TEST_') || sessionId.contains('sandbox');
  }
  
  static bool isTestTransactionId(String transactionId) {
    return transactionId.startsWith('TEST_') || transactionId.contains('sandbox');
  }
}
