import 'package:objectbox/objectbox.dart';
import '../../domain/entities/smr_report.dart';

@Entity()
class ObjectBoxSMRReportModel {
  @Id()
  int dbId = 0;

  String id;
  String so;
  String partner;
  String partnerId;
  String productName;
  int startDateMillis;
  int lastDateMillis;
  int openingBalance;
  int invoice;
  int srn;
  int closingBalance;
  int sales;
  String status;

  ObjectBoxSMRReportModel({
    this.dbId = 0,
    required this.id,
    required this.so,
    required this.partner,
    required this.partnerId,
    required this.productName,
    required this.startDateMillis,
    required this.lastDateMillis,
    required this.openingBalance,
    required this.invoice,
    required this.srn,
    required this.closingBalance,
    required this.sales,
    required this.status,
  });

  factory ObjectBoxSMRReportModel.fromEntity(SMRReport entity) {
    return ObjectBoxSMRReportModel(
      id: entity.id,
      so: entity.so,
      partner: entity.partner,
      partnerId: entity.partnerId,
      productName: entity.productName,
      startDateMillis: entity.startDate.millisecondsSinceEpoch,
      lastDateMillis: entity.lastDate.millisecondsSinceEpoch,
      openingBalance: entity.openingBalance,
      invoice: entity.invoice,
      srn: entity.srn,
      closingBalance: entity.closingBalance,
      sales: entity.sales,
      status: entity.status,
    );
  }

  SMRReport toEntity() {
    return SMRReport(
      id: id,
      so: so,
      partner: partner,
      partnerId: partnerId,
      productName: productName,
      startDate: DateTime.fromMillisecondsSinceEpoch(startDateMillis),
      lastDate: DateTime.fromMillisecondsSinceEpoch(lastDateMillis),
      openingBalance: openingBalance,
      invoice: invoice,
      srn: srn,
      closingBalance: closingBalance,
      sales: sales,
      status: status,
    );
  }
}
