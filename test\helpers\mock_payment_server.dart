import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'payment_test_config.dart';

/// Mock HTTP server for testing payment API endpoints
class MockPaymentServer {
  late HttpServer _server;
  int _port = 8080;
  bool _isRunning = false;
  
  // Track requests for verification
  final List<HttpRequest> _requests = [];
  
  // Configurable responses
  Map<String, dynamic> _healthResponse = PaymentTestConfig.mockHealthResponse;
  Map<String, dynamic> _sessionResponse = PaymentTestConfig.mockPaymentSessionResponse;
  Map<String, dynamic> _statusResponse = PaymentTestConfig.mockPaymentStatusPending;
  
  // Simulation settings
  bool _simulateSlowResponse = false;
  bool _simulateNetworkError = false;
  bool _simulateValidationError = false;
  Duration _responseDelay = Duration.zero;
  
  /// Start the mock server
  Future<void> start({int port = 8080}) async {
    _port = port;
    
    _server = await HttpServer.bind(InternetAddress.loopbackIPv4, _port);
    _isRunning = true;
    
    print('🚀 Mock Payment Server started on http://localhost:$_port');
    
    await for (HttpRequest request in _server) {
      _requests.add(request);
      await _handleRequest(request);
    }
  }
  
  /// Stop the mock server
  Future<void> stop() async {
    if (_isRunning) {
      await _server.close();
      _isRunning = false;
      print('🛑 Mock Payment Server stopped');
    }
  }
  
  /// Get the server URL
  String get baseUrl => 'http://localhost:$_port/api';
  
  /// Get all received requests
  List<HttpRequest> get requests => List.unmodifiable(_requests);
  
  /// Clear request history
  void clearRequests() => _requests.clear();
  
  /// Configure responses
  void setHealthResponse(Map<String, dynamic> response) {
    _healthResponse = response;
  }
  
  void setSessionResponse(Map<String, dynamic> response) {
    _sessionResponse = response;
  }
  
  void setStatusResponse(Map<String, dynamic> response) {
    _statusResponse = response;
  }
  
  /// Configure simulation settings
  void simulateSlowResponse(Duration delay) {
    _simulateSlowResponse = true;
    _responseDelay = delay;
  }
  
  void simulateNetworkError() {
    _simulateNetworkError = true;
  }
  
  void simulateValidationError() {
    _simulateValidationError = true;
  }
  
  void resetSimulation() {
    _simulateSlowResponse = false;
    _simulateNetworkError = false;
    _simulateValidationError = false;
    _responseDelay = Duration.zero;
  }
  
  /// Handle incoming requests
  Future<void> _handleRequest(HttpRequest request) async {
    // Add CORS headers
    request.response.headers.add('Access-Control-Allow-Origin', '*');
    request.response.headers.add('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    request.response.headers.add('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    
    // Handle preflight requests
    if (request.method == 'OPTIONS') {
      request.response.statusCode = 200;
      await request.response.close();
      return;
    }
    
    // Simulate network conditions
    if (_simulateSlowResponse) {
      await Future.delayed(_responseDelay);
    }
    
    if (_simulateNetworkError) {
      request.response.statusCode = 500;
      await _sendJsonResponse(request, PaymentTestConfig.mockNetworkError);
      return;
    }
    
    // Route requests
    final path = request.uri.path;
    print('📥 ${request.method} $path');
    
    try {
      if (path.startsWith('/api/zoho/health')) {
        await _handleHealthCheck(request);
      } else if (path.startsWith('/api/zoho/payments/create-session')) {
        await _handleCreateSession(request);
      } else if (path.startsWith('/api/zoho/payments/status/')) {
        await _handlePaymentStatus(request);
      } else {
        await _handle404(request);
      }
    } catch (e) {
      print('❌ Error handling request: $e');
      request.response.statusCode = 500;
      await _sendJsonResponse(request, {
        'success': false,
        'message': 'Internal server error',
        'error': 'INTERNAL_ERROR',
      });
    }
  }
  
  /// Handle health check endpoint
  Future<void> _handleHealthCheck(HttpRequest request) async {
    if (request.method != 'GET') {
      request.response.statusCode = 405;
      await _sendJsonResponse(request, {
        'success': false,
        'message': 'Method not allowed',
        'error': 'METHOD_NOT_ALLOWED',
      });
      return;
    }
    
    request.response.statusCode = 200;
    await _sendJsonResponse(request, _healthResponse);
  }
  
  /// Handle create payment session endpoint
  Future<void> _handleCreateSession(HttpRequest request) async {
    if (request.method != 'POST') {
      request.response.statusCode = 405;
      await _sendJsonResponse(request, {
        'success': false,
        'message': 'Method not allowed',
        'error': 'METHOD_NOT_ALLOWED',
      });
      return;
    }
    
    // Check content type
    final contentType = request.headers.contentType;
    if (contentType?.mimeType != 'application/json') {
      request.response.statusCode = 415;
      await _sendJsonResponse(request, {
        'success': false,
        'message': 'Unsupported media type',
        'error': 'UNSUPPORTED_MEDIA_TYPE',
      });
      return;
    }
    
    // Read request body
    final body = await utf8.decoder.bind(request).join();
    Map<String, dynamic> requestData;
    
    try {
      requestData = jsonDecode(body);
    } catch (e) {
      request.response.statusCode = 400;
      await _sendJsonResponse(request, {
        'success': false,
        'message': 'Invalid JSON',
        'error': 'INVALID_JSON',
      });
      return;
    }
    
    // Simulate validation error
    if (_simulateValidationError) {
      request.response.statusCode = 400;
      await _sendJsonResponse(request, PaymentTestConfig.mockValidationError);
      return;
    }
    
    // Validate required fields
    final validationErrors = <String, List<String>>{};
    
    if (requestData['amount'] == null || requestData['amount'] <= 0) {
      validationErrors['amount'] = ['Amount must be greater than 0'];
    }
    
    if (requestData['currency'] != 'INR') {
      validationErrors['currency'] = ['Only INR is supported'];
    }
    
    if (requestData['invoice_number'] == null || requestData['invoice_number'].toString().isEmpty) {
      validationErrors['invoice_number'] = ['Invoice number cannot be empty'];
    }
    
    if (requestData['customer_id'] == null || requestData['customer_id'].toString().isEmpty) {
      validationErrors['customer_id'] = ['Customer ID cannot be empty'];
    }
    
    if (validationErrors.isNotEmpty) {
      request.response.statusCode = 400;
      await _sendJsonResponse(request, {
        'success': false,
        'message': 'Validation failed',
        'error': 'VALIDATION_ERROR',
        'details': validationErrors,
      });
      return;
    }
    
    // Generate dynamic response
    final sessionId = PaymentTestConfig.generateTestSessionId();
    final transactionId = PaymentTestConfig.generateTestTransactionId();
    
    final response = Map<String, dynamic>.from(_sessionResponse);
    response['data']['payment_session_id'] = sessionId;
    response['data']['transaction_id'] = transactionId;
    response['data']['amount'] = requestData['amount'].toString();
    response['data']['invoice_number'] = requestData['invoice_number'];
    response['data']['payment_url'] = 'https://payments.zoho.in/checkout/$sessionId';
    
    request.response.statusCode = 201;
    await _sendJsonResponse(request, response);
  }
  
  /// Handle payment status endpoint
  Future<void> _handlePaymentStatus(HttpRequest request) async {
    if (request.method != 'GET') {
      request.response.statusCode = 405;
      await _sendJsonResponse(request, {
        'success': false,
        'message': 'Method not allowed',
        'error': 'METHOD_NOT_ALLOWED',
      });
      return;
    }
    
    // Extract session ID from path
    final pathSegments = request.uri.pathSegments;
    if (pathSegments.length < 4) {
      request.response.statusCode = 400;
      await _sendJsonResponse(request, {
        'success': false,
        'message': 'Invalid session ID',
        'error': 'INVALID_SESSION_ID',
      });
      return;
    }
    
    final sessionId = pathSegments[3].replaceAll('/', ''); // Remove trailing slash
    
    // Simulate session not found
    if (sessionId.contains('invalid') || sessionId.contains('notfound')) {
      request.response.statusCode = 404;
      await _sendJsonResponse(request, {
        'success': false,
        'message': 'Session not found',
        'error': 'SESSION_NOT_FOUND',
      });
      return;
    }
    
    // Return configured status response
    final response = Map<String, dynamic>.from(_statusResponse);
    response['data']['payment_session_id'] = sessionId;
    
    request.response.statusCode = 200;
    await _sendJsonResponse(request, response);
  }
  
  /// Handle 404 errors
  Future<void> _handle404(HttpRequest request) async {
    request.response.statusCode = 404;
    await _sendJsonResponse(request, {
      'success': false,
      'message': 'Endpoint not found',
      'error': 'NOT_FOUND',
    });
  }
  
  /// Send JSON response
  Future<void> _sendJsonResponse(HttpRequest request, Map<String, dynamic> data) async {
    request.response.headers.contentType = ContentType.json;
    request.response.write(jsonEncode(data));
    await request.response.close();
  }
  
  /// Utility methods for testing
  
  /// Get the last request of a specific type
  HttpRequest? getLastRequest(String method, String pathPattern) {
    return _requests.reversed.firstWhere(
      (req) => req.method == method && req.uri.path.contains(pathPattern),
      orElse: () => throw StateError('No matching request found'),
    );
  }
  
  /// Count requests matching criteria
  int countRequests(String method, String pathPattern) {
    return _requests.where(
      (req) => req.method == method && req.uri.path.contains(pathPattern),
    ).length;
  }
  
  /// Verify request was made
  bool hasRequest(String method, String pathPattern) {
    return _requests.any(
      (req) => req.method == method && req.uri.path.contains(pathPattern),
    );
  }
  
  /// Get request body as JSON
  Future<Map<String, dynamic>> getRequestBody(HttpRequest request) async {
    final body = await utf8.decoder.bind(request).join();
    return jsonDecode(body);
  }
}
