import 'package:objectbox/objectbox.dart';
import '../../../domain/entities/dues/dues.dart';

@Entity()
class DuesInvoiceModel {
  @Id()
  int dbId = 0;

  // Fields to identify which aging group this belongs to
  final String agingGroup;
  final String customerId;

  // Invoice fields
  final String id;
  final String invoiceId;
  final String invoiceNumber;
  final String invoiceDate;
  final String customerCode;
  final String customerName;
  final String customerDistrict;
  final String customerState;
  final String customerType;
  final String onBoardedTime;
  final String categoryType;
  final String invoiceType;
  final String retailType;
  final String invoiceStatus;
  final String invoiceRaisedBy;
  final String mode;
  final String businessVertical;
  final String feedCreditLimit;
  final String nonFeedCreditLimit;
  final String harvestCreditLimit;
  final double totalSalesInclTax;
  final double totalSalesExclTax;
  final double tcsAmount;
  final double tdsAmount;
  final double amountAfterTcs;
  final double shippingCharges;
  final String feedAmountAfterTcs;
  final double nonFeedAmountAfterTcs;
  final double creditNoteAmountWithTcs;
  final double payableAmount;
  final double paidAmount;
  final double due;
  final String dueDate;
  final int dueDays;
  final String aging;
  final String aging1;
  final String paymentCredibility;
  final double? totalPurchase;
  final double? totalSales;
  final String? salesTier;
  final double? healthcareSales;
  final double? feedSales;
  final double? chemicalSales;
  final double? equipmentSales;
  final double? harvestSales;
  final double? grossMargin;
  final String? lastPaidDate;
  final int lastSyncTimestamp;

  DuesInvoiceModel({
    this.dbId = 0,
    required this.agingGroup,
    required this.customerId,
    required this.id,
    required this.invoiceId,
    required this.invoiceNumber,
    required this.invoiceDate,
    required this.customerCode,
    required this.customerName,
    required this.customerDistrict,
    required this.customerState,
    required this.customerType,
    required this.onBoardedTime,
    required this.categoryType,
    required this.invoiceType,
    required this.retailType,
    required this.invoiceStatus,
    required this.invoiceRaisedBy,
    required this.mode,
    required this.businessVertical,
    required this.feedCreditLimit,
    required this.nonFeedCreditLimit,
    required this.harvestCreditLimit,
    required this.totalSalesInclTax,
    required this.totalSalesExclTax,
    required this.tcsAmount,
    required this.tdsAmount,
    required this.amountAfterTcs,
    required this.shippingCharges,
    required this.feedAmountAfterTcs,
    required this.nonFeedAmountAfterTcs,
    required this.creditNoteAmountWithTcs,
    required this.payableAmount,
    required this.paidAmount,
    required this.due,
    required this.dueDate,
    required this.dueDays,
    required this.aging,
    required this.aging1,
    required this.paymentCredibility,
    this.totalPurchase,
    this.totalSales,
    this.salesTier,
    this.healthcareSales,
    this.feedSales,
    this.chemicalSales,
    this.equipmentSales,
    this.harvestSales,
    this.grossMargin,
    this.lastPaidDate,
    required this.lastSyncTimestamp,
  });

  factory DuesInvoiceModel.fromJson(
    Map<String, dynamic> json,
    String agingGroup,
    String customerId,
  ) {
    // Handle lastPaidDate which might be an empty object
    String? lastPaidDate;
    if (json['lastPaidDate'] != null &&
        json['lastPaidDate'] is Map &&
        (json['lastPaidDate'] as Map).isNotEmpty) {
      lastPaidDate = json['lastPaidDate'].toString();
    }

    return DuesInvoiceModel(
      agingGroup: agingGroup,
      customerId: customerId,
      id: json['_id'] ?? '',
      invoiceId: json['invoiceId'] ?? '',
      invoiceNumber: json['invoiceNumber'] ?? '',
      invoiceDate: json['invoiceDate'] ?? '',
      customerCode: json['customerCode'] ?? '',
      customerName: json['customerName'] ?? '',
      customerDistrict: json['customerDistrict'] ?? '',
      customerState: json['customerState'] ?? '',
      customerType: json['customerType'] ?? '',
      onBoardedTime: json['onBoardedTime'] ?? '',
      categoryType: json['categoryType'] ?? '',
      invoiceType: json['invoiceType'] ?? '',
      retailType: json['retailType'] ?? '',
      invoiceStatus: json['invoiceStatus'] ?? '',
      invoiceRaisedBy: json['invoiceRaisedBy'] ?? '',
      mode: json['mode'] ?? '',
      businessVertical: json['businessVertical'] ?? '',
      feedCreditLimit: json['feedCreditLimit'] ?? '0',
      nonFeedCreditLimit: json['nonFeedCreditLimit'] ?? '0',
      harvestCreditLimit: json['harvestCreditLimit'] ?? '0',
      totalSalesInclTax: (json['totalSalesInclTax'] ?? 0).toDouble(),
      totalSalesExclTax: (json['totalSalesExclTax'] ?? 0).toDouble(),
      tcsAmount: (json['tcsAmount'] ?? 0).toDouble(),
      tdsAmount: (json['tdsAmount'] ?? 0).toDouble(),
      amountAfterTcs: (json['amountAfterTcs'] ?? 0).toDouble(),
      shippingCharges: (json['shippingCharges'] ?? 0).toDouble(),
      feedAmountAfterTcs: json['feedAmountAfterTcs']?.toString() ?? '',
      nonFeedAmountAfterTcs: (json['nonFeedAmountAfterTcs'] ?? 0).toDouble(),
      creditNoteAmountWithTcs:
          (json['creditNoteAmountWithTcs'] ?? 0).toDouble(),
      payableAmount: (json['payableAmount'] ?? 0).toDouble(),
      paidAmount: (json['paidAmount'] ?? 0).toDouble(),
      due: (json['due'] ?? 0).toDouble(),
      dueDate: json['dueDate'] ?? '',
      dueDays: json['dueDays'] ?? 0,
      aging: json['aging'] ?? '',
      aging1: json['aging1'] ?? '',
      paymentCredibility: json['paymentCredibility'] ?? '',
      totalPurchase: (json['totalPurchase'] ?? 0).toDouble(),
      totalSales: (json['totalSales'] ?? 0).toDouble(),
      salesTier: json['salesTier'],
      healthcareSales: (json['healthcareSales'] ?? 0).toDouble(),
      feedSales: (json['feedSales'] ?? 0).toDouble(),
      chemicalSales: (json['chemicalSales'] ?? 0).toDouble(),
      equipmentSales: (json['equipmentSales'] ?? 0).toDouble(),
      harvestSales: (json['harvestSales'] ?? 0).toDouble(),
      grossMargin: (json['grossMargin'] ?? 0).toDouble(),
      lastPaidDate: lastPaidDate,
      lastSyncTimestamp: DateTime.now().millisecondsSinceEpoch,
    );
  }

  DuesInvoice toEntity() {
    return DuesInvoice(
      id: id,
      invoiceId: invoiceId,
      invoiceNumber: invoiceNumber,
      invoiceDate: invoiceDate,
      customerId: customerId,
      customerCode: customerCode,
      customerName: customerName,
      customerDistrict: customerDistrict,
      customerState: customerState,
      customerType: customerType,
      onBoardedTime: onBoardedTime,
      categoryType: categoryType,
      invoiceType: invoiceType,
      retailType: retailType,
      invoiceStatus: invoiceStatus,
      invoiceRaisedBy: invoiceRaisedBy,
      mode: mode,
      businessVertical: businessVertical,
      feedCreditLimit: feedCreditLimit,
      nonFeedCreditLimit: nonFeedCreditLimit,
      harvestCreditLimit: harvestCreditLimit,
      totalSalesInclTax: totalSalesInclTax,
      totalSalesExclTax: totalSalesExclTax,
      tcsAmount: tcsAmount,
      tdsAmount: tdsAmount,
      amountAfterTcs: amountAfterTcs,
      shippingCharges: shippingCharges,
      feedAmountAfterTcs: feedAmountAfterTcs,
      nonFeedAmountAfterTcs: nonFeedAmountAfterTcs,
      creditNoteAmountWithTcs: creditNoteAmountWithTcs,
      payableAmount: payableAmount,
      paidAmount: paidAmount,
      due: due,
      dueDate: dueDate,
      dueDays: dueDays,
      aging: aging,
      aging1: aging1,
      paymentCredibility: paymentCredibility,
      totalPurchase: totalPurchase,
      totalSales: totalSales,
      salesTier: salesTier,
      healthcareSales: healthcareSales,
      feedSales: feedSales,
      chemicalSales: chemicalSales,
      equipmentSales: equipmentSales,
      harvestSales: harvestSales,
      grossMargin: grossMargin,
      lastPaidDate: lastPaidDate,
    );
  }
}

@Entity()
class DuesAgingGroupModel {
  @Id()
  int dbId = 0;

  final String customerId;
  final double totalPayableAmount;
  final String aging;
  final int dueDays;
  final int lastSyncTimestamp;

  // No constructor parameter for invoices - this will be handled via relationship queries

  DuesAgingGroupModel({
    this.dbId = 0,
    required this.customerId,
    required this.totalPayableAmount,
    required this.aging,
    required this.dueDays,
    required this.lastSyncTimestamp,
  });

  factory DuesAgingGroupModel.fromJson(
    Map<String, dynamic> json,
    String customerId,
  ) {
    return DuesAgingGroupModel(
      customerId: customerId,
      totalPayableAmount: (json['totalPayableAmount'] ?? 0).toDouble(),
      aging: json['aging'] ?? '',
      dueDays: json['dueDays'] ?? 0,
      lastSyncTimestamp: DateTime.now().millisecondsSinceEpoch,
    );
  }
}

@Entity()
class DuesSummaryModel {
  @Id()
  int dbId = 0;

  final String customerId;
  final double totalDue;
  final int lastSyncTimestamp;

  // No constructor parameter for agingGroups - this will be handled via relationship queries

  DuesSummaryModel({
    this.dbId = 0,
    required this.customerId,
    required this.totalDue,
    required this.lastSyncTimestamp,
  });
}
