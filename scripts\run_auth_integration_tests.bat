@echo off
echo ========================================
echo Running AquaPartner Authentication Integration Tests
echo ========================================
echo.

echo Checking Flutter installation...
flutter --version
if %errorlevel% neq 0 (
    echo ERROR: Flutter is not installed or not in PATH
    pause
    exit /b 1
)

echo.
echo Getting dependencies...
flutter pub get
if %errorlevel% neq 0 (
    echo ERROR: Failed to get dependencies
    pause
    exit /b 1
)

echo.
echo Running comprehensive authentication flow integration tests...
echo.
echo Test Details:
echo - Phone Number: 9999999999 (test number)
echo - Valid OTP: 123456
echo - Invalid OTP: 123123
echo - Tests cover complete authentication flow from phone entry to completion
echo.

flutter test integration_test/comprehensive_auth_flow_test.dart --verbose
set test_result=%errorlevel%

echo.
echo ========================================
if %test_result% equ 0 (
    echo ✅ ALL AUTHENTICATION INTEGRATION TESTS PASSED!
    echo.
    echo Test Summary:
    echo ✅ Valid OTP Flow: Phone entry → OTP send → Valid OTP verification → AuthSuccess
    echo ✅ Invalid OTP Flow: Phone entry → OTP send → Invalid OTP verification → OtpVerificationError
    echo ✅ State Progression: Complete validation of all authentication state transitions
    echo ✅ Error Handling: Proper error states and messages for invalid scenarios
    echo ✅ Use Case Verification: All authentication use cases called correctly
    echo.
    echo The authentication flow is working correctly with:
    echo - Proper state management
    echo - Correct error handling
    echo - Complete user journey validation
    echo - Navigation error handling in test environment
) else (
    echo ❌ SOME AUTHENTICATION INTEGRATION TESTS FAILED!
    echo.
    echo Please check the test output above for details.
    echo Common issues:
    echo - Mock setup problems
    echo - State transition timing issues
    echo - Widget finding issues
    echo - Dependency injection problems
)
echo ========================================

pause
exit /b %test_result%
