import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import 'package:aquapartner/core/error/failures.dart';
import 'package:aquapartner/domain/repositories/user_repository.dart';
import 'package:aquapartner/domain/usecases/user_usecases.dart';
import 'package:aquapartner/domain/entities/user.dart';

// Mock classes
class MockUserRepository extends Mo<PERSON> implements UserRepository {}

void main() {
  setUpAll(() {
    // Register fallback values for mocktail
    registerFallbackValue(
      User(
        id: 0,
        phoneNumber: '',
        isVerified: false,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        needsSync: false,
      ),
    );
  });

  group('GetUserUseCase', () {
    late GetUserUseCase useCase;
    late MockUserRepository mockRepository;

    setUp(() {
      mockRepository = MockUserRepository();
      useCase = GetUserUseCase(mockRepository);
    });

    group('Successful Operations', () {
      test('should get user when user exists', () async {
        // arrange
        final testUser = User(
          id: 1,
          phoneNumber: '+919999999999',
          isVerified: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          needsSync: false,
        );
        when(
          () => mockRepository.getUser(),
        ).thenAnswer((_) async => Right(testUser));

        // act
        final result = await useCase.call();

        // assert
        expect(result, Right(testUser));
        verify(() => mockRepository.getUser()).called(1);
      });

      test('should get user with all properties correctly', () async {
        // arrange
        final testUser = User(
          id: 123,
          mongoId: 'mongo_123',
          phoneNumber: '+919999999999',
          isVerified: true,
          createdAt: DateTime(2024, 1, 1),
          updatedAt: DateTime(2024, 1, 2),
          needsSync: true,
        );
        when(
          () => mockRepository.getUser(),
        ).thenAnswer((_) async => Right(testUser));

        // act
        final result = await useCase.call();

        // assert
        result.fold((failure) => fail('Expected Right but got Left'), (user) {
          expect(user.id, 123);
          expect(user.mongoId, 'mongo_123');
          expect(user.phoneNumber, '+919999999999');
          expect(user.isVerified, true);
          expect(user.needsSync, true);
        });
      });
    });

    group('Error Handling', () {
      test('should return failure when repository fails', () async {
        // arrange
        when(
          () => mockRepository.getUser(),
        ).thenAnswer((_) async => Left(CacheFailure()));

        // act
        final result = await useCase.call();

        // assert
        expect(result, Left(CacheFailure()));
        verify(() => mockRepository.getUser()).called(1);
      });

      test('should handle server failure', () async {
        // arrange
        when(
          () => mockRepository.getUser(),
        ).thenAnswer((_) async => Left(ServerFailure()));

        // act
        final result = await useCase.call();

        // assert
        expect(result, Left(ServerFailure()));
      });

      test('should handle not found failure', () async {
        // arrange
        when(
          () => mockRepository.getUser(),
        ).thenAnswer((_) async => Left(NotFoundFailure()));

        // act
        final result = await useCase.call();

        // assert
        expect(result, Left(NotFoundFailure()));
      });
    });

    group('Edge Cases', () {
      test('should handle repository throwing exception', () async {
        // arrange
        when(
          () => mockRepository.getUser(),
        ).thenThrow(Exception('Unexpected error'));

        // act & assert
        expect(() => useCase.call(), throwsException);
      });

      test('should handle concurrent get user calls', () async {
        // arrange
        final testUser = User(
          id: 1,
          phoneNumber: '+919999999999',
          isVerified: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          needsSync: false,
        );
        when(
          () => mockRepository.getUser(),
        ).thenAnswer((_) async => Right(testUser));

        // act
        final results = await Future.wait([
          useCase.call(),
          useCase.call(),
          useCase.call(),
        ]);

        // assert
        for (final result in results) {
          expect(result, Right(testUser));
        }
        verify(() => mockRepository.getUser()).called(3);
      });
    });
  });

  group('SaveUserUseCase', () {
    late SaveUserUseCase useCase;
    late MockUserRepository mockRepository;

    setUp(() {
      mockRepository = MockUserRepository();
      useCase = SaveUserUseCase(mockRepository);
    });

    group('Successful Operations', () {
      test('should save user successfully', () async {
        // arrange
        final testUser = User(
          id: 1,
          phoneNumber: '+919999999999',
          isVerified: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          needsSync: false,
        );
        when(
          () => mockRepository.saveUser(testUser),
        ).thenAnswer((_) async => const Right(true));

        // act
        final result = await useCase.call(testUser);

        // assert
        expect(result, const Right(true));
        verify(() => mockRepository.saveUser(testUser)).called(1);
      });

      test('should save new user with default values', () async {
        // arrange
        final testUser = User(
          id: 0,
          phoneNumber: '+919999999999',
          isVerified: false,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          needsSync: true,
        );
        when(
          () => mockRepository.saveUser(testUser),
        ).thenAnswer((_) async => const Right(true));

        // act
        final result = await useCase.call(testUser);

        // assert
        expect(result, const Right(true));
        verify(() => mockRepository.saveUser(testUser)).called(1);
      });
    });

    group('Error Handling', () {
      test('should return failure when repository fails', () async {
        // arrange
        final testUser = User(
          id: 1,
          phoneNumber: '+919999999999',
          isVerified: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          needsSync: false,
        );
        when(
          () => mockRepository.saveUser(testUser),
        ).thenAnswer((_) async => Left(CacheFailure()));

        // act
        final result = await useCase.call(testUser);

        // assert
        expect(result, Left(CacheFailure()));
      });

      test('should handle validation failure', () async {
        // arrange
        final testUser = User(
          id: 1,
          phoneNumber: 'invalid_phone',
          isVerified: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          needsSync: false,
        );
        when(() => mockRepository.saveUser(testUser)).thenAnswer(
          (_) async => Left(ValidationFailure('Invalid phone number')),
        );

        // act
        final result = await useCase.call(testUser);

        // assert
        expect(result, Left(ValidationFailure('Invalid phone number')));
      });
    });

    group('Edge Cases', () {
      test('should handle saving user with null mongoId', () async {
        // arrange
        final testUser = User(
          id: 1,
          mongoId: null,
          phoneNumber: '+919999999999',
          isVerified: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          needsSync: false,
        );
        when(
          () => mockRepository.saveUser(testUser),
        ).thenAnswer((_) async => const Right(true));

        // act
        final result = await useCase.call(testUser);

        // assert
        expect(result, const Right(true));
      });

      test('should handle concurrent save operations', () async {
        // arrange
        final testUser1 = User(
          id: 1,
          phoneNumber: '+919999999999',
          isVerified: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          needsSync: false,
        );
        final testUser2 = User(
          id: 2,
          phoneNumber: '+919876543211',
          isVerified: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          needsSync: false,
        );
        when(
          () => mockRepository.saveUser(any()),
        ).thenAnswer((_) async => const Right(true));

        // act
        final results = await Future.wait([
          useCase.call(testUser1),
          useCase.call(testUser2),
        ]);

        // assert
        for (final result in results) {
          expect(result, const Right(true));
        }
        verify(() => mockRepository.saveUser(any())).called(2);
      });
    });
  });

  group('UpdateUserUseCase', () {
    late UpdateUserUseCase useCase;
    late MockUserRepository mockRepository;

    setUp(() {
      mockRepository = MockUserRepository();
      useCase = UpdateUserUseCase(mockRepository);
    });

    group('Successful Operations', () {
      test('should update user successfully', () async {
        // arrange
        final testUser = User(
          id: 1,
          phoneNumber: '+919999999999',
          isVerified: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          needsSync: true,
        );
        when(
          () => mockRepository.updateUser(testUser),
        ).thenAnswer((_) async => const Right(true));

        // act
        final result = await useCase.call(testUser);

        // assert
        expect(result, const Right(true));
        verify(() => mockRepository.updateUser(testUser)).called(1);
      });

      test('should update user verification status', () async {
        // arrange
        final testUser = User(
          id: 1,
          phoneNumber: '+919999999999',
          isVerified: true,
          createdAt: DateTime.now().subtract(Duration(days: 1)),
          updatedAt: DateTime.now(),
          needsSync: true,
        );
        when(
          () => mockRepository.updateUser(testUser),
        ).thenAnswer((_) async => const Right(true));

        // act
        final result = await useCase.call(testUser);

        // assert
        expect(result, const Right(true));
        verify(() => mockRepository.updateUser(testUser)).called(1);
      });
    });

    group('Error Handling', () {
      test('should return failure when repository fails', () async {
        // arrange
        final testUser = User(
          id: 1,
          phoneNumber: '+919999999999',
          isVerified: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          needsSync: false,
        );
        when(
          () => mockRepository.updateUser(testUser),
        ).thenAnswer((_) async => Left(ServerFailure()));

        // act
        final result = await useCase.call(testUser);

        // assert
        expect(result, Left(ServerFailure()));
      });

      test('should handle not found failure', () async {
        // arrange
        final testUser = User(
          id: 999,
          phoneNumber: '+919999999999',
          isVerified: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          needsSync: false,
        );
        when(
          () => mockRepository.updateUser(testUser),
        ).thenAnswer((_) async => Left(NotFoundFailure()));

        // act
        final result = await useCase.call(testUser);

        // assert
        expect(result, Left(NotFoundFailure()));
      });
    });
  });

  group('DeleteUserUseCase', () {
    late DeleteUserUseCase useCase;
    late MockUserRepository mockRepository;

    setUp(() {
      mockRepository = MockUserRepository();
      useCase = DeleteUserUseCase(mockRepository);
    });

    group('Successful Operations', () {
      test('should delete user successfully', () async {
        // arrange
        when(
          () => mockRepository.deleteUser(),
        ).thenAnswer((_) async => const Right(true));

        // act
        final result = await useCase.call();

        // assert
        expect(result, const Right(true));
        verify(() => mockRepository.deleteUser()).called(1);
      });
    });

    group('Error Handling', () {
      test('should return failure when repository fails', () async {
        // arrange
        when(
          () => mockRepository.deleteUser(),
        ).thenAnswer((_) async => Left(CacheFailure()));

        // act
        final result = await useCase.call();

        // assert
        expect(result, Left(CacheFailure()));
      });

      test('should handle not found failure', () async {
        // arrange
        when(
          () => mockRepository.deleteUser(),
        ).thenAnswer((_) async => Left(NotFoundFailure()));

        // act
        final result = await useCase.call();

        // assert
        expect(result, Left(NotFoundFailure()));
      });
    });

    group('Edge Cases', () {
      test('should handle concurrent delete operations', () async {
        // arrange
        when(
          () => mockRepository.deleteUser(),
        ).thenAnswer((_) async => const Right(true));

        // act
        final results = await Future.wait([useCase.call(), useCase.call()]);

        // assert
        for (final result in results) {
          expect(result, const Right(true));
        }
        verify(() => mockRepository.deleteUser()).called(2);
      });
    });
  });
}
