// lib/domain/usecases/get_customer_payments.dart
import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../entities/payments/customer_payment.dart';
import '../entities/payments/payments_summary.dart';
import '../repositories/customer_payments_repository.dart';

class GetCustomerPayments {
  final CustomerPaymentsRepository repository;

  GetCustomerPayments(this.repository);

  Future<Either<Failure, PaymentsSummary>> call(String customerId) {
    return repository.getCustomerPayments(customerId);
  }
}

class SyncCustomerPayments {
  final CustomerPaymentsRepository repository;

  SyncCustomerPayments(this.repository);

  Future<Either<Failure, void>> call(String customerId) {
    return repository.syncCustomerPayments(customerId);
  }
}

class GetLocalCustomerPayments {
  final CustomerPaymentsRepository repository;

  GetLocalCustomerPayments(this.repository);

  Future<Either<Failure, List<CustomerPayment>>> call(String customerId) {
    return repository.getLocalCustomerPayments(customerId);
  }
}
