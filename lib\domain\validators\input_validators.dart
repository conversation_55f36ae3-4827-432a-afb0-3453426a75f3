class InputValidators {
  static String? validateFullName(String? name) {
    if (name?.isEmpty ?? true) {
      return "Enter Your Name";
    } else if (name != null && name.length < 3) {
      return "Name must be at least 3 characters";
    }
    return null;
  }

  static String? validatePhoneNumber(String? phoneNumber) {
    if (phoneNumber?.isEmpty ?? true) {
      return "Enter Your Phone Number";
    } else if (phoneNumber != null && phoneNumber.length != 10) {
      return "Phone number must be 10 digits";
    }
    return null;
  }

  static String? validateOtp(String? otp) {
    if (otp?.isEmpty ?? true) {
      return "Please Enter OTP";
    } else if (otp != null && otp.length != 6) {
      return "Otp should be 6 digits";
    }
    return null;
  }

}