import 'package:aquapartner/core/utils/logger.dart';
import 'package:aquapartner/domain/entities/customer.dart';
import 'package:dartz/dartz.dart';

import '../../core/error/failures.dart';
import '../usecases/customer_usercases.dart';
import '../usecases/user_usecases.dart';

abstract class AuthService {
  Future<Either<Failure, Customer?>> getCurrentCustomer();
}

class AuthServiceImpl implements AuthService {
  final GetUserUseCase getUserUseCase;
  final GetCustomerByMobileNumber getCustomerByMobileNumber;
  final AppLogger logger;

  Customer? _cachedCustomer; // Changed to nullable
  DateTime? _cacheTimestamp;
  static const _cacheDuration = Duration(hours: 4);

  AuthServiceImpl({
    required this.getUserUseCase,
    required this.getCustomerByMobileNumber,
    required this.logger,
  });

  @override
  Future<Either<Failure, Customer?>> getCurrentCustomer() async {
    // Check if cache is valid
    if (_cachedCustomer != null &&
        _cacheTimestamp != null &&
        DateTime.now().difference(_cacheTimestamp!) < _cacheDuration) {
      return Right(_cachedCustomer);
    }

    final userResult = await getUserUseCase();

    return userResult.fold(
      (failure) {
        logger.e('Failed to get user: ');
        return Left(failure);
      },
      (user) async {
        // Step 2: Get the customer using the user's mobile number
        final customerResult = await getCustomerByMobileNumber(
          user.phoneNumber,
        );

        return customerResult.fold(
          (failure) {
            logger.e('Failed to get customer: ');
            return Left(failure);
          },
          (customer) {
            if (customer == null) {
              logger.e(
                'Customer not found for mobile number: ${user.phoneNumber}',
              );
              return Left(ServerFailure());
            }

            logger.i(
              'Customer retrieved successfully: ${customer.companyName}',
            );

            // Cache the customer ID
            _cachedCustomer = customer;

            _cacheTimestamp = DateTime.now();

            logger.i(
              'Retrieved and cached customer ID: ${customer.customerId}',
            );
            return Right(_cachedCustomer);
          },
        );
      },
    );
  }

  void clearCache() {
    _cachedCustomer = null;
    _cacheTimestamp = null;
  }
}
