// lib/data/datasources/credit_notes_local_data_source.dart
import 'package:objectbox/objectbox.dart';

import '../../../core/error/exceptions.dart';
import '../../../objectbox.g.dart';
import '../../models/credit_notes/credit_note_model.dart';

abstract class CreditNotesLocalDataSource {
  Future<List<CreditNoteModel>> getCreditNotes(String customerId);
  Future<void> cacheCreditNotes(List<CreditNoteModel> notes);
}

class CreditNotesLocalDataSourceImpl implements CreditNotesLocalDataSource {
  final Box<CreditNoteModel> creditNoteBox;
  final Store store;

  CreditNotesLocalDataSourceImpl({
    required this.store,
    required this.creditNoteBox,
  });

  @override
  Future<List<CreditNoteModel>> getCreditNotes(String customerId) async {
    try {
      final query =
          creditNoteBox
              .query(CreditNoteModel_.customerId.equals(customerId))
              .build();
      final notes = query.find();
      query.close();
      return notes;
    } catch (e) {
      throw CacheException();
    }
  }

  @override
  Future<void> cacheCreditNotes(List<CreditNoteModel> notes) async {
    if (notes.isEmpty) return; // Nothing to cache

    final customerId =
        notes.first.customerId; // Assuming all notes are for the same customer

    try {
      // Use a transaction for atomic write (remove old, put new)
      store.runInTransaction(TxMode.write, () {
        // 1. Find existing notes for this customer
        final query =
            creditNoteBox
                .query(CreditNoteModel_.customerId.equals(customerId))
                .build();
        final existingIds = query.findIds(); // More efficient to get only IDs
        query.close();

        // 2. Remove existing notes for this customer if any
        if (existingIds.isNotEmpty) {
          creditNoteBox.removeMany(existingIds);
        }

        // 3. Put the new notes
        creditNoteBox.putMany(notes);
      });
    } catch (e) {
      throw CacheException();
    }
  }
}
