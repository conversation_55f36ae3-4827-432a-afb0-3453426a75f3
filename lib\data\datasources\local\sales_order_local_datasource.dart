import 'package:objectbox/objectbox.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../core/error/exceptions.dart';
import '../../../core/utils/logger.dart';
import '../../../objectbox.g.dart';
import '../../models/sales_order/sales_order_model.dart';
import '../../models/sales_order/sales_order_item_model.dart';

abstract class SalesOrderLocalDataSource {
  /// Gets the cached list of sales orders for a specific customer
  ///
  /// Throws [CacheException] if no cached data is present
  Future<List<SalesOrderModel>> getSalesOrders(String customerId);

  /// Gets a specific sales order by ID
  ///
  /// Throws [CacheException] if no cached data is present
  Future<SalesOrderModel> getSalesOrderById(String salesOrderId);

  /// Caches a list of sales orders
  Future<void> cacheSalesOrders(
    List<SalesOrderModel> salesOrders,
    String customerId,
  );

  /// Checks if sales orders need to be synced
  Future<bool> checkIfSyncNeeded(String customerId);

  /// Updates the last sync time for a customer's sales orders
  Future<void> updateLastSyncTime(String customerId);
}

class SalesOrderLocalDataSourceImpl implements SalesOrderLocalDataSource {
  final Store store;
  final Box<SalesOrderModel> salesOrderBox;
  final Box<SalesOrderItemModel> salesOrderItemBox;
  final SharedPreferences sharedPreferences;
  final AppLogger logger;

  SalesOrderLocalDataSourceImpl({
    required this.store,
    required this.salesOrderBox,
    required this.salesOrderItemBox,
    required this.sharedPreferences,
    required this.logger,
  });

  @override
  Future<List<SalesOrderModel>> getSalesOrders(String customerId) async {
    try {
      logger.i(
        'Getting sales orders from local storage for customer: $customerId',
      );
      final query =
          salesOrderBox
              .query(SalesOrderModel_.customerId.equals(customerId))
              .build();
      final salesOrders = query.find();
      query.close();

      if (salesOrders.isEmpty) {
        logger.w(
          'No sales orders found in local storage for customer: $customerId',
        );
        throw CacheException();
      }

      logger.i('Found ${salesOrders.length} sales orders in local storage');
      return salesOrders;
    } catch (e) {
      logger.e('Error getting sales orders from local storage: $e');
      throw CacheException();
    }
  }

  @override
  Future<SalesOrderModel> getSalesOrderById(String salesOrderId) async {
    try {
      logger.i('Getting sales order from local storage with ID: $salesOrderId');
      final query =
          salesOrderBox
              .query(SalesOrderModel_.salesOrderId.equals(salesOrderId))
              .build();
      final salesOrder = query.findFirst();
      query.close();

      if (salesOrder == null) {
        logger.w(
          'No sales order found in local storage with ID: $salesOrderId',
        );
        throw CacheException();
      }

      logger.i('Found sales order in local storage');
      return salesOrder;
    } catch (e) {
      logger.e('Error getting sales order from local storage: $e');
      throw CacheException();
    }
  }

  @override
  Future<void> cacheSalesOrders(
    List<SalesOrderModel> salesOrders,
    String customerId,
  ) async {
    try {
      logger.i('Caching ${salesOrders.length} sales orders to local storage');

      store.runInTransaction(TxMode.write, () {
        // Remove existing sales orders for this customer
        final query =
            salesOrderBox
                .query(SalesOrderModel_.customerId.equals(customerId))
                .build();
        final existingSalesOrders = query.find();
        query.close();

        // Get all existing sales order IDs to remove their items
        final existingSalesOrderIds =
            existingSalesOrders.map((so) => so.salesOrderId).toList();

        // Remove existing sales orders
        for (var order in existingSalesOrders) {
          salesOrderBox.remove(order.id);
        }

        // Remove existing sales order items for these orders
        for (var salesOrderId in existingSalesOrderIds) {
          final itemQuery =
              salesOrderItemBox
                  .query(
                    SalesOrderItemModel_.salesOrderIdValue.equals(salesOrderId),
                  )
                  .build();
          final existingItems = itemQuery.find();
          itemQuery.close();

          for (var item in existingItems) {
            salesOrderItemBox.remove(item.id);
          }
        }

        // Insert new sales orders and their items
        for (var salesOrder in salesOrders) {
          // Store the sales order
          final orderId = salesOrderBox.put(salesOrder);

          // Store each item and link it to the sales order
          for (var item in salesOrder.items) {
            item.salesOrder.targetId = orderId;
            salesOrderItemBox.put(item);
          }
        }
      });

      logger.i('Successfully cached sales orders and items to local storage');
    } catch (e) {
      logger.e('Error caching sales orders to local storage: $e');
      throw CacheException();
    }
  }

  @override
  Future<bool> checkIfSyncNeeded(String customerId) async {
    try {
      logger.i('Checking if sync is needed for customer: $customerId');
      final lastSyncKey = 'last_sales_orders_sync_$customerId';
      final lastSyncTime = sharedPreferences.getString(lastSyncKey);

      if (lastSyncTime == null) {
        logger.i('No previous sync found, sync is needed');
        return true;
      }

      final lastSync = DateTime.parse(lastSyncTime);
      final now = DateTime.now();
      final difference = now.difference(lastSync);

      // Sync if last sync was more than 24 hours ago
      final syncNeeded = difference.inHours > 24;
      logger.i(
        'Last sync was ${difference.inHours} hours ago. Sync needed: $syncNeeded',
      );
      return syncNeeded;
    } catch (e) {
      logger.e('Error checking if sync is needed: $e');
      return true; // Default to sync needed if there's an error
    }
  }

  @override
  Future<void> updateLastSyncTime(String customerId) async {
    try {
      logger.i('Updating last sync time for customer: $customerId');
      final lastSyncKey = 'last_sales_orders_sync_$customerId';
      final now = DateTime.now().toIso8601String();
      await sharedPreferences.setString(lastSyncKey, now);
      logger.i('Successfully updated last sync time');
    } catch (e) {
      logger.e('Error updating last sync time: $e');
      throw CacheException();
    }
  }
}
