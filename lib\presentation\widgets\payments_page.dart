import 'package:aqua_ui/aqua_ui.dart';
import 'package:aquapartner/core/mixins/analytics_mixin.dart';
import 'package:aquapartner/core/utils/currency_formatter.dart';
import 'package:aquapartner/presentation/widgets/loading_widget.dart';
import 'package:aquapartner/presentation/widgets/styled_generic_table.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';

import '../../domain/entities/payments/customer_payment.dart';
import '../cubit/payments/customer_payments_cubit.dart';
import '../cubit/payments/customer_payments_state.dart';

class PaymentsPage extends StatefulWidget {
  const PaymentsPage({super.key});

  @override
  State<PaymentsPage> createState() => _PaymentsPageState();
}

class _PaymentsPageState extends State<PaymentsPage> with AnalyticsMixin {
  @override
  String get screenName => 'PaymentsPage';

  // Track screen view time
  DateTime? _screenViewStartTime;
  DateTime? _lastInteractionTime;

  // Track user interactions
  int _paymentTapCount = 0;
  int _scrollCount = 0;

  // Track scroll position
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _screenViewStartTime = DateTime.now();
    _lastInteractionTime = _screenViewStartTime;

    // Add scroll listener for analytics
    _scrollController.addListener(_onScroll);

    context.read<CustomerPaymentsCubit>().loadCustomerPayments();

    // Track page initialization
    trackEvent('payments_page_initialized');
  }

  @override
  void dispose() {
    // Track total time spent on screen
    _trackScreenDuration();

    // Track final engagement metrics
    _trackEngagementMetrics();

    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _trackScreenDuration() {
    if (_screenViewStartTime != null) {
      final duration = DateTime.now().difference(_screenViewStartTime!);
      trackEvent(
        'payments_screen_duration',
        params: {
          'duration_ms': duration.inMilliseconds.toString(),
          'duration_seconds': duration.inSeconds.toString(),
        },
      );
    }
  }

  void _trackEngagementMetrics() {
    trackEvent(
      'payments_engagement',
      params: {
        'payment_tap_count': _paymentTapCount.toString(),
        'scroll_count': _scrollCount.toString(),
        'time_since_last_interaction':
            _lastInteractionTime != null
                ? DateTime.now()
                    .difference(_lastInteractionTime!)
                    .inSeconds
                    .toString()
                : '0',
      },
    );
  }

  void _onScroll() {
    // Don't track every tiny scroll event - only significant ones
    if (!_scrollController.hasClients) return;

    // Track scroll position as percentage
    final scrollPosition = _scrollController.position.pixels;
    final maxScroll = _scrollController.position.maxScrollExtent;

    if (maxScroll <= 0) return;

    final scrollPercentage = (scrollPosition / maxScroll * 100).round();

    // Only track at 25%, 50%, 75%, and 100% scroll positions
    if (scrollPercentage == 25 ||
        scrollPercentage == 50 ||
        scrollPercentage == 75 ||
        scrollPercentage == 100) {
      _scrollCount++;
      _lastInteractionTime = DateTime.now();

      trackEvent(
        'payments_scrolled',
        params: {
          'scroll_percentage': scrollPercentage.toString(),
          'scroll_position': scrollPosition.toString(),
        },
      );
    }
  }

  void _onPaymentItemTapped(CustomerPayment payment) {
    _paymentTapCount++;
    _lastInteractionTime = DateTime.now();

    trackUserInteraction(
      'payment_item_tapped',
      'table_row',
      elementId: payment.paymentsNumber,
      additionalParams: {
        'payment_id': payment.paymentsNumber,
        'payment_amount': payment.amount.toString(),
        'payment_date': payment.paymentsDate.toIso8601String(),
        'payment_tap_count': _paymentTapCount.toString(),
        'time_on_screen_before_tap':
            _screenViewStartTime != null
                ? DateTime.now()
                    .difference(_screenViewStartTime!)
                    .inSeconds
                    .toString()
                : '0',
      },
    );

    // Add navigation or detail view logic here
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<CustomerPaymentsCubit, CustomerPaymentsState>(
      builder: (context, state) {
        if (state is CustomerPaymentsLoaded) {
          if (state.paymentsSummary.payments.isEmpty) {
            // Track empty state view
            trackEvent('empty_state_viewed');

            return Center(
              child: AquaText.subheadline("You dont have any payments"),
            );
          }

          return state.paymentsSummary.payments.isNotEmpty
              ? RefreshIndicator(
                onRefresh: () async {
                  trackEvent('manual_refresh_triggered');
                  await context
                      .read<CustomerPaymentsCubit>()
                      .invalidateAndSync();
                },
                child: SingleChildScrollView(
                  controller: _scrollController,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 16),
                        StyledGenericTable<CustomerPayment>(
                          items: state.paymentsSummary.payments,
                          showDividers: true,
                          onRowTap: _onPaymentItemTapped,
                          columns: [
                            ColumnConfig<CustomerPayment>(
                              title: 'Date',
                              width: 100,
                              cellBuilder:
                                  (payment) => Align(
                                    alignment: Alignment.center,
                                    child: AquaText.body(
                                      DateFormat(
                                        'dd-MM-yyyy',
                                      ).format(payment.paymentsDate),
                                    ),
                                  ),
                            ),
                            ColumnConfig<CustomerPayment>(
                              title: 'Ref Number',
                              width: 150,
                              cellBuilder:
                                  (payment) => AquaText.body(
                                    payment.paymentsNumber,
                                    weight: AquaFontWeight.semibold,
                                    color: acPrimaryBlue,
                                  ),
                            ),
                            ColumnConfig<CustomerPayment>(
                              title: 'Amount',
                              width: 100,
                              titleAlignment: Alignment.center,
                              bodyAlignment: Alignment.centerRight,
                              cellBuilder:
                                  (payment) => AquaText.body(
                                    CurrencyFormatter.formatAsINR(
                                      double.parse(
                                        payment.amount
                                            .replaceAll('INR ', '')
                                            .replaceAll(',', ''),
                                      ),
                                      decimalPlaces: 0,
                                    ),
                                    weight: AquaFontWeight.bold,
                                  ),
                            ),
                            ColumnConfig<CustomerPayment>(
                              title: 'Status',
                              width: 120,
                              cellBuilder:
                                  (payment) =>
                                      AquaText.body(payment.paymentsMode),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              )
              : Center(
                child: AquaText.subheadline("You dont have any payments"),
              );
        }
        return LoadingWidget(
          message: "Please wait, your payments are loading.",
        );
      },
      listener: (context, state) {
        // Track state changes for analytics
        if (state is CustomerPaymentsLoading) {
          trackEvent('payments_loading');
        } else if (state is CustomerPaymentsLoaded) {
          trackEvent(
            'payments_loaded',
            params: {
              'payment_count': state.paymentsSummary.payments.length,
              'total_amount': state.paymentsSummary.totalSum.toString(),
              'is_from_cache': state.isFromCache ? 'true' : 'false',
            },
          );
        } else if (state is CustomerPaymentsError) {
          trackEvent(
            'payments_error',
            params: {'error_message': state.message},
          );
        }
      },
    );
  }
}
