class Product {
  final String productName;
  final String category;
  final String categoryType;
  final String subCategory;
  final String tagLine;
  final String productTag;
  final String productImage;
  final String content;
  final String sortOrder;
  final String status;

  Product({
    required this.productName,
    required this.category,
    required this.categoryType,
    required this.subCategory,
    required this.tagLine,
    required this.productTag,
    required this.productImage,
    required this.content,
    required this.sortOrder,
    required this.status,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Product &&
          runtimeType == other.runtimeType &&
          productName == other.productName &&
          category == other.category &&
          categoryType == other.categoryType &&
          subCategory == other.subCategory &&
          tagLine == other.tagLine &&
          productTag == other.productTag &&
          productImage == other.productImage &&
          content == other.content &&
          sortOrder == other.sortOrder &&
          status == other.status;

  @override
  int get hashCode =>
      productName.hashCode ^
      category.hashCode ^
      categoryType.hashCode ^
      subCategory.hashCode ^
      tagLine.hashCode ^
      productTag.hashCode ^
      productImage.hashCode ^
      content.hashCode ^
      sortOrder.hashCode ^
      status.hashCode;

  @override
  String toString() {
    return 'Product{productName: $productName, category: $category, categoryType: $categoryType, subCategory: $subCategory, tagLine: $tagLine, productTag: $productTag, productImage: $productImage, content: $content, sortOrder: $sortOrder, status: $status}';
  }
}
