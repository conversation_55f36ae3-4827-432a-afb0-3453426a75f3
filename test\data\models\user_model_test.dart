import 'package:flutter_test/flutter_test.dart';
import 'package:aquapartner/data/models/user_model.dart';
import 'package:aquapartner/domain/entities/user.dart';

void main() {
  group('UserModel Tests', () {
    late UserModel testUserModel;
    late User testUserEntity;
    late Map<String, dynamic> testJson;

    setUp(() {
      testUserModel = UserModel(
        id: 1,
        phoneNumber: '+919999999999',
        isVerified: true,
        mongoId: '507f1f77bcf86cd799439011',
        needsSync: false,
        createdAt: DateTime(2024, 1, 1, 12, 0, 0),
        updatedAt: DateTime(2024, 1, 2, 12, 0, 0),
      );

      testUserEntity = User(
        id: 1,
        phoneNumber: '+919999999999',
        isVerified: true,
        mongoId: '507f1f77bcf86cd799439011',
        needsSync: false,
        createdAt: DateTime(2024, 1, 1, 12, 0, 0),
        updatedAt: DateTime(2024, 1, 2, 12, 0, 0),
      );

      testJson = {
        '_id': '507f1f77bcf86cd799439011',
        'phoneNumber': '+919999999999',
        'isVerified': true,
        'createdAt': '2024-01-01T12:00:00.000',
        'updatedAt': '2024-01-02T12:00:00.000',
      };
    });

    group('Constructor Tests', () {
      test('should create UserModel with all required fields', () {
        expect(testUserModel.id, equals(1));
        expect(testUserModel.phoneNumber, equals('+919999999999'));
        expect(testUserModel.isVerified, isTrue);
        expect(testUserModel.mongoId, equals('507f1f77bcf86cd799439011'));
        expect(testUserModel.needsSync, isFalse);
        expect(testUserModel.createdAt, equals(DateTime(2024, 1, 1, 12, 0, 0)));
        expect(testUserModel.updatedAt, equals(DateTime(2024, 1, 2, 12, 0, 0)));
      });

      test('should create UserModel with default values', () {
        final userModel = UserModel(
          phoneNumber: '+919999999999',
          isVerified: false,
        );

        expect(userModel.id, equals(0));
        expect(userModel.phoneNumber, equals('+919999999999'));
        expect(userModel.isVerified, isFalse);
        expect(userModel.mongoId, isNull);
        expect(userModel.needsSync, isTrue);
        expect(userModel.createdAt, isNotNull);
        expect(userModel.updatedAt, isNotNull);
      });

      test('should create UserModel with custom createdAt and updatedAt', () {
        final customCreatedAt = DateTime(2023, 12, 25);
        final customUpdatedAt = DateTime(2023, 12, 26);

        final userModel = UserModel(
          phoneNumber: '+919999999999',
          isVerified: true,
          createdAt: customCreatedAt,
          updatedAt: customUpdatedAt,
        );

        expect(userModel.createdAt, equals(customCreatedAt));
        expect(userModel.updatedAt, equals(customUpdatedAt));
      });
    });

    group('Entity Conversion Tests', () {
      test('should convert from User entity to UserModel correctly', () {
        final userModel = UserModel.fromEntity(testUserEntity);

        expect(userModel.id, equals(testUserEntity.id));
        expect(userModel.phoneNumber, equals(testUserEntity.phoneNumber));
        expect(userModel.isVerified, equals(testUserEntity.isVerified));
        expect(userModel.mongoId, equals(testUserEntity.mongoId));
        expect(userModel.needsSync, equals(testUserEntity.needsSync));
        expect(userModel.createdAt, equals(testUserEntity.createdAt));
        expect(userModel.updatedAt, equals(testUserEntity.updatedAt));
      });

      test('should convert from UserModel to User entity correctly', () {
        final userEntity = testUserModel.toEntity();

        expect(userEntity.id, equals(testUserModel.id));
        expect(userEntity.phoneNumber, equals(testUserModel.phoneNumber));
        expect(userEntity.isVerified, equals(testUserModel.isVerified));
        expect(userEntity.mongoId, equals(testUserModel.mongoId));
        expect(userEntity.needsSync, equals(testUserModel.needsSync));
        expect(userEntity.createdAt, equals(testUserModel.createdAt));
        expect(userEntity.updatedAt, equals(testUserModel.updatedAt));
      });

      test('should maintain data integrity during entity conversions', () {
        final convertedModel = UserModel.fromEntity(testUserEntity);
        final convertedEntity = convertedModel.toEntity();

        expect(convertedEntity.id, equals(testUserEntity.id));
        expect(convertedEntity.phoneNumber, equals(testUserEntity.phoneNumber));
        expect(convertedEntity.isVerified, equals(testUserEntity.isVerified));
        expect(convertedEntity.mongoId, equals(testUserEntity.mongoId));
        expect(convertedEntity.needsSync, equals(testUserEntity.needsSync));
        expect(convertedEntity.createdAt, equals(testUserEntity.createdAt));
        expect(convertedEntity.updatedAt, equals(testUserEntity.updatedAt));
      });
    });

    group('JSON Serialization Tests', () {
      test('should convert UserModel to JSON correctly', () {
        final json = testUserModel.toJson();

        expect(json['phoneNumber'], equals('+919999999999'));
        expect(json['isVerified'], isTrue);
        expect(json['createdAt'], equals('2024-01-01T12:00:00.000'));
        expect(json.containsKey('updatedAt'), isTrue);
        expect(json.containsKey('_id'), isTrue);
      });

      test('should convert UserModel to JSON without mongoId when null', () {
        final userModelWithoutMongoId = UserModel(
          phoneNumber: '+919999999999',
          isVerified: true,
          mongoId: null,
        );

        final json = userModelWithoutMongoId.toJson();

        expect(json.containsKey('_id'), isFalse);
        expect(json['phoneNumber'], equals('+919999999999'));
        expect(json['isVerified'], isTrue);
      });

      test('should create UserModel from JSON correctly', () {
        final userModel = UserModel.fromJson(testJson);

        expect(userModel.phoneNumber, equals('+919999999999'));
        expect(userModel.isVerified, isTrue);
        expect(userModel.mongoId, equals('507f1f77bcf86cd799439011'));
        expect(userModel.needsSync, isFalse);
        expect(
          userModel.createdAt,
          equals(DateTime.parse('2024-01-01T12:00:00.000')),
        );
        expect(
          userModel.updatedAt,
          equals(DateTime.parse('2024-01-02T12:00:00.000')),
        );
      });

      test('should maintain data integrity during JSON conversions', () {
        final json = testUserModel.toJson();
        final recreatedModel = UserModel.fromJson(json);

        expect(recreatedModel.phoneNumber, equals(testUserModel.phoneNumber));
        expect(recreatedModel.isVerified, equals(testUserModel.isVerified));
        expect(
          recreatedModel.mongoId,
          contains(testUserModel.mongoId!),
        ); // ObjectId wraps the string
        expect(
          recreatedModel.needsSync,
          isFalse,
        ); // Should be false when from JSON
      });
    });

    group('CopyWith Tests', () {
      test('should create copy with updated fields', () {
        final updatedModel = testUserModel.copyWith(
          phoneNumber: '+919999999999',
          isVerified: false,
          needsSync: true,
        );

        expect(updatedModel.id, equals(testUserModel.id));
        expect(updatedModel.phoneNumber, equals('+919999999999'));
        expect(updatedModel.isVerified, isFalse);
        expect(updatedModel.mongoId, equals(testUserModel.mongoId));
        expect(updatedModel.needsSync, isTrue);
        expect(updatedModel.createdAt, equals(testUserModel.createdAt));
        expect(updatedModel.updatedAt, equals(testUserModel.updatedAt));
      });

      test(
        'should create copy with same values when no parameters provided',
        () {
          final copiedModel = testUserModel.copyWith();

          expect(copiedModel.id, equals(testUserModel.id));
          expect(copiedModel.phoneNumber, equals(testUserModel.phoneNumber));
          expect(copiedModel.isVerified, equals(testUserModel.isVerified));
          expect(copiedModel.mongoId, equals(testUserModel.mongoId));
          expect(copiedModel.needsSync, equals(testUserModel.needsSync));
          expect(copiedModel.createdAt, equals(testUserModel.createdAt));
          expect(copiedModel.updatedAt, equals(testUserModel.updatedAt));
        },
      );

      test('should update mongoId and needsSync for sync operations', () {
        final syncedModel = testUserModel.copyWith(
          mongoId: 'new_mongo_id_123',
          needsSync: false,
          updatedAt: DateTime(2024, 1, 3, 12, 0, 0),
        );

        expect(syncedModel.mongoId, equals('new_mongo_id_123'));
        expect(syncedModel.needsSync, isFalse);
        expect(syncedModel.updatedAt, equals(DateTime(2024, 1, 3, 12, 0, 0)));
      });
    });

    group('Edge Cases and Validation', () {
      test('should handle empty phone number', () {
        final userModel = UserModel(phoneNumber: '', isVerified: false);

        expect(userModel.phoneNumber, equals(''));
        expect(userModel.isVerified, isFalse);
      });

      test('should handle very long phone number', () {
        const longPhoneNumber = '+919999999999123456789';
        final userModel = UserModel(
          phoneNumber: longPhoneNumber,
          isVerified: true,
        );

        expect(userModel.phoneNumber, equals(longPhoneNumber));
      });

      test('should handle special characters in mongoId', () {
        const specialMongoId = 'special-mongo_id.123';
        final userModel = UserModel(
          phoneNumber: '+919999999999',
          isVerified: true,
          mongoId: specialMongoId,
        );

        expect(userModel.mongoId, equals(specialMongoId));
      });

      test('should handle extreme dates', () {
        final extremeDate = DateTime(1970, 1, 1);
        final userModel = UserModel(
          phoneNumber: '+919999999999',
          isVerified: true,
          createdAt: extremeDate,
          updatedAt: extremeDate,
        );

        expect(userModel.createdAt, equals(extremeDate));
        expect(userModel.updatedAt, equals(extremeDate));
      });
    });
  });
}
