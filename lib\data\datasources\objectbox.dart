import 'dart:io';

import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';

import '../../objectbox.g.dart';

/// Singleton class to manage ObjectBox store instance
class ObjectBox {
  /// The Store of this app.
  late final Store store;

  /// Create an instance of ObjectBox to use throughout the app.
  ObjectBox._create(this.store) {
    // Optional: add any additional setup code here
  }

  /// Create an instance of ObjectBox to use throughout the app.
  static Future<ObjectBox> create() async {
    final docsDir = await getApplicationDocumentsDirectory();
    final dbDir = p.join(docsDir.path, "objectbox");

    try {
      final store = await openStore(directory: dbDir);
      return ObjectBox._create(store);
    } catch (e) {
      // If there's a schema mismatch, delete the database and recreate it
      if (e.toString().contains('does not match existing UID')) {
        final dbDirectory = Directory(dbDir);
        if (await dbDirectory.exists()) {
          await dbDirectory.delete(recursive: true);
        }
        // Try again with a fresh database
        final store = await openStore(directory: dbDir);
        return ObjectBox._create(store);
      } else {
        // For other errors, rethrow
        rethrow;
      }
    }
  }

  /// Get a Box of type T
  Box<T> box<T>() => store.box<T>();

  /// Put an entity or a list of entities into the box
  int put<T>(T entity) => box<T>().put(entity);

  /// Put a list of entities into the box
  List<int> putMany<T>(List<T> entities) => box<T>().putMany(entities);

  /// Get an entity by id
  T? get<T>(int id) => box<T>().get(id);

  /// Get all entities from the box
  List<T> getAll<T>() => box<T>().getAll();

  /// Query entities with a condition
  Query<T> query<T>(Condition<T> condition) =>
      box<T>().query(condition).build();

  /// Remove an entity by id
  bool remove<T>(int id) => box<T>().remove(id);

  /// Remove multiple entities by ids
  int removeMany<T>(List<int> ids) => box<T>().removeMany(ids);

  /// Remove all entities from the box
  int removeAll<T>() => box<T>().removeAll();

  Future<void> deleteDatabase() async {
    final docsDir = await getApplicationDocumentsDirectory();
    final dbDir = Directory(p.join(docsDir.path, "objectbox"));
    if (await dbDir.exists()) {
      await dbDir.delete(recursive: true);
    }
  }

  void dispose() {
    store.close();
  }
}

/// Singleton instance of ObjectBox
late ObjectBox objectbox;

/// Initialize ObjectBox for use throughout the app
Future<void> initObjectBox() async {
  try {
    objectbox = await ObjectBox.create();
    print('ObjectBox initialized successfully');
  } catch (e) {
    print('Failed to initialize ObjectBox: $e');
    rethrow;
  }
}
