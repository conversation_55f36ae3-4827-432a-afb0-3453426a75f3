import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../entities/price_list.dart';

abstract class PriceListRepository {
  /// Gets price lists from local cache first
  Future<Either<Failure, List<PriceList>>> getPriceLists();
  
  /// Forces a sync from remote source
  Future<Either<Failure, List<PriceList>>> syncPriceLists();
  
  /// Gets a specific price list by state
  Future<Either<Failure, PriceList?>> getPriceListByState(String state);
}