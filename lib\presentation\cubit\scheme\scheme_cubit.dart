import 'dart:async';
import 'package:aquapartner/core/utils/logger.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

import '../../../core/utils/date_time_utils.dart';
import '../../../domain/entities/scheme/customer_scheme.dart';
import '../../../domain/repositories/customer_scheme_repository.dart';
import '../../../domain/services/auth_service.dart';
import 'scheme_state.dart';

class SchemeCubit extends Cubit<SchemeState> {
  final CustomerSchemeRepository repository;
  final AuthService authService;
  final AppLogger logger;
  final Connectivity connectivity;

  // Storage for current customer ID and timestamps
  String? _currentCustomerId;
  DateTime? _lastSyncTime;
  bool _isLoadingData = false;
  bool _isSyncing = false;

  // Subscription for connectivity changes
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;

  // Config for data freshness
  static const Duration _dataFreshnessThreshold = Duration(hours: 1);

  // Debounce timer for frequent calls
  Timer? _debounceTimer;

  SchemeCubit({
    required this.repository,
    required this.authService,
    required this.logger,
    required this.connectivity,
  }) : super(SchemeInitial()) {
    // Listen for connectivity changes to sync when connection is restored
    _connectivitySubscription = connectivity.onConnectivityChanged.listen(
      _handleConnectivityChange,
    );
  }

  /// Load customer scheme data with deduplication of concurrent calls
  Future<void> loadScheme() async {
    if (_isLoadingData) {
      logger.i('Already loading data, ignoring duplicate call');
      return;
    }

    _isLoadingData = true;

    try {
      // Get customer ID only if we don't have one yet
      if (_currentCustomerId == null) {
        await _getCustomerId();
        if (_currentCustomerId == null) {
          emit(
            SchemeError('User not authenticated or customer ID not available'),
          );
          _isLoadingData = false;
          return;
        }
      }

      // Update UI state to loading
      if (state is SchemeLoaded) {
        final currentState = state as SchemeLoaded;
        emit(
          SchemeLoading(
            previousScheme: currentState.scheme,
            lastSyncTime: currentState.lastSyncTime,
          ),
        );
      } else {
        emit(SchemeLoading());
      }

      // Get scheme data from repository
      final scheme = await repository.getCustomerScheme(_currentCustomerId!);

      if (scheme == null) {
        _handleDataLoadFailure('Could not load scheme data');
        return;
      }
      // Emit loaded state - optimize by checking if data actually changed
      final currentState = state;
      if (currentState is SchemeLoaded &&
          _schemeEquals(currentState.scheme, scheme) &&
          !currentState.hasError &&
          !currentState.isSyncing) {
        logger.i('Scheme data unchanged, not updating state');
      } else {
        emit(
          SchemeLoaded(
            scheme: scheme,
            isSyncing: _isSyncing,
            lastSyncTime: _lastSyncTime,
          ),
        );
      }

      // Check if we need to sync in the background using debounce
      _debouncedBackgroundSync();
    } catch (e) {
      logger.e('Error loading scheme: ${e.toString()}');
      _handleDataLoadFailure('Error: $e');
    } finally {
      _isLoadingData = false;
    }
  }

  /// Get and cache the customer ID
  Future<void> _getCustomerId() async {
    try {
      final customerResult = await authService.getCurrentCustomer();
      customerResult.fold(
        (failure) {
          logger.e('Failed to get customer ID: ');
          _currentCustomerId = null;
          emit(SchemeError("Failed to retrieve customer, try again"));
        },
        (customer) {
          if (customer != null && customer.customerId.isNotEmpty) {
            _currentCustomerId = customer.customerId;
            logger.i('Retrieved customer ID: $_currentCustomerId');
          } else {
            logger.w('Retrieved empty customer ID');
            _currentCustomerId = null;
          }
        },
      );
    } catch (e) {
      logger.e('Error getting customer ID: ${e.toString()}');
      _currentCustomerId = null;
    }
  }

  /// Handle failure during data loading
  void _handleDataLoadFailure(String errorMessage) {
    if (state is SchemeLoading &&
        (state as SchemeLoading).previousScheme != null) {
      final previousState = state as SchemeLoading;
      emit(
        SchemeLoaded(
          scheme: previousState.previousScheme!,
          isSyncing: _isSyncing,
          lastSyncTime: previousState.lastSyncTime,
          hasError: true,
          errorMessage: errorMessage,
        ),
      );
    } else {
      emit(SchemeError(errorMessage));
    }
  }

  /// Sync customer scheme data with concurrency control
  Future<void> syncScheme() async {
    if (_isSyncing) {
      logger.i('Sync already in progress, ignoring duplicate call');
      return;
    }

    if (_currentCustomerId == null) {
      logger.w('Cannot sync: No customer ID available');
      return;
    }

    _isSyncing = true;

    try {
      // Update UI to show syncing state
      if (state is SchemeLoaded) {
        final currentState = state as SchemeLoaded;
        emit(currentState.copyWith(isSyncing: true, hasError: false));
      }

      // Check connectivity before attempting sync
      final connectivityResults = await connectivity.checkConnectivity();
      final hasConnection = connectivityResults.any(
        (result) => result != ConnectivityResult.none,
      );

      if (!hasConnection) {
        throw Exception('No internet connection available');
      }

      // Perform sync
      await repository.syncCustomerScheme(_currentCustomerId!);

      // Update sync time
      _lastSyncTime = DateTime.now();
      logger.i('Scheme synced successfully for customer: $_currentCustomerId');

      // Reload data from local storage to get the updated data
      final scheme = await repository.getCustomerScheme(_currentCustomerId!);

      if (scheme != null && state is SchemeLoaded) {
        emit(
          SchemeLoaded(
            scheme: scheme,
            isSyncing: false,
            lastSyncTime: _lastSyncTime,
            hasError: false,
          ),
        );
      } else if (state is SchemeLoaded) {
        // Keep current data but update sync status
        final currentState = state as SchemeLoaded;
        emit(
          currentState.copyWith(isSyncing: false, lastSyncTime: _lastSyncTime),
        );
      }
    } catch (e) {
      logger.e('Error syncing scheme: ${e.toString()}');

      if (state is SchemeLoaded) {
        final currentState = state as SchemeLoaded;
        emit(
          currentState.copyWith(
            isSyncing: false,
            hasError: true,
            errorMessage: 'Sync failed: $e',
          ),
        );
      }
    } finally {
      _isSyncing = false;
    }
  }

  /// Refreshes data optimally by only doing one operation if possible
  Future<void> refreshScheme() async {
    if (_isLoadingData || _isSyncing) {
      logger.i('Already loading or syncing data, ignoring refresh request');
      return;
    }

    try {
      // Get customer ID if needed
      if (_currentCustomerId == null) {
        await _getCustomerId();
        if (_currentCustomerId == null) {
          emit(
            SchemeError('User not authenticated or customer ID not available'),
          );
          return;
        }
      }

      // Check connectivity
      final connectivityResults = await connectivity.checkConnectivity();
      final hasConnection = connectivityResults.any(
        (result) => result != ConnectivityResult.none,
      );

      if (hasConnection) {
        // If we have connection, sync and load in one operation
        _isSyncing = true;

        // Update UI to show syncing
        if (state is SchemeLoaded) {
          final currentState = state as SchemeLoaded;
          emit(currentState.copyWith(isSyncing: true));
        } else {
          emit(SchemeLoading());
        }

        try {
          // Sync data
          await repository.syncCustomerScheme(_currentCustomerId!);
          _lastSyncTime = DateTime.now();

          // Get updated data
          final scheme = await repository.getCustomerScheme(
            _currentCustomerId!,
          );

          if (scheme != null) {
            emit(
              SchemeLoaded(
                scheme: scheme,
                isSyncing: false,
                lastSyncTime: _lastSyncTime,
                hasError: false,
              ),
            );
          } else {
            _handleDataLoadFailure('Could not load scheme data after sync');
          }
        } catch (e) {
          logger.e('Error during refresh: ${e.toString()}');
          _handleDataLoadFailure('Refresh failed: $e');
        } finally {
          _isSyncing = false;
        }
      } else {
        // If offline, just load from cache
        await loadScheme();
      }
    } catch (e) {
      logger.e('Error refreshing scheme: ${e.toString()}');
      if (state is SchemeLoaded) {
        final currentState = state as SchemeLoaded;
        emit(
          currentState.copyWith(
            isSyncing: false,
            hasError: true,
            errorMessage: 'Refresh failed: $e',
          ),
        );
      } else {
        emit(SchemeError('Failed to refresh: $e'));
      }
    }
  }

  /// Check if data is stale and needs refresh
  bool isDataStale() {
    if (_lastSyncTime == null) return true;

    final now = DateTime.now();
    return now.difference(_lastSyncTime!) > _dataFreshnessThreshold;
  }

  /// Get formatted last sync time
  String getLastSyncTimeFormatted() {
    if (_lastSyncTime == null) return 'Never';
    return DateTimeUtils.formatRelativeTime(_lastSyncTime!);
  }

  /// Dismiss current error
  void dismissError() {
    if (state is SchemeLoaded && (state as SchemeLoaded).hasError) {
      final currentState = state as SchemeLoaded;
      emit(currentState.copyWith(hasError: false, errorMessage: null));
    }
  }

  /// Handle connectivity changes
  void _handleConnectivityChange(List<ConnectivityResult> results) {
    final hasConnection = results.any(
      (result) => result != ConnectivityResult.none,
    );

    if (hasConnection &&
        state is SchemeLoaded &&
        isDataStale() &&
        !_isSyncing) {
      logger.i('Connection restored and data is stale, syncing in background');
      _debouncedBackgroundSync();
    }
  }

  /// Debounce background sync to prevent excessive calls
  void _debouncedBackgroundSync() {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(seconds: 2), () {
      if (isDataStale() && !_isSyncing) {
        logger.i('Data is stale, syncing in background');
        syncScheme();
      }
    });
  }

  /// Compare schemes to prevent unnecessary updates
  bool _schemeEquals(CustomerScheme a, CustomerScheme b) {
    // Implement a proper comparison based on your entity structure
    // This is a basic implementation that might need adjustment
    return a.customerId == b.customerId &&
        a.toString() ==
            b.toString(); // Simplified - improve based on your entity
  }

  @override
  Future<void> close() {
    _debounceTimer?.cancel();
    _connectivitySubscription?.cancel();
    return super.close();
  }
}
