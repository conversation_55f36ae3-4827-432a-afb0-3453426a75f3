import 'dart:convert';

import '../../../domain/entities/dashboard/dashboard_entity.dart';
import 'dashboard_model.dart';

class DashboardJsonParser {
  static DashboardEntity modelToEntity(DashboardModel model) {
    return DashboardEntity(
      customerId: model.customerId,
      sales: _parseSales(model.salesJson),
      payments: _parsePayments(model.paymentsJson),
      dues: _parseDues(model.duesJson),
      salesReturn: model.salesReturnJson, // Added salesReturn
      categoryTypeSales: _parseCategoryTypeSales(model.categoryTypeSalesJson),
      liquidation: _parseLiquidation(model.liquidationJson),
      myFarmers: _parseMyFarmers(model.myFarmersJson),
    );
  }

  static SalesEntity _parseSales(String salesJson) {
    try {
      final Map<String, dynamic> salesMap = json.decode(salesJson);
      final Map<String, List<MonthSalesEntity>> yearlyData = {};

      salesMap.forEach((year, monthsData) {
        if (monthsData is List) {
          final List<MonthSalesEntity> monthsList = [];

          for (var monthData in monthsData) {
            if (monthData is Map) {
              monthData.forEach((month, data) {
                if (data is Map &&
                    data.containsKey('totalSales') &&
                    data.containsKey('weeks')) {
                  final List<WeekSalesEntity> weeks = [];

                  if (data['weeks'] is List) {
                    for (var weekData in data['weeks']) {
                      if (weekData is Map) {
                        weekData.forEach((week, amount) {
                          weeks.add(
                            WeekSalesEntity(
                              week: week.toString(), // Ensure week is string
                              amount: (amount is num) ? amount.toDouble() : 0.0,
                            ),
                          );
                        });
                      }
                    }
                  }

                  monthsList.add(
                    MonthSalesEntity(
                      month: month.toString(), // Ensure month is string
                      totalSales:
                          (data['totalSales'] is num)
                              ? data['totalSales'].toDouble()
                              : 0.0,
                      weeks: weeks,
                    ),
                  );
                }
              });
            }
          }

          yearlyData[year] = monthsList;
        }
      });

      return SalesEntity(yearlyData: yearlyData);
    } catch (e) {
      print('Error parsing sales data: $e');
      return SalesEntity(yearlyData: {});
    }
  }

  static PaymentsEntity _parsePayments(String paymentsJson) {
    try {
      final Map<String, dynamic> paymentsMap = json.decode(paymentsJson);
      final Map<String, List<MonthPaymentEntity>> yearlyData = {};

      paymentsMap.forEach((year, monthsData) {
        if (monthsData is List) {
          final List<MonthPaymentEntity> monthsList = [];

          for (var monthData in monthsData) {
            if (monthData is Map) {
              monthData.forEach((month, data) {
                if (data is Map &&
                    data.containsKey('totalPayment') &&
                    data.containsKey('weeks')) {
                  final List<WeekPaymentEntity> weeks = [];

                  if (data['weeks'] is List) {
                    for (var weekData in data['weeks']) {
                      if (weekData is Map) {
                        weekData.forEach((week, amount) {
                          weeks.add(
                            WeekPaymentEntity(
                              week: week.toString(), // Ensure week is string
                              amount: (amount is num) ? amount.toDouble() : 0.0,
                            ),
                          );
                        });
                      }
                    }
                  }

                  monthsList.add(
                    MonthPaymentEntity(
                      month: month.toString(), // Ensure month is string
                      totalPayment:
                          (data['totalPayment'] is num)
                              ? data['totalPayment'].toDouble()
                              : 0.0,
                      weeks: weeks,
                    ),
                  );
                }
              });
            }
          }

          yearlyData[year] = monthsList;
        }
      });

      return PaymentsEntity(yearlyData: yearlyData);
    } catch (e) {
      print('Error parsing payments data: $e');
      return PaymentsEntity(yearlyData: {});
    }
  }

  static List<DueTierEntity> _parseDues(String duesJson) {
    try {
      final List<dynamic> duesList = json.decode(duesJson);
      return duesList.map((due) {
        return DueTierEntity(
          ageTier: due['ageTier'] as String? ?? '',
          totalAmount:
              (due['totalAmount'] is num) ? due['totalAmount'].toDouble() : 0.0,
        );
      }).toList();
    } catch (e) {
      print('Error parsing dues data: $e');
      return [];
    }
  }

  static List<CategorySalesEntity> _parseCategoryTypeSales(
    String categoryTypeSalesJson,
  ) {
    try {
      final List<dynamic> categoriesList = json.decode(categoryTypeSalesJson);
      return categoriesList.map((category) {
        final Map<String, List<MonthCategorySalesEntity>> yearlySales = {};

        if (category['yearlySales'] is Map) {
          category['yearlySales'].forEach((year, monthsData) {
            if (monthsData is List) {
              final List<MonthCategorySalesEntity> monthsList = [];

              for (var monthData in monthsData) {
                if (monthData is Map) {
                  monthData.forEach((month, data) {
                    if (data is Map &&
                        data.containsKey('totalPayableAmount') &&
                        data.containsKey('weeks')) {
                      final List<WeekCategorySalesEntity> weeks = [];

                      if (data['weeks'] is List) {
                        for (var weekData in data['weeks']) {
                          if (weekData is Map) {
                            weekData.forEach((week, amount) {
                              weeks.add(
                                WeekCategorySalesEntity(
                                  week:
                                      week.toString(), // Ensure week is string
                                  amount:
                                      (amount is num) ? amount.toDouble() : 0.0,
                                ),
                              );
                            });
                          }
                        }
                      }

                      monthsList.add(
                        MonthCategorySalesEntity(
                          month: month.toString(), // Ensure month is string
                          totalPayableAmount:
                              (data['totalPayableAmount'] is num)
                                  ? data['totalPayableAmount'].toDouble()
                                  : 0.0,
                          weeks: weeks,
                        ),
                      );
                    }
                  });
                }
              }

              yearlySales[year] = monthsList;
            }
          });
        }

        return CategorySalesEntity(
          categoryType: category['categoryType'] as String? ?? '',
          yearlySales: yearlySales,
        );
      }).toList();
    } catch (e) {
      print('Error parsing category sales data: $e');
      return [];
    }
  }

  static LiquidationEntity _parseLiquidation(String liquidationJson) {
    try {
      final Map<String, dynamic> liquidationMap = json.decode(liquidationJson);
      final List<YearLiquidationEntity> liquidationByYear = [];

      if (liquidationMap.containsKey('liquidationByYear') &&
          liquidationMap['liquidationByYear'] is List) {
        for (var yearData in liquidationMap['liquidationByYear']) {
          if (yearData is Map &&
              yearData.containsKey('year') &&
              yearData.containsKey('months')) {
            final Map<String, double> months = {};

            if (yearData['months'] is Map) {
              yearData['months'].forEach((month, amount) {
                months[month.toString()] =
                    (amount is num) ? amount.toDouble() : 0.0;
              });
            }

            liquidationByYear.add(
              YearLiquidationEntity(
                year: yearData['year'] as String? ?? '',
                months: months,
              ),
            );
          }
        }
      }

      return LiquidationEntity(
        totalLiquidation:
            (liquidationMap['totalLiquidation'] is num)
                ? liquidationMap['totalLiquidation'].toDouble()
                : 0.0,
        liquidationByYear: liquidationByYear,
      );
    } catch (e) {
      print('Error parsing liquidation data: $e');
      return LiquidationEntity(totalLiquidation: 0.0, liquidationByYear: []);
    }
  }

  static MyFarmersEntity _parseMyFarmers(String myFarmersJson) {
    try {
      final Map<String, dynamic> myFarmersMap = json.decode(myFarmersJson);
      final List<FarmerEntity> totalFarmers = [];

      if (myFarmersMap.containsKey('totalFarmers') &&
          myFarmersMap['totalFarmers'] is List) {
        for (var farmerData in myFarmersMap['totalFarmers']) {
          if (farmerData is Map &&
              farmerData.containsKey('farmerName') &&
              farmerData.containsKey('visits')) {
            final List<FarmerVisitEntity> visits = [];

            if (farmerData['visits'] is List) {
              for (var visitData in farmerData['visits']) {
                if (visitData is Map) {
                  visits.add(
                    FarmerVisitEntity(
                      createdDateTime: _parseDateTime(
                        visitData['createdDateTime'],
                      ),
                      doc:
                          visitData['doc'] is int
                              ? visitData['doc']
                              : int.tryParse(
                                    visitData['doc']?.toString() ?? '0',
                                  ) ??
                                  0,
                      pondId: visitData['pondId'] as String? ?? '',
                      farmerId: visitData['farmerId'] as String? ?? '',
                      mobileNumber: visitData['mobileNumber'] as String? ?? '',
                      productUsed: visitData['productUsed'] as String? ?? '',
                    ),
                  );
                }
              }
            }

            totalFarmers.add(
              FarmerEntity(
                farmerName: farmerData['farmerName'] as String? ?? '',
                visits: visits,
              ),
            );
          }
        }
      }

      // The provided JSON does not contain 'potentialFarmers', maintaining the default value of 0
      return MyFarmersEntity(
        totalFarmers: totalFarmers,
        potentialFarmers:
            (myFarmersMap['potentialFarmers'] is num)
                ? myFarmersMap['potentialFarmers'] as int
                : 0,
      );
    } catch (e) {
      print('Error parsing my farmers data: $e');
      return MyFarmersEntity(totalFarmers: [], potentialFarmers: 0);
    }
  }

  // Helper method to safely parse DateTime
  static DateTime _parseDateTime(dynamic dateTimeString) {
    if (dateTimeString is String) {
      try {
        return DateTime.parse(dateTimeString);
      } catch (e) {
        print('Error parsing date: $e');
      }
    }
    return DateTime.now(); // Default value if parsing fails
  }
}
