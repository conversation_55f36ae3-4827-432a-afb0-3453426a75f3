{"_note1": "KEEP THIS FILE! Check it into a version control system (VCS) like git.", "_note2": "ObjectBox manages crucial IDs for your object model. See docs for details.", "_note3": "If you have VCS merge conflicts, you must resolve them according to ObjectBox docs.", "entities": [{"id": "1:8132863513438965635", "lastPropertyId": "9:4938733412201330713", "name": "UserModel", "properties": [{"id": "2:7813727624144775754", "name": "id", "type": 6, "flags": 1}, {"id": "4:526574474414702220", "name": "phoneNumber", "type": 9}, {"id": "5:7980170251532068486", "name": "isVerified", "type": 1}, {"id": "6:7980500831310509350", "name": "createdAt", "type": 10}, {"id": "7:5208234223804611006", "name": "updatedAt", "type": 10}, {"id": "8:2118680721624706564", "name": "mongoId", "type": 9}, {"id": "9:4938733412201330713", "name": "needsSync", "type": 1}], "relations": []}, {"id": "10:6497017795649644319", "lastPropertyId": "5:8104177192492005546", "name": "ObjectBoxPriceListModel", "properties": [{"id": "1:8556425286383023064", "name": "id", "type": 6, "flags": 1}, {"id": "3:2626206172310092672", "name": "state", "type": 9, "flags": 2048, "indexId": "6:3742090795844408593"}, {"id": "4:3754461554430980708", "name": "date", "type": 9}, {"id": "5:8104177192492005546", "name": "pricesJson", "type": 9}], "relations": []}, {"id": "11:4053864638235085459", "lastPropertyId": "6:4553388345887935296", "name": "ObjectBoxProductCatalogueModel", "properties": [{"id": "1:8737843534052490798", "name": "id", "type": 6, "flags": 1}, {"id": "2:653327529234038547", "name": "name", "type": 9, "flags": 2048, "indexId": "7:8040199475711434506"}, {"id": "3:4228581716791851764", "name": "image", "type": 9}, {"id": "4:8128377353822469712", "name": "sortOrder", "type": 9}, {"id": "5:1809192526727901814", "name": "status", "type": 9}, {"id": "6:4553388345887935296", "name": "productsJson", "type": 9}], "relations": []}, {"id": "12:7833949612224344513", "lastPropertyId": "10:3887770387528273979", "name": "ObjectBoxInterestedProductModel", "properties": [{"id": "1:8513985077141153372", "name": "id", "type": 6, "flags": 1}, {"id": "2:43613475106035517", "name": "mongoId", "type": 9}, {"id": "3:1034804957431170406", "name": "customerId", "type": 9}, {"id": "5:1971598139448684571", "name": "mobile", "type": 9}, {"id": "7:7307437366485070377", "name": "productName", "type": 9}, {"id": "8:4856697159961573277", "name": "datetimeString", "type": 9}, {"id": "9:3987825882492718805", "name": "source", "type": 9}, {"id": "10:3887770387528273979", "name": "isSynced", "type": 1}], "relations": []}, {"id": "13:8468007029682329754", "lastPropertyId": "11:5622652746103328370", "name": "DashboardModel", "properties": [{"id": "1:9022496726046368994", "name": "id", "type": 6, "flags": 1}, {"id": "2:6929165119548024968", "name": "customerId", "type": 9}, {"id": "3:4997330481003813196", "name": "salesJson", "type": 9}, {"id": "4:4543177410590619780", "name": "paymentsJson", "type": 9}, {"id": "5:5530082251785703314", "name": "dues<PERSON><PERSON>", "type": 9}, {"id": "6:582387755001714748", "name": "categoryTypeSalesJson", "type": 9}, {"id": "7:9172942400491217766", "name": "liquidationJson", "type": 9}, {"id": "8:2418990437687080394", "name": "myF<PERSON><PERSON><PERSON>son", "type": 9}, {"id": "9:254608919767970805", "name": "isSynced", "type": 1}, {"id": "10:2272719825210090880", "name": "lastSyncedAt", "type": 10}, {"id": "11:5622652746103328370", "name": "salesReturnJson", "type": 8}], "relations": []}, {"id": "15:2622465375205671178", "lastPropertyId": "12:3859852864115044945", "name": "ObjectBoxCustomerModel", "properties": [{"id": "1:6362589730972117812", "name": "id", "type": 6, "flags": 1}, {"id": "2:1859011929673787960", "name": "customerId", "type": 9}, {"id": "3:7858176125402207777", "name": "customerName", "type": 9}, {"id": "4:5460500779320762236", "name": "email", "type": 9}, {"id": "5:6454775288910420704", "name": "mobileNumber", "type": 9}, {"id": "6:2334873330753522952", "name": "companyName", "type": 9}, {"id": "7:3636794851379987538", "name": "gstNo", "type": 9}, {"id": "8:6912575503435310675", "name": "businessVertical", "type": 9}, {"id": "9:7295265513261415069", "name": "customerCode", "type": 9}, {"id": "10:5917572739379987366", "name": "billing<PERSON><PERSON>ress", "type": 9}, {"id": "11:4629293298796669117", "name": "isSynced", "type": 1}, {"id": "12:3859852864115044945", "name": "mongoId", "type": 9}], "relations": []}, {"id": "16:4882109654508943798", "lastPropertyId": "5:6103668384829530570", "name": "CustomerSchemeModel", "properties": [{"id": "1:7260573194139425711", "name": "id", "type": 6, "flags": 1}, {"id": "2:6709881415313945185", "name": "customerId", "type": 9}, {"id": "3:7583906144425343118", "name": "customerName", "type": 9}, {"id": "4:3236732186720400177", "name": "sales", "type": 8}, {"id": "5:6103668384829530570", "name": "payment", "type": 8}], "relations": []}, {"id": "17:3701370663433457316", "lastPropertyId": "11:4153835492055554100", "name": "PersonDetailsModel", "properties": [{"id": "1:6147390920666881630", "name": "id", "type": 6, "flags": 1}, {"id": "2:792125977741017443", "name": "rowId", "type": 9}, {"id": "3:256517745059490305", "name": "customerName", "type": 9}, {"id": "4:3187848432950131906", "name": "email", "type": 9}, {"id": "5:3948870499311951649", "name": "status", "type": 9}, {"id": "6:865920990051817821", "name": "mobileNumber", "type": 9}, {"id": "7:2823896851968162335", "name": "userId", "type": 9}, {"id": "8:9188961002572575732", "name": "modifiedTime", "type": 10}, {"id": "9:8684926135621129447", "name": "empId", "type": 9}, {"id": "10:3125156980840738563", "name": "profile", "type": 9}, {"id": "11:4153835492055554100", "name": "supportPersonId", "type": 11, "flags": 520, "indexId": "8:4638698375262512895", "relationTarget": "SupportPersonModel"}], "relations": []}, {"id": "18:1051626658990781257", "lastPropertyId": "3:632205812450862498", "name": "SupportPersonModel", "properties": [{"id": "1:783141879205471926", "name": "id", "type": 6, "flags": 1}, {"id": "2:4332631631160806328", "name": "profile", "type": 9}, {"id": "3:632205812450862498", "name": "customerSchemeId", "type": 11, "flags": 520, "indexId": "9:8762833972066506423", "relationTarget": "CustomerSchemeModel"}], "relations": [{"id": "2:*****************", "name": "details", "targetId": "17:3701370663433457316"}]}, {"id": "19:2444024015785672861", "lastPropertyId": "10:6098133699297006561", "name": "AccountStatementEntryModel", "properties": [{"id": "1:2928430498290875247", "name": "id", "type": 6, "flags": 1}, {"id": "2:950680320747994017", "name": "txnDate", "type": 10}, {"id": "3:341790021140250483", "name": "vchType", "type": 9}, {"id": "4:2466611929087199845", "name": "invoiceNumber", "type": 9}, {"id": "5:8469104074325282159", "name": "particulars", "type": 9}, {"id": "6:*******************", "name": "debit", "type": 8}, {"id": "7:588032369962280548", "name": "credit", "type": 8}, {"id": "8:7677653408465930989", "name": "balance", "type": 8}, {"id": "9:3696303761255138272", "name": "amount", "type": 8}, {"id": "10:6098133699297006561", "name": "accountStatementId", "type": 11, "flags": 520, "indexId": "10:2502110069488155991", "relationTarget": "AccountStatementModel"}], "relations": []}, {"id": "20:5979677694765782216", "lastPropertyId": "3:1855142523239838595", "name": "AccountStatementModel", "properties": [{"id": "1:6396700639685618170", "name": "id", "type": 6, "flags": 1}, {"id": "2:7122518752470478765", "name": "customerId", "type": 9}, {"id": "3:1855142523239838595", "name": "lastSyncTime", "type": 10}], "relations": []}, {"id": "25:1687087133803093204", "lastPropertyId": "15:*******************", "name": "ObjectBoxSMRReportModel", "properties": [{"id": "1:6397847808783189873", "name": "dbId", "type": 6, "flags": 1}, {"id": "2:1450841342048403087", "name": "id", "type": 9}, {"id": "3:5375964479294417724", "name": "so", "type": 9}, {"id": "4:711710645736501298", "name": "partner", "type": 9}, {"id": "5:5754368479896380668", "name": "partnerId", "type": 9}, {"id": "6:6035029412205846250", "name": "productName", "type": 9}, {"id": "7:830511392432985601", "name": "startDateMillis", "type": 6}, {"id": "8:3256267686538221989", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": 6}, {"id": "9:3334466111894879092", "name": "openingBalance", "type": 6}, {"id": "10:3113039439677848166", "name": "invoice", "type": 6}, {"id": "11:3179800971532456492", "name": "srn", "type": 6}, {"id": "12:4727647823855550433", "name": "closingBalance", "type": 6}, {"id": "13:3968256279929481908", "name": "sales", "type": 6}, {"id": "15:*******************", "name": "status", "type": 9}], "relations": []}, {"id": "26:5416699127255561662", "lastPropertyId": "21:6237716295348987742", "name": "SalesOrderItemModel", "properties": [{"id": "1:7167440356484885687", "name": "id", "type": 6, "flags": 1}, {"id": "2:7737593003972711591", "name": "itemId", "type": 9}, {"id": "3:3100404337787651179", "name": "salesOrderId", "type": 11, "flags": 520, "indexId": "21:2009609364210507854", "relationTarget": "SalesOrderModel"}, {"id": "4:2456863774633971004", "name": "productId", "type": 9}, {"id": "5:3946714783675453608", "name": "createdTime", "type": 10}, {"id": "6:3423228074150619622", "name": "entityDiscountPercent", "type": 9}, {"id": "7:784117138974314833", "name": "hsnSac", "type": 9}, {"id": "8:3393527901717541499", "name": "invoicedQuantityCancelled", "type": 9}, {"id": "9:2609076071390153045", "name": "itemName", "type": 9}, {"id": "10:7765421063351668472", "name": "itemPrice", "type": 8}, {"id": "11:2331774167357820782", "name": "lastModifiedTime", "type": 10}, {"id": "12:830206783349988229", "name": "manuallyFulfilledQuantity", "type": 9}, {"id": "13:180022786844220169", "name": "nonPackageQuantity", "type": 9}, {"id": "14:5696159774009477777", "name": "placeOfSupply", "type": 9}, {"id": "15:5099135192701815505", "name": "quantityDelivered", "type": 9}, {"id": "16:7158714979499589809", "name": "quantityDropshipped", "type": 9}, {"id": "17:9010704902478492513", "name": "quantityPacked", "type": 9}, {"id": "18:7780765051678068175", "name": "salesVertices", "type": 9}, {"id": "19:709681365198656245", "name": "sno", "type": 9}, {"id": "20:941887312270880434", "name": "total", "type": 8}, {"id": "21:6237716295348987742", "name": "salesOrderIdValue", "type": 9}], "relations": []}, {"id": "27:814500980066085354", "lastPropertyId": "17:3623601299003227251", "name": "SalesOrderModel", "properties": [{"id": "1:7405754324650357678", "name": "id", "type": 6, "flags": 1}, {"id": "2:5343516595216219020", "name": "salesOrderId", "type": 9}, {"id": "3:1363887062538266965", "name": "addressId", "type": 9}, {"id": "4:7807200534879677880", "name": "createdTime", "type": 10}, {"id": "5:2019887442922116963", "name": "customerId", "type": 9}, {"id": "6:3583856748476134431", "name": "invoicedStatus", "type": 9}, {"id": "7:3565157346822835000", "name": "lastModifiedTime", "type": 10}, {"id": "8:3921361928928515776", "name": "orderSource", "type": 9}, {"id": "9:2875833075594970722", "name": "paidStatus", "type": 9}, {"id": "10:4040722605130953239", "name": "paymentTermsLabel", "type": 9}, {"id": "11:458825167804020246", "name": "saleOrderDate", "type": 9}, {"id": "12:3981989085695837120", "name": "salesChannel", "type": 9}, {"id": "13:3897852389454277407", "name": "salesOrderNumber", "type": 9}, {"id": "14:819497710774748801", "name": "subTotal", "type": 8}, {"id": "15:7072201275031871278", "name": "total", "type": 8}, {"id": "16:7158113136285340973", "name": "isSynced", "type": 1}, {"id": "17:3623601299003227251", "name": "lastSyncedAt", "type": 10}], "relations": []}, {"id": "28:7619398349975584887", "lastPropertyId": "51:441455897292697180", "name": "DuesInvoiceModel", "properties": [{"id": "1:7221392760934666100", "name": "dbId", "type": 6, "flags": 1}, {"id": "2:6023872549552880213", "name": "id", "type": 9}, {"id": "3:3483892206142404005", "name": "invoiceId", "type": 9}, {"id": "4:7063254952992476396", "name": "invoiceNumber", "type": 9}, {"id": "5:2190826640675120663", "name": "invoiceDate", "type": 9}, {"id": "6:5697410686505965898", "name": "customerId", "type": 9}, {"id": "7:7456241346203121943", "name": "customerCode", "type": 9}, {"id": "8:398327572644078650", "name": "customerName", "type": 9}, {"id": "9:131798308043163566", "name": "customerDistrict", "type": 9}, {"id": "10:1772126115172089239", "name": "customerState", "type": 9}, {"id": "11:8904552996483299871", "name": "customerType", "type": 9}, {"id": "12:1840444700691947883", "name": "onBoardedTime", "type": 9}, {"id": "13:4260176181610541630", "name": "categoryType", "type": 9}, {"id": "14:9081603995030863668", "name": "invoiceType", "type": 9}, {"id": "15:7498461819220089835", "name": "retailType", "type": 9}, {"id": "16:7958611760564938994", "name": "invoiceStatus", "type": 9}, {"id": "17:7167825524676708325", "name": "invoiceRaisedBy", "type": 9}, {"id": "18:3498666996772316161", "name": "mode", "type": 9}, {"id": "19:7800248198626572704", "name": "businessVertical", "type": 9}, {"id": "20:6204560503607037547", "name": "feedCreditLimit", "type": 9}, {"id": "21:3389197110740654563", "name": "nonFeedCreditLimit", "type": 9}, {"id": "22:7033647679331004774", "name": "harvestCreditLimit", "type": 9}, {"id": "23:52570706318842876", "name": "totalSalesInclTax", "type": 8}, {"id": "24:3003028962167604791", "name": "totalSalesExclTax", "type": 8}, {"id": "25:1890601964275028998", "name": "tcsAmount", "type": 8}, {"id": "26:*******************", "name": "tdsAmount", "type": 8}, {"id": "27:2501809142453363128", "name": "amountAfterTcs", "type": 8}, {"id": "28:7774996494545512508", "name": "shippingCharges", "type": 8}, {"id": "29:2753145153817392635", "name": "feedAmountAfterTcs", "type": 9}, {"id": "30:158733123297542433", "name": "nonFeedAmountAfterTcs", "type": 8}, {"id": "31:2619648730580184171", "name": "creditNoteAmountWithTcs", "type": 8}, {"id": "32:8661217912258206786", "name": "payableAmount", "type": 8}, {"id": "33:593345335258065642", "name": "paidAmount", "type": 8}, {"id": "34:*******************", "name": "due", "type": 8}, {"id": "35:5529948356816085672", "name": "dueDate", "type": 9}, {"id": "36:8980716763372168926", "name": "dueDays", "type": 6}, {"id": "37:3970908177143511512", "name": "aging", "type": 9}, {"id": "38:*******************", "name": "aging1", "type": 9}, {"id": "39:3282417326346831659", "name": "paymentCredibility", "type": 9}, {"id": "40:5387381558480726994", "name": "lastSyncTimestamp", "type": 6}, {"id": "41:*******************", "name": "agingGroup", "type": 9}, {"id": "42:6915059801132089276", "name": "totalPurchase", "type": 8}, {"id": "43:8273125161154969619", "name": "totalSales", "type": 8}, {"id": "44:2773420444728003732", "name": "salesTier", "type": 9}, {"id": "45:7889574794486911301", "name": "healthcareSales", "type": 8}, {"id": "46:6808074449000085639", "name": "feedSales", "type": 8}, {"id": "47:1683772898227190673", "name": "chemicalSales", "type": 8}, {"id": "48:7580631555469817698", "name": "equipmentSales", "type": 8}, {"id": "49:6895125440864941958", "name": "harvestSales", "type": 8}, {"id": "50:9091561991743632346", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": 8}, {"id": "51:441455897292697180", "name": "lastPaidDate", "type": 9}], "relations": []}, {"id": "29:127483350007423367", "lastPropertyId": "7:4512270287764814387", "name": "DuesSummaryModel", "properties": [{"id": "1:345235054106148237", "name": "dbId", "type": 6, "flags": 1}, {"id": "2:2991516920252848603", "name": "customerId", "type": 9}, {"id": "6:1412528597938276577", "name": "lastSyncTimestamp", "type": 6}, {"id": "7:4512270287764814387", "name": "totalDue", "type": 8}], "relations": []}, {"id": "30:9091547537228068617", "lastPropertyId": "6:8582727884228263061", "name": "DuesAgingGroupModel", "properties": [{"id": "1:1715781981689014823", "name": "dbId", "type": 6, "flags": 1}, {"id": "2:65026892410913163", "name": "customerId", "type": 9}, {"id": "3:4824358007860747929", "name": "totalPayableAmount", "type": 8}, {"id": "4:5702601372873634046", "name": "aging", "type": 9}, {"id": "5:4646503659370937824", "name": "dueDays", "type": 6}, {"id": "6:8582727884228263061", "name": "lastSyncTimestamp", "type": 6}], "relations": []}, {"id": "31:7816786059719311089", "lastPropertyId": "17:6747616249130049067", "name": "ObjectBoxInvoiceItemModel", "properties": [{"id": "1:3688884873476147441", "name": "id", "type": 6, "flags": 1}, {"id": "2:3872318638764654221", "name": "itemId", "type": 9, "flags": 2080, "indexId": "22:3912524812525392017"}, {"id": "3:4714000339497844760", "name": "productId", "type": 9}, {"id": "4:942107222364866681", "name": "itemName", "type": 9}, {"id": "5:7910087827951546296", "name": "quantity", "type": 8}, {"id": "6:5865147781021639805", "name": "invoiceId", "type": 11, "flags": 520, "indexId": "23:915386503993134094", "relationTarget": "ObjectBoxInvoiceModel"}, {"id": "7:1947070160035015647", "name": "createdTime", "type": 12}, {"id": "8:516513924816206289", "name": "discountAmount", "type": 8}, {"id": "9:3385496905153057694", "name": "hsnSac", "type": 9}, {"id": "10:8671827139221004908", "name": "itemPrice", "type": 8}, {"id": "11:1058950144964030219", "name": "lastModifiedTime", "type": 12}, {"id": "12:2934306333960414288", "name": "placeOfSupply", "type": 9}, {"id": "13:1855909174778511250", "name": "productCategory", "type": 9}, {"id": "14:3145052887252235700", "name": "source", "type": 9}, {"id": "15:6098840131587579956", "name": "subTotal", "type": 8}, {"id": "16:8626289698790986047", "name": "total", "type": 8}, {"id": "17:6747616249130049067", "name": "invoiceIdValue", "type": 9}], "relations": []}, {"id": "32:2984471775005976020", "lastPropertyId": "15:4672069971120187876", "name": "ObjectBoxInvoiceModel", "properties": [{"id": "1:2969209294938822207", "name": "id", "type": 6, "flags": 1}, {"id": "2:742984557286471550", "name": "invoiceId", "type": 9, "flags": 2080, "indexId": "24:1961136530793205594"}, {"id": "3:3745630360584062594", "name": "addressId", "type": 9}, {"id": "4:1519386098629883908", "name": "ageInDays", "type": 6}, {"id": "5:8370055229180809377", "name": "ageTier", "type": 9}, {"id": "6:1388802623974722053", "name": "balance", "type": 8}, {"id": "7:2651638587235825050", "name": "customerId", "type": 9}, {"id": "8:2376053456584480940", "name": "deliveryMode", "type": 9}, {"id": "9:1884854625849539497", "name": "deliveryStatus", "type": 9}, {"id": "10:1518941922949514244", "name": "dueDate", "type": 12}, {"id": "11:5754963795496695956", "name": "invoiceDate", "type": 12}, {"id": "12:1767104733507217961", "name": "invoiceNumber", "type": 9}, {"id": "13:7731550889137730817", "name": "invoiceStatus", "type": 9}, {"id": "14:7438185837840136530", "name": "subTotal", "type": 8}, {"id": "15:4672069971120187876", "name": "total", "type": 8}], "relations": []}, {"id": "33:679105967871262757", "lastPropertyId": "10:578327840526067982", "name": "CustomerPaymentModel", "properties": [{"id": "1:1792888609935635447", "name": "dbId", "type": 6, "flags": 1}, {"id": "2:97659671459228479", "name": "id", "type": 9}, {"id": "3:8601900336231996056", "name": "customerId", "type": 9}, {"id": "4:6123934333867825743", "name": "customerName", "type": 9}, {"id": "5:9209896867895205658", "name": "categoryType", "type": 9}, {"id": "6:5459970059774669810", "name": "amount", "type": 9}, {"id": "7:8824680098220050848", "name": "paymentsDate", "type": 9}, {"id": "8:5603741182695248744", "name": "paymentId", "type": 9}, {"id": "9:5889486873869265141", "name": "paymentsMode", "type": 9}, {"id": "10:578327840526067982", "name": "paymentsNumber", "type": 9}], "relations": []}, {"id": "34:6820031882220582708", "lastPropertyId": "17:8360644782941912249", "name": "CreditNoteModel", "properties": [{"id": "1:8344894409991460058", "name": "dbId", "type": 6, "flags": 1}, {"id": "2:8540249337993205732", "name": "customerId", "type": 9, "flags": 2048, "indexId": "25:952934008462249335"}, {"id": "3:5397776548367442609", "name": "id", "type": 9, "flags": 34848, "indexId": "26:2407415480963475852"}, {"id": "4:2535383220083015425", "name": "customerName", "type": 9}, {"id": "5:3737282665629663668", "name": "date", "type": 10}, {"id": "6:*******************", "name": "creditNoteId", "type": 9}, {"id": "7:2663345182548744627", "name": "creditNoteNumber", "type": 9}, {"id": "8:1516585349460284434", "name": "invoiceId", "type": 9}, {"id": "9:665538327453405788", "name": "productId", "type": 9}, {"id": "10:1836079966870208808", "name": "itemName", "type": 9}, {"id": "11:6813492393889107428", "name": "brand", "type": 9}, {"id": "12:1820580770661053171", "name": "itemCategory", "type": 9}, {"id": "13:*******************", "name": "categoryType", "type": 9}, {"id": "14:1903214856020461235", "name": "quantity", "type": 6}, {"id": "15:7078132522428659070", "name": "amount", "type": 8}, {"id": "16:******************", "name": "tag", "type": 9}, {"id": "17:8360644782941912249", "name": "category", "type": 9}], "relations": []}, {"id": "35:8133874219143661632", "lastPropertyId": "3:1658834792794860199", "name": "FarmerEntity", "properties": [{"id": "1:5883270228869891054", "name": "id", "type": 6, "flags": 1}, {"id": "2:*******************", "name": "name", "type": 9}, {"id": "3:1658834792794860199", "name": "mobileNumber", "type": 9}], "relations": []}, {"id": "36:5990479454950517684", "lastPropertyId": "13:5923613917358837074", "name": "VisitEntity", "properties": [{"id": "1:7747438256384921919", "name": "id", "type": 6, "flags": 1}, {"id": "2:3672420115274906416", "name": "createdDateTimeMillis", "type": 6}, {"id": "3:3859352571271079345", "name": "doc", "type": 6}, {"id": "7:4150908857375190288", "name": "mobileNumber", "type": 9}, {"id": "9:4592440719662545700", "name": "productUsed", "type": 9}, {"id": "10:2095085204175045813", "name": "farmerId", "type": 11, "flags": 520, "indexId": "29:4251531164893688960", "relationTarget": "FarmerEntity"}, {"id": "12:12617839105876289", "name": "pondId", "type": 9}, {"id": "13:5923613917358837074", "name": "<PERSON><PERSON><PERSON>", "type": 9}], "relations": []}], "lastEntityId": "36:5990479454950517684", "lastIndexId": "29:4251531164893688960", "lastRelationId": "3:6048782637445196261", "lastSequenceId": "0:0", "modelVersion": 5, "modelVersionParserMinimum": 5, "retiredEntityUids": [2866476964716789107, 6445089904477806503, 8533437899419862511, 6346573762641548921, 3601745023699878972, 7020672411963003329, 1752825334234401646, 1790285891352590687, 5676693064730952399, 1650133050956712868, 4509021158960870247, 4146949866952824582, 6404753190517256118], "retiredIndexUids": [7255140425930971515, 2119861996282406426, 3267626651500785862, 5011226780311576653, 4462066830847960845, 6873172300178442970, 2194643639767647429, 3607978129057609020, 8936514519946942187, 7310727773008741557, 296498085273555962, 1015949556075453124], "retiredPropertyUids": [8388459128206106591, 5636055644662326932, 1169265267166184952, 2388711096337726496, 8861020485375795895, 270837165743595999, 793938725259152277, 1563202682503174701, 8355976867704545661, 5099237017975173754, 7075728947111179954, 4322425104449788961, 3993589733776758073, 4768657512218037365, 1575436969333688208, 2181172578181573078, 2476321902614475038, 4556729902342696833, 7651943591486451077, 1182891608037039387, 2073137537291178056, 3874737163156346881, 4991812519640035851, 5612035577542603083, 8561785599753987957, 250153355248888333, 7062226278682579236, 4054982596461724859, 2524102093304326149, 5241893158921378181, 7657628598326822192, 6594852177405164434, 2501801424973031461, 789569295604937614, 2287258747934666075, 6453373360768749285, 4162773669143378749, 1077112486451167144, 1094907220582399423, 6505817429074173834, 6188791987711909113, 6705324769637601471, 5890853705337606327, 8632366525323256232, 4335408070175991523, 5997134815169542041, 4731012527649833497, 2964778942020128477, 8273983021459210857, 3651051516081632186, 3242525344324181140, 1435072699679368339, 7112377478872975540, 9087922757283571929, 1678345538501387635, 7454758496131018270, 5984597822987910239, 8311814153899672034, 5728157447144185807, 8104532247944011169, 7235274985749960035, 5845163556572532293, 4766619360327114985, 384004621616346184, 2229114844192788705, 8841285723146974482, 140482400439287038, 2498132937658317465, 985463713764876279, 2710702048246889539, 6381342920409254804, 4872375087398157762, 5378132087831818010, 3069364533194312324, 2279962904945262555, 2725771844612150584, 7273623594572657705, 2626673310542235113, 1279787718924848880, 484305213093869750, 3356703007213921275, 4270002305945466487, 3136594338322364707, 5755093868979904646, 8060799698929120816, 8571654************, 5158255751037730820, 5312007745704503561, 3812801803937502657, 2643142419514223232, 1876344152710584444, 3864524601008105124, 7782115253920410161, 2462010731464983063, 6677433326524801815, 1778985345252331494, 4988338041594029627, 6399023576511470846, 8209878501355854604, 3342733780568396212, 6213357013771936579, 2569898773272001570, 3266407006511653655, 500451559102827360, 2504184041899239332, 3078158018787913943, 6772024230737910181, 2704467930772856433, 302985683283538112], "retiredRelationUids": [2937710677954111317, 6048782637445196261], "version": 1}