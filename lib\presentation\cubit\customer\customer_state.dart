part of 'customer_cubit.dart';

abstract class CustomerState extends Equatable {
  const CustomerState();

  @override
  List<Object?> get props => [];
}

class CustomerInitial extends CustomerState {}

class CustomerLoading extends CustomerState {}

class CustomerLoaded extends CustomerState {
  final Customer customer;

  const CustomerLoaded({required this.customer});

  @override
  List<Object?> get props => [customer];
}

class CustomerSyncing extends CustomerState {}

class CustomerSynced extends CustomerState {
  final Customer customer;

  const CustomerSynced({required this.customer});

  @override
  List<Object?> get props => [customer];
}

class CustomerError extends CustomerState {
  final String message;

  const CustomerError({required this.message});

  @override
  List<Object?> get props => [message];
}

class CustomerNetworkError extends CustomerError {
  const CustomerNetworkError({required super.message});
}
