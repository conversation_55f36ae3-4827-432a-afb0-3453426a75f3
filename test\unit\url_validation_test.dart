import 'package:flutter_test/flutter_test.dart';

void main() {
  group('URL Validation Logic', () {
    test('should validate and fix various URL formats', () {
      final testCases = [
        {
          'input': 'https://payments.zoho.com/checkout/session123',
          'expected': 'https://payments.zoho.com/checkout/session123',
          'description': 'Complete HTTPS URL'
        },
        {
          'input': 'payments.zoho.com/checkout/session123',
          'expected': 'https://payments.zoho.com/checkout/session123',
          'description': 'URL without scheme'
        },
        {
          'input': '/api/payments/checkout/session123',
          'expected': 'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/api/payments/checkout/session123',
          'description': 'Relative path'
        },
        {
          'input': '//payments.zoho.com/checkout/session123',
          'expected': 'https://payments.zoho.com/checkout/session123',
          'description': 'Protocol-relative URL'
        },
        {
          'input': '  https://payments.zoho.com/checkout/session123  ',
          'expected': 'https://payments.zoho.com/checkout/session123',
          'description': 'URL with whitespace'
        },
      ];

      for (final testCase in testCases) {
        final input = testCase['input'] as String;
        final expected = testCase['expected'] as String;
        final description = testCase['description'] as String;
        
        final result = _validateAndFixPaymentUrl(input);
        expect(result, expected, reason: 'Failed for $description: $input');
        
        // Also verify the result can be parsed as a valid URI
        expect(() => Uri.parse(result), returnsNormally, 
               reason: 'Result should be a valid URI: $result');
      }
    });

    test('should throw for invalid URLs', () {
      final invalidUrls = ['', '   ', 'invalid://url with spaces'];
      
      for (final url in invalidUrls) {
        expect(() => _validateAndFixPaymentUrl(url), throwsArgumentError,
               reason: 'Should throw for invalid URL: "$url"');
      }
    });

    test('should handle edge cases', () {
      // Test various edge cases
      expect(_validateAndFixPaymentUrl('http://example.com'), 'http://example.com');
      expect(_validateAndFixPaymentUrl('https://example.com'), 'https://example.com');
      expect(_validateAndFixPaymentUrl('example.com'), 'https://example.com');
      expect(_validateAndFixPaymentUrl('/path'), 
             'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/path');
      expect(_validateAndFixPaymentUrl('//example.com'), 'https://example.com');
    });
  });
}

/// Copy of the validation logic from ZohoPaymentWebView for testing
String _validateAndFixPaymentUrl(String paymentUrl) {
  if (paymentUrl.isEmpty) {
    throw ArgumentError('Payment URL cannot be empty');
  }

  // Remove any leading/trailing whitespace
  final trimmedUrl = paymentUrl.trim();
  
  if (trimmedUrl.isEmpty) {
    throw ArgumentError('Payment URL cannot be empty after trimming');
  }
  
  String finalUrl;
  
  // Check if URL already has a scheme
  if (trimmedUrl.startsWith('http://') || trimmedUrl.startsWith('https://')) {
    finalUrl = trimmedUrl;
  }
  // If URL starts with '//', add https:
  else if (trimmedUrl.startsWith('//')) {
    finalUrl = 'https:$trimmedUrl';
  }
  // If URL starts with '/', assume it's a relative path and add base URL
  else if (trimmedUrl.startsWith('/')) {
    finalUrl = 'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net$trimmedUrl';
  }
  // If URL doesn't start with scheme, assume https://
  else {
    finalUrl = 'https://$trimmedUrl';
  }
  
  // Validate the final URL can be parsed
  try {
    Uri.parse(finalUrl);
  } catch (e) {
    throw ArgumentError('Invalid URL format after processing: $finalUrl');
  }
  
  return finalUrl;
}
