import 'package:aquapartner/data/datasources/local/smr_report_local_datasource.dart';
import 'package:aquapartner/data/datasources/remote/smr_report_remote_datasource.dart';
import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../../core/network/network_info.dart';
import '../../core/utils/logger.dart';
import '../../domain/entities/smr_report.dart';
import '../../domain/repositories/smr_report_repository.dart';
import '../models/smr_report_model.dart';

class SMRReportRepositoryImpl implements SMRReportRepository {
  final SMRReportRemoteDataSource remoteDataSource;
  final SMRReportLocalDataSource localDataSource;
  final NetworkInfo networkInfo;
  final AppLogger logger;

  SMRReportRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
    required this.networkInfo,
    required this.logger,
  });

  @override
  Future<Either<Failure, List<SMRReport>>> getSMRReports(
    String customerId,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        // Try to fetch from API
        final List<SMRReportModel> reports = await remoteDataSource
            .getSMRReports(customerId);

        if (reports.isEmpty) {
          return Left(NetworkFailure());
        }

        // Cache the reports locally
        await localDataSource.cacheSMRReports(reports, customerId);

        return Right(reports);
      } catch (e) {
        logger.e('Error fetching SMR reports from API: $e');
        // If API fails, try to get from local
        return await getLocalSMRReports(customerId);
      }
    } else {
      // If offline, get from local
      return await getLocalSMRReports(customerId);
    }
  }

  @override
  Future<Either<Failure, List<SMRReport>>> getLocalSMRReports(
    String customerId,
  ) async {
    try {
      final localReports = await localDataSource.getLastSMRReports(customerId);
      return Right(localReports);
    } catch (e) {
      logger.e('Error fetching SMR reports from local storage: $e');
      return Left(CacheFailure());
    }
  }

  @override
  Future<Either<Failure, bool>> syncSMRReports(String customerId) async {
    if (await networkInfo.isConnected) {
      try {
        // Fetch latest reports from remote
        final reports = await remoteDataSource.getSMRReports(customerId);

        // Update local cache
        await localDataSource.cacheSMRReports(reports, customerId);

        return const Right(true);
      } catch (e) {
        logger.e('Error syncing SMR reports: $e');
        return Left(ServerFailure());
      }
    } else {
      return Left(NetworkFailure());
    }
  }
}
