import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      throw UnsupportedError(
        'DefaultFirebaseOptions have not been configured for web - '
        'you can reconfigure this by running the FlutterFire CLI again.',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDgKpAYnaUBZEqs1jp04OUd83Qs1eIRnoQ',
    appId: '1:296786761398:android:765f8505a48e70b655f817',
    messagingSenderId: '296786761398',
    projectId: 'aquaconnect-partner-prod',
    storageBucket: 'aquaconnect-partner-prod.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyACd4QLepy9_o67bTM7BB8PIzH496M_WYg',
    appId: '1:296786761398:ios:61b8967fa2731f8155f817',
    messagingSenderId: '296786761398',
    projectId: 'aquaconnect-partner-prod',
    storageBucket: 'aquaconnect-partner-prod.appspot.com',
    androidClientId:
        '296786761398-2u9nqj16657skuulu89ool8i0010vji5.apps.googleusercontent.com',
    iosBundleId: 'blue.aquaconnect.partnerselfservice',
  );
}
