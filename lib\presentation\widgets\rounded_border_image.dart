import 'package:flutter/material.dart';

class RoundedBorderImage extends StatelessWidget {
  final String imageUrl;
  const RoundedBorderImage(this.imageUrl, {super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity, // Adjust width as needed
      // Adjust height as needed
      decoration: BoxDecoration(
        border: Border.all(
          color: Color(0xFFE2E2E2), // Customize border color
          width: 1.0, // Customize border width
        ),
        borderRadius: BorderRadius.circular(8), // Customize border radius
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(
          20,
        ), // Match the container's border radius
        child: Image.asset(imageUrl, fit: BoxFit.contain),
      ),
    );
  }
}
