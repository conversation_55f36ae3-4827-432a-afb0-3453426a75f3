import 'package:aqua_ui/aqua_ui.dart';
import 'package:flutter/material.dart';
import '../../../domain/entities/dues/dues.dart';

class HorizontalAgingFilterGroup extends StatefulWidget {
  final List<DuesAgingGroup> agingGroups;
  final Function(List<String>) onSelectionChanged;
  final List<String> initialSelection;

  const HorizontalAgingFilterGroup({
    super.key,
    required this.agingGroups,
    required this.onSelectionChanged,
    this.initialSelection = const [],
  });

  @override
  State<HorizontalAgingFilterGroup> createState() =>
      _HorizontalAgingFilterGroupState();
}

class _HorizontalAgingFilterGroupState
    extends State<HorizontalAgingFilterGroup> {
  late Set<String> _selectedAgings;

  @override
  void initState() {
    super.initState();
    _selectedAgings = Set.from(widget.initialSelection);
  }

  @override
  Widget build(BuildContext context) {
    // Extract unique aging values
    final uniqueAgings =
        widget.agingGroups.map((group) => group.aging).toSet().toList();

    return SizedBox(
      height: 50,
      width: double.infinity,
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Individual aging options
            ...uniqueAgings.map((aging) {
              return Padding(
                padding: const EdgeInsets.only(right: 2),
                child: FilterChip(
                  label: SizedBox(
                    width: aging.length * 9,
                    child: AquaText.body(
                      aging,
                      weight: AquaFontWeight.medium,
                      textAlign: TextAlign.center,
                    ),
                  ),
                  selected: _selectedAgings.contains(aging),
                  onSelected: (selected) {
                    setState(() {
                      if (selected) {
                        _selectedAgings.add(aging);
                      } else {
                        _selectedAgings.remove(aging);
                      }
                    });
                    widget.onSelectionChanged(_selectedAgings.toList());
                  },
                  backgroundColor: acGrey200,
                  selectedColor: acPrimaryBlue.withValues(alpha: 0.2),
                  checkmarkColor: acPrimaryBlue,
                ),
              );
            }),
          ],
        ),
      ),
    );
  }
}
