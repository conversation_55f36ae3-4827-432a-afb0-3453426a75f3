# AquaPartner Testing Guide

## 📋 Overview

This guide provides comprehensive information about the testing framework implemented for the AquaPartner Flutter application. Our testing strategy ensures code quality, prevents regressions, and enables confident deployments through automated CI/CD pipelines.

## 🏗️ Test Architecture

### Test Structure
```
test/
├── core/
│   ├── mixins/
│   │   └── analytics_mixin_test.dart
│   ├── services/
│   │   └── analytics_service_test.dart
│   └── utils/
│       └── string_utils_test.dart
├── domain/
│   └── entities/
│       └── product_test.dart
├── presentation/
│   ├── cubit/
│   │   └── auth/
│   │       └── auth_cubit_test.dart
│   └── screens/
│       └── login_screen_test.dart
├── helpers/
│   └── test_helpers.dart
├── mocks/
│   ├── mock_analytics.dart
│   └── mock_auth.dart
├── test_config.dart
└── test_runner.dart
```

### Test Categories

#### 1. **Unit Tests** 🔧
- **Purpose**: Test individual functions, methods, and classes in isolation
- **Location**: `test/core/`, `test/domain/`
- **Coverage**: Business logic, utilities, entities, services

#### 2. **Widget Tests** 🎨
- **Purpose**: Test UI components and user interactions
- **Location**: `test/presentation/widgets/`
- **Coverage**: Custom widgets, form validation, user interactions

#### 3. **Screen Tests** 📱
- **Purpose**: Test complete screen functionality and navigation
- **Location**: `test/presentation/screens/`
- **Coverage**: Screen rendering, user flows, analytics tracking

#### 4. **Cubit/Bloc Tests** 🔄
- **Purpose**: Test state management logic
- **Location**: `test/presentation/cubit/`
- **Coverage**: State transitions, business logic, error handling

#### 5. **Integration Tests** 🔗
- **Purpose**: Test complete user journeys and system integration
- **Location**: `integration_test/`
- **Coverage**: End-to-end flows, API integration, database operations

## 🚀 Running Tests

### Local Development

#### Run All Tests
```bash
flutter test
```

#### Run Specific Test Categories
```bash
# Unit tests only
flutter test test/core/ test/domain/

# Widget tests only
flutter test test/presentation/widgets/

# Screen tests only
flutter test test/presentation/screens/

# Cubit tests only
flutter test test/presentation/cubit/
```

#### Run Tests with Coverage
```bash
flutter test --coverage
```

#### Generate Coverage Report
```bash
# Install lcov (Linux/macOS)
sudo apt-get install lcov  # Ubuntu/Debian
brew install lcov          # macOS

# Generate HTML report
genhtml coverage/lcov.info -o coverage/html

# Open report
open coverage/html/index.html
```

### CI/CD Pipeline

Our GitHub Actions workflow automatically:
1. Runs all tests on pull requests
2. Generates coverage reports
3. Blocks merges if tests fail
4. Uploads coverage to Codecov
5. Builds test APKs

## 📊 Coverage Requirements

### Minimum Coverage Thresholds
- **Line Coverage**: 80%
- **Branch Coverage**: 75%
- **Function Coverage**: 85%

### Priority Areas (Higher Coverage Required)
- **Critical (90%+)**: Authentication, Analytics, Core Services
- **High (85%+)**: Screen Logic, Business Entities, Utilities
- **Medium (80%+)**: Widgets, Data Models, Repositories
- **Low (75%+)**: Theme, Constants, Configuration

## 🧪 Writing Tests

### Test Structure Template

```dart
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

void main() {
  group('ComponentName Tests', () {
    late MockDependency mockDependency;
    late ComponentUnderTest component;

    setUp(() {
      mockDependency = MockDependency();
      component = ComponentUnderTest(mockDependency);
    });

    tearDown(() {
      // Clean up resources
    });

    group('Feature Group', () {
      test('should do something when condition is met', () {
        // Arrange
        when(() => mockDependency.method()).thenReturn(expectedValue);

        // Act
        final result = component.methodUnderTest();

        // Assert
        expect(result, equals(expectedValue));
        verify(() => mockDependency.method()).called(1);
      });
    });
  });
}
```

### Widget Test Template

```dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../helpers/test_helpers.dart';

void main() {
  group('WidgetName Tests', () {
    Widget createWidget() {
      return TestHelpers.createTestApp(
        home: BlocProvider(
          create: (context) => mockCubit,
          child: WidgetUnderTest(),
        ),
      );
    }

    testWidgets('should render correctly', (tester) async {
      await TestHelpers.pumpAndSettle(tester, createWidget());

      expect(find.text('Expected Text'), findsOneWidget);
      expect(find.byType(ExpectedWidget), findsOneWidget);
    });
  });
}
```

### Analytics Test Template

```dart
testWidgets('should track analytics event', (tester) async {
  await TestHelpers.pumpAndSettle(tester, createWidget());

  // Perform action
  await TestHelpers.tapAndSettle(tester, find.text('Button'));

  // Verify analytics
  expect(mockAnalyticsService.hasLoggedEvent('event_name'), isTrue);
  
  final events = mockAnalyticsService.getEventsByName('event_name');
  expect(events.length, equals(1));
  expect(events.first['parameters']['key'], equals('value'));
});
```

## 🔧 Test Utilities

### TestHelpers Class
Provides common utilities for test setup:
- `createTestWidget()`: Wraps widgets with MaterialApp
- `createTestApp()`: Creates full app context
- `pumpAndSettle()`: Pumps widget and waits for animations
- `tapAndSettle()`: Taps element and waits for animations
- `enterText()`: Enters text in form fields

### Mock Classes
Pre-configured mocks for common dependencies:
- `MockAnalyticsService`: Analytics tracking
- `MockAuthRepository`: Authentication logic
- `MockFirebaseAuth`: Firebase Auth service
- `MockConnectivity`: Network connectivity

### Test Configuration
- `TestConfig.initialize()`: Sets up test environment
- Method channel mocks for Firebase services
- SharedPreferences mock setup
- Dependency injection configuration

## 📈 Best Practices

### 1. Test Naming
```dart
// Good
test('should return user data when authentication is successful')

// Bad
test('test user auth')
```

### 2. Test Organization
- Group related tests using `group()`
- Use descriptive group names
- Follow Arrange-Act-Assert pattern

### 3. Mock Usage
- Mock external dependencies
- Use `when()` for setup, `verify()` for assertions
- Reset mocks between tests

### 4. Widget Testing
- Test user interactions, not implementation details
- Verify UI state changes
- Test error states and edge cases

### 5. Analytics Testing
- Verify all user interactions are tracked
- Test event parameters and screen tracking
- Ensure error tracking works correctly

## 🚨 Common Issues & Solutions

### Issue: Tests Timeout
**Solution**: Increase timeout or optimize test setup
```dart
testWidgets('test name', (tester) async {
  // ...
}, timeout: Timeout(Duration(minutes: 2)));
```

### Issue: Method Channel Errors
**Solution**: Add method channel mocks in `TestConfig`

### Issue: Dependency Injection Conflicts
**Solution**: Reset DI container in `tearDown()`
```dart
tearDown(() async {
  if (di.getIt.isRegistered<Service>()) {
    di.getIt.unregister<Service>();
  }
});
```

### Issue: Analytics Not Tracked
**Solution**: Ensure analytics service is properly mocked and registered

## 📋 Test Checklist

Before submitting a PR, ensure:

- [ ] All new code has corresponding tests
- [ ] Tests cover both success and failure scenarios
- [ ] Analytics tracking is tested for user interactions
- [ ] Edge cases and error conditions are covered
- [ ] Tests pass locally with `flutter test`
- [ ] Coverage meets minimum thresholds
- [ ] No test flakiness or timeouts
- [ ] Mock dependencies are properly configured

## 🔄 Continuous Integration

### GitHub Actions Workflow
Our CI pipeline includes:
1. **Code Analysis**: `flutter analyze`
2. **Format Check**: `dart format --set-exit-if-changed`
3. **Unit Tests**: `flutter test --coverage`
4. **Integration Tests**: Run on Android emulator
5. **Build Test**: Verify APK builds successfully
6. **Coverage Upload**: Send results to Codecov

### Quality Gates
- Tests must pass to merge
- Coverage cannot decrease by more than 5%
- No new analyzer warnings
- Code must be properly formatted

## 📚 Additional Resources

- [Flutter Testing Documentation](https://docs.flutter.dev/testing)
- [Bloc Testing Guide](https://bloclibrary.dev/#/testing)
- [Mocktail Documentation](https://pub.dev/packages/mocktail)
- [Firebase Testing](https://firebase.google.com/docs/rules/unit-tests)

## 🤝 Contributing

When adding new features:
1. Write tests first (TDD approach recommended)
2. Ensure analytics tracking is implemented and tested
3. Add integration tests for complete user flows
4. Update this guide if new testing patterns are introduced

---

**Happy Testing! 🧪✨**
