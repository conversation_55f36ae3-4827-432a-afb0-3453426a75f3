import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../core/utils/logger.dart';
import 'connectivity_state.dart';

class ConnectivityCubit extends Cubit<ConnectivityState> {
  final Connectivity connectivity;
  final AppLogger logger;
  late StreamSubscription<List<ConnectivityResult>> _connectivitySubscription;

  ConnectivityCubit({required this.connectivity, required this.logger})
    : super(ConnectivityState.initial()) {
    _initConnectivity();
    _connectivitySubscription = connectivity.onConnectivityChanged.listen(
      _updateConnectionStatus,
    );
  }

  Future<void> _initConnectivity() async {
    try {
      final result = await connectivity.checkConnectivity();
      _updateConnectionStatus(result);
    } catch (e) {
      emit(ConnectivityState.initial());
    }
  }

  void _updateConnectionStatus(List<ConnectivityResult> results) {
    // Check if any of the results indicate a connection
    final hasConnection = results.any(
      (result) => result != ConnectivityResult.none,
    );

    if (!hasConnection) {
      emit(const ConnectivityState(status: ConnectionStatus.disconnected));
    } else {
      emit(const ConnectivityState(status: ConnectionStatus.connected));
    }
  }

  @override
  Future<void> close() {
    _connectivitySubscription.cancel();
    return super.close();
  }
}
