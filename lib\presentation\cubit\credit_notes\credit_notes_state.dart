import 'package:equatable/equatable.dart';

import '../../../domain/entities/credit_notes/credit_note.dart';

abstract class CreditNotesState extends Equatable {
  const CreditNotesState();

  @override
  List<Object?> get props => [];
}

class CreditNotesInitial extends CreditNotesState {}

class CreditNotesLoading extends CreditNotesState {}

class CreditNotesSyncing extends CreditNotesState {}

class CreditNotesLoaded extends CreditNotesState {
  final List<CreditNote> creditNotes;
  final bool isFromCache;
  final bool isCacheStale;
  final bool isBackgroundSyncInProgress;

  const CreditNotesLoaded(
    this.creditNotes, {
    this.isFromCache = false,
    this.isCacheStale = false,
    this.isBackgroundSyncInProgress = false,
  });

  CreditNotesLoaded copyWith({
    List<CreditNote>? creditNotes,
    bool? isFromCache,
    bool? isCacheStale,
    bool? isBackgroundSyncInProgress,
  }) {
    return CreditNotesLoaded(
      creditNotes ?? this.creditNotes,
      isFromCache: isFromCache ?? this.isFromCache,
      isCacheStale: isCacheStale ?? this.isCacheStale,
      isBackgroundSyncInProgress:
          isBackgroundSyncInProgress ?? this.isBackgroundSyncInProgress,
    );
  }

  @override
  List<Object?> get props => [
    creditNotes,
    isFromCache,
    isCacheStale,
    isBackgroundSyncInProgress,
  ];
}

class CreditNotesError extends CreditNotesState {
  final String message;

  const CreditNotesError(this.message);

  @override
  List<Object?> get props => [message];
}
