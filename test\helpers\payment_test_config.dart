import 'dart:convert';

/// Test configuration for payment integration testing
class PaymentTestConfig {
  // Test Environment URLs
  static const String productionBaseUrl = 'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/api';
  static const String stagingBaseUrl = 'https://staging-aquapartner.azurewebsites.net/api';
  static const String localBaseUrl = 'http://localhost:3000/api';
  
  // Test Payment Data
  static const testCustomers = [
    {
      'id': 'TEST-CUST-001',
      'name': 'Test Customer One',
      'email': '<EMAIL>',
      'phone': '+************',
    },
    {
      'id': 'TEST-CUST-002',
      'name': 'Test Customer Two',
      'email': '<EMAIL>',
      'phone': '+************',
    },
    {
      'id': 'TEST-CUST-003',
      'name': 'Very Long Customer Name That Might Cause Display Issues',
      'email': '<EMAIL>',
      'phone': '******-123-4567',
    },
  ];
  
  static const testInvoices = [
    {
      'number': 'INV-TEST-001',
      'amount': 100.0,
      'currency': 'INR',
      'description': 'Test Invoice 001',
      'customerId': 'TEST-CUST-001',
    },
    {
      'number': 'INV-TEST-002',
      'amount': 250.50,
      'currency': 'INR',
      'description': 'Test Invoice 002',
      'customerId': 'TEST-CUST-002',
    },
    {
      'number': 'INV-TEST-003',
      'amount': 9999.99,
      'currency': 'INR',
      'description': 'Large Amount Test Invoice',
      'customerId': 'TEST-CUST-003',
    },
    {
      'number': 'INV-2024/Q1_001',
      'amount': 500.0,
      'currency': 'INR',
      'description': 'Special Characters Test',
      'customerId': 'TEST-CUST-001',
    },
  ];
  
  // Test Payment Cards (Zoho Test Cards)
  static const testCards = {
    'success': {
      'number': '****************',
      'expiry': '12/25',
      'cvv': '123',
      'name': 'Test Success',
      'description': 'Always succeeds',
    },
    'failure': {
      'number': '****************',
      'expiry': '12/25',
      'cvv': '123',
      'name': 'Test Failure',
      'description': 'Always fails',
    },
    'expired': {
      'number': '****************',
      'expiry': '12/20',
      'cvv': '123',
      'name': 'Test Expired',
      'description': 'Expired card',
    },
    'insufficient_funds': {
      'number': '****************',
      'expiry': '12/25',
      'cvv': '123',
      'name': 'Test Insufficient',
      'description': 'Insufficient funds',
    },
  };
  
  // Mock API Responses
  static Map<String, dynamic> mockHealthResponse = {
    'timestamp': '2024-01-01T00:00:00Z',
    'service': 'zoho-payment-api',
    'version': '1.0.0',
    'status': 'healthy',
    'checks': {
      'database': 'ok',
      'zoho_api': 'ok',
      'redis': 'ok',
    }
  };
  
  static Map<String, dynamic> mockPaymentSessionResponse = {
    'success': true,
    'message': 'Payment session created successfully',
    'data': {
      'payment_session_id': 'test_session_123456789',
      'amount': '100.0',
      'currency': 'INR',
      'description': 'Test payment session',
      'invoice_number': 'INV-TEST-001',
      'created_time': **********,
      'transaction_id': 'test_txn_123456789',
      'expires_in': '3600',
      'payment_url': 'https://payments.zoho.in/checkout/test_session_123456789',
    }
  };
  
  static Map<String, dynamic> mockPaymentStatusPending = {
    'success': true,
    'message': 'Payment status retrieved',
    'data': {
      'transaction_id': 'test_txn_123456789',
      'payment_session_id': 'test_session_123456789',
      'status': 'pending',
      'amount': 100.0,
      'currency': 'INR',
      'description': 'Test payment session',
      'invoice_number': 'INV-TEST-001',
      'customer_id': 'TEST-CUST-001',
      'customer_name': 'Test Customer One',
      'customer_email': '<EMAIL>',
      'session_created_time': '2024-01-01T00:00:00Z',
      'session_expires_at': '2024-01-01T01:00:00Z',
    }
  };
  
  static Map<String, dynamic> mockPaymentStatusSuccess = {
    'success': true,
    'message': 'Payment status retrieved',
    'data': {
      'transaction_id': 'test_txn_123456789',
      'payment_session_id': 'test_session_123456789',
      'status': 'completed',
      'amount': 100.0,
      'currency': 'INR',
      'description': 'Test payment session',
      'invoice_number': 'INV-TEST-001',
      'customer_id': 'TEST-CUST-001',
      'customer_name': 'Test Customer One',
      'customer_email': '<EMAIL>',
      'payment_id': 'zoho_payment_123456789',
      'payment_method': 'credit_card',
      'session_created_time': '2024-01-01T00:00:00Z',
      'payment_completed_time': '2024-01-01T00:05:00Z',
      'session_expires_at': '2024-01-01T01:00:00Z',
    }
  };
  
  static Map<String, dynamic> mockPaymentStatusFailed = {
    'success': true,
    'message': 'Payment status retrieved',
    'data': {
      'transaction_id': 'test_txn_123456789',
      'payment_session_id': 'test_session_123456789',
      'status': 'failed',
      'amount': 100.0,
      'currency': 'INR',
      'description': 'Test payment session',
      'invoice_number': 'INV-TEST-001',
      'customer_id': 'TEST-CUST-001',
      'customer_name': 'Test Customer One',
      'customer_email': '<EMAIL>',
      'session_created_time': '2024-01-01T00:00:00Z',
      'session_expires_at': '2024-01-01T01:00:00Z',
      'error_code': 'PAYMENT_DECLINED',
      'error_message': 'Payment was declined by the bank',
    }
  };
  
  // Error Response Templates
  static Map<String, dynamic> mockValidationError = {
    'success': false,
    'message': 'Validation failed',
    'error': 'VALIDATION_ERROR',
    'details': {
      'amount': ['Amount must be greater than 0'],
      'currency': ['Only INR is supported'],
      'invoice_number': ['Invoice number cannot be empty'],
    }
  };
  
  static Map<String, dynamic> mockNetworkError = {
    'success': false,
    'message': 'Network error occurred',
    'error': 'NETWORK_ERROR',
  };
  
  static Map<String, dynamic> mockAuthError = {
    'success': false,
    'message': 'Authentication required',
    'error': 'AUTH_ERROR',
  };
  
  // Test Timeouts
  static const Duration shortTimeout = Duration(seconds: 5);
  static const Duration mediumTimeout = Duration(seconds: 15);
  static const Duration longTimeout = Duration(seconds: 30);
  static const Duration pollingTimeout = Duration(minutes: 5);
  
  // Test Intervals
  static const Duration pollingInterval = Duration(seconds: 3);
  static const Duration retryDelay = Duration(seconds: 2);
  
  // Helper Methods
  static String getBaseUrl(String environment) {
    switch (environment.toLowerCase()) {
      case 'production':
        return productionBaseUrl;
      case 'staging':
        return stagingBaseUrl;
      case 'local':
        return localBaseUrl;
      default:
        throw ArgumentError('Unknown environment: $environment');
    }
  }
  
  static Map<String, dynamic> getTestCustomer(String id) {
    return testCustomers.firstWhere(
      (customer) => customer['id'] == id,
      orElse: () => testCustomers.first,
    );
  }
  
  static Map<String, dynamic> getTestInvoice(String number) {
    return testInvoices.firstWhere(
      (invoice) => invoice['number'] == number,
      orElse: () => testInvoices.first,
    );
  }
  
  static Map<String, dynamic> getTestCard(String type) {
    return testCards[type] ?? testCards['success']!;
  }
  
  static String toJsonString(Map<String, dynamic> data) {
    return jsonEncode(data);
  }
  
  static Map<String, dynamic> fromJsonString(String jsonString) {
    return jsonDecode(jsonString);
  }
  
  // Test Data Generators
  static String generateTestInvoiceNumber() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return 'TEST-INV-$timestamp';
  }
  
  static String generateTestCustomerId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return 'TEST-CUST-$timestamp';
  }
  
  static String generateTestSessionId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return 'test_session_$timestamp';
  }
  
  static String generateTestTransactionId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return 'test_txn_$timestamp';
  }
}
