import 'package:aquapartner/domain/repositories/user_repository.dart';
import 'package:aquapartner/domain/usecases/auth_usecases.dart';
import 'package:aquapartner/domain/usecases/sync_usecases.dart';
import 'package:aquapartner/domain/usecases/user_usecases.dart';
import 'package:aquapartner/domain/usecases/customer_usercases.dart';
import 'package:aquapartner/domain/entities/customer.dart';
import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:aquapartner/presentation/cubit/auth/auth_cubit.dart';
import 'package:aquapartner/presentation/cubit/auth/auth_state.dart';
import 'package:aquapartner/core/utils/logger.dart';
import 'package:aquapartner/core/error/failures.dart';
import 'package:dartz/dartz.dart';

import '../../../mocks/mock_analytics.dart';
import '../../../mocks/mock_auth.dart';
import 'package:aquapartner/domain/entities/user.dart' as domain;

// Custom logger for testing
class TestAppLogger implements AppLogger {
  List<String> errorMessages = [];
  List<Object> errorObjects = [];
  List<String> infoMessages = [];
  List<String> debugMessages = [];
  List<String> warningMessages = [];

  @override
  void e(String message, [dynamic error, StackTrace? stackTrace]) {
    errorMessages.add(message);
    if (error != null) errorObjects.add(error);
  }

  @override
  void i(String message) {
    infoMessages.add(message);
  }

  @override
  void d(String message) {
    debugMessages.add(message);
  }

  @override
  void w(String message) {
    warningMessages.add(message);
  }

  @override
  void enableFirebaseVerboseLogging() {
    // No-op for testing
  }
}

class MockCheckAuthStatusUseCase extends Mock
    implements CheckAuthStatusUseCase {}

class MockGetCustomerByMobileNumber extends Mock
    implements GetCustomerByMobileNumber {}

class MockUserRepository extends Mock implements UserRepository {}

class MockSyncUserUseCase extends Mock implements SyncUserUseCase {}

class MockGetSyncStatusUseCase extends Mock implements GetSyncStatusUseCase {}

class MockSendOtpUseCase extends Mock implements SendOtpUseCase {}

class MockVerifyOtpUseCase extends Mock implements VerifyOtpUseCase {}

class MockSignOutUseCase extends Mock implements SignOutUseCase {}

class MockGetUserUseCase extends Mock implements GetUserUseCase {}

class MockSaveUserUseCase extends Mock implements SaveUserUseCase {}

class MockUpdateUserUseCase extends Mock implements UpdateUserUseCase {}

void main() {
  setUpAll(() {
    // Register fallback values for mocktail
    registerFallbackValue(
      domain.User(
        id: 0,
        phoneNumber: '',
        isVerified: false,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        needsSync: false,
      ),
    );
  });

  group('AuthCubit Tests', () {
    late AuthCubit authCubit;
    late MockAuthRepository mockAuthRepository;
    late MockSyncUserUseCase mockSyncUserUseCase;
    late MockGetSyncStatusUseCase mockGetSyncStatusUseCase;
    late MockSendOtpUseCase mockSendOtpUseCase;
    late MockVerifyOtpUseCase mockVerifyOtpUseCase;
    late MockSignOutUseCase mockSignOutUseCase;
    late MockGetUserUseCase mockGetUserUseCase;
    late MockSaveUserUseCase mockSaveUserUseCase;
    late MockUpdateUserUseCase mockUpdateUserUseCase;
    late MockGetCustomerByMobileNumber mockGetCustomerByMobileNumber;
    late MockCheckAuthStatusUseCase mockCheckAuthStatusUseCase;

    setUp(() {
      mockAuthRepository = MockAuthRepository();
      mockSyncUserUseCase = MockSyncUserUseCase();
      mockGetSyncStatusUseCase = MockGetSyncStatusUseCase();
      mockSendOtpUseCase = MockSendOtpUseCase();
      mockVerifyOtpUseCase = MockVerifyOtpUseCase();
      mockSignOutUseCase = MockSignOutUseCase();
      mockGetUserUseCase = MockGetUserUseCase();
      mockSaveUserUseCase = MockSaveUserUseCase();
      mockUpdateUserUseCase = MockUpdateUserUseCase();
      mockGetCustomerByMobileNumber = MockGetCustomerByMobileNumber();
      mockCheckAuthStatusUseCase = MockCheckAuthStatusUseCase();

      // Set up default behavior for mocks
      when(
        () => mockGetSyncStatusUseCase.call(),
      ).thenAnswer((_) => Stream.value(false));
      when(
        () => mockSendOtpUseCase.call(any()),
      ).thenAnswer((_) async => const Right('test_verification_id'));
      when(
        () => mockVerifyOtpUseCase.call(any(), any()),
      ).thenAnswer((_) async => const Right(true));
      when(
        () => mockSignOutUseCase.call(),
      ).thenAnswer((_) async => const Right(true));

      authCubit = AuthCubit(
        sendOtpUseCase: mockSendOtpUseCase,
        verifyOtpUseCase: mockVerifyOtpUseCase,
        signOutUseCase: mockSignOutUseCase,
        getUserUseCase: mockGetUserUseCase,
        saveUserUseCase: mockSaveUserUseCase,
        updateUserUseCase: mockUpdateUserUseCase,
        syncUserUseCase: mockSyncUserUseCase,
        getSyncStatusUseCase: mockGetSyncStatusUseCase,
        logger: TestAppLogger(),
        checkAuthStatusUseCase: mockCheckAuthStatusUseCase,
        getCustomerByMobileNumber: mockGetCustomerByMobileNumber,
        analyticsService: MockAnalyticsService(),
      );
    });

    tearDown(() {
      authCubit.close();
    });

    group('Initial State', () {
      test('should have initial state as AuthInitial', () {
        expect(authCubit.state, isA<AuthInitial>());
      });
    });

    group('Send OTP', () {
      const testPhoneNumber = '+919999999999';

      blocTest<AuthCubit, AuthState>(
        'should emit [AuthLoading, OtpSent] when OTP is sent successfully',
        build: () {
          when(
            () => mockSendOtpUseCase.call(any()),
          ).thenAnswer((_) async => const Right('test_verification_id'));
          return authCubit;
        },
        act: (cubit) => cubit.sendOtp(testPhoneNumber),
        expect:
            () => [
              isA<AuthLoading>(),
              isA<OtpSent>().having(
                (state) => state.phoneNumber,
                'phoneNumber',
                testPhoneNumber,
              ),
            ],
        verify: (cubit) {
          verify(() => mockSendOtpUseCase.call(any())).called(1);
        },
      );

      blocTest<AuthCubit, AuthState>(
        'should emit [AuthLoading, PhoneNumberError] when phone number is invalid',
        build: () {
          when(() => mockSendOtpUseCase.call(any())).thenAnswer(
            (_) async => Left(ValidationFailure('Invalid phone number')),
          );
          return authCubit;
        },
        act:
            (cubit) => cubit.sendOtp('+919999999999'), // Invalid number in mock
        expect:
            () => [
              isA<AuthLoading>(),
              isA<PhoneNumberError>().having(
                (state) => state.message,
                'message',
                'Invalid phone number',
              ),
            ],
      );

      blocTest<AuthCubit, AuthState>(
        'should emit [AuthLoading, AuthSuccess] when auto-verification occurs',
        build: () {
          // Mock auto-verification by having the use case return a user directly
          when(
            () => mockSendOtpUseCase.call(any()),
          ).thenAnswer((_) async => const Right('auto_verified'));
          when(() => mockGetUserUseCase.call()).thenAnswer(
            (_) async => Right(
              domain.User(
                id: 1,
                phoneNumber: '+918888888888',
                isVerified: true,
                createdAt: DateTime.now(),
                updatedAt: DateTime.now(),
                needsSync: false,
              ),
            ),
          );
          return authCubit;
        },
        act:
            (cubit) =>
                cubit.sendOtp('+918888888888'), // Auto-verify number in mock
        expect: () => [isA<AuthLoading>(), isA<OtpSent>()],
      );

      blocTest<AuthCubit, AuthState>(
        'should emit [AuthLoading, PhoneNumberError] when network error occurs',
        build: () {
          when(
            () => mockSendOtpUseCase.call(any()),
          ).thenAnswer((_) async => Left(NetworkFailure()));
          return authCubit;
        },
        act: (cubit) => cubit.sendOtp(testPhoneNumber),
        expect:
            () => [
              isA<AuthLoading>(),
              isA<PhoneNumberError>().having(
                (state) => state.message,
                'message',
                contains('No internet connection'),
              ),
            ],
      );

      blocTest<AuthCubit, AuthState>(
        'should emit [AuthLoading, PhoneNumberError] when too many requests error occurs',
        build: () {
          when(() => mockSendOtpUseCase.call(any())).thenAnswer(
            (_) async => Left(ValidationFailure('Too many requests')),
          );
          return authCubit;
        },
        act: (cubit) => cubit.sendOtp(testPhoneNumber),
        expect:
            () => [
              isA<AuthLoading>(),
              isA<PhoneNumberError>().having(
                (state) => state.message,
                'message',
                contains('Too many requests'),
              ),
            ],
      );
    });

    group('Verify OTP', () {
      const testOtp = '123456';
      const testVerificationId = 'test_verification_id';

      setUp(() async {
        // First send OTP to set up internal state properly
        when(
          () => mockSendOtpUseCase.call(any()),
        ).thenAnswer((_) async => const Right(testVerificationId));

        // Send OTP to initialize the cubit's internal verification ID
        await authCubit.sendOtp('+919999999999');
      });

      blocTest<AuthCubit, AuthState>(
        'should emit [AuthLoading, AuthSuccess] when OTP is verified successfully',
        build: () {
          when(
            () => mockVerifyOtpUseCase.call(any(), any()),
          ).thenAnswer((_) async => const Right(true));
          // Mock customer lookup
          when(() => mockGetCustomerByMobileNumber.call(any())).thenAnswer(
            (_) async => Right(
              Customer(
                customerId: 'CUST001',
                customerName: 'Test Customer',
                email: '<EMAIL>',
                mobileNumber: '+919999999999',
                companyName: 'Test Company',
                gstNo: 'GST123456789',
                businessVertical: 'Technology',
                customerCode: 'TC001',
                billingAddress: 'Test Address',
              ),
            ),
          );

          // Mock save user
          when(
            () => mockSaveUserUseCase.call(any()),
          ).thenAnswer((_) async => const Right(true));

          // Mock get user after save
          when(() => mockGetUserUseCase.call()).thenAnswer(
            (_) async => Right(
              domain.User(
                id: 1,
                phoneNumber: '+919999999999',
                isVerified: true,
                createdAt: DateTime.now(),
                updatedAt: DateTime.now(),
                needsSync: false,
              ),
            ),
          );
          return authCubit;
        },

        act: (cubit) => cubit.verifyOtp(testOtp),
        expect: () => [isA<AuthLoading>(), isA<AuthSuccess>()],
      );

      blocTest<AuthCubit, AuthState>(
        'should emit [AuthLoading, OtpVerificationError] when OTP is invalid',
        build: () {
          when(
            () => mockVerifyOtpUseCase.call(any(), any()),
          ).thenAnswer((_) async => Left(ValidationFailure('Invalid OTP')));
          return authCubit;
        },

        act: (cubit) => cubit.verifyOtp('000000'), // Invalid OTP in mock
        expect:
            () => [
              isA<AuthLoading>(),
              isA<OtpVerificationError>().having(
                (state) => state.message,
                'message',
                contains('Invalid OTP'),
              ),
            ],
      );

      blocTest<AuthCubit, AuthState>(
        'should emit [AuthLoading, OtpVerificationError] when verification ID is invalid',
        build: () {
          when(() => mockVerifyOtpUseCase.call(any(), any())).thenAnswer(
            (_) async => Left(ValidationFailure('Invalid verification ID')),
          );
          return authCubit;
        },

        act: (cubit) => cubit.verifyOtp(testOtp),
        expect:
            () => [
              isA<AuthLoading>(),
              isA<OtpVerificationError>().having(
                (state) => state.message,
                'message',
                contains('Invalid verification ID'),
              ),
            ],
      );

      blocTest<AuthCubit, AuthState>(
        'should emit [AuthLoading, OtpVerificationError] when network error occurs during verification',
        build: () {
          when(
            () => mockVerifyOtpUseCase.call(any(), any()),
          ).thenAnswer((_) async => Left(NetworkFailure()));
          return authCubit;
        },

        act: (cubit) => cubit.verifyOtp(testOtp),
        expect:
            () => [
              isA<AuthLoading>(),
              isA<OtpVerificationError>().having(
                (state) => state.message,
                'message',
                contains('No internet connection'),
              ),
            ],
      );
    });

    group('Sign Out', () {
      blocTest<AuthCubit, AuthState>(
        'should emit [AuthLoading, AuthInitial] when sign out is successful',
        build: () {
          when(() => mockSignOutUseCase.call()).thenAnswer((_) async {});
          return authCubit;
        },
        seed:
            () => AuthSuccess(
              user: domain.User(
                id: 1,
                phoneNumber: '+919999999999',
                isVerified: true,
                createdAt: DateTime.now(),
                updatedAt: DateTime.now(),
                needsSync: false,
              ),
            ),
        act: (cubit) => cubit.signOut(),
        expect: () => [isA<AuthLoading>(), isA<Unauthenticated>()],
        verify: (cubit) {
          verify(() => mockSignOutUseCase.call()).called(1);
        },
      );

      blocTest<AuthCubit, AuthState>(
        'should handle sign out failure gracefully',
        build: () {
          when(
            () => mockSignOutUseCase.call(),
          ).thenThrow(Exception('Sign out failed'));
          return authCubit;
        },
        seed:
            () => AuthSuccess(
              user: domain.User(
                id: 1,
                phoneNumber: '+919999999999',
                isVerified: true,
                createdAt: DateTime.now(),
                updatedAt: DateTime.now(),
                needsSync: true,
              ),
            ),
        act: (cubit) => cubit.signOut(),
        // The exception will bubble up since signOut doesn't have try-catch
        errors: () => [isA<Exception>()],
        expect: () => [isA<AuthLoading>()],
      );
    });

    group('Reset State', () {
      test('should reset state to AuthInitial', () {
        authCubit.emit(
          AuthSuccess(
            user: domain.User(
              id: 1,
              phoneNumber: '+919999999999',
              isVerified: true,
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
              needsSync: true,
            ),
          ),
        );
        expect(authCubit.state, isA<AuthSuccess>());

        authCubit.resetState();
        expect(authCubit.state, isA<AuthInitial>());
      });
    });

    group('Check Authentication Status', () {
      test('should return true when user is authenticated', () {
        mockAuthRepository.setAuthenticated(true);
        authCubit.emit(
          AuthSuccess(
            user: domain.User(
              id: 1,
              phoneNumber: '+919999999999',
              isVerified: true,
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
              needsSync: true,
            ),
          ),
        );

        expect(authCubit.state is AuthSuccess, isTrue);
      });

      test('should return false when user is not authenticated', () {
        mockAuthRepository.setAuthenticated(false);
        authCubit.emit(AuthInitial());

        expect(authCubit.state is! AuthSuccess, isTrue);
      });
    });

    group('Edge Cases and Error Recovery', () {
      blocTest<AuthCubit, AuthState>(
        'should handle invalid OTP format with proper validation',
        build: () {
          when(
            () => mockSendOtpUseCase.call(any()),
          ).thenAnswer((_) async => const Right('test_verification_id'));
          return authCubit;
        },
        act: (cubit) async {
          // First send OTP to set up proper state
          await cubit.sendOtp('9999999999');
          // Then try to verify with invalid OTP format
          await cubit.verifyOtp('abc');
        },
        expect:
            () => [
              isA<AuthLoading>(),
              isA<OtpSent>(),
              predicate<OtpVerificationError>(
                (state) => state.message.contains('Invalid OTP format'),
              ),
            ],
      );

      blocTest<AuthCubit, AuthState>(
        'should handle expired OTP',
        build: () {
          when(
            () => mockSendOtpUseCase.call(any()),
          ).thenAnswer((_) async => const Right('test_verification_id'));
          when(
            () => mockVerifyOtpUseCase.call(any(), any()),
          ).thenAnswer((_) async => Left(ValidationFailure('OTP expired')));
          return authCubit;
        },
        act: (cubit) async {
          await cubit.sendOtp('9999999999');
          await cubit.verifyOtp('123456');
        },
        expect:
            () => [
              isA<AuthLoading>(),
              isA<OtpSent>(),
              isA<AuthLoading>(),
              predicate<OtpVerificationError>(
                (state) => state.message.contains('expired'),
              ),
            ],
      );

      blocTest<AuthCubit, AuthState>(
        'should handle network timeout during OTP verification',
        build: () {
          when(
            () => mockSendOtpUseCase.call(any()),
          ).thenAnswer((_) async => const Right('test_verification_id'));
          when(
            () => mockVerifyOtpUseCase.call(any(), any()),
          ).thenAnswer((_) async => Left(NetworkFailure()));
          return authCubit;
        },
        act: (cubit) async {
          await cubit.sendOtp('9999999999');
          await cubit.verifyOtp('123456');
        },
        expect:
            () => [
              isA<AuthLoading>(),
              isA<OtpSent>(),
              isA<AuthLoading>(),
              predicate<OtpVerificationError>(
                (state) => state.message.contains('No internet connection'),
              ),
            ],
      );

      blocTest<AuthCubit, AuthState>(
        'should retry OTP send after failure',
        build: () {
          when(
            () => mockSendOtpUseCase.call(any()),
          ).thenAnswer((_) async => const Right('new_verification_id'));
          return authCubit;
        },
        seed: () => PhoneNumberError(message: 'Previous error'),
        act: (cubit) => cubit.sendOtp('+919999999999'),
        expect:
            () => [
              isA<AuthLoading>(),
              predicate<OtpSent>(
                (state) => state.verificationId == 'new_verification_id',
              ),
            ],
      );

      blocTest<AuthCubit, AuthState>(
        'should handle multiple rapid OTP requests',
        build: () {
          when(() => mockSendOtpUseCase.call(any())).thenAnswer(
            (_) async => Left(ValidationFailure('Too many requests')),
          );
          return authCubit;
        },
        act: (cubit) async {
          // Simulate rapid requests
          cubit.sendOtp('+919999999999');
          cubit.sendOtp('+919999999999');
          cubit.sendOtp('+919999999999');
        },
        expect:
            () => [
              isA<AuthLoading>(),
              predicate<PhoneNumberError>(
                (state) => state.message.contains('Too many requests'),
              ),
            ],
      );
    });

    group('User Session Management', () {
      blocTest<AuthCubit, AuthState>(
        'should save user data after successful verification',
        build: () {
          when(
            () => mockSendOtpUseCase.call(any()),
          ).thenAnswer((_) async => const Right('test_verification_id'));
          when(
            () => mockVerifyOtpUseCase.call(any(), any()),
          ).thenAnswer((_) async => const Right(true));
          when(() => mockGetUserUseCase.call()).thenAnswer(
            (_) async => Right(
              domain.User(
                id: 1,
                phoneNumber: '9999999999',
                isVerified: true,
                createdAt: DateTime.now(),
                updatedAt: DateTime.now(),
                needsSync: false,
              ),
            ),
          );
          when(
            () => mockSaveUserUseCase.call(any()),
          ).thenAnswer((_) async => const Right(true));
          when(() => mockGetCustomerByMobileNumber.call(any())).thenAnswer(
            (_) async => Right(
              Customer(
                customerId: 'test_customer',
                customerName: 'Test Customer',
                email: '<EMAIL>',
                mobileNumber: '+919999999999',
                companyName: 'Test Company',
                gstNo: 'GST123456789',
                businessVertical: 'Technology',
                customerCode: 'TC001',
                billingAddress: 'Test Address',
              ),
            ),
          );
          return authCubit;
        },
        act: (cubit) async {
          await cubit.sendOtp('9999999999');
          await cubit.verifyOtp('123456');
        },
        verify: (_) {
          verify(() => mockSaveUserUseCase.call(any())).called(1);
        },
      );

      blocTest<AuthCubit, AuthState>(
        'should handle user data persistence failure',
        build: () {
          when(
            () => mockSendOtpUseCase.call(any()),
          ).thenAnswer((_) async => const Right('test_verification_id'));
          when(
            () => mockVerifyOtpUseCase.call(any(), any()),
          ).thenAnswer((_) async => const Right(true));
          when(
            () => mockGetUserUseCase.call(),
          ).thenAnswer((_) async => Left(CacheFailure()));
          return authCubit;
        },
        act: (cubit) async {
          await cubit.sendOtp('9999999999');
          await cubit.verifyOtp('123456');
        },
        expect:
            () => [
              isA<AuthLoading>(),
              isA<OtpSent>(),
              isA<AuthLoading>(),
              isA<AuthError>(),
            ],
      );

      blocTest<AuthCubit, AuthState>(
        'should handle session restoration on app restart',
        build: () {
          when(
            () => mockCheckAuthStatusUseCase.call(),
          ).thenAnswer((_) async => const Right(true));
          when(() => mockGetUserUseCase.call()).thenAnswer(
            (_) async => Right(
              domain.User(
                id: 1,
                phoneNumber: '9999999999',
                isVerified: true,
                createdAt: DateTime.now(),
                updatedAt: DateTime.now(),
                needsSync: false,
              ),
            ),
          );
          return authCubit;
        },
        act: (cubit) => cubit.checkAuthStatus(),
        expect: () => [isA<AuthLoading>(), isA<AuthSuccess>()],
      );
    });

    group('Customer Data Loading', () {
      blocTest<AuthCubit, AuthState>(
        'should load customer data after successful authentication',
        build: () {
          when(
            () => mockSendOtpUseCase.call(any()),
          ).thenAnswer((_) async => const Right('test_verification_id'));
          when(
            () => mockVerifyOtpUseCase.call(any(), any()),
          ).thenAnswer((_) async => const Right(true));
          when(() => mockGetUserUseCase.call()).thenAnswer(
            (_) async => Right(
              domain.User(
                id: 1,
                phoneNumber: '9999999999',
                isVerified: true,
                createdAt: DateTime.now(),
                updatedAt: DateTime.now(),
                needsSync: false,
              ),
            ),
          );
          when(
            () => mockSaveUserUseCase.call(any()),
          ).thenAnswer((_) async => const Right(true));
          when(
            () => mockGetCustomerByMobileNumber.call('9999999999'),
          ).thenAnswer(
            (_) async => Right(
              Customer(
                customerId: 'test_customer',
                customerName: 'Test Customer',
                email: '<EMAIL>',
                mobileNumber: '+919999999999',
                companyName: 'Test Company',
                gstNo: 'GST123456789',
                businessVertical: 'Technology',
                customerCode: 'TC001',
                billingAddress: 'Test Address',
              ),
            ),
          );
          return authCubit;
        },
        act: (cubit) async {
          await cubit.sendOtp('9999999999');
          await cubit.verifyOtp('123456');
        },
        expect:
            () => [
              isA<AuthLoading>(),
              isA<OtpSent>(),
              isA<AuthLoading>(),
              predicate<AuthSuccess>(
                (state) => state.user.phoneNumber == '9999999999',
              ),
            ],
        verify: (_) {
          verify(
            () => mockGetCustomerByMobileNumber.call('9999999999'),
          ).called(1);
        },
      );

      blocTest<AuthCubit, AuthState>(
        'should handle customer data loading failure',
        build: () {
          when(
            () => mockSendOtpUseCase.call(any()),
          ).thenAnswer((_) async => const Right('test_verification_id'));
          when(
            () => mockVerifyOtpUseCase.call(any(), any()),
          ).thenAnswer((_) async => const Right(true));
          when(() => mockGetUserUseCase.call()).thenAnswer(
            (_) async => Right(
              domain.User(
                id: 1,
                phoneNumber: '9999999999',
                isVerified: true,
                createdAt: DateTime.now(),
                updatedAt: DateTime.now(),
                needsSync: false,
              ),
            ),
          );
          when(
            () => mockSaveUserUseCase.call(any()),
          ).thenAnswer((_) async => const Right(true));
          when(
            () => mockGetCustomerByMobileNumber.call(any()),
          ).thenAnswer((_) async => Left(NotFoundFailure()));
          return authCubit;
        },
        act: (cubit) async {
          await cubit.sendOtp('9999999999');
          await cubit.verifyOtp('123456');
        },
        expect:
            () => [
              isA<AuthLoading>(),
              isA<OtpSent>(),
              isA<AuthLoading>(),
              predicate<AuthError>(
                (state) => state.message.contains('not registered'),
              ),
            ],
      );
    });
  });
}
