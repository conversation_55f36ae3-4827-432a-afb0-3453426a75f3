import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/error/failures.dart';
import '../../../core/utils/logger.dart';
import '../../../domain/entities/price_list.dart';
import '../../../domain/usecases/price_list_usecases.dart';
import 'price_list_state.dart';

class PriceListCubit extends Cubit<PriceListState> {
  final GetPriceListsUseCase getPriceListsUseCase;
  final SyncPriceListsUseCase syncPriceListsUseCase;
  final AppLogger logger;

  PriceListCubit({
    required this.getPriceListsUseCase,
    required this.syncPriceListsUseCase,
    required this.logger,
  }) : super(const PriceListState());

  Future<void> loadPriceLists() async {
    if (state.isLoading) return;

    emit(state.copyWith(status: PriceListStatus.loading, errorMessage: null));

    final result = await getPriceListsUseCase();

    result.fold(
      (failure) {
        logger.e("Failed to load price lists", failure);
        String errorMessage = _getReadableErrorMessage(failure);

        emit(
          state.copyWith(
            status: PriceListStatus.error,
            errorMessage: errorMessage,
          ),
        );
      },
      (priceLists) {
        logger.i("Successfully loaded ${priceLists.length} price lists");

        // Check if list is not empty before accessing first element
        final selectedState = priceLists.isNotEmpty ? priceLists.first : null;

        // Emit a single state update with all changes
        emit(
          state.copyWith(
            status: PriceListStatus.success,
            priceLists: priceLists,
            selectedState: selectedState,
          ),
        );
      },
    );
  }

  Future<void> changeState(PriceList newPriceList) async {
    emit(
      state.copyWith(
        selectedState: newPriceList,
        status: PriceListStatus.stateChange,
      ),
    );
  }

  Future<void> syncPriceLists() async {
    if (state.isSyncing) return;

    // Store the current selected state to restore it after sync
    final currentSelectedState = state.selectedState;
    final currentSelectedStateName = currentSelectedState?.state;
    
    emit(state.copyWith(status: PriceListStatus.syncing, errorMessage: null));

    final result = await syncPriceListsUseCase();

    result.fold(
      (failure) {
        logger.e("Failed to sync price lists", failure);
        String errorMessage = _getReadableErrorMessage(failure);

        // Consistent error handling approach
        final newStatus =
            state.priceLists.isNotEmpty
                ? PriceListStatus.success
                : PriceListStatus.error;

        emit(state.copyWith(status: newStatus, errorMessage: errorMessage));
      },
      (priceLists) {
        logger.i("Successfully synced ${priceLists.length} price lists");

        // Try to preserve the previously selected state if possible
        PriceList? newSelectedState;
        
        if (currentSelectedStateName != null && priceLists.isNotEmpty) {
          // Try to find the same state in the updated list
          newSelectedState = priceLists.firstWhere(
            (priceList) => priceList.state == currentSelectedStateName,
            orElse: () => priceLists.first,
          );
          logger.i("Restored selected state: ${newSelectedState.state}");
        } 
        // If we didn't have a selected state or couldn't find it, select the first one if available
        else if (priceLists.isNotEmpty) {
          newSelectedState = priceLists.first;
          logger.i("Selected first state: ${newSelectedState.state}");
        }

        emit(
          state.copyWith(
            status: PriceListStatus.success,
            priceLists: priceLists,
            selectedState: newSelectedState,
          ),
        );
      },
    );
  }

  String _getReadableErrorMessage(Failure failure) {
    if (failure is NetworkFailure) {
      return "No internet connection. Please check your connection and try again.";
    } else if (failure is ServerFailure) {
      return "Check your internet connection, Please try again later.";
    } else if (failure is CacheFailure) {
      return "No data available. Please connect to the internet to download price lists.";
    } else {
      return "An unexpected error occurred. Please try again.";
    }
  }
}