{"protocolVersion":"0.1.1","runnerVersion":"1.25.15","pid":24360,"type":"start","time":0}
{"suite":{"id":0,"platform":"vm","path":"D:/flutter projects/aquapartner/test/core/mixins/analytics_mixin_test.dart"},"type":"suite","time":0}
{"test":{"id":1,"name":"loading D:/flutter projects/aquapartner/test/core/mixins/analytics_mixin_test.dart","suiteID":0,"groupIDs":[],"metadata":{"skip":false,"skipReason":null},"line":null,"column":null,"url":null},"type":"testStart","time":1}
{"suite":{"id":2,"platform":"vm","path":"D:/flutter projects/aquapartner/test/core/network/app_check_interceptor_test.dart"},"type":"suite","time":4}
{"test":{"id":3,"name":"loading D:/flutter projects/aquapartner/test/core/network/app_check_interceptor_test.dart","suiteID":2,"groupIDs":[],"metadata":{"skip":false,"skipReason":null},"line":null,"column":null,"url":null},"type":"testStart","time":4}
{"suite":{"id":4,"platform":"vm","path":"D:/flutter projects/aquapartner/test/core/network/performance_interceptor_test.dart"},"type":"suite","time":5}
{"test":{"id":5,"name":"loading D:/flutter projects/aquapartner/test/core/network/performance_interceptor_test.dart","suiteID":4,"groupIDs":[],"metadata":{"skip":false,"skipReason":null},"line":null,"column":null,"url":null},"type":"testStart","time":5}
{"suite":{"id":6,"platform":"vm","path":"D:/flutter projects/aquapartner/test/core/services/analytics_service_test.dart"},"type":"suite","time":6}
{"test":{"id":7,"name":"loading D:/flutter projects/aquapartner/test/core/services/analytics_service_test.dart","suiteID":6,"groupIDs":[],"metadata":{"skip":false,"skipReason":null},"line":null,"column":null,"url":null},"type":"testStart","time":6}
{"suite":{"id":8,"platform":"vm","path":"D:/flutter projects/aquapartner/test/core/services/app_check_service_test.dart"},"type":"suite","time":6}
{"test":{"id":9,"name":"loading D:/flutter projects/aquapartner/test/core/services/app_check_service_test.dart","suiteID":8,"groupIDs":[],"metadata":{"skip":false,"skipReason":null},"line":null,"column":null,"url":null},"type":"testStart","time":6}
{"suite":{"id":10,"platform":"vm","path":"D:/flutter projects/aquapartner/test/core/services/performance_monitoring_service_test.dart"},"type":"suite","time":7}
{"test":{"id":11,"name":"loading D:/flutter projects/aquapartner/test/core/services/performance_monitoring_service_test.dart","suiteID":10,"groupIDs":[],"metadata":{"skip":false,"skipReason":null},"line":null,"column":null,"url":null},"type":"testStart","time":7}
{"count":15,"time":9,"type":"allSuites"}
{"testID":1,"result":"success","skipped":false,"hidden":true,"type":"testDone","time":2495}
{"group":{"id":12,"suiteID":0,"parentID":null,"name":"","metadata":{"skip":false,"skipReason":null},"testCount":6,"line":null,"column":null,"url":null},"type":"group","time":2498}
{"group":{"id":13,"suiteID":0,"parentID":12,"name":"AnalyticsMixin Tests","metadata":{"skip":false,"skipReason":null},"testCount":6,"line":72,"column":3,"url":"file:///D:/flutter%20projects/aquapartner/test/core/mixins/analytics_mixin_test.dart"},"type":"group","time":2499}
{"test":{"id":14,"name":"AnalyticsMixin Tests (setUpAll)","suiteID":0,"groupIDs":[12,13],"metadata":{"skip":false,"skipReason":null},"line":75,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/core/mixins/analytics_mixin_test.dart"},"type":"testStart","time":2499}
{"testID":14,"result":"success","skipped":false,"hidden":true,"type":"testDone","time":2530}
{"group":{"id":15,"suiteID":0,"parentID":13,"name":"AnalyticsMixin Tests Screen Tracking","metadata":{"skip":false,"skipReason":null},"testCount":2,"line":99,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/core/mixins/analytics_mixin_test.dart"},"type":"group","time":2530}
{"test":{"id":16,"name":"AnalyticsMixin Tests Screen Tracking should track screen view on widget creation","suiteID":0,"groupIDs":[12,13,15],"metadata":{"skip":false,"skipReason":null},"line":175,"column":5,"url":"package:flutter_test/src/widget_tester.dart","root_line":100,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/core/mixins/analytics_mixin_test.dart"},"type":"testStart","time":2530}
{"testID":3,"result":"success","skipped":false,"hidden":true,"type":"testDone","time":2659}
{"group":{"id":17,"suiteID":2,"parentID":null,"name":"","metadata":{"skip":false,"skipReason":null},"testCount":3,"line":null,"column":null,"url":null},"type":"group","time":2659}
{"group":{"id":18,"suiteID":2,"parentID":17,"name":"AppCheckInterceptor","metadata":{"skip":false,"skipReason":null},"testCount":3,"line":96,"column":3,"url":"file:///D:/flutter%20projects/aquapartner/test/core/network/app_check_interceptor_test.dart"},"type":"group","time":2659}
{"test":{"id":19,"name":"AppCheckInterceptor onRequest should add App Check token to headers when available","suiteID":2,"groupIDs":[17,18],"metadata":{"skip":false,"skipReason":null},"line":97,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/core/network/app_check_interceptor_test.dart"},"type":"testStart","time":2659}
{"testID":19,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":2690}
{"test":{"id":20,"name":"AppCheckInterceptor onRequest should log warning when token is not available","suiteID":2,"groupIDs":[17,18],"metadata":{"skip":false,"skipReason":null},"line":123,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/core/network/app_check_interceptor_test.dart"},"type":"testStart","time":2690}
{"testID":20,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":2696}
{"test":{"id":21,"name":"AppCheckInterceptor onRequest should handle errors gracefully","suiteID":2,"groupIDs":[17,18],"metadata":{"skip":false,"skipReason":null},"line":143,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/core/network/app_check_interceptor_test.dart"},"type":"testStart","time":2696}
{"testID":21,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":2701}
{"testID":5,"result":"success","skipped":false,"hidden":true,"type":"testDone","time":2898}
{"group":{"id":22,"suiteID":4,"parentID":null,"name":"","metadata":{"skip":false,"skipReason":null},"testCount":4,"line":null,"column":null,"url":null},"type":"group","time":2898}
{"group":{"id":23,"suiteID":4,"parentID":22,"name":"PerformanceInterceptor","metadata":{"skip":false,"skipReason":null},"testCount":4,"line":244,"column":3,"url":"file:///D:/flutter%20projects/aquapartner/test/core/network/performance_interceptor_test.dart"},"type":"group","time":2898}
{"test":{"id":24,"name":"PerformanceInterceptor onRequest should start HTTP metric","suiteID":4,"groupIDs":[22,23],"metadata":{"skip":false,"skipReason":null},"line":245,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/core/network/performance_interceptor_test.dart"},"type":"testStart","time":2898}
{"testID":24,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":2937}
{"test":{"id":25,"name":"PerformanceInterceptor onResponse should stop HTTP metric","suiteID":4,"groupIDs":[22,23],"metadata":{"skip":false,"skipReason":null},"line":270,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/core/network/performance_interceptor_test.dart"},"type":"testStart","time":2937}
{"testID":25,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":2947}
{"test":{"id":26,"name":"PerformanceInterceptor onError should stop HTTP metric with error information","suiteID":4,"groupIDs":[22,23],"metadata":{"skip":false,"skipReason":null},"line":301,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/core/network/performance_interceptor_test.dart"},"type":"testStart","time":2947}
{"testID":26,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":2952}
{"test":{"id":27,"name":"PerformanceInterceptor onRequest should handle errors gracefully","suiteID":4,"groupIDs":[22,23],"metadata":{"skip":false,"skipReason":null},"line":329,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/core/network/performance_interceptor_test.dart"},"type":"testStart","time":2952}
{"testID":27,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":2955}
{"testID":16,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":3253}
{"test":{"id":28,"name":"AnalyticsMixin Tests Screen Tracking should track screen duration on dispose","suiteID":0,"groupIDs":[12,13,15],"metadata":{"skip":false,"skipReason":null},"line":175,"column":5,"url":"package:flutter_test/src/widget_tester.dart","root_line":124,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/core/mixins/analytics_mixin_test.dart"},"type":"testStart","time":3253}
{"testID":28,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":3354}
{"group":{"id":29,"suiteID":0,"parentID":13,"name":"AnalyticsMixin Tests User Interaction Tracking","metadata":{"skip":false,"skipReason":null},"testCount":2,"line":149,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/core/mixins/analytics_mixin_test.dart"},"type":"group","time":3354}
{"test":{"id":30,"name":"AnalyticsMixin Tests User Interaction Tracking should track user interactions","suiteID":0,"groupIDs":[12,13,29],"metadata":{"skip":false,"skipReason":null},"line":175,"column":5,"url":"package:flutter_test/src/widget_tester.dart","root_line":150,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/core/mixins/analytics_mixin_test.dart"},"type":"testStart","time":3354}
{"testID":7,"result":"success","skipped":false,"hidden":true,"type":"testDone","time":3396}
{"group":{"id":31,"suiteID":6,"parentID":null,"name":"","metadata":{"skip":false,"skipReason":null},"testCount":17,"line":null,"column":null,"url":null},"type":"group","time":3396}
{"group":{"id":32,"suiteID":6,"parentID":31,"name":"AnalyticsService Tests","metadata":{"skip":false,"skipReason":null},"testCount":17,"line":8,"column":3,"url":"file:///D:/flutter%20projects/aquapartner/test/core/services/analytics_service_test.dart"},"type":"group","time":3396}
{"group":{"id":33,"suiteID":6,"parentID":32,"name":"AnalyticsService Tests Event Logging","metadata":{"skip":false,"skipReason":null},"testCount":6,"line":24,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/core/services/analytics_service_test.dart"},"type":"group","time":3396}
{"test":{"id":34,"name":"AnalyticsService Tests Event Logging should log event with name only","suiteID":6,"groupIDs":[31,32,33],"metadata":{"skip":false,"skipReason":null},"line":25,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/core/services/analytics_service_test.dart"},"type":"testStart","time":3396}
{"testID":34,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":3432}
{"test":{"id":35,"name":"AnalyticsService Tests Event Logging should log event with parameters","suiteID":6,"groupIDs":[31,32,33],"metadata":{"skip":false,"skipReason":null},"line":43,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/core/services/analytics_service_test.dart"},"type":"testStart","time":3432}
{"testID":35,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":3436}
{"test":{"id":36,"name":"AnalyticsService Tests Event Logging should log multiple events","suiteID":6,"groupIDs":[31,32,33],"metadata":{"skip":false,"skipReason":null},"line":64,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/core/services/analytics_service_test.dart"},"type":"testStart","time":3436}
{"testID":36,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":3440}
{"test":{"id":37,"name":"AnalyticsService Tests Event Logging should handle null parameters","suiteID":6,"groupIDs":[31,32,33],"metadata":{"skip":false,"skipReason":null},"line":75,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/core/services/analytics_service_test.dart"},"type":"testStart","time":3440}
{"testID":37,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":3443}
{"test":{"id":38,"name":"AnalyticsService Tests Event Logging should handle empty parameters","suiteID":6,"groupIDs":[31,32,33],"metadata":{"skip":false,"skipReason":null},"line":91,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/core/services/analytics_service_test.dart"},"type":"testStart","time":3443}
{"testID":38,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":3446}
{"test":{"id":39,"name":"AnalyticsService Tests Event Logging should handle complex parameter types","suiteID":6,"groupIDs":[31,32,33],"metadata":{"skip":false,"skipReason":null},"line":107,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/core/services/analytics_service_test.dart"},"type":"testStart","time":3446}
{"testID":39,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":3449}
{"group":{"id":40,"suiteID":6,"parentID":32,"name":"AnalyticsService Tests User Properties","metadata":{"skip":false,"skipReason":null},"testCount":4,"line":132,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/core/services/analytics_service_test.dart"},"type":"group","time":3450}
{"test":{"id":41,"name":"AnalyticsService Tests User Properties should set user property","suiteID":6,"groupIDs":[31,32,40],"metadata":{"skip":false,"skipReason":null},"line":133,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/core/services/analytics_service_test.dart"},"type":"testStart","time":3450}
{"testID":41,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":3453}
{"test":{"id":42,"name":"AnalyticsService Tests User Properties should set multiple user properties","suiteID":6,"groupIDs":[31,32,40],"metadata":{"skip":false,"skipReason":null},"line":149,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/core/services/analytics_service_test.dart"},"type":"testStart","time":3453}
{"testID":42,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":3456}
{"test":{"id":43,"name":"AnalyticsService Tests User Properties should handle null user property value","suiteID":6,"groupIDs":[31,32,40],"metadata":{"skip":false,"skipReason":null},"line":178,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/core/services/analytics_service_test.dart"},"type":"testStart","time":3456}
{"testID":43,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":3459}
{"test":{"id":44,"name":"AnalyticsService Tests User Properties should update existing user property","suiteID":6,"groupIDs":[31,32,40],"metadata":{"skip":false,"skipReason":null},"line":193,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/core/services/analytics_service_test.dart"},"type":"testStart","time":3460}
{"testID":44,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":3462}
{"group":{"id":45,"suiteID":6,"parentID":32,"name":"AnalyticsService Tests Screen Tracking","metadata":{"skip":false,"skipReason":null},"testCount":4,"line":216,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/core/services/analytics_service_test.dart"},"type":"group","time":3462}
{"test":{"id":46,"name":"AnalyticsService Tests Screen Tracking should set current screen","suiteID":6,"groupIDs":[31,32,45],"metadata":{"skip":false,"skipReason":null},"line":217,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/core/services/analytics_service_test.dart"},"type":"testStart","time":3462}
{"testID":46,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":3465}
{"test":{"id":47,"name":"AnalyticsService Tests Screen Tracking should set current screen with class override","suiteID":6,"groupIDs":[31,32,45],"metadata":{"skip":false,"skipReason":null},"line":225,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/core/services/analytics_service_test.dart"},"type":"testStart","time":3466}
{"testID":47,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":3468}
{"test":{"id":48,"name":"AnalyticsService Tests Screen Tracking should handle null screen name","suiteID":6,"groupIDs":[31,32,45],"metadata":{"skip":false,"skipReason":null},"line":233,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/core/services/analytics_service_test.dart"},"type":"testStart","time":3468}
{"testID":48,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":3471}
{"test":{"id":49,"name":"AnalyticsService Tests Screen Tracking should update current screen","suiteID":6,"groupIDs":[31,32,45],"metadata":{"skip":false,"skipReason":null},"line":239,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/core/services/analytics_service_test.dart"},"type":"testStart","time":3471}
{"testID":49,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":3475}
{"group":{"id":50,"suiteID":6,"parentID":32,"name":"AnalyticsService Tests Parameter Validation","metadata":{"skip":false,"skipReason":null},"testCount":3,"line":251,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/core/services/analytics_service_test.dart"},"type":"group","time":3476}
{"test":{"id":51,"name":"AnalyticsService Tests Parameter Validation should handle long event names","suiteID":6,"groupIDs":[31,32,50],"metadata":{"skip":false,"skipReason":null},"line":252,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/core/services/analytics_service_test.dart"},"type":"testStart","time":3476}
{"testID":51,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":3479}
{"test":{"id":52,"name":"AnalyticsService Tests Parameter Validation should handle special characters in event names","suiteID":6,"groupIDs":[31,32,50],"metadata":{"skip":false,"skipReason":null},"line":262,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/core/services/analytics_service_test.dart"},"type":"testStart","time":3479}
{"testID":52,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":3483}
{"test":{"id":53,"name":"AnalyticsService Tests Parameter Validation should handle Unicode characters in parameters","suiteID":6,"groupIDs":[31,32,50],"metadata":{"skip":false,"skipReason":null},"line":271,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/core/services/analytics_service_test.dart"},"type":"testStart","time":3484}
{"testID":53,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":3487}
{"testID":30,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":3531}
{"test":{"id":54,"name":"AnalyticsMixin Tests User Interaction Tracking should track custom events","suiteID":0,"groupIDs":[12,13,29],"metadata":{"skip":false,"skipReason":null},"line":175,"column":5,"url":"package:flutter_test/src/widget_tester.dart","root_line":177,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/core/mixins/analytics_mixin_test.dart"},"type":"testStart","time":3531}
{"testID":54,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":3601}
{"group":{"id":55,"suiteID":0,"parentID":13,"name":"AnalyticsMixin Tests Error Tracking","metadata":{"skip":false,"skipReason":null},"testCount":1,"line":200,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/core/mixins/analytics_mixin_test.dart"},"type":"group","time":3602}
{"test":{"id":56,"name":"AnalyticsMixin Tests Error Tracking should track errors","suiteID":0,"groupIDs":[12,13,55],"metadata":{"skip":false,"skipReason":null},"line":175,"column":5,"url":"package:flutter_test/src/widget_tester.dart","root_line":201,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/core/mixins/analytics_mixin_test.dart"},"type":"testStart","time":3602}
{"suite":{"id":57,"platform":"vm","path":"D:/flutter projects/aquapartner/test/core/utils/string_utils_test.dart"},"type":"suite","time":3626}
{"test":{"id":58,"name":"loading D:/flutter projects/aquapartner/test/core/utils/string_utils_test.dart","suiteID":57,"groupIDs":[],"metadata":{"skip":false,"skipReason":null},"line":null,"column":null,"url":null},"type":"testStart","time":3626}
{"testID":56,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":3665}
{"group":{"id":59,"suiteID":0,"parentID":13,"name":"AnalyticsMixin Tests User Flow Tracking","metadata":{"skip":false,"skipReason":null},"testCount":1,"line":226,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/core/mixins/analytics_mixin_test.dart"},"type":"group","time":3665}
{"test":{"id":60,"name":"AnalyticsMixin Tests User Flow Tracking should track user flows","suiteID":0,"groupIDs":[12,13,59],"metadata":{"skip":false,"skipReason":null},"line":175,"column":5,"url":"package:flutter_test/src/widget_tester.dart","root_line":227,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/core/mixins/analytics_mixin_test.dart"},"type":"testStart","time":3665}
{"testID":9,"result":"success","skipped":false,"hidden":true,"type":"testDone","time":3669}
{"group":{"id":61,"suiteID":8,"parentID":null,"name":"","metadata":{"skip":false,"skipReason":null},"testCount":4,"line":null,"column":null,"url":null},"type":"group","time":3669}
{"group":{"id":62,"suiteID":8,"parentID":61,"name":"AppCheckService","metadata":{"skip":false,"skipReason":null},"testCount":4,"line":81,"column":3,"url":"file:///D:/flutter%20projects/aquapartner/test/core/services/app_check_service_test.dart"},"type":"group","time":3670}
{"test":{"id":63,"name":"AppCheckService initialize should activate App Check and enable token refresh","suiteID":8,"groupIDs":[61,62],"metadata":{"skip":false,"skipReason":null},"line":82,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/core/services/app_check_service_test.dart"},"type":"testStart","time":3670}
{"testID":63,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":3696}
{"test":{"id":64,"name":"AppCheckService getToken should return token when successful","suiteID":8,"groupIDs":[61,62],"metadata":{"skip":false,"skipReason":null},"line":95,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/core/services/app_check_service_test.dart"},"type":"testStart","time":3696}
{"testID":64,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":3700}
{"test":{"id":65,"name":"AppCheckService getToken should return debug token when error occurs in debug mode","suiteID":8,"groupIDs":[61,62],"metadata":{"skip":false,"skipReason":null},"line":106,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/core/services/app_check_service_test.dart"},"type":"testStart","time":3701}
{"testID":65,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":3706}
{"test":{"id":66,"name":"AppCheckService getToken should initialize if not already initialized","suiteID":8,"groupIDs":[61,62],"metadata":{"skip":false,"skipReason":null},"line":122,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/core/services/app_check_service_test.dart"},"type":"testStart","time":3707}
{"testID":66,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":3710}
{"testID":60,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":3725}
{"test":{"id":67,"name":"AnalyticsMixin Tests (tearDownAll)","suiteID":0,"groupIDs":[12,13],"metadata":{"skip":false,"skipReason":null},"line":null,"column":null,"url":null},"type":"testStart","time":3725}
{"testID":67,"result":"success","skipped":false,"hidden":true,"type":"testDone","time":3727}
{"testID":11,"result":"success","skipped":false,"hidden":true,"type":"testDone","time":3991}
{"group":{"id":68,"suiteID":10,"parentID":null,"name":"","metadata":{"skip":false,"skipReason":null},"testCount":6,"line":null,"column":null,"url":null},"type":"group","time":3991}
{"group":{"id":69,"suiteID":10,"parentID":68,"name":"PerformanceMonitoringService","metadata":{"skip":false,"skipReason":null},"testCount":6,"line":170,"column":3,"url":"file:///D:/flutter%20projects/aquapartner/test/core/services/performance_monitoring_service_test.dart"},"type":"group","time":3991}
{"test":{"id":70,"name":"PerformanceMonitoringService service should be initialized properly","suiteID":10,"groupIDs":[68,69],"metadata":{"skip":false,"skipReason":null},"line":171,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/core/services/performance_monitoring_service_test.dart"},"type":"testStart","time":3991}
{"testID":70,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":4019}
{"test":{"id":71,"name":"PerformanceMonitoringService startTrace should create and start a new trace","suiteID":10,"groupIDs":[68,69],"metadata":{"skip":false,"skipReason":null},"line":176,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/core/services/performance_monitoring_service_test.dart"},"type":"testStart","time":4020}
{"testID":71,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":4024}
{"test":{"id":72,"name":"PerformanceMonitoringService stopTrace should stop the trace and return duration","suiteID":10,"groupIDs":[68,69],"metadata":{"skip":false,"skipReason":null},"line":186,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/core/services/performance_monitoring_service_test.dart"},"type":"testStart","time":4025}
{"testID":72,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":4028}
{"test":{"id":73,"name":"PerformanceMonitoringService incrementMetric should increment a counter in a trace","suiteID":10,"groupIDs":[68,69],"metadata":{"skip":false,"skipReason":null},"line":198,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/core/services/performance_monitoring_service_test.dart"},"type":"testStart","time":4028}
{"testID":73,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":4034}
{"test":{"id":74,"name":"PerformanceMonitoringService startHttpMetric should create and start a new HTTP metric","suiteID":10,"groupIDs":[68,69],"metadata":{"skip":false,"skipReason":null},"line":210,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/core/services/performance_monitoring_service_test.dart"},"type":"testStart","time":4034}
{"testID":74,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":4040}
{"test":{"id":75,"name":"PerformanceMonitoringService stopHttpMetric should set attributes and stop the HTTP metric","suiteID":10,"groupIDs":[68,69],"metadata":{"skip":false,"skipReason":null},"line":220,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/core/services/performance_monitoring_service_test.dart"},"type":"testStart","time":4040}
{"testID":75,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":4045}
{"suite":{"id":76,"platform":"vm","path":"D:/flutter projects/aquapartner/test/domain/entities/product_test.dart"},"type":"suite","time":4693}
{"test":{"id":77,"name":"loading D:/flutter projects/aquapartner/test/domain/entities/product_test.dart","suiteID":76,"groupIDs":[],"metadata":{"skip":false,"skipReason":null},"line":null,"column":null,"url":null},"type":"testStart","time":4693}
{"testID":58,"result":"success","skipped":false,"hidden":true,"type":"testDone","time":4859}
{"group":{"id":78,"suiteID":57,"parentID":null,"name":"","metadata":{"skip":false,"skipReason":null},"testCount":20,"line":null,"column":null,"url":null},"type":"group","time":4859}
{"group":{"id":79,"suiteID":57,"parentID":78,"name":"StringUtils Tests","metadata":{"skip":false,"skipReason":null},"testCount":20,"line":5,"column":3,"url":"file:///D:/flutter%20projects/aquapartner/test/core/utils/string_utils_test.dart"},"type":"group","time":4859}
{"group":{"id":80,"suiteID":57,"parentID":79,"name":"StringUtils Tests decodeUnicodeEscapes","metadata":{"skip":false,"skipReason":null},"testCount":20,"line":6,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/core/utils/string_utils_test.dart"},"type":"group","time":4859}
{"test":{"id":81,"name":"StringUtils Tests decodeUnicodeEscapes should decode basic Unicode escape sequences","suiteID":57,"groupIDs":[78,79,80],"metadata":{"skip":false,"skipReason":null},"line":7,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/core/utils/string_utils_test.dart"},"type":"testStart","time":4859}
{"testID":81,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":4884}
{"test":{"id":82,"name":"StringUtils Tests decodeUnicodeEscapes should decode Unicode escape sequences with special characters","suiteID":57,"groupIDs":[78,79,80],"metadata":{"skip":false,"skipReason":null},"line":15,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/core/utils/string_utils_test.dart"},"type":"testStart","time":4885}
{"testID":82,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":4889}
{"test":{"id":83,"name":"StringUtils Tests decodeUnicodeEscapes should decode Unicode escape sequences with emojis","suiteID":57,"groupIDs":[78,79,80],"metadata":{"skip":false,"skipReason":null},"line":23,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/core/utils/string_utils_test.dart"},"type":"testStart","time":4889}
{"testID":83,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":4891}
{"test":{"id":84,"name":"StringUtils Tests decodeUnicodeEscapes should handle mixed content with Unicode escapes","suiteID":57,"groupIDs":[78,79,80],"metadata":{"skip":false,"skipReason":null},"line":31,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/core/utils/string_utils_test.dart"},"type":"testStart","time":4892}
{"testID":84,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":4894}
{"test":{"id":85,"name":"StringUtils Tests decodeUnicodeEscapes should handle string without Unicode escapes","suiteID":57,"groupIDs":[78,79,80],"metadata":{"skip":false,"skipReason":null},"line":39,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/core/utils/string_utils_test.dart"},"type":"testStart","time":4895}
{"testID":85,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":4897}
{"test":{"id":86,"name":"StringUtils Tests decodeUnicodeEscapes should handle empty string","suiteID":57,"groupIDs":[78,79,80],"metadata":{"skip":false,"skipReason":null},"line":47,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/core/utils/string_utils_test.dart"},"type":"testStart","time":4897}
{"testID":86,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":4899}
{"test":{"id":87,"name":"StringUtils Tests decodeUnicodeEscapes should handle string with only Unicode escapes","suiteID":57,"groupIDs":[78,79,80],"metadata":{"skip":false,"skipReason":null},"line":55,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/core/utils/string_utils_test.dart"},"type":"testStart","time":4900}
{"testID":87,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":4902}
{"test":{"id":88,"name":"StringUtils Tests decodeUnicodeEscapes should handle malformed Unicode escapes gracefully","suiteID":57,"groupIDs":[78,79,80],"metadata":{"skip":false,"skipReason":null},"line":63,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/core/utils/string_utils_test.dart"},"type":"testStart","time":4902}
{"testID":88,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":4905}
{"test":{"id":89,"name":"StringUtils Tests decodeUnicodeEscapes should handle lowercase Unicode escapes","suiteID":57,"groupIDs":[78,79,80],"metadata":{"skip":false,"skipReason":null},"line":70,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/core/utils/string_utils_test.dart"},"type":"testStart","time":4905}
{"testID":89,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":4909}
{"test":{"id":90,"name":"StringUtils Tests decodeUnicodeEscapes should handle multiple consecutive Unicode escapes","suiteID":57,"groupIDs":[78,79,80],"metadata":{"skip":false,"skipReason":null},"line":78,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/core/utils/string_utils_test.dart"},"type":"testStart","time":4909}
{"testID":90,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":4912}
{"test":{"id":91,"name":"StringUtils Tests decodeUnicodeEscapes should handle Unicode escapes at string boundaries","suiteID":57,"groupIDs":[78,79,80],"metadata":{"skip":false,"skipReason":null},"line":86,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/core/utils/string_utils_test.dart"},"type":"testStart","time":4912}
{"testID":91,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":4914}
{"test":{"id":92,"name":"StringUtils Tests decodeUnicodeEscapes should handle Unicode escapes with numbers","suiteID":57,"groupIDs":[78,79,80],"metadata":{"skip":false,"skipReason":null},"line":97,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/core/utils/string_utils_test.dart"},"type":"testStart","time":4915}
{"testID":92,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":4917}
{"test":{"id":93,"name":"StringUtils Tests decodeUnicodeEscapes should handle Unicode escapes with punctuation","suiteID":57,"groupIDs":[78,79,80],"metadata":{"skip":false,"skipReason":null},"line":105,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/core/utils/string_utils_test.dart"},"type":"testStart","time":4918}
{"testID":93,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":4920}
{"test":{"id":94,"name":"StringUtils Tests decodeUnicodeEscapes should handle backslash that is not Unicode escape","suiteID":57,"groupIDs":[78,79,80],"metadata":{"skip":false,"skipReason":null},"line":113,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/core/utils/string_utils_test.dart"},"type":"testStart","time":4920}
{"testID":94,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":4922}
{"test":{"id":95,"name":"StringUtils Tests decodeUnicodeEscapes should handle very long strings with Unicode escapes","suiteID":57,"groupIDs":[78,79,80],"metadata":{"skip":false,"skipReason":null},"line":121,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/core/utils/string_utils_test.dart"},"type":"testStart","time":4923}
{"testID":95,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":4927}
{"test":{"id":96,"name":"StringUtils Tests decodeUnicodeEscapes should handle Unicode escapes with whitespace","suiteID":57,"groupIDs":[78,79,80],"metadata":{"skip":false,"skipReason":null},"line":133,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/core/utils/string_utils_test.dart"},"type":"testStart","time":4928}
{"testID":96,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":4930}
{"test":{"id":97,"name":"StringUtils Tests decodeUnicodeEscapes should handle high Unicode values","suiteID":57,"groupIDs":[78,79,80],"metadata":{"skip":false,"skipReason":null},"line":141,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/core/utils/string_utils_test.dart"},"type":"testStart","time":4930}
{"testID":97,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":4933}
{"test":{"id":98,"name":"StringUtils Tests decodeUnicodeEscapes should handle null input gracefully","suiteID":57,"groupIDs":[78,79,80],"metadata":{"skip":false,"skipReason":null},"line":149,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/core/utils/string_utils_test.dart"},"type":"testStart","time":4933}
{"testID":98,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":4935}
{"test":{"id":99,"name":"StringUtils Tests decodeUnicodeEscapes should preserve existing Unicode characters","suiteID":57,"groupIDs":[78,79,80],"metadata":{"skip":false,"skipReason":null},"line":155,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/core/utils/string_utils_test.dart"},"type":"testStart","time":4935}
{"testID":99,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":4937}
{"test":{"id":100,"name":"StringUtils Tests decodeUnicodeEscapes should handle case sensitivity in hex digits","suiteID":57,"groupIDs":[78,79,80],"metadata":{"skip":false,"skipReason":null},"line":163,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/core/utils/string_utils_test.dart"},"type":"testStart","time":4938}
{"testID":100,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":4940}
{"suite":{"id":101,"platform":"vm","path":"D:/flutter projects/aquapartner/test/domain/usecases/auth_usecases_test.dart"},"type":"suite","time":5437}
{"test":{"id":102,"name":"loading D:/flutter projects/aquapartner/test/domain/usecases/auth_usecases_test.dart","suiteID":101,"groupIDs":[],"metadata":{"skip":false,"skipReason":null},"line":null,"column":null,"url":null},"type":"testStart","time":5437}
{"testID":77,"result":"success","skipped":false,"hidden":true,"type":"testDone","time":5925}
{"group":{"id":103,"suiteID":76,"parentID":null,"name":"","metadata":{"skip":false,"skipReason":null},"testCount":12,"line":null,"column":null,"url":null},"type":"group","time":5925}
{"group":{"id":104,"suiteID":76,"parentID":103,"name":"Product Entity Tests","metadata":{"skip":false,"skipReason":null},"testCount":12,"line":5,"column":3,"url":"file:///D:/flutter%20projects/aquapartner/test/domain/entities/product_test.dart"},"type":"group","time":5925}
{"group":{"id":105,"suiteID":76,"parentID":104,"name":"Product Entity Tests Product Creation","metadata":{"skip":false,"skipReason":null},"testCount":2,"line":15,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/domain/entities/product_test.dart"},"type":"group","time":5925}
{"test":{"id":106,"name":"Product Entity Tests Product Creation should create Product with all required fields","suiteID":76,"groupIDs":[103,104,105],"metadata":{"skip":false,"skipReason":null},"line":16,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/domain/entities/product_test.dart"},"type":"testStart","time":5925}
{"testID":106,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":6045}
{"test":{"id":107,"name":"Product Entity Tests Product Creation should create Product with empty optional fields","suiteID":76,"groupIDs":[103,104,105],"metadata":{"skip":false,"skipReason":null},"line":37,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/domain/entities/product_test.dart"},"type":"testStart","time":6050}
{"suite":{"id":108,"platform":"vm","path":"D:/flutter projects/aquapartner/test/presentation/cubit/auth/auth_cubit_test.dart"},"type":"suite","time":6127}
{"test":{"id":109,"name":"loading D:/flutter projects/aquapartner/test/presentation/cubit/auth/auth_cubit_test.dart","suiteID":108,"groupIDs":[],"metadata":{"skip":false,"skipReason":null},"line":null,"column":null,"url":null},"type":"testStart","time":6127}
{"testID":107,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":6133}
{"group":{"id":110,"suiteID":76,"parentID":104,"name":"Product Entity Tests Product Equality","metadata":{"skip":false,"skipReason":null},"testCount":3,"line":59,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/domain/entities/product_test.dart"},"type":"group","time":6140}
{"test":{"id":111,"name":"Product Entity Tests Product Equality should be equal when all properties are the same","suiteID":76,"groupIDs":[103,104,110],"metadata":{"skip":false,"skipReason":null},"line":60,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/domain/entities/product_test.dart"},"type":"testStart","time":6140}
{"testID":111,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":6145}
{"test":{"id":112,"name":"Product Entity Tests Product Equality should not be equal when properties differ","suiteID":76,"groupIDs":[103,104,110],"metadata":{"skip":false,"skipReason":null},"line":91,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/domain/entities/product_test.dart"},"type":"testStart","time":6145}
{"testID":112,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":6149}
{"test":{"id":113,"name":"Product Entity Tests Product Equality should not be equal when properties differ","suiteID":76,"groupIDs":[103,104,110],"metadata":{"skip":false,"skipReason":null},"line":122,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/domain/entities/product_test.dart"},"type":"testStart","time":6150}
{"testID":113,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":6153}
{"group":{"id":114,"suiteID":76,"parentID":104,"name":"Product Entity Tests Product String Representation","metadata":{"skip":false,"skipReason":null},"testCount":1,"line":153,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/domain/entities/product_test.dart"},"type":"group","time":6153}
{"test":{"id":115,"name":"Product Entity Tests Product String Representation should return proper string representation","suiteID":76,"groupIDs":[103,104,114],"metadata":{"skip":false,"skipReason":null},"line":154,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/domain/entities/product_test.dart"},"type":"testStart","time":6153}
{"testID":115,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":6156}
{"group":{"id":116,"suiteID":76,"parentID":104,"name":"Product Entity Tests Product Validation","metadata":{"skip":false,"skipReason":null},"testCount":4,"line":177,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/domain/entities/product_test.dart"},"type":"group","time":6156}
{"test":{"id":117,"name":"Product Entity Tests Product Validation should handle special characters in product name","suiteID":76,"groupIDs":[103,104,116],"metadata":{"skip":false,"skipReason":null},"line":178,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/domain/entities/product_test.dart"},"type":"testStart","time":6156}
{"testID":117,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":6159}
{"test":{"id":118,"name":"Product Entity Tests Product Validation should handle HTML content","suiteID":76,"groupIDs":[103,104,116],"metadata":{"skip":false,"skipReason":null},"line":195,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/domain/entities/product_test.dart"},"type":"testStart","time":6159}
{"testID":118,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":6162}
{"test":{"id":119,"name":"Product Entity Tests Product Validation should handle long product names","suiteID":76,"groupIDs":[103,104,116],"metadata":{"skip":false,"skipReason":null},"line":215,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/domain/entities/product_test.dart"},"type":"testStart","time":6163}
{"testID":119,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":6166}
{"test":{"id":120,"name":"Product Entity Tests Product Validation should handle Unicode characters","suiteID":76,"groupIDs":[103,104,116],"metadata":{"skip":false,"skipReason":null},"line":236,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/domain/entities/product_test.dart"},"type":"testStart","time":6167}
{"testID":120,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":6170}
{"group":{"id":121,"suiteID":76,"parentID":104,"name":"Product Entity Tests Product Edge Cases","metadata":{"skip":false,"skipReason":null},"testCount":2,"line":258,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/domain/entities/product_test.dart"},"type":"group","time":6170}
{"test":{"id":122,"name":"Product Entity Tests Product Edge Cases should handle empty strings","suiteID":76,"groupIDs":[103,104,121],"metadata":{"skip":false,"skipReason":null},"line":259,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/domain/entities/product_test.dart"},"type":"testStart","time":6170}
{"testID":122,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":6173}
{"test":{"id":123,"name":"Product Entity Tests Product Edge Cases should handle whitespace-only strings","suiteID":76,"groupIDs":[103,104,121],"metadata":{"skip":false,"skipReason":null},"line":280,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/domain/entities/product_test.dart"},"type":"testStart","time":6173}
{"testID":123,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":6176}
{"suite":{"id":124,"platform":"vm","path":"D:/flutter projects/aquapartner/test/presentation/cubit/dashboard/dashboard_cubit_test.dart"},"type":"suite","time":6193}
{"test":{"id":125,"name":"loading D:/flutter projects/aquapartner/test/presentation/cubit/dashboard/dashboard_cubit_test.dart","suiteID":124,"groupIDs":[],"metadata":{"skip":false,"skipReason":null},"line":null,"column":null,"url":null},"type":"testStart","time":6193}
{"suite":{"id":126,"platform":"vm","path":"D:/flutter projects/aquapartner/test/presentation/screens/login_screen_test.dart"},"type":"suite","time":6379}
{"test":{"id":127,"name":"loading D:/flutter projects/aquapartner/test/presentation/screens/login_screen_test.dart","suiteID":126,"groupIDs":[],"metadata":{"skip":false,"skipReason":null},"line":null,"column":null,"url":null},"type":"testStart","time":6379}
{"testID":102,"result":"success","skipped":false,"hidden":true,"type":"testDone","time":6585}
{"group":{"id":128,"suiteID":101,"parentID":null,"name":"","metadata":{"skip":false,"skipReason":null},"testCount":13,"line":null,"column":null,"url":null},"type":"group","time":6585}
{"group":{"id":129,"suiteID":101,"parentID":128,"name":"Authentication Use Cases Tests","metadata":{"skip":false,"skipReason":null},"testCount":13,"line":11,"column":3,"url":"file:///D:/flutter%20projects/aquapartner/test/domain/usecases/auth_usecases_test.dart"},"type":"group","time":6585}
{"group":{"id":130,"suiteID":101,"parentID":129,"name":"Authentication Use Cases Tests SendOtpUseCase","metadata":{"skip":false,"skipReason":null},"testCount":5,"line":18,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/domain/usecases/auth_usecases_test.dart"},"type":"group","time":6586}
{"test":{"id":131,"name":"Authentication Use Cases Tests SendOtpUseCase should return verification ID when OTP is sent successfully","suiteID":101,"groupIDs":[128,129,130],"metadata":{"skip":false,"skipReason":null},"line":25,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/domain/usecases/auth_usecases_test.dart"},"type":"testStart","time":6586}
{"testID":131,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":6638}
{"test":{"id":132,"name":"Authentication Use Cases Tests SendOtpUseCase should return NetworkFailure when no internet connection","suiteID":101,"groupIDs":[128,129,130],"metadata":{"skip":false,"skipReason":null},"line":44,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/domain/usecases/auth_usecases_test.dart"},"type":"testStart","time":6638}
{"testID":132,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":6643}
{"test":{"id":133,"name":"Authentication Use Cases Tests SendOtpUseCase should return ServerFailure when server error occurs","suiteID":101,"groupIDs":[128,129,130],"metadata":{"skip":false,"skipReason":null},"line":62,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/domain/usecases/auth_usecases_test.dart"},"type":"testStart","time":6643}
{"testID":133,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":6646}
{"test":{"id":134,"name":"Authentication Use Cases Tests SendOtpUseCase should return ValidationFailure for invalid phone number","suiteID":101,"groupIDs":[128,129,130],"metadata":{"skip":false,"skipReason":null},"line":77,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/domain/usecases/auth_usecases_test.dart"},"type":"testStart","time":6647}
{"testID":134,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":6650}
{"test":{"id":135,"name":"Authentication Use Cases Tests SendOtpUseCase should return ValidationFailure when too many requests","suiteID":101,"groupIDs":[128,129,130],"metadata":{"skip":false,"skipReason":null},"line":95,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/domain/usecases/auth_usecases_test.dart"},"type":"testStart","time":6651}
{"testID":135,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":6654}
{"group":{"id":136,"suiteID":101,"parentID":129,"name":"Authentication Use Cases Tests VerifyOtpUseCase","metadata":{"skip":false,"skipReason":null},"testCount":5,"line":111,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/domain/usecases/auth_usecases_test.dart"},"type":"group","time":6654}
{"test":{"id":137,"name":"Authentication Use Cases Tests VerifyOtpUseCase should return true when OTP is verified successfully","suiteID":101,"groupIDs":[128,129,136],"metadata":{"skip":false,"skipReason":null},"line":118,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/domain/usecases/auth_usecases_test.dart"},"type":"testStart","time":6654}
{"testID":137,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":6657}
{"test":{"id":138,"name":"Authentication Use Cases Tests VerifyOtpUseCase should return ValidationFailure when OTP is invalid","suiteID":101,"groupIDs":[128,129,136],"metadata":{"skip":false,"skipReason":null},"line":136,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/domain/usecases/auth_usecases_test.dart"},"type":"testStart","time":6658}
{"testID":138,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":6661}
{"test":{"id":139,"name":"Authentication Use Cases Tests VerifyOtpUseCase should return ValidationFailure when OTP is expired","suiteID":101,"groupIDs":[128,129,136],"metadata":{"skip":false,"skipReason":null},"line":154,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/domain/usecases/auth_usecases_test.dart"},"type":"testStart","time":6661}
{"testID":139,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":6664}
{"test":{"id":140,"name":"Authentication Use Cases Tests VerifyOtpUseCase should return NetworkFailure when no internet connection","suiteID":101,"groupIDs":[128,129,136],"metadata":{"skip":false,"skipReason":null},"line":172,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/domain/usecases/auth_usecases_test.dart"},"type":"testStart","time":6664}
{"testID":140,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":6667}
{"test":{"id":141,"name":"Authentication Use Cases Tests VerifyOtpUseCase should return ValidationFailure when verification ID is invalid","suiteID":101,"groupIDs":[128,129,136],"metadata":{"skip":false,"skipReason":null},"line":193,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/domain/usecases/auth_usecases_test.dart"},"type":"testStart","time":6668}
{"testID":141,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":6671}
{"group":{"id":142,"suiteID":101,"parentID":129,"name":"Authentication Use Cases Tests SignOutUseCase","metadata":{"skip":false,"skipReason":null},"testCount":3,"line":217,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/domain/usecases/auth_usecases_test.dart"},"type":"group","time":6671}
{"test":{"id":143,"name":"Authentication Use Cases Tests SignOutUseCase should complete successfully when sign out succeeds","suiteID":101,"groupIDs":[128,129,142],"metadata":{"skip":false,"skipReason":null},"line":224,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/domain/usecases/auth_usecases_test.dart"},"type":"testStart","time":6671}
{"testID":143,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":6675}
{"test":{"id":144,"name":"Authentication Use Cases Tests SignOutUseCase should handle sign out failure gracefully","suiteID":101,"groupIDs":[128,129,142],"metadata":{"skip":false,"skipReason":null},"line":235,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/domain/usecases/auth_usecases_test.dart"},"type":"testStart","time":6675}
{"testID":144,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":6682}
{"test":{"id":145,"name":"Authentication Use Cases Tests SignOutUseCase should handle network error during sign out","suiteID":101,"groupIDs":[128,129,142],"metadata":{"skip":false,"skipReason":null},"line":246,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/domain/usecases/auth_usecases_test.dart"},"type":"testStart","time":6683}
{"testID":145,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":6686}
{"suite":{"id":146,"platform":"vm","path":"D:/flutter projects/aquapartner/test/presentation/screens/verify_otp_screen_test.dart"},"type":"suite","time":7331}
{"test":{"id":147,"name":"loading D:/flutter projects/aquapartner/test/presentation/screens/verify_otp_screen_test.dart","suiteID":146,"groupIDs":[],"metadata":{"skip":false,"skipReason":null},"line":null,"column":null,"url":null},"type":"testStart","time":7332}
{"testID":109,"result":"success","skipped":false,"hidden":true,"type":"testDone","time":7525}
{"group":{"id":148,"suiteID":108,"parentID":null,"name":"","metadata":{"skip":false,"skipReason":null},"testCount":25,"line":null,"column":null,"url":null},"type":"group","time":7525}
{"test":{"id":149,"name":"(setUpAll)","suiteID":108,"groupIDs":[148],"metadata":{"skip":false,"skipReason":null},"line":75,"column":3,"url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/auth/auth_cubit_test.dart"},"type":"testStart","time":7525}
{"testID":149,"result":"success","skipped":false,"hidden":true,"type":"testDone","time":7549}
{"group":{"id":150,"suiteID":108,"parentID":148,"name":"AuthCubit Tests","metadata":{"skip":false,"skipReason":null},"testCount":25,"line":89,"column":3,"url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/auth/auth_cubit_test.dart"},"type":"group","time":7550}
{"group":{"id":151,"suiteID":108,"parentID":150,"name":"AuthCubit Tests Initial State","metadata":{"skip":false,"skipReason":null},"testCount":1,"line":150,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/auth/auth_cubit_test.dart"},"type":"group","time":7550}
{"test":{"id":152,"name":"AuthCubit Tests Initial State should have initial state as AuthInitial","suiteID":108,"groupIDs":[148,150,151],"metadata":{"skip":false,"skipReason":null},"line":151,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/auth/auth_cubit_test.dart"},"type":"testStart","time":7550}
{"testID":152,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":7593}
{"group":{"id":153,"suiteID":108,"parentID":150,"name":"AuthCubit Tests Send OTP","metadata":{"skip":false,"skipReason":null},"testCount":5,"line":156,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/auth/auth_cubit_test.dart"},"type":"group","time":7593}
{"test":{"id":154,"name":"AuthCubit Tests Send OTP should emit [AuthLoading, OtpSent] when OTP is sent successfully","suiteID":108,"groupIDs":[148,150,153],"metadata":{"skip":false,"skipReason":null},"line":153,"column":8,"url":"package:bloc_test/src/bloc_test.dart","root_line":159,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/auth/auth_cubit_test.dart"},"type":"testStart","time":7593}
{"testID":154,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":7630}
{"test":{"id":155,"name":"AuthCubit Tests Send OTP should emit [AuthLoading, PhoneNumberError] when phone number is invalid","suiteID":108,"groupIDs":[148,150,153],"metadata":{"skip":false,"skipReason":null},"line":153,"column":8,"url":"package:bloc_test/src/bloc_test.dart","root_line":182,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/auth/auth_cubit_test.dart"},"type":"testStart","time":7630}
{"testID":155,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":7636}
{"test":{"id":156,"name":"AuthCubit Tests Send OTP should emit [AuthLoading, AuthSuccess] when auto-verification occurs","suiteID":108,"groupIDs":[148,150,153],"metadata":{"skip":false,"skipReason":null},"line":153,"column":8,"url":"package:bloc_test/src/bloc_test.dart","root_line":203,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/auth/auth_cubit_test.dart"},"type":"testStart","time":7636}
{"testID":156,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":7641}
{"test":{"id":157,"name":"AuthCubit Tests Send OTP should emit [AuthLoading, PhoneNumberError] when network error occurs","suiteID":108,"groupIDs":[148,150,153],"metadata":{"skip":false,"skipReason":null},"line":153,"column":8,"url":"package:bloc_test/src/bloc_test.dart","root_line":230,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/auth/auth_cubit_test.dart"},"type":"testStart","time":7641}
{"testID":157,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":7646}
{"test":{"id":158,"name":"AuthCubit Tests Send OTP should emit [AuthLoading, PhoneNumberError] when too many requests error occurs","suiteID":108,"groupIDs":[148,150,153],"metadata":{"skip":false,"skipReason":null},"line":153,"column":8,"url":"package:bloc_test/src/bloc_test.dart","root_line":250,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/auth/auth_cubit_test.dart"},"type":"testStart","time":7646}
{"testID":158,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":7651}
{"group":{"id":159,"suiteID":108,"parentID":150,"name":"AuthCubit Tests Verify OTP","metadata":{"skip":false,"skipReason":null},"testCount":4,"line":271,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/auth/auth_cubit_test.dart"},"type":"group","time":7651}
{"test":{"id":160,"name":"AuthCubit Tests Verify OTP should emit [AuthLoading, AuthSuccess] when OTP is verified successfully","suiteID":108,"groupIDs":[148,150,159],"metadata":{"skip":false,"skipReason":null},"line":153,"column":8,"url":"package:bloc_test/src/bloc_test.dart","root_line":285,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/auth/auth_cubit_test.dart"},"type":"testStart","time":7651}
{"testID":160,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":7663}
{"test":{"id":161,"name":"AuthCubit Tests Verify OTP should emit [AuthLoading, OtpVerificationError] when OTP is invalid","suiteID":108,"groupIDs":[148,150,159],"metadata":{"skip":false,"skipReason":null},"line":153,"column":8,"url":"package:bloc_test/src/bloc_test.dart","root_line":333,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/auth/auth_cubit_test.dart"},"type":"testStart","time":7664}
{"testID":161,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":7671}
{"test":{"id":162,"name":"AuthCubit Tests Verify OTP should emit [AuthLoading, OtpVerificationError] when verification ID is invalid","suiteID":108,"groupIDs":[148,150,159],"metadata":{"skip":false,"skipReason":null},"line":153,"column":8,"url":"package:bloc_test/src/bloc_test.dart","root_line":354,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/auth/auth_cubit_test.dart"},"type":"testStart","time":7672}
{"testID":162,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":7678}
{"test":{"id":163,"name":"AuthCubit Tests Verify OTP should emit [AuthLoading, OtpVerificationError] when network error occurs during verification","suiteID":108,"groupIDs":[148,150,159],"metadata":{"skip":false,"skipReason":null},"line":153,"column":8,"url":"package:bloc_test/src/bloc_test.dart","root_line":375,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/auth/auth_cubit_test.dart"},"type":"testStart","time":7678}
{"testID":163,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":7683}
{"group":{"id":164,"suiteID":108,"parentID":150,"name":"AuthCubit Tests Sign Out","metadata":{"skip":false,"skipReason":null},"testCount":2,"line":397,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/auth/auth_cubit_test.dart"},"type":"group","time":7683}
{"test":{"id":165,"name":"AuthCubit Tests Sign Out should emit [AuthLoading, AuthInitial] when sign out is successful","suiteID":108,"groupIDs":[148,150,164],"metadata":{"skip":false,"skipReason":null},"line":153,"column":8,"url":"package:bloc_test/src/bloc_test.dart","root_line":398,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/auth/auth_cubit_test.dart"},"type":"testStart","time":7683}
{"testID":165,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":7687}
{"test":{"id":166,"name":"AuthCubit Tests Sign Out should handle sign out failure gracefully","suiteID":108,"groupIDs":[148,150,164],"metadata":{"skip":false,"skipReason":null},"line":153,"column":8,"url":"package:bloc_test/src/bloc_test.dart","root_line":422,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/auth/auth_cubit_test.dart"},"type":"testStart","time":7688}
{"testID":166,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":7696}
{"group":{"id":167,"suiteID":108,"parentID":150,"name":"AuthCubit Tests Reset State","metadata":{"skip":false,"skipReason":null},"testCount":1,"line":448,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/auth/auth_cubit_test.dart"},"type":"group","time":7696}
{"test":{"id":168,"name":"AuthCubit Tests Reset State should reset state to AuthInitial","suiteID":108,"groupIDs":[148,150,167],"metadata":{"skip":false,"skipReason":null},"line":449,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/auth/auth_cubit_test.dart"},"type":"testStart","time":7696}
{"testID":168,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":7700}
{"group":{"id":169,"suiteID":108,"parentID":150,"name":"AuthCubit Tests Check Authentication Status","metadata":{"skip":false,"skipReason":null},"testCount":2,"line":469,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/auth/auth_cubit_test.dart"},"type":"group","time":7701}
{"test":{"id":170,"name":"AuthCubit Tests Check Authentication Status should return true when user is authenticated","suiteID":108,"groupIDs":[148,150,169],"metadata":{"skip":false,"skipReason":null},"line":470,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/auth/auth_cubit_test.dart"},"type":"testStart","time":7701}
{"testID":170,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":7704}
{"test":{"id":171,"name":"AuthCubit Tests Check Authentication Status should return false when user is not authenticated","suiteID":108,"groupIDs":[148,150,169],"metadata":{"skip":false,"skipReason":null},"line":488,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/auth/auth_cubit_test.dart"},"type":"testStart","time":7705}
{"testID":171,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":7709}
{"group":{"id":172,"suiteID":108,"parentID":150,"name":"AuthCubit Tests Edge Cases and Error Recovery","metadata":{"skip":false,"skipReason":null},"testCount":5,"line":496,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/auth/auth_cubit_test.dart"},"type":"group","time":7709}
{"test":{"id":173,"name":"AuthCubit Tests Edge Cases and Error Recovery should handle invalid OTP format with proper validation","suiteID":108,"groupIDs":[148,150,172],"metadata":{"skip":false,"skipReason":null},"line":153,"column":8,"url":"package:bloc_test/src/bloc_test.dart","root_line":497,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/auth/auth_cubit_test.dart"},"type":"testStart","time":7709}
{"testID":173,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":7713}
{"test":{"id":174,"name":"AuthCubit Tests Edge Cases and Error Recovery should handle expired OTP","suiteID":108,"groupIDs":[148,150,172],"metadata":{"skip":false,"skipReason":null},"line":153,"column":8,"url":"package:bloc_test/src/bloc_test.dart","root_line":521,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/auth/auth_cubit_test.dart"},"type":"testStart","time":7714}
{"testID":174,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":7719}
{"test":{"id":175,"name":"AuthCubit Tests Edge Cases and Error Recovery should handle network timeout during OTP verification","suiteID":108,"groupIDs":[148,150,172],"metadata":{"skip":false,"skipReason":null},"line":153,"column":8,"url":"package:bloc_test/src/bloc_test.dart","root_line":547,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/auth/auth_cubit_test.dart"},"type":"testStart","time":7720}
{"testID":175,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":7726}
{"test":{"id":176,"name":"AuthCubit Tests Edge Cases and Error Recovery should retry OTP send after failure","suiteID":108,"groupIDs":[148,150,172],"metadata":{"skip":false,"skipReason":null},"line":153,"column":8,"url":"package:bloc_test/src/bloc_test.dart","root_line":573,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/auth/auth_cubit_test.dart"},"type":"testStart","time":7726}
{"testID":176,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":7731}
{"test":{"id":177,"name":"AuthCubit Tests Edge Cases and Error Recovery should handle multiple rapid OTP requests","suiteID":108,"groupIDs":[148,150,172],"metadata":{"skip":false,"skipReason":null},"line":153,"column":8,"url":"package:bloc_test/src/bloc_test.dart","root_line":592,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/auth/auth_cubit_test.dart"},"type":"testStart","time":7732}
{"testID":177,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":7737}
{"group":{"id":178,"suiteID":108,"parentID":150,"name":"AuthCubit Tests User Session Management","metadata":{"skip":false,"skipReason":null},"testCount":3,"line":616,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/auth/auth_cubit_test.dart"},"type":"group","time":7737}
{"test":{"id":179,"name":"AuthCubit Tests User Session Management should save user data after successful verification","suiteID":108,"groupIDs":[148,150,178],"metadata":{"skip":false,"skipReason":null},"line":153,"column":8,"url":"package:bloc_test/src/bloc_test.dart","root_line":617,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/auth/auth_cubit_test.dart"},"type":"testStart","time":7737}
{"testID":179,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":7744}
{"test":{"id":180,"name":"AuthCubit Tests User Session Management should handle user data persistence failure","suiteID":108,"groupIDs":[148,150,178],"metadata":{"skip":false,"skipReason":null},"line":153,"column":8,"url":"package:bloc_test/src/bloc_test.dart","root_line":667,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/auth/auth_cubit_test.dart"},"type":"testStart","time":7744}
{"testID":180,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":7749}
{"test":{"id":181,"name":"AuthCubit Tests User Session Management should handle session restoration on app restart","suiteID":108,"groupIDs":[148,150,178],"metadata":{"skip":false,"skipReason":null},"line":153,"column":8,"url":"package:bloc_test/src/bloc_test.dart","root_line":694,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/auth/auth_cubit_test.dart"},"type":"testStart","time":7750}
{"testID":181,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":7754}
{"group":{"id":182,"suiteID":108,"parentID":150,"name":"AuthCubit Tests Customer Data Loading","metadata":{"skip":false,"skipReason":null},"testCount":2,"line":719,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/auth/auth_cubit_test.dart"},"type":"group","time":7755}
{"test":{"id":183,"name":"AuthCubit Tests Customer Data Loading should load customer data after successful authentication","suiteID":108,"groupIDs":[148,150,182],"metadata":{"skip":false,"skipReason":null},"line":153,"column":8,"url":"package:bloc_test/src/bloc_test.dart","root_line":720,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/auth/auth_cubit_test.dart"},"type":"testStart","time":7755}
{"testID":183,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":7762}
{"test":{"id":184,"name":"AuthCubit Tests Customer Data Loading should handle customer data loading failure","suiteID":108,"groupIDs":[148,150,182],"metadata":{"skip":false,"skipReason":null},"line":153,"column":8,"url":"package:bloc_test/src/bloc_test.dart","root_line":783,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/auth/auth_cubit_test.dart"},"type":"testStart","time":7763}
{"testID":184,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":7770}
{"test":{"id":185,"name":"(tearDownAll)","suiteID":108,"groupIDs":[148],"metadata":{"skip":false,"skipReason":null},"line":null,"column":null,"url":null},"type":"testStart","time":7770}
{"testID":185,"result":"success","skipped":false,"hidden":true,"type":"testDone","time":7773}
{"testID":125,"result":"success","skipped":false,"hidden":true,"type":"testDone","time":7816}
{"group":{"id":186,"suiteID":124,"parentID":null,"name":"","metadata":{"skip":false,"skipReason":null},"testCount":17,"line":null,"column":null,"url":null},"type":"group","time":7816}
{"group":{"id":187,"suiteID":124,"parentID":186,"name":"DashboardCubit Tests","metadata":{"skip":false,"skipReason":null},"testCount":17,"line":66,"column":3,"url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/dashboard/dashboard_cubit_test.dart"},"type":"group","time":7816}
{"test":{"id":188,"name":"DashboardCubit Tests (setUpAll)","suiteID":124,"groupIDs":[186,187],"metadata":{"skip":false,"skipReason":null},"line":103,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/dashboard/dashboard_cubit_test.dart"},"type":"testStart","time":7816}
{"testID":188,"result":"success","skipped":false,"hidden":true,"type":"testDone","time":7853}
{"group":{"id":189,"suiteID":124,"parentID":187,"name":"DashboardCubit Tests Cubit Creation and Initial State","metadata":{"skip":false,"skipReason":null},"testCount":1,"line":152,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/dashboard/dashboard_cubit_test.dart"},"type":"group","time":7853}
{"test":{"id":190,"name":"DashboardCubit Tests Cubit Creation and Initial State should create cubit without errors","suiteID":124,"groupIDs":[186,187,189],"metadata":{"skip":false,"skipReason":null},"line":153,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/dashboard/dashboard_cubit_test.dart"},"type":"testStart","time":7853}
{"testID":190,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":8139}
{"group":{"id":191,"suiteID":124,"parentID":187,"name":"DashboardCubit Tests Dashboard Data Loading","metadata":{"skip":false,"skipReason":null},"testCount":1,"line":173,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/dashboard/dashboard_cubit_test.dart"},"type":"group","time":8139}
{"test":{"id":192,"name":"DashboardCubit Tests Dashboard Data Loading should handle successful data loading","suiteID":124,"groupIDs":[186,187,191],"metadata":{"skip":false,"skipReason":null},"line":153,"column":8,"url":"package:bloc_test/src/bloc_test.dart","root_line":174,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/dashboard/dashboard_cubit_test.dart"},"type":"testStart","time":8140}
{"suite":{"id":193,"platform":"vm","path":"D:/flutter projects/aquapartner/test/version_checker_test.dart"},"type":"suite","time":8301}
{"test":{"id":194,"name":"loading D:/flutter projects/aquapartner/test/version_checker_test.dart","suiteID":193,"groupIDs":[],"metadata":{"skip":false,"skipReason":null},"line":null,"column":null,"url":null},"type":"testStart","time":8301}
{"testID":127,"result":"success","skipped":false,"hidden":true,"type":"testDone","time":8320}
{"group":{"id":195,"suiteID":126,"parentID":null,"name":"","metadata":{"skip":false,"skipReason":null},"testCount":16,"line":null,"column":null,"url":null},"type":"group","time":8320}
{"group":{"id":196,"suiteID":126,"parentID":195,"name":"LoginScreen Tests","metadata":{"skip":false,"skipReason":null},"testCount":16,"line":55,"column":3,"url":"file:///D:/flutter%20projects/aquapartner/test/presentation/screens/login_screen_test.dart"},"type":"group","time":8320}
{"test":{"id":197,"name":"LoginScreen Tests (setUpAll)","suiteID":126,"groupIDs":[195,196],"metadata":{"skip":false,"skipReason":null},"line":75,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/presentation/screens/login_screen_test.dart"},"type":"testStart","time":8320}
{"testID":197,"result":"success","skipped":false,"hidden":true,"type":"testDone","time":8345}
{"group":{"id":198,"suiteID":126,"parentID":196,"name":"LoginScreen Tests Widget Rendering","metadata":{"skip":false,"skipReason":null},"testCount":3,"line":166,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/presentation/screens/login_screen_test.dart"},"type":"group","time":8345}
{"test":{"id":199,"name":"LoginScreen Tests Widget Rendering should render login screen with all elements","suiteID":126,"groupIDs":[195,196,198],"metadata":{"skip":false,"skipReason":null},"line":175,"column":5,"url":"package:flutter_test/src/widget_tester.dart","root_line":167,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/screens/login_screen_test.dart"},"type":"testStart","time":8345}
{"suite":{"id":200,"platform":"vm","path":"D:/flutter projects/aquapartner/test/widget_test.dart"},"type":"suite","time":8527}
{"test":{"id":201,"name":"loading D:/flutter projects/aquapartner/test/widget_test.dart","suiteID":200,"groupIDs":[],"metadata":{"skip":false,"skipReason":null},"line":null,"column":null,"url":null},"type":"testStart","time":8527}
{"testID":147,"result":"success","skipped":false,"hidden":true,"type":"testDone","time":8681}
{"group":{"id":202,"suiteID":146,"parentID":null,"name":"","metadata":{"skip":false,"skipReason":null},"testCount":9,"line":null,"column":null,"url":null},"type":"group","time":8682}
{"group":{"id":203,"suiteID":146,"parentID":202,"name":"VerifyOtpScreen Tests","metadata":{"skip":false,"skipReason":null},"testCount":9,"line":62,"column":3,"url":"file:///D:/flutter%20projects/aquapartner/test/presentation/screens/verify_otp_screen_test.dart"},"type":"group","time":8682}
{"test":{"id":204,"name":"VerifyOtpScreen Tests (setUpAll)","suiteID":146,"groupIDs":[202,203],"metadata":{"skip":false,"skipReason":null},"line":70,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/presentation/screens/verify_otp_screen_test.dart"},"type":"testStart","time":8682}
{"testID":204,"result":"success","skipped":false,"hidden":true,"type":"testDone","time":8714}
{"group":{"id":205,"suiteID":146,"parentID":203,"name":"VerifyOtpScreen Tests Widget Rendering","metadata":{"skip":false,"skipReason":null},"testCount":3,"line":141,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/presentation/screens/verify_otp_screen_test.dart"},"type":"group","time":8714}
{"test":{"id":206,"name":"VerifyOtpScreen Tests Widget Rendering should render verify OTP screen without crashing","suiteID":146,"groupIDs":[202,203,205],"metadata":{"skip":false,"skipReason":null},"line":175,"column":5,"url":"package:flutter_test/src/widget_tester.dart","root_line":142,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/screens/verify_otp_screen_test.dart"},"type":"testStart","time":8714}
{"testID":192,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":8758}
{"group":{"id":207,"suiteID":124,"parentID":187,"name":"DashboardCubit Tests Sync Operations","metadata":{"skip":false,"skipReason":null},"testCount":1,"line":207,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/dashboard/dashboard_cubit_test.dart"},"type":"group","time":8759}
{"test":{"id":208,"name":"DashboardCubit Tests Sync Operations should handle sync operations","suiteID":124,"groupIDs":[186,187,207],"metadata":{"skip":false,"skipReason":null},"line":153,"column":8,"url":"package:bloc_test/src/bloc_test.dart","root_line":208,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/dashboard/dashboard_cubit_test.dart"},"type":"testStart","time":8759}
{"testID":194,"result":"success","skipped":false,"hidden":true,"type":"testDone","time":9317}
{"group":{"id":209,"suiteID":193,"parentID":null,"name":"","metadata":{"skip":false,"skipReason":null},"testCount":11,"line":null,"column":null,"url":null},"type":"group","time":9317}
{"group":{"id":210,"suiteID":193,"parentID":209,"name":"AppVersion","metadata":{"skip":false,"skipReason":null},"testCount":11,"line":5,"column":3,"url":"file:///D:/flutter%20projects/aquapartner/test/version_checker_test.dart"},"type":"group","time":9318}
{"group":{"id":211,"suiteID":193,"parentID":210,"name":"AppVersion Constructor validation","metadata":{"skip":false,"skipReason":null},"testCount":3,"line":6,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/version_checker_test.dart"},"type":"group","time":9318}
{"test":{"id":212,"name":"AppVersion Constructor validation should create valid version with three components","suiteID":193,"groupIDs":[209,210,211],"metadata":{"skip":false,"skipReason":null},"line":7,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/version_checker_test.dart"},"type":"testStart","time":9318}
{"testID":212,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":9340}
{"test":{"id":213,"name":"AppVersion Constructor validation should create valid version with two components","suiteID":193,"groupIDs":[209,210,211],"metadata":{"skip":false,"skipReason":null},"line":11,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/version_checker_test.dart"},"type":"testStart","time":9341}
{"testID":213,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":9343}
{"test":{"id":214,"name":"AppVersion Constructor validation should throw FormatException for invalid versions","suiteID":193,"groupIDs":[209,210,211],"metadata":{"skip":false,"skipReason":null},"line":15,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/version_checker_test.dart"},"type":"testStart","time":9344}
{"testID":214,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":9352}
{"group":{"id":215,"suiteID":193,"parentID":210,"name":"AppVersion Version comparison","metadata":{"skip":false,"skipReason":null},"testCount":5,"line":25,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/version_checker_test.dart"},"type":"group","time":9352}
{"test":{"id":216,"name":"AppVersion Version comparison should correctly compare major versions","suiteID":193,"groupIDs":[209,210,215],"metadata":{"skip":false,"skipReason":null},"line":26,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/version_checker_test.dart"},"type":"testStart","time":9352}
{"testID":216,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":9355}
{"test":{"id":217,"name":"AppVersion Version comparison should correctly compare minor versions","suiteID":193,"groupIDs":[209,210,215],"metadata":{"skip":false,"skipReason":null},"line":31,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/version_checker_test.dart"},"type":"testStart","time":9355}
{"testID":217,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":9359}
{"test":{"id":218,"name":"AppVersion Version comparison should correctly compare patch versions","suiteID":193,"groupIDs":[209,210,215],"metadata":{"skip":false,"skipReason":null},"line":36,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/version_checker_test.dart"},"type":"testStart","time":9359}
{"testID":218,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":9362}
{"test":{"id":219,"name":"AppVersion Version comparison should handle equal versions","suiteID":193,"groupIDs":[209,210,215],"metadata":{"skip":false,"skipReason":null},"line":41,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/version_checker_test.dart"},"type":"testStart","time":9362}
{"testID":219,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":9365}
{"test":{"id":220,"name":"AppVersion Version comparison should handle missing patch version","suiteID":193,"groupIDs":[209,210,215],"metadata":{"skip":false,"skipReason":null},"line":46,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/version_checker_test.dart"},"type":"testStart","time":9365}
{"testID":208,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":9367}
{"group":{"id":221,"suiteID":124,"parentID":187,"name":"DashboardCubit Tests Error Handling","metadata":{"skip":false,"skipReason":null},"testCount":1,"line":242,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/dashboard/dashboard_cubit_test.dart"},"type":"group","time":9367}
{"test":{"id":222,"name":"DashboardCubit Tests Error Handling should handle errors gracefully","suiteID":124,"groupIDs":[186,187,221],"metadata":{"skip":false,"skipReason":null},"line":153,"column":8,"url":"package:bloc_test/src/bloc_test.dart","root_line":243,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/dashboard/dashboard_cubit_test.dart"},"type":"testStart","time":9367}
{"testID":220,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":9368}
{"group":{"id":223,"suiteID":193,"parentID":210,"name":"AppVersion Object methods","metadata":{"skip":false,"skipReason":null},"testCount":3,"line":53,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/version_checker_test.dart"},"type":"group","time":9368}
{"test":{"id":224,"name":"AppVersion Object methods toString should return version string","suiteID":193,"groupIDs":[209,210,223],"metadata":{"skip":false,"skipReason":null},"line":54,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/version_checker_test.dart"},"type":"testStart","time":9368}
{"testID":224,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":9371}
{"test":{"id":225,"name":"AppVersion Object methods equals should work correctly","suiteID":193,"groupIDs":[209,210,223],"metadata":{"skip":false,"skipReason":null},"line":59,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/version_checker_test.dart"},"type":"testStart","time":9371}
{"testID":225,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":9373}
{"test":{"id":226,"name":"AppVersion Object methods hashCode should be consistent","suiteID":193,"groupIDs":[209,210,223],"metadata":{"skip":false,"skipReason":null},"line":68,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/version_checker_test.dart"},"type":"testStart","time":9373}
{"testID":226,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":9376}
{"testID":199,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":9454}
{"test":{"id":227,"name":"LoginScreen Tests Widget Rendering should pre-fill phone number when provided","suiteID":126,"groupIDs":[195,196,198],"metadata":{"skip":false,"skipReason":null},"line":175,"column":5,"url":"package:flutter_test/src/widget_tester.dart","root_line":187,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/screens/login_screen_test.dart"},"type":"testStart","time":9454}
{"testID":227,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":9515}
{"test":{"id":228,"name":"LoginScreen Tests Widget Rendering should disable button when disconnected","suiteID":126,"groupIDs":[195,196,198],"metadata":{"skip":false,"skipReason":null},"line":175,"column":5,"url":"package:flutter_test/src/widget_tester.dart","root_line":200,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/screens/login_screen_test.dart"},"type":"testStart","time":9515}
{"testID":201,"result":"success","skipped":false,"hidden":true,"type":"testDone","time":9528}
{"group":{"id":229,"suiteID":200,"parentID":null,"name":"","metadata":{"skip":false,"skipReason":null},"testCount":1,"line":null,"column":null,"url":null},"type":"group","time":9529}
{"test":{"id":230,"name":"Counter increments smoke test","suiteID":200,"groupIDs":[229],"metadata":{"skip":false,"skipReason":null},"line":175,"column":5,"url":"package:flutter_test/src/widget_tester.dart","root_line":67,"root_column":3,"root_url":"file:///D:/flutter%20projects/aquapartner/test/widget_test.dart"},"type":"testStart","time":9529}
{"testID":228,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":9715}
{"group":{"id":231,"suiteID":126,"parentID":196,"name":"LoginScreen Tests Form Validation","metadata":{"skip":false,"skipReason":null},"testCount":3,"line":223,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/presentation/screens/login_screen_test.dart"},"type":"group","time":9715}
{"test":{"id":232,"name":"LoginScreen Tests Form Validation should show validation error for empty phone number","suiteID":126,"groupIDs":[195,196,231],"metadata":{"skip":false,"skipReason":null},"line":175,"column":5,"url":"package:flutter_test/src/widget_tester.dart","root_line":224,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/screens/login_screen_test.dart"},"type":"testStart","time":9715}
{"testID":206,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":9790}
{"test":{"id":233,"name":"VerifyOtpScreen Tests Widget Rendering should handle loading state","suiteID":146,"groupIDs":[202,203,205],"metadata":{"skip":false,"skipReason":null},"line":175,"column":5,"url":"package:flutter_test/src/widget_tester.dart","root_line":151,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/screens/verify_otp_screen_test.dart"},"type":"testStart","time":9790}
{"testID":232,"messageType":"print","message":"┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────","type":"print","time":9809}
{"testID":232,"messageType":"print","message":"│ 2025-06-01 08:32:26.521","type":"print","time":9810}
{"testID":232,"messageType":"print","message":"├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄","type":"print","time":9810}
{"testID":232,"messageType":"print","message":"│ 🐛 Need to Validating Form","type":"print","time":9810}
{"testID":232,"messageType":"print","message":"└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────","type":"print","time":9810}
{"testID":232,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":9810}
{"test":{"id":234,"name":"LoginScreen Tests Form Validation should show validation error for invalid phone number","suiteID":126,"groupIDs":[195,196,231],"metadata":{"skip":false,"skipReason":null},"line":175,"column":5,"url":"package:flutter_test/src/widget_tester.dart","root_line":237,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/screens/login_screen_test.dart"},"type":"testStart","time":9811}
{"testID":233,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":9858}
{"test":{"id":235,"name":"VerifyOtpScreen Tests Widget Rendering should handle error states","suiteID":146,"groupIDs":[202,203,205],"metadata":{"skip":false,"skipReason":null},"line":175,"column":5,"url":"package:flutter_test/src/widget_tester.dart","root_line":165,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/screens/verify_otp_screen_test.dart"},"type":"testStart","time":9858}
{"testID":234,"messageType":"print","message":"┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────","type":"print","time":9875}
{"testID":234,"messageType":"print","message":"│ 2025-06-01 08:32:26.602","type":"print","time":9876}
{"testID":234,"messageType":"print","message":"├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄","type":"print","time":9876}
{"testID":234,"messageType":"print","message":"│ 🐛 Need to Validating Form","type":"print","time":9876}
{"testID":234,"messageType":"print","message":"└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────","type":"print","time":9876}
{"testID":234,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":9876}
{"test":{"id":236,"name":"LoginScreen Tests Form Validation should accept valid phone number","suiteID":126,"groupIDs":[195,196,231],"metadata":{"skip":false,"skipReason":null},"line":175,"column":5,"url":"package:flutter_test/src/widget_tester.dart","root_line":251,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/screens/login_screen_test.dart"},"type":"testStart","time":9877}
{"testID":236,"messageType":"print","message":"┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────","type":"print","time":9964}
{"testID":236,"messageType":"print","message":"│ 2025-06-01 08:32:26.665","type":"print","time":9965}
{"testID":236,"messageType":"print","message":"├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄","type":"print","time":9965}
{"testID":236,"messageType":"print","message":"│ 🐛 Need to Validating Form","type":"print","time":9965}
{"testID":236,"messageType":"print","message":"└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────","type":"print","time":9965}
{"testID":236,"messageType":"print","message":"┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────","type":"print","time":9965}
{"testID":236,"messageType":"print","message":"│ 2025-06-01 08:32:26.665","type":"print","time":9966}
{"testID":236,"messageType":"print","message":"├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄","type":"print","time":9966}
{"testID":236,"messageType":"print","message":"│ 🐛 Validation Success, need to send OTP","type":"print","time":9966}
{"testID":236,"messageType":"print","message":"└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────","type":"print","time":9966}
{"testID":236,"messageType":"print","message":"\u001b[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────\u001b[0m","type":"print","time":9967}
{"testID":236,"messageType":"print","message":"\u001b[38;5;12m│ 2025-06-01 08:32:26.685\u001b[0m","type":"print","time":9967}
{"testID":236,"messageType":"print","message":"\u001b[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\u001b[0m","type":"print","time":9967}
{"testID":236,"messageType":"print","message":"\u001b[38;5;12m│ 💡 Auth state in listener: AuthLoading\u001b[0m","type":"print","time":9967}
{"testID":236,"messageType":"print","message":"\u001b[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────\u001b[0m","type":"print","time":9968}
{"testID":236,"messageType":"print","message":"\u001b[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────\u001b[0m","type":"print","time":9968}
{"testID":236,"messageType":"print","message":"\u001b[38;5;12m│ 2025-06-01 08:32:26.686\u001b[0m","type":"print","time":9968}
{"testID":236,"messageType":"print","message":"\u001b[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\u001b[0m","type":"print","time":9968}
{"testID":236,"messageType":"print","message":"\u001b[38;5;12m│ 💡 Auth state in listener: AuthError\u001b[0m","type":"print","time":9968}
{"testID":236,"messageType":"print","message":"\u001b[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────\u001b[0m","type":"print","time":9969}
{"testID":236,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":9969}
{"group":{"id":237,"suiteID":126,"parentID":196,"name":"LoginScreen Tests Authentication Flow","metadata":{"skip":false,"skipReason":null},"testCount":4,"line":269,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/presentation/screens/login_screen_test.dart"},"type":"group","time":9969}
{"test":{"id":238,"name":"LoginScreen Tests Authentication Flow should show loading state when sending OTP","suiteID":126,"groupIDs":[195,196,237],"metadata":{"skip":false,"skipReason":null},"line":175,"column":5,"url":"package:flutter_test/src/widget_tester.dart","root_line":270,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/screens/login_screen_test.dart"},"type":"testStart","time":9969}
{"testID":235,"messageType":"print","message":"\u001b[38;5;196m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────\u001b[0m","type":"print","time":9972}
{"testID":235,"messageType":"print","message":"\u001b[38;5;196m│ 2025-06-01 08:32:26.635\u001b[0m","type":"print","time":9972}
{"testID":235,"messageType":"print","message":"\u001b[38;5;196m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\u001b[0m","type":"print","time":9972}
{"testID":235,"messageType":"print","message":"\u001b[38;5;196m│ ⛔ OTP verification error: Invalid OTP\u001b[0m","type":"print","time":9972}
{"testID":235,"messageType":"print","message":"\u001b[38;5;196m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────\u001b[0m","type":"print","time":9972}
{"testID":235,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":9973}
{"group":{"id":239,"suiteID":146,"parentID":203,"name":"VerifyOtpScreen Tests Basic Functionality","metadata":{"skip":false,"skipReason":null},"testCount":1,"line":182,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/presentation/screens/verify_otp_screen_test.dart"},"type":"group","time":9973}
{"test":{"id":240,"name":"VerifyOtpScreen Tests Basic Functionality should handle cubit method calls","suiteID":146,"groupIDs":[202,203,239],"metadata":{"skip":false,"skipReason":null},"line":175,"column":5,"url":"package:flutter_test/src/widget_tester.dart","root_line":183,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/screens/verify_otp_screen_test.dart"},"type":"testStart","time":9973}
{"testID":222,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":9974}
{"group":{"id":241,"suiteID":124,"parentID":187,"name":"DashboardCubit Tests Auto-Refresh","metadata":{"skip":false,"skipReason":null},"testCount":2,"line":279,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/dashboard/dashboard_cubit_test.dart"},"type":"group","time":9975}
{"test":{"id":242,"name":"DashboardCubit Tests Auto-Refresh should enable and disable auto-refresh","suiteID":124,"groupIDs":[186,187,241],"metadata":{"skip":false,"skipReason":null},"line":280,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/dashboard/dashboard_cubit_test.dart"},"type":"testStart","time":9975}
{"testID":240,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":10029}
{"group":{"id":243,"suiteID":146,"parentID":203,"name":"VerifyOtpScreen Tests State Management","metadata":{"skip":false,"skipReason":null},"testCount":1,"line":201,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/presentation/screens/verify_otp_screen_test.dart"},"type":"group","time":10029}
{"test":{"id":244,"name":"VerifyOtpScreen Tests State Management should handle auth state changes","suiteID":146,"groupIDs":[202,203,243],"metadata":{"skip":false,"skipReason":null},"line":175,"column":5,"url":"package:flutter_test/src/widget_tester.dart","root_line":202,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/screens/verify_otp_screen_test.dart"},"type":"testStart","time":10029}
{"testID":238,"messageType":"print","message":"\u001b[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────\u001b[0m","type":"print","time":10042}
{"testID":238,"messageType":"print","message":"\u001b[38;5;12m│ 2025-06-01 08:32:26.755\u001b[0m","type":"print","time":10042}
{"testID":238,"messageType":"print","message":"\u001b[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\u001b[0m","type":"print","time":10042}
{"testID":238,"messageType":"print","message":"\u001b[38;5;12m│ 💡 Auth state in listener: AuthLoading\u001b[0m","type":"print","time":10042}
{"testID":238,"messageType":"print","message":"\u001b[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────\u001b[0m","type":"print","time":10042}
{"testID":238,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":10042}
{"test":{"id":245,"name":"LoginScreen Tests Authentication Flow should navigate to OTP screen on successful OTP send","suiteID":126,"groupIDs":[195,196,237],"metadata":{"skip":false,"skipReason":null},"line":175,"column":5,"url":"package:flutter_test/src/widget_tester.dart","root_line":288,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/screens/login_screen_test.dart"},"type":"testStart","time":10043}
{"testID":244,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":10083}
{"group":{"id":246,"suiteID":146,"parentID":203,"name":"VerifyOtpScreen Tests Connectivity Handling","metadata":{"skip":false,"skipReason":null},"testCount":1,"line":228,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/presentation/screens/verify_otp_screen_test.dart"},"type":"group","time":10083}
{"test":{"id":247,"name":"VerifyOtpScreen Tests Connectivity Handling should handle connectivity changes","suiteID":146,"groupIDs":[202,203,246],"metadata":{"skip":false,"skipReason":null},"line":175,"column":5,"url":"package:flutter_test/src/widget_tester.dart","root_line":229,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/screens/verify_otp_screen_test.dart"},"type":"testStart","time":10083}
{"testID":245,"messageType":"print","message":"\u001b[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────\u001b[0m","type":"print","time":10128}
{"testID":245,"messageType":"print","message":"\u001b[38;5;12m│ 2025-06-01 08:32:26.827\u001b[0m","type":"print","time":10128}
{"testID":245,"messageType":"print","message":"\u001b[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\u001b[0m","type":"print","time":10128}
{"testID":245,"messageType":"print","message":"\u001b[38;5;12m│ 💡 Auth state in listener: OtpSent\u001b[0m","type":"print","time":10128}
{"testID":245,"messageType":"print","message":"\u001b[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────\u001b[0m","type":"print","time":10129}
{"testID":245,"messageType":"print","message":"\u001b[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────\u001b[0m","type":"print","time":10129}
{"testID":245,"messageType":"print","message":"\u001b[38;5;12m│ 2025-06-01 08:32:26.827\u001b[0m","type":"print","time":10129}
{"testID":245,"messageType":"print","message":"\u001b[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\u001b[0m","type":"print","time":10129}
{"testID":245,"messageType":"print","message":"\u001b[38;5;12m│ 💡 OTP sent, navigating to verification screen\u001b[0m","type":"print","time":10130}
{"testID":245,"messageType":"print","message":"\u001b[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────\u001b[0m","type":"print","time":10130}
{"testID":245,"messageType":"print","message":"\u001b[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────\u001b[0m","type":"print","time":10130}
{"testID":245,"messageType":"print","message":"\u001b[38;5;12m│ 2025-06-01 08:32:26.827\u001b[0m","type":"print","time":10130}
{"testID":245,"messageType":"print","message":"\u001b[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\u001b[0m","type":"print","time":10130}
{"testID":245,"messageType":"print","message":"\u001b[38;5;12m│ 💡 Verification ID: test_verification_id\u001b[0m","type":"print","time":10130}
{"testID":245,"messageType":"print","message":"\u001b[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────\u001b[0m","type":"print","time":10130}
{"testID":245,"messageType":"print","message":"\u001b[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────\u001b[0m","type":"print","time":10130}
{"testID":245,"messageType":"print","message":"\u001b[38;5;12m│ 2025-06-01 08:32:26.827\u001b[0m","type":"print","time":10131}
{"testID":245,"messageType":"print","message":"\u001b[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\u001b[0m","type":"print","time":10131}
{"testID":245,"messageType":"print","message":"\u001b[38;5;12m│ 💡 Phone Number: +919999999999\u001b[0m","type":"print","time":10131}
{"testID":245,"messageType":"print","message":"\u001b[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────\u001b[0m","type":"print","time":10131}
{"testID":245,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":10132}
{"test":{"id":248,"name":"LoginScreen Tests Authentication Flow should show error message on OTP send failure","suiteID":126,"groupIDs":[195,196,237],"metadata":{"skip":false,"skipReason":null},"line":175,"column":5,"url":"package:flutter_test/src/widget_tester.dart","root_line":313,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/screens/login_screen_test.dart"},"type":"testStart","time":10132}
{"testID":247,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":10135}
{"group":{"id":249,"suiteID":146,"parentID":203,"name":"VerifyOtpScreen Tests Analytics Integration","metadata":{"skip":false,"skipReason":null},"testCount":1,"line":250,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/presentation/screens/verify_otp_screen_test.dart"},"type":"group","time":10135}
{"test":{"id":250,"name":"VerifyOtpScreen Tests Analytics Integration should work with analytics service","suiteID":146,"groupIDs":[202,203,249],"metadata":{"skip":false,"skipReason":null},"line":175,"column":5,"url":"package:flutter_test/src/widget_tester.dart","root_line":251,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/screens/verify_otp_screen_test.dart"},"type":"testStart","time":10135}
{"testID":230,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":10157}
{"testID":242,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":10180}
{"test":{"id":251,"name":"DashboardCubit Tests Auto-Refresh should trigger periodic refresh when auto-refresh is enabled","suiteID":124,"groupIDs":[186,187,241],"metadata":{"skip":false,"skipReason":null},"line":153,"column":8,"url":"package:bloc_test/src/bloc_test.dart","root_line":303,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/dashboard/dashboard_cubit_test.dart"},"type":"testStart","time":10180}
{"testID":250,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":10190}
{"group":{"id":252,"suiteID":146,"parentID":203,"name":"VerifyOtpScreen Tests Edge Cases","metadata":{"skip":false,"skipReason":null},"testCount":2,"line":260,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/presentation/screens/verify_otp_screen_test.dart"},"type":"group","time":10190}
{"test":{"id":253,"name":"VerifyOtpScreen Tests Edge Cases should handle different phone number formats","suiteID":146,"groupIDs":[202,203,252],"metadata":{"skip":false,"skipReason":null},"line":175,"column":5,"url":"package:flutter_test/src/widget_tester.dart","root_line":261,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/screens/verify_otp_screen_test.dart"},"type":"testStart","time":10190}
{"testID":248,"messageType":"print","message":"\u001b[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────\u001b[0m","type":"print","time":10224}
{"testID":248,"messageType":"print","message":"\u001b[38;5;12m│ 2025-06-01 08:32:26.921\u001b[0m","type":"print","time":10225}
{"testID":248,"messageType":"print","message":"\u001b[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\u001b[0m","type":"print","time":10225}
{"testID":248,"messageType":"print","message":"\u001b[38;5;12m│ 💡 Auth state in listener: PhoneNumberError\u001b[0m","type":"print","time":10225}
{"testID":248,"messageType":"print","message":"\u001b[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────\u001b[0m","type":"print","time":10225}
{"testID":248,"messageType":"print","message":"\u001b[38;5;196m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────\u001b[0m","type":"print","time":10225}
{"testID":248,"messageType":"print","message":"\u001b[38;5;196m│ 2025-06-01 08:32:26.921\u001b[0m","type":"print","time":10226}
{"testID":248,"messageType":"print","message":"\u001b[38;5;196m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\u001b[0m","type":"print","time":10226}
{"testID":248,"messageType":"print","message":"\u001b[38;5;196m│ ⛔ Phone number error: Network error\u001b[0m","type":"print","time":10226}
{"testID":248,"messageType":"print","message":"\u001b[38;5;196m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────\u001b[0m","type":"print","time":10226}
{"testID":248,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":10227}
{"test":{"id":254,"name":"LoginScreen Tests Authentication Flow should disable button when offline","suiteID":126,"groupIDs":[195,196,237],"metadata":{"skip":false,"skipReason":null},"line":175,"column":5,"url":"package:flutter_test/src/widget_tester.dart","root_line":336,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/screens/login_screen_test.dart"},"type":"testStart","time":10227}
{"testID":253,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":10240}
{"test":{"id":255,"name":"VerifyOtpScreen Tests Edge Cases should handle cubit method calls without errors","suiteID":146,"groupIDs":[202,203,252],"metadata":{"skip":false,"skipReason":null},"line":175,"column":5,"url":"package:flutter_test/src/widget_tester.dart","root_line":286,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/screens/verify_otp_screen_test.dart"},"type":"testStart","time":10240}
{"testID":254,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":10288}
{"group":{"id":256,"suiteID":126,"parentID":196,"name":"LoginScreen Tests Analytics Tracking","metadata":{"skip":false,"skipReason":null},"testCount":4,"line":360,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/presentation/screens/login_screen_test.dart"},"type":"group","time":10288}
{"test":{"id":257,"name":"LoginScreen Tests Analytics Tracking should track screen view on load","suiteID":126,"groupIDs":[195,196,256],"metadata":{"skip":false,"skipReason":null},"line":175,"column":5,"url":"package:flutter_test/src/widget_tester.dart","root_line":361,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/screens/login_screen_test.dart"},"type":"testStart","time":10288}
{"testID":255,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":10289}
{"test":{"id":258,"name":"VerifyOtpScreen Tests (tearDownAll)","suiteID":146,"groupIDs":[202,203],"metadata":{"skip":false,"skipReason":null},"line":89,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/presentation/screens/verify_otp_screen_test.dart"},"type":"testStart","time":10290}
{"testID":258,"result":"success","skipped":false,"hidden":true,"type":"testDone","time":10293}
{"testID":257,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":10338}
{"test":{"id":259,"name":"LoginScreen Tests Analytics Tracking should track send OTP button tap","suiteID":126,"groupIDs":[195,196,256],"metadata":{"skip":false,"skipReason":null},"line":175,"column":5,"url":"package:flutter_test/src/widget_tester.dart","root_line":376,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/screens/login_screen_test.dart"},"type":"testStart","time":10338}
{"testID":259,"messageType":"print","message":"┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────","type":"print","time":10394}
{"testID":259,"messageType":"print","message":"│ 2025-06-01 08:32:27.121","type":"print","time":10394}
{"testID":259,"messageType":"print","message":"├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄","type":"print","time":10394}
{"testID":259,"messageType":"print","message":"│ 🐛 Need to Validating Form","type":"print","time":10394}
{"testID":259,"messageType":"print","message":"└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────","type":"print","time":10395}
{"testID":259,"messageType":"print","message":"┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────","type":"print","time":10395}
{"testID":259,"messageType":"print","message":"│ 2025-06-01 08:32:27.121","type":"print","time":10395}
{"testID":259,"messageType":"print","message":"├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄","type":"print","time":10395}
{"testID":259,"messageType":"print","message":"│ 🐛 Validation Success, need to send OTP","type":"print","time":10395}
{"testID":259,"messageType":"print","message":"└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────","type":"print","time":10395}
{"testID":259,"messageType":"print","message":"\u001b[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────\u001b[0m","type":"print","time":10396}
{"testID":259,"messageType":"print","message":"\u001b[38;5;12m│ 2025-06-01 08:32:27.121\u001b[0m","type":"print","time":10397}
{"testID":259,"messageType":"print","message":"\u001b[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\u001b[0m","type":"print","time":10397}
{"testID":259,"messageType":"print","message":"\u001b[38;5;12m│ 💡 Auth state in listener: AuthLoading\u001b[0m","type":"print","time":10397}
{"testID":259,"messageType":"print","message":"\u001b[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────\u001b[0m","type":"print","time":10397}
{"testID":259,"messageType":"print","message":"\u001b[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────\u001b[0m","type":"print","time":10397}
{"testID":259,"messageType":"print","message":"\u001b[38;5;12m│ 2025-06-01 08:32:27.121\u001b[0m","type":"print","time":10397}
{"testID":259,"messageType":"print","message":"\u001b[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\u001b[0m","type":"print","time":10397}
{"testID":259,"messageType":"print","message":"\u001b[38;5;12m│ 💡 Auth state in listener: AuthError\u001b[0m","type":"print","time":10398}
{"testID":259,"messageType":"print","message":"\u001b[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────\u001b[0m","type":"print","time":10398}
{"testID":259,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":10398}
{"test":{"id":260,"name":"LoginScreen Tests Analytics Tracking should track form validation failure","suiteID":126,"groupIDs":[195,196,256],"metadata":{"skip":false,"skipReason":null},"line":175,"column":5,"url":"package:flutter_test/src/widget_tester.dart","root_line":424,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/screens/login_screen_test.dart"},"type":"testStart","time":10398}
{"testID":260,"messageType":"print","message":"┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────","type":"print","time":10448}
{"testID":260,"messageType":"print","message":"│ 2025-06-01 08:32:27.176","type":"print","time":10448}
{"testID":260,"messageType":"print","message":"├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄","type":"print","time":10448}
{"testID":260,"messageType":"print","message":"│ 🐛 Need to Validating Form","type":"print","time":10448}
{"testID":260,"messageType":"print","message":"└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────","type":"print","time":10448}
{"testID":260,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":10449}
{"test":{"id":261,"name":"LoginScreen Tests Analytics Tracking should track authentication flow start","suiteID":126,"groupIDs":[195,196,256],"metadata":{"skip":false,"skipReason":null},"line":175,"column":5,"url":"package:flutter_test/src/widget_tester.dart","root_line":447,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/screens/login_screen_test.dart"},"type":"testStart","time":10449}
{"testID":261,"messageType":"print","message":"┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────","type":"print","time":10520}
{"testID":261,"messageType":"print","message":"│ 2025-06-01 08:32:27.239","type":"print","time":10521}
{"testID":261,"messageType":"print","message":"├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄","type":"print","time":10521}
{"testID":261,"messageType":"print","message":"│ 🐛 Need to Validating Form","type":"print","time":10521}
{"testID":261,"messageType":"print","message":"└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────","type":"print","time":10521}
{"testID":261,"messageType":"print","message":"┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────","type":"print","time":10521}
{"testID":261,"messageType":"print","message":"│ 2025-06-01 08:32:27.239","type":"print","time":10521}
{"testID":261,"messageType":"print","message":"├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄","type":"print","time":10521}
{"testID":261,"messageType":"print","message":"│ 🐛 Validation Success, need to send OTP","type":"print","time":10521}
{"testID":261,"messageType":"print","message":"└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────","type":"print","time":10521}
{"testID":261,"messageType":"print","message":"\u001b[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────\u001b[0m","type":"print","time":10521}
{"testID":261,"messageType":"print","message":"\u001b[38;5;12m│ 2025-06-01 08:32:27.239\u001b[0m","type":"print","time":10521}
{"testID":261,"messageType":"print","message":"\u001b[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\u001b[0m","type":"print","time":10521}
{"testID":261,"messageType":"print","message":"\u001b[38;5;12m│ 💡 Auth state in listener: AuthLoading\u001b[0m","type":"print","time":10521}
{"testID":261,"messageType":"print","message":"\u001b[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────\u001b[0m","type":"print","time":10521}
{"testID":261,"messageType":"print","message":"\u001b[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────\u001b[0m","type":"print","time":10521}
{"testID":261,"messageType":"print","message":"\u001b[38;5;12m│ 2025-06-01 08:32:27.239\u001b[0m","type":"print","time":10521}
{"testID":261,"messageType":"print","message":"\u001b[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\u001b[0m","type":"print","time":10521}
{"testID":261,"messageType":"print","message":"\u001b[38;5;12m│ 💡 Auth state in listener: AuthError\u001b[0m","type":"print","time":10521}
{"testID":261,"messageType":"print","message":"\u001b[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────\u001b[0m","type":"print","time":10521}
{"testID":261,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":10521}
{"group":{"id":262,"suiteID":126,"parentID":196,"name":"LoginScreen Tests Edge Cases","metadata":{"skip":false,"skipReason":null},"testCount":2,"line":475,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/presentation/screens/login_screen_test.dart"},"type":"group","time":10522}
{"test":{"id":263,"name":"LoginScreen Tests Edge Cases should handle rapid button taps","suiteID":126,"groupIDs":[195,196,262],"metadata":{"skip":false,"skipReason":null},"line":175,"column":5,"url":"package:flutter_test/src/widget_tester.dart","root_line":476,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/screens/login_screen_test.dart"},"type":"testStart","time":10522}
{"testID":263,"messageType":"print","message":"┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────","type":"print","time":10581}
{"testID":263,"messageType":"print","message":"│ 2025-06-01 08:32:27.304","type":"print","time":10581}
{"testID":263,"messageType":"print","message":"├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄","type":"print","time":10582}
{"testID":263,"messageType":"print","message":"│ 🐛 Need to Validating Form","type":"print","time":10582}
{"testID":263,"messageType":"print","message":"└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────","type":"print","time":10582}
{"testID":263,"messageType":"print","message":"┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────","type":"print","time":10582}
{"testID":263,"messageType":"print","message":"│ 2025-06-01 08:32:27.304","type":"print","time":10582}
{"testID":263,"messageType":"print","message":"├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄","type":"print","time":10582}
{"testID":263,"messageType":"print","message":"│ 🐛 Validation Success, need to send OTP","type":"print","time":10583}
{"testID":263,"messageType":"print","message":"└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────","type":"print","time":10583}
{"testID":263,"messageType":"print","message":"\u001b[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────\u001b[0m","type":"print","time":10583}
{"testID":263,"messageType":"print","message":"\u001b[38;5;12m│ 2025-06-01 08:32:27.305\u001b[0m","type":"print","time":10583}
{"testID":263,"messageType":"print","message":"\u001b[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\u001b[0m","type":"print","time":10583}
{"testID":263,"messageType":"print","message":"\u001b[38;5;12m│ 💡 Auth state in listener: AuthLoading\u001b[0m","type":"print","time":10584}
{"testID":263,"messageType":"print","message":"\u001b[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────\u001b[0m","type":"print","time":10584}
{"testID":263,"messageType":"print","message":"\u001b[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────\u001b[0m","type":"print","time":10584}
{"testID":263,"messageType":"print","message":"\u001b[38;5;12m│ 2025-06-01 08:32:27.305\u001b[0m","type":"print","time":10584}
{"testID":263,"messageType":"print","message":"\u001b[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\u001b[0m","type":"print","time":10584}
{"testID":263,"messageType":"print","message":"\u001b[38;5;12m│ 💡 Auth state in listener: AuthError\u001b[0m","type":"print","time":10584}
{"testID":263,"messageType":"print","message":"\u001b[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────\u001b[0m","type":"print","time":10584}
{"testID":263,"messageType":"print","message":"┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────","type":"print","time":10585}
{"testID":263,"messageType":"print","message":"│ 2025-06-01 08:32:27.307","type":"print","time":10585}
{"testID":263,"messageType":"print","message":"├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄","type":"print","time":10585}
{"testID":263,"messageType":"print","message":"│ 🐛 Need to Validating Form","type":"print","time":10585}
{"testID":263,"messageType":"print","message":"└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────","type":"print","time":10585}
{"testID":263,"messageType":"print","message":"┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────","type":"print","time":10585}
{"testID":263,"messageType":"print","message":"│ 2025-06-01 08:32:27.307","type":"print","time":10585}
{"testID":263,"messageType":"print","message":"├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄","type":"print","time":10585}
{"testID":263,"messageType":"print","message":"│ 🐛 Validation Success, need to send OTP","type":"print","time":10586}
{"testID":263,"messageType":"print","message":"└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────","type":"print","time":10586}
{"testID":263,"messageType":"print","message":"\u001b[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────\u001b[0m","type":"print","time":10586}
{"testID":263,"messageType":"print","message":"\u001b[38;5;12m│ 2025-06-01 08:32:27.307\u001b[0m","type":"print","time":10586}
{"testID":263,"messageType":"print","message":"\u001b[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\u001b[0m","type":"print","time":10586}
{"testID":263,"messageType":"print","message":"\u001b[38;5;12m│ 💡 Auth state in listener: AuthLoading\u001b[0m","type":"print","time":10586}
{"testID":263,"messageType":"print","message":"\u001b[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────\u001b[0m","type":"print","time":10586}
{"testID":263,"messageType":"print","message":"\u001b[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────\u001b[0m","type":"print","time":10586}
{"testID":263,"messageType":"print","message":"\u001b[38;5;12m│ 2025-06-01 08:32:27.307\u001b[0m","type":"print","time":10587}
{"testID":263,"messageType":"print","message":"\u001b[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\u001b[0m","type":"print","time":10587}
{"testID":263,"messageType":"print","message":"\u001b[38;5;12m│ 💡 Auth state in listener: AuthError\u001b[0m","type":"print","time":10587}
{"testID":263,"messageType":"print","message":"\u001b[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────\u001b[0m","type":"print","time":10587}
{"testID":263,"messageType":"print","message":"┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────","type":"print","time":10587}
{"testID":263,"messageType":"print","message":"│ 2025-06-01 08:32:27.309","type":"print","time":10587}
{"testID":263,"messageType":"print","message":"├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄","type":"print","time":10587}
{"testID":263,"messageType":"print","message":"│ 🐛 Need to Validating Form","type":"print","time":10588}
{"testID":263,"messageType":"print","message":"└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────","type":"print","time":10588}
{"testID":263,"messageType":"print","message":"┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────","type":"print","time":10588}
{"testID":263,"messageType":"print","message":"│ 2025-06-01 08:32:27.309","type":"print","time":10588}
{"testID":263,"messageType":"print","message":"├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄","type":"print","time":10588}
{"testID":263,"messageType":"print","message":"│ 🐛 Validation Success, need to send OTP","type":"print","time":10588}
{"testID":263,"messageType":"print","message":"└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────","type":"print","time":10588}
{"testID":263,"messageType":"print","message":"\u001b[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────\u001b[0m","type":"print","time":10589}
{"testID":263,"messageType":"print","message":"\u001b[38;5;12m│ 2025-06-01 08:32:27.309\u001b[0m","type":"print","time":10589}
{"testID":263,"messageType":"print","message":"\u001b[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\u001b[0m","type":"print","time":10589}
{"testID":263,"messageType":"print","message":"\u001b[38;5;12m│ 💡 Auth state in listener: AuthLoading\u001b[0m","type":"print","time":10589}
{"testID":263,"messageType":"print","message":"\u001b[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────\u001b[0m","type":"print","time":10589}
{"testID":263,"messageType":"print","message":"\u001b[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────\u001b[0m","type":"print","time":10589}
{"testID":263,"messageType":"print","message":"\u001b[38;5;12m│ 2025-06-01 08:32:27.310\u001b[0m","type":"print","time":10590}
{"testID":263,"messageType":"print","message":"\u001b[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\u001b[0m","type":"print","time":10590}
{"testID":263,"messageType":"print","message":"\u001b[38;5;12m│ 💡 Auth state in listener: AuthError\u001b[0m","type":"print","time":10590}
{"testID":263,"messageType":"print","message":"\u001b[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────\u001b[0m","type":"print","time":10590}
{"testID":263,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":10590}
{"test":{"id":264,"name":"LoginScreen Tests Edge Cases should handle very long phone numbers","suiteID":126,"groupIDs":[195,196,262],"metadata":{"skip":false,"skipReason":null},"line":175,"column":5,"url":"package:flutter_test/src/widget_tester.dart","root_line":502,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/screens/login_screen_test.dart"},"type":"testStart","time":10590}
{"testID":264,"messageType":"print","message":"┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────","type":"print","time":10657}
{"testID":264,"messageType":"print","message":"│ 2025-06-01 08:32:27.378","type":"print","time":10657}
{"testID":264,"messageType":"print","message":"├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄","type":"print","time":10657}
{"testID":264,"messageType":"print","message":"│ 🐛 Need to Validating Form","type":"print","time":10657}
{"testID":264,"messageType":"print","message":"└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────","type":"print","time":10657}
{"testID":264,"messageType":"print","message":"┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────","type":"print","time":10658}
{"testID":264,"messageType":"print","message":"│ 2025-06-01 08:32:27.378","type":"print","time":10658}
{"testID":264,"messageType":"print","message":"├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄","type":"print","time":10658}
{"testID":264,"messageType":"print","message":"│ 🐛 Validation Success, need to send OTP","type":"print","time":10658}
{"testID":264,"messageType":"print","message":"└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────","type":"print","time":10658}
{"testID":264,"messageType":"print","message":"\u001b[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────\u001b[0m","type":"print","time":10658}
{"testID":264,"messageType":"print","message":"\u001b[38;5;12m│ 2025-06-01 08:32:27.378\u001b[0m","type":"print","time":10658}
{"testID":264,"messageType":"print","message":"\u001b[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\u001b[0m","type":"print","time":10659}
{"testID":264,"messageType":"print","message":"\u001b[38;5;12m│ 💡 Auth state in listener: AuthLoading\u001b[0m","type":"print","time":10659}
{"testID":264,"messageType":"print","message":"\u001b[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────\u001b[0m","type":"print","time":10659}
{"testID":264,"messageType":"print","message":"\u001b[38;5;12m┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────\u001b[0m","type":"print","time":10659}
{"testID":264,"messageType":"print","message":"\u001b[38;5;12m│ 2025-06-01 08:32:27.378\u001b[0m","type":"print","time":10659}
{"testID":264,"messageType":"print","message":"\u001b[38;5;12m├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄\u001b[0m","type":"print","time":10659}
{"testID":264,"messageType":"print","message":"\u001b[38;5;12m│ 💡 Auth state in listener: AuthError\u001b[0m","type":"print","time":10659}
{"testID":264,"messageType":"print","message":"\u001b[38;5;12m└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────\u001b[0m","type":"print","time":10659}
{"testID":264,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":10660}
{"test":{"id":265,"name":"LoginScreen Tests (tearDownAll)","suiteID":126,"groupIDs":[195,196],"metadata":{"skip":false,"skipReason":null},"line":null,"column":null,"url":null},"type":"testStart","time":10660}
{"testID":265,"result":"success","skipped":false,"hidden":true,"type":"testDone","time":10661}
{"testID":251,"error":"Expected: a value greater than <1>\n  Actual: <1>\n   Which: is not a value greater than <1>\nUnexpected number of calls\n","stackTrace":"package:matcher                                                     expect\npackage:mocktail/src/mocktail.dart 595:5                            VerificationResult.called\ntest\\presentation\\cubit\\dashboard\\dashboard_cubit_test.dart 330:13  main.<fn>.<fn>.<fn>\npackage:bloc_test/src/bloc_test.dart 228:21                         testBloc.<fn>\n===== asynchronous gap ===========================\ndart:async                                                          _Completer.completeError\npackage:bloc_test/src/bloc_test.dart 255:43                         _runZonedGuarded.<fn>\n===== asynchronous gap ===========================\ndart:async                                                          _CustomZone.registerBinaryCallback\npackage:bloc_test/src/bloc_test.dart 252:5                          _runZonedGuarded.<fn>\ndart:async                                                          runZonedGuarded\npackage:bloc_test/src/bloc_test.dart 251:3                          _runZonedGuarded\npackage:bloc_test/src/bloc_test.dart 198:11                         testBloc\npackage:bloc_test/src/bloc_test.dart 156:13                         blocTest.<fn>\n","isFailure":true,"type":"error","time":11051}
{"testID":251,"result":"failure","skipped":false,"hidden":false,"type":"testDone","time":11052}
{"group":{"id":266,"suiteID":124,"parentID":187,"name":"DashboardCubit Tests Pull-to-Refresh Functionality","metadata":{"skip":false,"skipReason":null},"testCount":2,"line":335,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/dashboard/dashboard_cubit_test.dart"},"type":"group","time":11052}
{"test":{"id":267,"name":"DashboardCubit Tests Pull-to-Refresh Functionality should handle pull-to-refresh without showing loading state","suiteID":124,"groupIDs":[186,187,266],"metadata":{"skip":false,"skipReason":null},"line":153,"column":8,"url":"package:bloc_test/src/bloc_test.dart","root_line":336,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/dashboard/dashboard_cubit_test.dart"},"type":"testStart","time":11052}
{"testID":267,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":11659}
{"test":{"id":268,"name":"DashboardCubit Tests Pull-to-Refresh Functionality should handle offline pull-to-refresh by loading from cache","suiteID":124,"groupIDs":[186,187,266],"metadata":{"skip":false,"skipReason":null},"line":153,"column":8,"url":"package:bloc_test/src/bloc_test.dart","root_line":364,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/dashboard/dashboard_cubit_test.dart"},"type":"testStart","time":11660}
{"testID":268,"error":"Expected: a value greater than <1>\n  Actual: <1>\n   Which: is not a value greater than <1>\nUnexpected number of calls\n","stackTrace":"package:matcher                                                     expect\npackage:mocktail/src/mocktail.dart 595:5                            VerificationResult.called\ntest\\presentation\\cubit\\dashboard\\dashboard_cubit_test.dart 394:13  main.<fn>.<fn>.<fn>\npackage:bloc_test/src/bloc_test.dart 228:21                         testBloc.<fn>\n===== asynchronous gap ===========================\ndart:async                                                          _Completer.completeError\npackage:bloc_test/src/bloc_test.dart 255:43                         _runZonedGuarded.<fn>\n===== asynchronous gap ===========================\ndart:async                                                          _CustomZone.registerBinaryCallback\npackage:bloc_test/src/bloc_test.dart 252:5                          _runZonedGuarded.<fn>\ndart:async                                                          runZonedGuarded\npackage:bloc_test/src/bloc_test.dart 251:3                          _runZonedGuarded\npackage:bloc_test/src/bloc_test.dart 198:11                         testBloc\npackage:bloc_test/src/bloc_test.dart 156:13                         blocTest.<fn>\n","isFailure":true,"type":"error","time":12271}
{"testID":268,"result":"failure","skipped":false,"hidden":false,"type":"testDone","time":12271}
{"group":{"id":269,"suiteID":124,"parentID":187,"name":"DashboardCubit Tests One-Way Synchronization","metadata":{"skip":false,"skipReason":null},"testCount":2,"line":400,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/dashboard/dashboard_cubit_test.dart"},"type":"group","time":12272}
{"test":{"id":270,"name":"DashboardCubit Tests One-Way Synchronization should clear local data and replace with server data during sync","suiteID":124,"groupIDs":[186,187,269],"metadata":{"skip":false,"skipReason":null},"line":153,"column":8,"url":"package:bloc_test/src/bloc_test.dart","root_line":401,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/dashboard/dashboard_cubit_test.dart"},"type":"testStart","time":12272}
{"testID":270,"error":"Expected: [\n            <<Instance of 'DashboardLoaded'>>,\n            <<Instance of 'DashboardLoaded'>>,\n            <<Instance of 'DashboardLoaded'>>\n          ]\n  Actual: [\n            DashboardLoaded:DashboardLoaded(DashboardEntity(test_customer_123, SalesEntity({}), PaymentsEntity({}), [], 0.0, [], LiquidationEntity(0.0, []), MyFarmersEntity([], 0)), false, 2025-06-01 08:32:29.015578, false, null),\n            DashboardLoaded:DashboardLoaded(DashboardEntity(test_customer_123, SalesEntity({}), PaymentsEntity({}), [], 0.0, [], LiquidationEntity(0.0, []), MyFarmersEntity([], 0)), true, 2025-06-01 08:32:29.015578, false, null)\n          ]\n   Which: at location [2] is [\n            DashboardLoaded:DashboardLoaded(DashboardEntity(test_customer_123, SalesEntity({}), PaymentsEntity({}), [], 0.0, [], LiquidationEntity(0.0, []), MyFarmersEntity([], 0)), false, 2025-06-01 08:32:29.015578, false, null),\n            DashboardLoaded:DashboardLoaded(DashboardEntity(test_customer_123, SalesEntity({}), PaymentsEntity({}), [], 0.0, [], LiquidationEntity(0.0, []), MyFarmersEntity([], 0)), true, 2025-06-01 08:32:29.015578, false, null)\n          ] which shorter than expected\n","stackTrace":"package:matcher                              expect\npackage:bloc_test/src/bloc_test.dart 218:16  testBloc.<fn>\n===== asynchronous gap ===========================\ndart:async                                   _Completer.completeError\npackage:bloc_test/src/bloc_test.dart 255:43  _runZonedGuarded.<fn>\n===== asynchronous gap ===========================\ndart:async                                   _CustomZone.registerBinaryCallback\npackage:bloc_test/src/bloc_test.dart 252:5   _runZonedGuarded.<fn>\ndart:async                                   runZonedGuarded\npackage:bloc_test/src/bloc_test.dart 251:3   _runZonedGuarded\npackage:bloc_test/src/bloc_test.dart 198:11  testBloc\npackage:bloc_test/src/bloc_test.dart 156:13  blocTest.<fn>\n","isFailure":true,"type":"error","time":12885}
{"testID":270,"result":"failure","skipped":false,"hidden":false,"type":"testDone","time":12886}
{"test":{"id":271,"name":"DashboardCubit Tests One-Way Synchronization should handle sync failure and maintain existing data","suiteID":124,"groupIDs":[186,187,269],"metadata":{"skip":false,"skipReason":null},"line":153,"column":8,"url":"package:bloc_test/src/bloc_test.dart","root_line":448,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/dashboard/dashboard_cubit_test.dart"},"type":"testStart","time":12886}
{"testID":271,"error":"Expected: [<<Instance of 'DashboardLoaded'>>, <<Instance of 'DashboardLoaded'>>]\n  Actual: [\n            DashboardLoaded:DashboardLoaded(DashboardEntity(test_customer_123, SalesEntity({}), PaymentsEntity({}), [], 0.0, [], LiquidationEntity(0.0, []), MyFarmersEntity([], 0)), false, 2025-06-01 08:32:29.629319, false, null),\n            DashboardLoaded:DashboardLoaded(DashboardEntity(test_customer_123, SalesEntity({}), PaymentsEntity({}), [], 0.0, [], LiquidationEntity(0.0, []), MyFarmersEntity([], 0)), true, 2025-06-01 08:32:29.629319, false, null),\n            DashboardLoaded:DashboardLoaded(DashboardEntity(test_customer_123, SalesEntity({}), PaymentsEntity({}), [], 0.0, [], LiquidationEntity(0.0, []), MyFarmersEntity([], 0)), false, 2025-06-01 08:32:29.629319, true, Sync failed: try again later)\n          ]\n   Which: at location [2] is [\n            DashboardLoaded:DashboardLoaded(DashboardEntity(test_customer_123, SalesEntity({}), PaymentsEntity({}), [], 0.0, [], LiquidationEntity(0.0, []), MyFarmersEntity([], 0)), false, 2025-06-01 08:32:29.629319, false, null),\n            DashboardLoaded:DashboardLoaded(DashboardEntity(test_customer_123, SalesEntity({}), PaymentsEntity({}), [], 0.0, [], LiquidationEntity(0.0, []), MyFarmersEntity([], 0)), true, 2025-06-01 08:32:29.629319, false, null),\n            DashboardLoaded:DashboardLoaded(DashboardEntity(test_customer_123, SalesEntity({}), PaymentsEntity({}), [], 0.0, [], LiquidationEntity(0.0, []), MyFarmersEntity([], 0)), false, 2025-06-01 08:32:29.629319, true, Sync failed: try again later)\n          ] which longer than expected\n","stackTrace":"package:matcher                              expect\npackage:bloc_test/src/bloc_test.dart 218:16  testBloc.<fn>\n===== asynchronous gap ===========================\ndart:async                                   _Completer.completeError\npackage:bloc_test/src/bloc_test.dart 255:43  _runZonedGuarded.<fn>\n===== asynchronous gap ===========================\ndart:async                                   _CustomZone.registerBinaryCallback\npackage:bloc_test/src/bloc_test.dart 252:5   _runZonedGuarded.<fn>\ndart:async                                   runZonedGuarded\npackage:bloc_test/src/bloc_test.dart 251:3   _runZonedGuarded\npackage:bloc_test/src/bloc_test.dart 198:11  testBloc\npackage:bloc_test/src/bloc_test.dart 156:13  blocTest.<fn>\n","isFailure":true,"type":"error","time":13497}
{"testID":271,"result":"failure","skipped":false,"hidden":false,"type":"testDone","time":13497}
{"group":{"id":272,"suiteID":124,"parentID":187,"name":"DashboardCubit Tests Analytics Tracking","metadata":{"skip":false,"skipReason":null},"testCount":2,"line":483,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/dashboard/dashboard_cubit_test.dart"},"type":"group","time":13497}
{"test":{"id":273,"name":"DashboardCubit Tests Analytics Tracking should set current user in analytics during prefetch","suiteID":124,"groupIDs":[186,187,272],"metadata":{"skip":false,"skipReason":null},"line":484,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/dashboard/dashboard_cubit_test.dart"},"type":"testStart","time":13497}
{"testID":273,"error":"Used on a non-mocktail object","stackTrace":"package:matcher                                                     fail\npackage:mocktail/src/mocktail.dart 522:7                            _makeVerify.<fn>\ntest\\presentation\\cubit\\dashboard\\dashboard_cubit_test.dart 500:15  main.<fn>.<fn>.<fn>\n","isFailure":true,"type":"error","time":13704}
{"testID":273,"result":"failure","skipped":false,"hidden":false,"type":"testDone","time":13705}
{"test":{"id":274,"name":"DashboardCubit Tests Analytics Tracking should track dashboard load events","suiteID":124,"groupIDs":[186,187,272],"metadata":{"skip":false,"skipReason":null},"line":153,"column":8,"url":"package:bloc_test/src/bloc_test.dart","root_line":507,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/dashboard/dashboard_cubit_test.dart"},"type":"testStart","time":13705}
{"testID":274,"error":"Expected: [<<Instance of 'DashboardLoaded'>>, <<Instance of 'DashboardLoaded'>>]\n  Actual: [\n            DashboardLoaded:DashboardLoaded(DashboardEntity(test_customer_123, SalesEntity({}), PaymentsEntity({}), [], 0.0, [], LiquidationEntity(0.0, []), MyFarmersEntity([], 0)), false, 2025-06-01 08:32:30.447557, false, null)\n          ]\n   Which: at location [1] is [\n            DashboardLoaded:DashboardLoaded(DashboardEntity(test_customer_123, SalesEntity({}), PaymentsEntity({}), [], 0.0, [], LiquidationEntity(0.0, []), MyFarmersEntity([], 0)), false, 2025-06-01 08:32:30.447557, false, null)\n          ] which shorter than expected\n","stackTrace":"package:matcher                              expect\npackage:bloc_test/src/bloc_test.dart 218:16  testBloc.<fn>\n===== asynchronous gap ===========================\ndart:async                                   _Completer.completeError\npackage:bloc_test/src/bloc_test.dart 255:43  _runZonedGuarded.<fn>\n===== asynchronous gap ===========================\ndart:async                                   _CustomZone.registerBinaryCallback\npackage:bloc_test/src/bloc_test.dart 252:5   _runZonedGuarded.<fn>\ndart:async                                   runZonedGuarded\npackage:bloc_test/src/bloc_test.dart 251:3   _runZonedGuarded\npackage:bloc_test/src/bloc_test.dart 198:11  testBloc\npackage:bloc_test/src/bloc_test.dart 156:13  blocTest.<fn>\n","isFailure":true,"type":"error","time":14313}
{"testID":274,"result":"failure","skipped":false,"hidden":false,"type":"testDone","time":14313}
{"group":{"id":275,"suiteID":124,"parentID":187,"name":"DashboardCubit Tests Connectivity Handling","metadata":{"skip":false,"skipReason":null},"testCount":2,"line":539,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/dashboard/dashboard_cubit_test.dart"},"type":"group","time":14313}
{"test":{"id":276,"name":"DashboardCubit Tests Connectivity Handling should show offline indicator when connectivity is lost","suiteID":124,"groupIDs":[186,187,275],"metadata":{"skip":false,"skipReason":null},"line":153,"column":8,"url":"package:bloc_test/src/bloc_test.dart","root_line":540,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/dashboard/dashboard_cubit_test.dart"},"type":"testStart","time":14313}
{"testID":276,"error":"Expected: [<<Instance of 'DashboardLoaded'>>, <satisfies function>]\n  Actual: [\n            DashboardLoaded:DashboardLoaded(DashboardEntity(test_customer_123, SalesEntity({}), PaymentsEntity({}), [], 0.0, [], LiquidationEntity(0.0, []), MyFarmersEntity([], 0)), false, 2025-06-01 08:32:31.055646, true, Offline mode - using cached data)\n          ]\n   Which: at location [1] is [\n            DashboardLoaded:DashboardLoaded(DashboardEntity(test_customer_123, SalesEntity({}), PaymentsEntity({}), [], 0.0, [], LiquidationEntity(0.0, []), MyFarmersEntity([], 0)), false, 2025-06-01 08:32:31.055646, true, Offline mode - using cached data)\n          ] which shorter than expected\n","stackTrace":"package:matcher                              expect\npackage:bloc_test/src/bloc_test.dart 218:16  testBloc.<fn>\n===== asynchronous gap ===========================\ndart:async                                   _Completer.completeError\npackage:bloc_test/src/bloc_test.dart 255:43  _runZonedGuarded.<fn>\n===== asynchronous gap ===========================\ndart:async                                   _CustomZone.registerBinaryCallback\npackage:bloc_test/src/bloc_test.dart 252:5   _runZonedGuarded.<fn>\ndart:async                                   runZonedGuarded\npackage:bloc_test/src/bloc_test.dart 251:3   _runZonedGuarded\npackage:bloc_test/src/bloc_test.dart 198:11  testBloc\npackage:bloc_test/src/bloc_test.dart 156:13  blocTest.<fn>\n","isFailure":true,"type":"error","time":14924}
{"testID":276,"result":"failure","skipped":false,"hidden":false,"type":"testDone","time":14924}
{"test":{"id":277,"name":"DashboardCubit Tests Connectivity Handling should prevent sync when offline","suiteID":124,"groupIDs":[186,187,275],"metadata":{"skip":false,"skipReason":null},"line":153,"column":8,"url":"package:bloc_test/src/bloc_test.dart","root_line":579,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/dashboard/dashboard_cubit_test.dart"},"type":"testStart","time":14925}
{"testID":277,"error":"Expected: [<<Instance of 'DashboardLoaded'>>]\n  Actual: [\n            DashboardLoaded:DashboardLoaded(DashboardEntity(test_customer_123, SalesEntity({}), PaymentsEntity({}), [], 0.0, [], LiquidationEntity(0.0, []), MyFarmersEntity([], 0)), false, 2025-06-01 08:32:31.668135, true, Offline mode - using cached data),\n            DashboardLoaded:DashboardLoaded(DashboardEntity(test_customer_123, SalesEntity({}), PaymentsEntity({}), [], 0.0, [], LiquidationEntity(0.0, []), MyFarmersEntity([], 0)), false, 2025-06-01 08:32:31.668135, true, No internet connection)\n          ]\n   Which: at location [1] is [\n            DashboardLoaded:DashboardLoaded(DashboardEntity(test_customer_123, SalesEntity({}), PaymentsEntity({}), [], 0.0, [], LiquidationEntity(0.0, []), MyFarmersEntity([], 0)), false, 2025-06-01 08:32:31.668135, true, Offline mode - using cached data),\n            DashboardLoaded:DashboardLoaded(DashboardEntity(test_customer_123, SalesEntity({}), PaymentsEntity({}), [], 0.0, [], LiquidationEntity(0.0, []), MyFarmersEntity([], 0)), false, 2025-06-01 08:32:31.668135, true, No internet connection)\n          ] which longer than expected\n","stackTrace":"package:matcher                              expect\npackage:bloc_test/src/bloc_test.dart 218:16  testBloc.<fn>\n===== asynchronous gap ===========================\ndart:async                                   _Completer.completeError\npackage:bloc_test/src/bloc_test.dart 255:43  _runZonedGuarded.<fn>\n===== asynchronous gap ===========================\ndart:async                                   _CustomZone.registerBinaryCallback\npackage:bloc_test/src/bloc_test.dart 252:5   _runZonedGuarded.<fn>\ndart:async                                   runZonedGuarded\npackage:bloc_test/src/bloc_test.dart 251:3   _runZonedGuarded\npackage:bloc_test/src/bloc_test.dart 198:11  testBloc\npackage:bloc_test/src/bloc_test.dart 156:13  blocTest.<fn>\n","isFailure":true,"type":"error","time":15535}
{"testID":277,"result":"failure","skipped":false,"hidden":false,"type":"testDone","time":15535}
{"group":{"id":278,"suiteID":124,"parentID":187,"name":"DashboardCubit Tests Edge Cases and Error Recovery","metadata":{"skip":false,"skipReason":null},"testCount":3,"line":613,"column":5,"url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/dashboard/dashboard_cubit_test.dart"},"type":"group","time":15535}
{"test":{"id":279,"name":"DashboardCubit Tests Edge Cases and Error Recovery should handle auth service failure gracefully","suiteID":124,"groupIDs":[186,187,278],"metadata":{"skip":false,"skipReason":null},"line":153,"column":8,"url":"package:bloc_test/src/bloc_test.dart","root_line":614,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/dashboard/dashboard_cubit_test.dart"},"type":"testStart","time":15535}
{"testID":279,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":16244}
{"test":{"id":280,"name":"DashboardCubit Tests Edge Cases and Error Recovery should handle multiple rapid sync requests","suiteID":124,"groupIDs":[186,187,278],"metadata":{"skip":false,"skipReason":null},"line":153,"column":8,"url":"package:bloc_test/src/bloc_test.dart","root_line":646,"root_column":7,"root_url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/dashboard/dashboard_cubit_test.dart"},"type":"testStart","time":16244}
{"testID":280,"error":"Expected: a value less than or equal to <1>\n  Actual: <3>\n   Which: is not a value less than or equal to <1>\nUnexpected number of calls\n","stackTrace":"package:matcher                                                     expect\npackage:mocktail/src/mocktail.dart 595:5                            VerificationResult.called\ntest\\presentation\\cubit\\dashboard\\dashboard_cubit_test.dart 680:13  main.<fn>.<fn>.<fn>\npackage:bloc_test/src/bloc_test.dart 228:21                         testBloc.<fn>\n===== asynchronous gap ===========================\ndart:async                                                          _Completer.completeError\npackage:bloc_test/src/bloc_test.dart 255:43                         _runZonedGuarded.<fn>\n===== asynchronous gap ===========================\ndart:async                                                          _CustomZone.registerBinaryCallback\npackage:bloc_test/src/bloc_test.dart 252:5                          _runZonedGuarded.<fn>\ndart:async                                                          runZonedGuarded\npackage:bloc_test/src/bloc_test.dart 251:3                          _runZonedGuarded\npackage:bloc_test/src/bloc_test.dart 198:11                         testBloc\npackage:bloc_test/src/bloc_test.dart 156:13                         blocTest.<fn>\n","isFailure":true,"type":"error","time":16857}
{"testID":280,"result":"failure","skipped":false,"hidden":false,"type":"testDone","time":16857}
{"test":{"id":281,"name":"DashboardCubit Tests Edge Cases and Error Recovery should properly dispose resources on close","suiteID":124,"groupIDs":[186,187,278],"metadata":{"skip":false,"skipReason":null},"line":684,"column":7,"url":"file:///D:/flutter%20projects/aquapartner/test/presentation/cubit/dashboard/dashboard_cubit_test.dart"},"type":"testStart","time":16857}
{"testID":281,"result":"success","skipped":false,"hidden":false,"type":"testDone","time":17063}
{"test":{"id":282,"name":"DashboardCubit Tests (tearDownAll)","suiteID":124,"groupIDs":[186,187],"metadata":{"skip":false,"skipReason":null},"line":null,"column":null,"url":null},"type":"testStart","time":17063}
{"testID":282,"result":"success","skipped":false,"hidden":true,"type":"testDone","time":17065}
{"success":false,"type":"done","time":18405}
