import 'package:aqua_ui/aqua_ui.dart';
import 'package:flutter/material.dart';

class LoadingWidget extends StatelessWidget {
  final String? message;
  const LoadingWidget({super.key, required this.message});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: ac<PERSON>hiteColor,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: acPrimaryBlue),
            SizedBox(height: 16),
            AquaText.subheadline(message ?? 'Loading...'),
          ],
        ),
      ),
    );
  }
}
