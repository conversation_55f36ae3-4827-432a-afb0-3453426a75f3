import 'package:flutter/material.dart';

import '../../domain/entities/product.dart';
import 'product_card_widget.dart';

class ProductCardListViewWidget extends StatelessWidget {
  final String brand;
  final List<Product> products;
  final bool isScrollable;
  const ProductCardListViewWidget({
    required this.products,
    required this.brand,
    required this.isScrollable,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      physics:
          isScrollable
              ? AlwaysScrollableScrollPhysics()
              : NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: products.length,
      separatorBuilder: (context, index) => SizedBox(height: 16),
      itemBuilder: (context, index) {
        final product = products[index];
        return ProductCardWidget(
          brand: brand,
          product: product.productName,
          image: product.productImage,
          description: product.tagLine,
          badge: product.productTag,
          onTap: () async {
            /*   await Navigator.pushNamed(
              context,
              AppRouter.productDetails,
              arguments: ProductDetailsArguments(
                productName: product.productName,
                productImage: product.productImage,
                htmlContent: product.content,
              ),
            ); */
            // Optionally refresh data when returning
          }, // You might want to make this dynamic based on product data
        );
      },
    );
  }
}
