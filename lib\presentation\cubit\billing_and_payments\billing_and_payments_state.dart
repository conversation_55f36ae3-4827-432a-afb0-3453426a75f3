import 'package:equatable/equatable.dart';

class BillingAndPaymentsPageState extends Equatable {
  final List<String> pages;
  final String selectedPage;

  const BillingAndPaymentsPageState({
    required this.pages,
    required this.selectedPage,
  });

  factory BillingAndPaymentsPageState.initial() =>
      const BillingAndPaymentsPageState(
        pages: ['Sales Orders', 'Dues', 'Invoices', 'Payments', 'Credit Notes'],
        selectedPage: 'Sales Orders',
      );

  BillingAndPaymentsPageState copyWith({
    List<String>? pages,
    String? selectedPage,
  }) {
    return BillingAndPaymentsPageState(
      pages: pages ?? this.pages,
      selectedPage: selectedPage ?? this.selectedPage,
    );
  }

  @override
  List<Object?> get props => [pages, selectedPage];
}
