import 'package:equatable/equatable.dart';
import '../../../../domain/entities/user.dart';

abstract class AuthState extends Equatable {
  @override
  List<Object?> get props => [];
}

class AuthInitial extends AuthState {}

class AuthLoading extends AuthState {}

class AuthFailure extends AuthState {
  final String message;
  AuthFailure(this.message);
}

class OtpSent extends AuthState {
  final String verificationId;
  final String phoneNumber;

  OtpSent({required this.verificationId, required this.phoneNumber});

  @override
  List<Object?> get props => [verificationId, phoneNumber];
}

class AuthSuccess extends AuthState {
  final User user;

  AuthSuccess({required this.user});

  @override
  List<Object?> get props => [user];
}

class AuthError extends AuthState {
  final String message;

  AuthError({required this.message});

  @override
  List<Object?> get props => [message];
}

class OtpVerificationError extends AuthError {
  OtpVerificationError({required String message}) : super(message: message);
}

class PhoneNumberError extends AuthError {
  PhoneNumberError({required String message}) : super(message: message);
}

class Unauthenticated extends AuthState {}

class SyncingUser extends AuthState {}

class SyncSuccess extends AuthState {}

class SyncError extends AuthState {
  final String message;

  SyncError({required this.message});

  @override
  List<Object?> get props => [message];
}

class AuthSyncing extends AuthState {}
