#!/usr/bin/env dart

import 'dart:io';
import 'dart:convert';

/// Script to set up test environment for payment integration testing
/// 
/// Usage: dart scripts/setup_test_environment.dart [environment]
/// 
/// This script:
/// 1. Validates test environment configuration
/// 2. Sets up test data files
/// 3. Configures mock responses
/// 4. Verifies API connectivity
/// 5. Generates test reports

void main(List<String> args) async {
  final environment = args.isNotEmpty ? args[0] : 'local';
  
  print('🔧 Setting up Payment Test Environment');
  print('📍 Environment: $environment');
  print('⏰ Started at: ${DateTime.now()}');
  print('=' * 60);
  
  final setup = TestEnvironmentSetup(environment);
  await setup.run();
}

class TestEnvironmentSetup {
  final String environment;
  
  TestEnvironmentSetup(this.environment);
  
  Future<void> run() async {
    try {
      await _validateEnvironment();
      await _setupTestData();
      await _configureMocks();
      await _verifyConnectivity();
      await _generateTestConfig();
      
      _printSuccess();
    } catch (e) {
      _printError('Setup failed: $e');
      exit(1);
    }
  }
  
  Future<void> _validateEnvironment() async {
    _printStep('🔍 Validating Environment');
    
    // Check Flutter installation
    final flutterResult = await Process.run('flutter', ['--version']);
    if (flutterResult.exitCode != 0) {
      throw Exception('Flutter not found. Please install Flutter.');
    }
    print('  ✅ Flutter installation verified');
    
    // Check Dart installation
    final dartResult = await Process.run('dart', ['--version']);
    if (dartResult.exitCode != 0) {
      throw Exception('Dart not found. Please install Dart.');
    }
    print('  ✅ Dart installation verified');
    
    // Check test dependencies
    final pubspecFile = File('pubspec.yaml');
    if (!pubspecFile.existsSync()) {
      throw Exception('pubspec.yaml not found. Run from project root.');
    }
    
    final pubspecContent = await pubspecFile.readAsString();
    final requiredDeps = ['flutter_test', 'mockito', 'http', 'integration_test'];
    
    for (final dep in requiredDeps) {
      if (!pubspecContent.contains(dep)) {
        print('  ⚠️  Missing dependency: $dep');
      } else {
        print('  ✅ Dependency found: $dep');
      }
    }
    
    // Validate environment-specific settings
    await _validateEnvironmentSettings();
  }
  
  Future<void> _validateEnvironmentSettings() async {
    switch (environment.toLowerCase()) {
      case 'production':
        print('  ⚠️  Production environment - use with caution');
        print('  📝 Base URL: https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/api');
        break;
      case 'staging':
        print('  ✅ Staging environment - safe for testing');
        print('  📝 Base URL: https://staging-aquapartner.azurewebsites.net/api');
        break;
      case 'local':
        print('  ✅ Local environment - using mock server');
        print('  📝 Base URL: http://localhost:8080/api');
        break;
      default:
        throw Exception('Unknown environment: $environment');
    }
  }
  
  Future<void> _setupTestData() async {
    _printStep('📊 Setting up Test Data');
    
    // Create test data directory
    final testDataDir = Directory('test/data');
    if (!testDataDir.existsSync()) {
      await testDataDir.create(recursive: true);
      print('  ✅ Created test data directory');
    }
    
    // Generate test customers
    await _generateTestCustomers();
    
    // Generate test invoices
    await _generateTestInvoices();
    
    // Generate test payment scenarios
    await _generateTestScenarios();
    
    print('  ✅ Test data files generated');
  }
  
  Future<void> _generateTestCustomers() async {
    final customers = [
      {
        'id': 'TEST-CUST-001',
        'name': 'Test Customer One',
        'email': '<EMAIL>',
        'phone': '+919876543210',
        'address': 'Test Address 1, Test City',
        'created_at': DateTime.now().toIso8601String(),
      },
      {
        'id': 'TEST-CUST-002',
        'name': 'Test Customer Two',
        'email': '<EMAIL>',
        'phone': '+919876543211',
        'address': 'Test Address 2, Test City',
        'created_at': DateTime.now().toIso8601String(),
      },
      {
        'id': 'TEST-CUST-003',
        'name': 'Very Long Customer Name That Might Cause Display Issues',
        'email': '<EMAIL>',
        'phone': '******-123-4567',
        'address': 'Very Long Address That Might Also Cause Display Issues, Very Long City Name',
        'created_at': DateTime.now().toIso8601String(),
      },
    ];
    
    final file = File('test/data/test_customers.json');
    await file.writeAsString(jsonEncode(customers));
  }
  
  Future<void> _generateTestInvoices() async {
    final invoices = [
      {
        'number': 'INV-TEST-001',
        'customer_id': 'TEST-CUST-001',
        'amount': 100.0,
        'currency': 'INR',
        'description': 'Test Invoice 001 - Small Amount',
        'due_date': DateTime.now().add(Duration(days: 30)).toIso8601String(),
        'status': 'overdue',
        'created_at': DateTime.now().subtract(Duration(days: 45)).toIso8601String(),
      },
      {
        'number': 'INV-TEST-002',
        'customer_id': 'TEST-CUST-002',
        'amount': 250.50,
        'currency': 'INR',
        'description': 'Test Invoice 002 - Decimal Amount',
        'due_date': DateTime.now().add(Duration(days: 15)).toIso8601String(),
        'status': 'pending',
        'created_at': DateTime.now().subtract(Duration(days: 30)).toIso8601String(),
      },
      {
        'number': 'INV-TEST-003',
        'customer_id': 'TEST-CUST-003',
        'amount': 9999.99,
        'currency': 'INR',
        'description': 'Test Invoice 003 - Large Amount',
        'due_date': DateTime.now().add(Duration(days: 7)).toIso8601String(),
        'status': 'overdue',
        'created_at': DateTime.now().subtract(Duration(days: 60)).toIso8601String(),
      },
      {
        'number': 'INV-2024/Q1_001',
        'customer_id': 'TEST-CUST-001',
        'amount': 500.0,
        'currency': 'INR',
        'description': 'Test Invoice with Special Characters in Number',
        'due_date': DateTime.now().add(Duration(days: 20)).toIso8601String(),
        'status': 'pending',
        'created_at': DateTime.now().subtract(Duration(days: 10)).toIso8601String(),
      },
    ];
    
    final file = File('test/data/test_invoices.json');
    await file.writeAsString(jsonEncode(invoices));
  }
  
  Future<void> _generateTestScenarios() async {
    final scenarios = [
      {
        'name': 'Successful Payment',
        'description': 'Complete payment flow with successful outcome',
        'customer_id': 'TEST-CUST-001',
        'invoice_number': 'INV-TEST-001',
        'amount': 100.0,
        'expected_outcome': 'success',
        'test_card': 'success',
        'steps': [
          'Tap Pay Now button',
          'Wait for WebView to load',
          'Fill payment form with success card',
          'Submit payment',
          'Verify success message',
        ],
      },
      {
        'name': 'Failed Payment',
        'description': 'Payment flow with card decline',
        'customer_id': 'TEST-CUST-002',
        'invoice_number': 'INV-TEST-002',
        'amount': 250.50,
        'expected_outcome': 'failure',
        'test_card': 'failure',
        'steps': [
          'Tap Pay Now button',
          'Wait for WebView to load',
          'Fill payment form with failure card',
          'Submit payment',
          'Verify error message',
        ],
      },
      {
        'name': 'Cancelled Payment',
        'description': 'User cancels payment in WebView',
        'customer_id': 'TEST-CUST-003',
        'invoice_number': 'INV-TEST-003',
        'amount': 9999.99,
        'expected_outcome': 'cancelled',
        'test_card': 'success',
        'steps': [
          'Tap Pay Now button',
          'Wait for WebView to load',
          'Tap back button or close WebView',
          'Verify cancellation handling',
        ],
      },
    ];
    
    final file = File('test/data/test_scenarios.json');
    await file.writeAsString(jsonEncode(scenarios));
  }
  
  Future<void> _configureMocks() async {
    _printStep('🎭 Configuring Mock Responses');
    
    // Create mock configuration directory
    final mockDir = Directory('test/mocks');
    if (!mockDir.existsSync()) {
      await mockDir.create(recursive: true);
      print('  ✅ Created mock configuration directory');
    }
    
    // Generate mock response files
    await _generateMockResponses();
    
    print('  ✅ Mock responses configured');
  }
  
  Future<void> _generateMockResponses() async {
    final mockResponses = {
      'health_success': {
        'timestamp': DateTime.now().toIso8601String(),
        'service': 'zoho-payment-api',
        'version': '1.0.0',
        'status': 'healthy',
        'checks': {
          'database': 'ok',
          'zoho_api': 'ok',
          'redis': 'ok',
        }
      },
      'session_success': {
        'success': true,
        'message': 'Payment session created successfully',
        'data': {
          'payment_session_id': 'mock_session_123',
          'amount': '100.0',
          'currency': 'INR',
          'description': 'Mock payment session',
          'invoice_number': 'INV-MOCK-001',
          'created_time': DateTime.now().millisecondsSinceEpoch ~/ 1000,
          'transaction_id': 'mock_txn_123',
          'expires_in': '3600',
          'payment_url': 'https://payments.zoho.in/checkout/mock_session_123',
        }
      },
      'status_pending': {
        'success': true,
        'message': 'Payment status retrieved',
        'data': {
          'transaction_id': 'mock_txn_123',
          'payment_session_id': 'mock_session_123',
          'status': 'pending',
          'amount': 100.0,
          'currency': 'INR',
          'description': 'Mock payment session',
          'invoice_number': 'INV-MOCK-001',
          'customer_id': 'TEST-CUST-001',
        }
      },
      'status_success': {
        'success': true,
        'message': 'Payment status retrieved',
        'data': {
          'transaction_id': 'mock_txn_123',
          'payment_session_id': 'mock_session_123',
          'status': 'completed',
          'amount': 100.0,
          'currency': 'INR',
          'description': 'Mock payment session',
          'invoice_number': 'INV-MOCK-001',
          'customer_id': 'TEST-CUST-001',
          'payment_id': 'zoho_payment_123',
          'payment_method': 'credit_card',
          'payment_completed_time': DateTime.now().toIso8601String(),
        }
      },
      'validation_error': {
        'success': false,
        'message': 'Validation failed',
        'error': 'VALIDATION_ERROR',
        'details': {
          'amount': ['Amount must be greater than 0'],
          'currency': ['Only INR is supported'],
        }
      },
    };
    
    for (final entry in mockResponses.entries) {
      final file = File('test/mocks/${entry.key}.json');
      await file.writeAsString(jsonEncode(entry.value));
    }
  }
  
  Future<void> _verifyConnectivity() async {
    _printStep('🌐 Verifying Connectivity');
    
    if (environment == 'local') {
      print('  ℹ️  Local environment - skipping connectivity check');
      return;
    }
    
    // Test API connectivity (basic check)
    try {
      final result = await Process.run('ping', ['-c', '1', 'google.com']);
      if (result.exitCode == 0) {
        print('  ✅ Internet connectivity verified');
      } else {
        print('  ⚠️  Internet connectivity issues detected');
      }
    } catch (e) {
      print('  ⚠️  Could not verify internet connectivity: $e');
    }
  }
  
  Future<void> _generateTestConfig() async {
    _printStep('⚙️  Generating Test Configuration');
    
    final config = {
      'environment': environment,
      'base_url': _getBaseUrl(),
      'test_data_path': 'test/data',
      'mock_data_path': 'test/mocks',
      'generated_at': DateTime.now().toIso8601String(),
      'test_settings': {
        'timeout_seconds': 30,
        'polling_interval_seconds': 3,
        'max_retries': 3,
        'enable_logging': true,
      },
      'test_cards': {
        'success': '****************',
        'failure': '****************',
        'expired': '****************',
      },
    };
    
    final file = File('test/test_config.json');
    await file.writeAsString(jsonEncode(config));
    
    print('  ✅ Test configuration generated: test/test_config.json');
  }
  
  String _getBaseUrl() {
    switch (environment.toLowerCase()) {
      case 'production':
        return 'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/api';
      case 'staging':
        return 'https://staging-aquapartner.azurewebsites.net/api';
      case 'local':
        return 'http://localhost:8080/api';
      default:
        return 'http://localhost:8080/api';
    }
  }
  
  void _printStep(String step) {
    print('\n$step');
    print('-' * step.length);
  }
  
  void _printSuccess() {
    print('\n' + '=' * 60);
    print('🎉 Test Environment Setup Complete!');
    print('=' * 60);
    print('Environment: $environment');
    print('Base URL: ${_getBaseUrl()}');
    print('Test Data: test/data/');
    print('Mock Data: test/mocks/');
    print('Configuration: test/test_config.json');
    print('\n📋 Next Steps:');
    print('1. Run unit tests: flutter test');
    print('2. Run API tests: dart scripts/test_payment_api.dart $environment');
    print('3. Run integration tests: flutter test integration_test/');
    print('4. Start manual testing using the generated test data');
    print('\n⏰ Completed at: ${DateTime.now()}');
  }
  
  void _printError(String error) {
    print('\n❌ $error');
    print('\n🔧 Troubleshooting:');
    print('1. Ensure Flutter and Dart are installed');
    print('2. Run "flutter pub get" to install dependencies');
    print('3. Check internet connectivity for remote environments');
    print('4. Verify you are running from the project root directory');
  }
}
