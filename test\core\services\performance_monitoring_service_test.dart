import 'package:aquapartner/core/services/performance_monitoring_service.dart';
import 'package:aquapartner/core/utils/logger.dart';
import 'package:firebase_performance/firebase_performance.dart';
import 'package:flutter_test/flutter_test.dart';

// Custom logger mock that captures error calls
class TestAppLogger implements AppLogger {
  List<String> errorMessages = [];
  List<Object> errorObjects = [];
  List<String> infoMessages = [];
  List<String> debugMessages = [];
  List<String> warningMessages = [];

  @override
  void e(String message, [dynamic error, StackTrace? stackTrace]) {
    errorMessages.add(message);
    if (error != null) errorObjects.add(error);
  }

  @override
  void i(String message) {
    infoMessages.add(message);
  }

  @override
  void d(String message) {
    debugMessages.add(message);
  }

  @override
  void w(String message) {
    warningMessages.add(message);
  }

  @override
  void enableFirebaseVerboseLogging() {
    // No-op for testing
  }
}

// Custom FirebasePerformance implementation for testing
class TestFirebasePerformance implements FirebasePerformance {
  bool _isEnabled = true;
  Map<String, TestTrace> traces = {};
  Map<String, TestHttpMetric> httpMetrics = {};

  @override
  Future<bool> isPerformanceCollectionEnabled() async {
    return _isEnabled;
  }

  @override
  Future<void> setPerformanceCollectionEnabled(bool enabled) async {
    _isEnabled = enabled;
  }

  @override
  Trace newTrace(String name) {
    final trace = TestTrace();
    traces[name] = trace;
    return trace;
  }

  @override
  HttpMetric newHttpMetric(String url, HttpMethod httpMethod) {
    final metric = TestHttpMetric();
    httpMetrics['$httpMethod $url'] = metric;
    return metric;
  }

  // Required to implement the interface
  @override
  dynamic noSuchMethod(Invocation invocation) => super.noSuchMethod(invocation);
}

// Custom Trace implementation for testing
class TestTrace implements Trace {
  bool _started = false;
  bool _stopped = false;
  Map<String, int> _counters = {};
  Map<String, String> _attributes = {};

  @override
  Future<void> start() async {
    _started = true;
  }

  @override
  Future<void> stop() async {
    _stopped = true;
  }

  @override
  void incrementMetric(String name, int value) {
    _counters[name] = (_counters[name] ?? 0) + value;
  }

  @override
  void putAttribute(String name, String value) {
    _attributes[name] = value;
  }

  bool get isStarted => _started;
  bool get isStopped => _stopped;
  Map<String, int> get counters => _counters;
  Map<String, String> get attributes => _attributes;

  // Required to implement the interface
  @override
  dynamic noSuchMethod(Invocation invocation) => super.noSuchMethod(invocation);
}

// Custom HttpMetric implementation for testing
class TestHttpMetric implements HttpMetric {
  bool _started = false;
  bool _stopped = false;
  int? _httpResponseCode;
  int? _requestPayloadSize;
  int? _responsePayloadSize;
  String? _responseContentType;

  @override
  Future<void> start() async {
    _started = true;
  }

  @override
  Future<void> stop() async {
    _stopped = true;
  }

  @override
  set httpResponseCode(int? value) {
    _httpResponseCode = value;
  }

  @override
  set requestPayloadSize(int? value) {
    _requestPayloadSize = value;
  }

  @override
  set responsePayloadSize(int? value) {
    _responsePayloadSize = value;
  }

  @override
  set responseContentType(String? value) {
    _responseContentType = value;
  }

  bool get isStarted => _started;
  bool get isStopped => _stopped;
  int? get httpResponseCode => _httpResponseCode;
  int? get requestPayloadSize => _requestPayloadSize;
  int? get responsePayloadSize => _responsePayloadSize;
  String? get responseContentType => _responseContentType;

  // Required to implement the interface
  @override
  dynamic noSuchMethod(Invocation invocation) => super.noSuchMethod(invocation);
}

void main() {
  late TestFirebasePerformance testFirebasePerformance;
  late TestAppLogger testLogger;
  late PerformanceMonitoringService service;

  setUp(() {
    testFirebasePerformance = TestFirebasePerformance();
    testLogger = TestAppLogger();
    service = PerformanceMonitoringService(testFirebasePerformance, testLogger);
  });

  group('PerformanceMonitoringService', () {
    test('service should be initialized properly', () {
      // Just verify the service was created successfully
      expect(service, isNotNull);
    });

    test('startTrace should create and start a new trace', () {
      // Act
      final result = service.startTrace('test_trace');

      // Assert
      expect(result, isTrue);
      expect(testFirebasePerformance.traces.containsKey('test_trace'), isTrue);
      expect(testFirebasePerformance.traces['test_trace']!.isStarted, isTrue);
    });

    test('stopTrace should stop the trace and return duration', () async {
      // Arrange - start a trace first
      service.startTrace('test_trace');

      // Act
      final result = await service.stopTrace('test_trace');

      // Assert
      expect(result, isNotNull);
      expect(testFirebasePerformance.traces['test_trace']!.isStopped, isTrue);
    });

    test('incrementMetric should increment a counter in a trace', () {
      // Arrange - start a trace first
      service.startTrace('test_trace');

      // Act
      final result = service.incrementMetric('test_trace', 'test_counter', 5);

      // Assert
      expect(result, isTrue);
      expect(
        testFirebasePerformance.traces['test_trace']!.counters['test_counter'],
        equals(5),
      );
    });

    test('startHttpMetric should create and start a new HTTP metric', () {
      // Act
      final result = service.startHttpMetric(
        'https://example.com/api',
        HttpMethod.Get,
      );

      // Assert
      expect(result, isNotNull);
      expect(
        testFirebasePerformance.httpMetrics.containsKey(
          'HttpMethod.Get https://example.com/api',
        ),
        isTrue,
      );
      expect(
        testFirebasePerformance
            .httpMetrics['HttpMethod.Get https://example.com/api']!
            .isStarted,
        isTrue,
      );
    });

    test(
      'stopHttpMetric should set attributes and stop the HTTP metric',
      () async {
        // Arrange
        final metric = service.startHttpMetric(
          'https://example.com/api',
          HttpMethod.Get,
        );

        // Act
        await service.stopHttpMetric(
          metric,
          responseCode: 200,
          requestSize: 1024,
          responseSize: 2048,
          contentType: 'application/json',
        );

        // Assert
        final testMetric =
            testFirebasePerformance
                .httpMetrics['HttpMethod.Get https://example.com/api']!;
        expect(testMetric.isStopped, isTrue);
        expect(testMetric.httpResponseCode, equals(200));
        expect(testMetric.requestPayloadSize, equals(1024));
        expect(testMetric.responsePayloadSize, equals(2048));
        expect(testMetric.responseContentType, equals('application/json'));
      },
    );
  });
}
