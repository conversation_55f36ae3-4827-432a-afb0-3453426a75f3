import 'package:dartz/dartz.dart';
import '../../../core/error/failures.dart';
import '../../../core/utils/logger.dart';
import '../../repositories/account_statement_repository.dart';

class InvalidateAccountStatementCacheUseCase {
  final AccountStatementRepository repository;
  final AppLogger logger;

  InvalidateAccountStatementCacheUseCase({
    required this.repository,
    required this.logger,
  });

  Future<Either<Failure, bool>> call(String customerId) async {
    logger.i('InvalidateAccountStatementCacheUseCase called for customer: $customerId');
    return repository.invalidateCache(customerId);
  }
}
