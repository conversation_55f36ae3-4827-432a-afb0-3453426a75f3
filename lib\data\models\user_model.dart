import 'package:mongo_dart/mongo_dart.dart';
import 'package:objectbox/objectbox.dart';
import '../../domain/entities/user.dart';

// In data/models/user_model.dart
@Entity()
class UserModel {
  @Id()
  int id;
  final String phoneNumber;
  final bool isVerified;

  // Make sure these fields exist
  final String? mongoId;
  final bool needsSync;

  @Property(type: PropertyType.date)
  final DateTime createdAt;
  @Property(type: PropertyType.date)
  final DateTime updatedAt;

  UserModel({
    this.id = 0,
    required this.phoneNumber,
    required this.isVerified,
    this.mongoId,
    this.needsSync = true, // Default to true for new users
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : this.createdAt = createdAt ?? DateTime.now(),
       this.updatedAt = updatedAt ?? DateTime.now();

  // Add or fix the copyWith method
  UserModel copyWith({
    int? id,
    String? phoneNumber,
    bool? isVerified,
    String? mongoId =
        'KEEP_CURRENT', // Use sentinel value to distinguish null from not provided
    bool? needsSync,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserModel(
      id: id ?? this.id,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      isVerified: isVerified ?? this.isVerified,
      mongoId: mongoId == 'KEEP_CURRENT' ? this.mongoId : mongoId,
      needsSync: needsSync ?? this.needsSync,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Make sure you have proper fromEntity and toEntity methods
  factory UserModel.fromEntity(User user) {
    return UserModel(
      id: user.id,
      phoneNumber: user.phoneNumber,
      isVerified: user.isVerified,
      mongoId: user.mongoId,
      needsSync: user.needsSync,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    );
  }

  User toEntity() {
    return User(
      id: id,
      phoneNumber: phoneNumber,
      isVerified: isVerified,
      mongoId: mongoId,
      needsSync: needsSync,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }

  // Add toJson and fromJson methods for MongoDB
  Map<String, dynamic> toJson() {
    return {
      if (mongoId != null) '_id': ObjectId.parse(mongoId!),
      'phoneNumber': phoneNumber,
      'isVerified': isVerified,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': DateTime.now().toIso8601String(),
    };
  }

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      phoneNumber: json['phoneNumber'] as String,
      isVerified: json['isVerified'] as bool,
      mongoId: json['_id'].toString(),
      needsSync: false, // If it's from MongoDB, it's synced
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }
}
