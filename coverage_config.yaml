# Coverage configuration for AquaPartner Flutter application
# This file configures code coverage collection and reporting

# Coverage collection settings
coverage:
  # Minimum coverage thresholds
  thresholds:
    line: 80 # Minimum line coverage percentage
    branch: 75 # Minimum branch coverage percentage
    function: 85 # Minimum function coverage percentage

  # Files and directories to include in coverage
  include:
    - lib/**/*.dart
    - lib/core/**/*.dart
    - lib/domain/**/*.dart
    - lib/presentation/**/*.dart
    - lib/data/**/*.dart

  # Files and directories to exclude from coverage
  exclude:
    - lib/main.dart
    - lib/**/*.g.dart # Generated files
    - lib/**/*.freezed.dart # Freezed generated files
    - lib/**/*.mocks.dart # Mock files
    - lib/injection_container.dart
    - lib/core/constants/**/*.dart
    - lib/core/routes/**/*.dart
    - lib/generated/**/*.dart
    - lib/l10n/**/*.dart # Localization files
    - test/**/*.dart # Test files themselves

  # Specific patterns to exclude
  exclude_patterns:
    - "*.g.dart"
    - "*.freezed.dart"
    - "*.mocks.dart"
    - "**/generated/**"
    - "**/l10n/**"

# Test configuration
test:
  # Test discovery patterns
  patterns:
    - "test/**/*_test.dart"
    - "test/**/test_*.dart"

  # Test execution settings
  timeout: 300s # 5 minutes timeout for tests
  concurrency: 4 # Number of concurrent test processes

  # Test categories
  categories:
    unit:
      - "test/core/**/*_test.dart"
      - "test/domain/**/*_test.dart"
      - "test/data/**/*_test.dart"

    widget:
      - "test/presentation/widgets/**/*_test.dart"

    screen:
      - "test/presentation/screens/**/*_test.dart"

    cubit:
      - "test/presentation/cubit/**/*_test.dart"

    integration:
      - "integration_test/**/*_test.dart"

    performance:
      - "test/performance/**/*_test.dart"

# Reporting configuration
reporting:
  # Output formats
  formats:
    - lcov # LCOV format for CI/CD
    - html # HTML report for local viewing (Windows compatible)
    - json # JSON format for programmatic access
    - cobertura # Cobertura XML for some CI systems

  # Output directories
  output:
    lcov: "coverage/lcov.info"
    html: "coverage/html/"
    json: "coverage/coverage.json"
    cobertura: "coverage/cobertura.xml"

  # Windows-specific configuration
  windows:
    use_dart_coverage: true
    html_generator: "dart_coverage_html"
    alternative_tools:
      - "VS Code Coverage Gutters extension"
      - "Flutter Coverage Helper extension"
      - "Online LCOV viewers"

  # Report settings
  settings:
    show_missing_lines: true
    show_branch_coverage: true
    show_function_coverage: true
    include_test_files: false

# Quality gates
quality_gates:
  # Fail build if coverage drops below these thresholds
  fail_on:
    line_coverage_below: 75
    branch_coverage_below: 70
    function_coverage_below: 80

  # Warning thresholds
  warn_on:
    line_coverage_below: 85
    branch_coverage_below: 80
    function_coverage_below: 90

  # Coverage trend analysis
  trend:
    enabled: true
    baseline_file: "coverage/baseline.json"
    fail_on_decrease: true
    max_decrease_percent: 5

# Priority areas for coverage
priority_areas:
  critical:
    - "lib/core/services/**/*.dart"
    - "lib/domain/repositories/**/*.dart"
    - "lib/presentation/cubit/**/*.dart"
    - "lib/core/mixins/**/*.dart"

  high:
    - "lib/presentation/screens/**/*.dart"
    - "lib/domain/entities/**/*.dart"
    - "lib/core/utils/**/*.dart"

  medium:
    - "lib/presentation/widgets/**/*.dart"
    - "lib/data/datasources/**/*.dart"
    - "lib/data/models/**/*.dart"

  low:
    - "lib/presentation/theme/**/*.dart"
    - "lib/core/constants/**/*.dart"

# CI/CD integration
ci_cd:
  # GitHub Actions integration
  github_actions:
    upload_to_codecov: true
    upload_to_coveralls: false
    generate_pr_comment: true

  # Coverage badges
  badges:
    enabled: true
    style: "flat-square"
    color_scheme:
      excellent: "brightgreen" # 90%+
      good: "green" # 80-89%
      fair: "yellow" # 70-79%
      poor: "orange" # 60-69%
      bad: "red" # <60%

# Advanced settings
advanced:
  # Parallel test execution
  parallel:
    enabled: true
    max_workers: 4

  # Memory settings
  memory:
    max_heap_size: "2G"

  # Caching
  cache:
    enabled: true
    directory: ".dart_tool/test_cache"

  # Debugging
  debug:
    verbose_output: false
    save_intermediate_files: false

# Custom rules
custom_rules:
  # Require tests for new files
  require_tests_for_new_files: true

  # Minimum test coverage for new code
  new_code_coverage_threshold: 90

  # Exclude experimental features from coverage requirements
  experimental_exclusions:
    - "lib/experimental/**/*.dart"
    - "lib/**/experimental_*.dart"

# Notifications
notifications:
  # Slack integration (if configured)
  slack:
    enabled: false
    webhook_url: ""
    channel: "#dev-notifications"

  # Email notifications
  email:
    enabled: false
    recipients: []

  # Console output
  console:
    enabled: true
    verbose: true
    colors: true
