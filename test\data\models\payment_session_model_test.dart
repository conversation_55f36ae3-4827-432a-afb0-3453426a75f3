import 'package:flutter_test/flutter_test.dart';
import '../../../lib/data/models/payments/payment_session_model.dart';
import '../../../lib/domain/entities/payments/payment_session.dart';

void main() {
  group('PaymentSessionModel', () {
    group('fromJson', () {
      test('should parse amount correctly when it is a string', () {
        // Arrange - This is the actual response format from the server
        final json = {
          'success': true,
          'message': 'Payment session created successfully',
          'data': {
            'payment_session_id': '5619000000231062',
            'amount': '60010.00', // String amount from server
            'currency': 'INR',
            'description': 'Payment for invoice AP-RI-2526-00142',
            'invoice_number': 'AP-RI-2526-00142',
            'created_time': 1750470725,
            'transaction_id': '68561045af51846180a60956',
            'expires_in': '15 minutes',
            'customer_id': '401088000053137251',
          },
          'payment_session': {
            'payments_session_id': '5619000000231062',
            'currency': 'INR',
            'amount': '60010.00', // String amount from server
            'description': 'Payment for invoice AP-RI-2526-00142',
            'invoice_number': 'AP-RI-2526-00142',
            'created_time': 1750470725,
            'meta_data': [
              {'key': 'customer_id', 'value': '401088000053137251'},
              {'key': 'invoice_number', 'value': 'AP-RI-2526-00142'}
            ]
          }
        };

        // Act
        final result = PaymentSessionModel.fromJson(json);

        // Assert
        expect(result.amount, 60010.0);
        expect(result.sessionId, '5619000000231062');
        expect(result.currency, 'INR');
        expect(result.invoiceNumber, 'AP-RI-2526-00142');
        expect(result.customerId, '401088000053137251');
        expect(result.description, 'Payment for invoice AP-RI-2526-00142');
        expect(result.status, PaymentSessionStatus.created);
      });

      test('should parse amount correctly when it is a double', () {
        // Arrange
        final json = {
          'payment_session': {
            'payments_session_id': 'session_123',
            'amount': 100.50, // Double amount
            'currency': 'INR',
            'invoice_number': 'INV-001',
            'created_time': 1640995200,
          },
          'data': {
            'customer_id': 'CUST-001',
          }
        };

        // Act
        final result = PaymentSessionModel.fromJson(json);

        // Assert
        expect(result.amount, 100.50);
      });

      test('should parse amount correctly when it is an int', () {
        // Arrange
        final json = {
          'payment_session': {
            'payments_session_id': 'session_123',
            'amount': 100, // Integer amount
            'currency': 'INR',
            'invoice_number': 'INV-001',
            'created_time': 1640995200,
          },
          'data': {
            'customer_id': 'CUST-001',
          }
        };

        // Act
        final result = PaymentSessionModel.fromJson(json);

        // Assert
        expect(result.amount, 100.0);
      });

      test('should handle null amount gracefully', () {
        // Arrange
        final json = {
          'payment_session': {
            'payments_session_id': 'session_123',
            'amount': null, // Null amount
            'currency': 'INR',
            'invoice_number': 'INV-001',
            'created_time': 1640995200,
          },
          'data': {
            'customer_id': 'CUST-001',
          }
        };

        // Act
        final result = PaymentSessionModel.fromJson(json);

        // Assert
        expect(result.amount, 0.0);
      });

      test('should handle invalid string amount gracefully', () {
        // Arrange
        final json = {
          'payment_session': {
            'payments_session_id': 'session_123',
            'amount': 'invalid_amount', // Invalid string amount
            'currency': 'INR',
            'invoice_number': 'INV-001',
            'created_time': 1640995200,
          },
          'data': {
            'customer_id': 'CUST-001',
          }
        };

        // Act
        final result = PaymentSessionModel.fromJson(json);

        // Assert
        expect(result.amount, 0.0);
      });
    });
  });
}
