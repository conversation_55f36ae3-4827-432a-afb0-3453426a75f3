import 'package:flutter_test/flutter_test.dart';
import '../../../lib/data/models/payments/payment_session_model.dart';
import '../../../lib/domain/entities/payments/payment_session.dart';

void main() {
  group('PaymentSessionModel', () {
    group('fromJson', () {
      test('should parse amount correctly when it is a string', () {
        // Arrange - This is the actual response format from the server
        final json = {
          'success': true,
          'message': 'Payment session created successfully',
          'data': {
            'payment_session_id': '5619000000231062',
            'amount': '60010.00', // String amount from server
            'currency': 'INR',
            'description': 'Payment for invoice AP-RI-2526-00142',
            'invoice_number': 'AP-RI-2526-00142',
            'created_time': 1750470725,
            'transaction_id': '68561045af51846180a60956',
            'expires_in': '15 minutes',
            'customer_id': '401088000053137251',
          },
          'payment_session': {
            'payments_session_id': '5619000000231062',
            'currency': 'INR',
            'amount': '60010.00', // String amount from server
            'description': 'Payment for invoice AP-RI-2526-00142',
            'invoice_number': 'AP-RI-2526-00142',
            'created_time': 1750470725,
            'meta_data': [
              {'key': 'customer_id', 'value': '401088000053137251'},
              {'key': 'invoice_number', 'value': 'AP-RI-2526-00142'},
            ],
          },
        };

        // Act
        final result = PaymentSessionModel.fromJson(json);

        // Assert
        expect(result.amount, 60010.0);
        expect(result.sessionId, '5619000000231062');
        expect(result.currency, 'INR');
        expect(result.invoiceNumber, 'AP-RI-2526-00142');
        expect(result.customerId, '401088000053137251');
        expect(result.description, 'Payment for invoice AP-RI-2526-00142');
        expect(result.status, PaymentSessionStatus.created);
      });

      test('should parse amount correctly when it is a double', () {
        // Arrange
        final json = {
          'payment_session': {
            'payments_session_id': 'session_123',
            'amount': 100.50, // Double amount
            'currency': 'INR',
            'invoice_number': 'INV-001',
            'created_time': 1640995200,
          },
          'data': {'customer_id': 'CUST-001'},
        };

        // Act
        final result = PaymentSessionModel.fromJson(json);

        // Assert
        expect(result.amount, 100.50);
      });

      test('should parse amount correctly when it is an int', () {
        // Arrange
        final json = {
          'payment_session': {
            'payments_session_id': 'session_123',
            'amount': 100, // Integer amount
            'currency': 'INR',
            'invoice_number': 'INV-001',
            'created_time': 1640995200,
          },
          'data': {'customer_id': 'CUST-001'},
        };

        // Act
        final result = PaymentSessionModel.fromJson(json);

        // Assert
        expect(result.amount, 100.0);
      });

      test('should handle null amount gracefully', () {
        // Arrange
        final json = {
          'payment_session': {
            'payments_session_id': 'session_123',
            'amount': null, // Null amount
            'currency': 'INR',
            'invoice_number': 'INV-001',
            'created_time': 1640995200,
          },
          'data': {'customer_id': 'CUST-001'},
        };

        // Act
        final result = PaymentSessionModel.fromJson(json);

        // Assert
        expect(result.amount, 0.0);
      });

      test('should handle invalid string amount gracefully', () {
        // Arrange
        final json = {
          'payment_session': {
            'payments_session_id': 'session_123',
            'amount': 'invalid_amount', // Invalid string amount
            'currency': 'INR',
            'invoice_number': 'INV-001',
            'created_time': 1640995200,
          },
          'data': {'customer_id': 'CUST-001'},
        };

        // Act
        final result = PaymentSessionModel.fromJson(json);

        // Assert
        expect(result.amount, 0.0);
      });
    });

    group('Payment URL Construction', () {
      test('should use provided payment_url when available', () {
        // Arrange
        final json = {
          'payment_session': {
            'payments_session_id': 'session_123',
            'payment_url': 'https://custom.payment.url/session_123',
            'amount': '100.00',
            'currency': 'INR',
            'invoice_number': 'INV-001',
            'created_time': 1640995200,
          },
          'data': {'customer_id': 'CUST-001'},
        };

        // Act
        final result = PaymentSessionModel.fromJson(json);

        // Assert
        expect(result.paymentUrl, 'https://custom.payment.url/session_123');
      });

      test('should construct Zoho URL when payment_url is missing', () {
        // Arrange - This matches the actual API response format
        final json = {
          'success': true,
          'message': 'Payment session created successfully',
          'data': {
            'payment_session_id': '5619000000231070',
            'amount': '100.00',
            'currency': 'INR',
            'description': 'Payment for invoice AP-RI-TEST',
            'invoice_number': 'AP-RI-TEST',
            'created_time': 1750470725,
            'transaction_id': '685618d6af51846180a60978',
            'customer_id': '401088000053137251',
          },
          'payment_session': {
            'payments_session_id': '5619000000231070',
            'currency': 'INR',
            'amount': '100.00',
            'description': 'Payment for invoice AP-RI-TEST',
            'invoice_number': 'AP-RI-TEST',
            'created_time': 1750470725,
            // Note: no payment_url field in the response
          },
        };

        // Act
        final result = PaymentSessionModel.fromJson(json);

        // Assert
        expect(
          result.paymentUrl,
          'https://checkout.zoho.com/v2/payment/5619000000231070',
        );
        expect(result.sessionId, '5619000000231070');
        expect(result.amount, 100.0);
      });

      test('should handle empty payment_url and construct from session_id', () {
        // Arrange
        final json = {
          'payment_session': {
            'payments_session_id': 'session_456',
            'payment_url': '', // Empty payment URL
            'amount': '200.00',
            'currency': 'INR',
            'invoice_number': 'INV-002',
            'created_time': 1640995200,
          },
          'data': {'customer_id': 'CUST-002'},
        };

        // Act
        final result = PaymentSessionModel.fromJson(json);

        // Assert
        expect(
          result.paymentUrl,
          'https://checkout.zoho.com/v2/payment/session_456',
        );
      });

      test(
        'should return empty string when both payment_url and session_id are missing',
        () {
          // Arrange
          final json = {
            'payment_session': {
              'amount': '300.00',
              'currency': 'INR',
              'invoice_number': 'INV-003',
              'created_time': 1640995200,
              // No payment_url or session_id
            },
            'data': {'customer_id': 'CUST-003'},
          };

          // Act
          final result = PaymentSessionModel.fromJson(json);

          // Assert
          expect(result.paymentUrl, '');
          expect(result.sessionId, '');
        },
      );
    });
  });
}
