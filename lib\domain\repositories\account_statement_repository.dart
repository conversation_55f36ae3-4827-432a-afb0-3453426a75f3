import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../entities/account_statement/account_statement.dart';

abstract class AccountStatementRepository {
  /// Get account statement for a customer
  /// Implements a caching-first approach - returns cached data immediately if available
  /// and triggers a background sync if needed
  Future<Either<Failure, AccountStatement?>> getAccountStatement(
    String customerId,
  );

  /// Sync account statement data with remote server
  Future<Either<Failure, AccountStatement?>> syncAccountStatement(
    String customerId,
  );

  /// Check if account statement data needs to be synced
  Future<Either<Failure, bool>> checkIfSyncNeeded(String customerId);

  /// Force invalidate the cache for a customer's account statement
  /// This will cause the next getAccountStatement call to trigger a sync
  Future<Either<Failure, bool>> invalidateCache(String customerId);
}
