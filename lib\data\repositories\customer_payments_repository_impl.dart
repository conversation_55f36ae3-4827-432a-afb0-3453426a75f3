// lib/data/repositories/customer_payments_repository_impl.dart
import 'package:aquapartner/core/utils/logger.dart';
import 'package:dartz/dartz.dart';
import '../../core/error/exceptions.dart';
import '../../core/error/failures.dart';
import '../../domain/entities/payments/customer_payment.dart';
import '../../domain/entities/payments/payments_summary.dart';
import '../../domain/repositories/customer_payments_repository.dart';

import '../../core/network/network_info.dart';
import '../datasources/local/customer_payments_local_data_source.dart';
import '../datasources/remote/customer_payments_remote_data_source.dart';

class CustomerPaymentsRepositoryImpl implements CustomerPaymentsRepository {
  final CustomerPaymentsRemoteDataSource remoteDataSource;
  final CustomerPaymentsLocalDataSource localDataSource;
  final NetworkInfo networkInfo;
  final AppLogger logger;

  CustomerPaymentsRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
    required this.networkInfo,
    required this.logger,
  });

  @override
  Future<Either<Failure, PaymentsSummary>> getCustomerPayments(
    String customerId,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        final remotePayments = await remoteDataSource.getCustomerPayments(
          customerId,
        );
        await localDataSource.cacheCustomerPayments(remotePayments.results);
        return Right(remotePayments.toEntity());
      } on ServerException catch (e) {
        logger.e('CacheException occurred: ${e.toString()}');
        return Left(ServerFailure());
      }
    } else {
      try {
        final localPayments = await localDataSource.getCustomerPayments(
          customerId,
        );
        final totalSum = localPayments.fold(0.0, (sum, payment) {
          final amountStr = payment.amount
              .replaceAll('INR ', '')
              .replaceAll(',', '');
          return sum += double.tryParse(amountStr) ?? 0.0;
        });

        return Right(
          PaymentsSummary(
            totalSum: totalSum,
            payments: localPayments.map((model) => model.toEntity()).toList(),
          ),
        );
      } on CacheException catch (e) {
        logger.e('CacheException occurred: ${e.toString()}');
        return Left(CacheFailure());
      }
    }
  }

  @override
  Future<Either<Failure, void>> syncCustomerPayments(String customerId) async {
    if (await networkInfo.isConnected) {
      try {
        final remotePayments = await remoteDataSource.getCustomerPayments(
          customerId,
        );
        await localDataSource.cacheCustomerPayments(remotePayments.results);
        return const Right(null);
      } on ServerException catch (e) {
        logger.e('ServerFailure occurred: ${e.toString()}');
        return Left(ServerFailure());
      }
    } else {
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, List<CustomerPayment>>> getLocalCustomerPayments(
    String customerId,
  ) async {
    try {
      final localPayments = await localDataSource.getCustomerPayments(
        customerId,
      );
      return Right(localPayments.map((model) => model.toEntity()).toList());
    } on CacheException catch (e) {
      logger.e('CacheException occurred: ${e.toString()}');
      return Left(CacheFailure());
    }
  }
}
