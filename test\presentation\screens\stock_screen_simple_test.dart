import 'package:aquapartner/presentation/cubit/smr_report/smr_report_cubit.dart';
import 'package:aquapartner/presentation/cubit/smr_report/smr_report_state.dart';
import 'package:aquapartner/presentation/screens/stock_screen.dart';
import 'package:aquapartner/domain/entities/smr_report.dart';
import 'package:bloc_test/bloc_test.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../helpers/test_helpers.dart';

// Mock classes
class MockSMRReportCubit extends MockCubit<SMRReportState>
    implements SMRReportCubit {
  @override
  Future<void> loadSMRReports({bool forceRefresh = false}) async {
    // Mock implementation - do nothing
  }

  @override
  Future<void> syncReports(String customerId) async {
    // Mock implementation - do nothing
  }

  @override
  void clearCache() {
    // Mock implementation - do nothing
  }
}

void main() {
  group('StockScreen Simple Tests', () {
    late MockSMRReportCubit mockSMRReportCubit;

    setUpAll(() {
      // Set up GetIt services for testing
      TestHelpers.setupGetItServices();
    });

    tearDownAll(() {
      // Clean up GetIt services after testing
      TestHelpers.cleanupGetItServices();
    });

    setUp(() {
      mockSMRReportCubit = MockSMRReportCubit();

      // Set up default state
      final testReports = [
        SMRReport(
          id: '1',
          so: 'SO001',
          partner: 'Test Partner 1',
          partnerId: 'P001',
          productName: 'Test Product 1',
          startDate: DateTime.now().subtract(Duration(days: 30)),
          lastDate: DateTime.now(),
          openingBalance: 100,
          invoice: 50,
          srn: 10,
          closingBalance: 140,
          sales: 20,
          status: 'Active',
        ),
      ];

      when(
        () => mockSMRReportCubit.state,
      ).thenReturn(SMRReportLoaded(reports: testReports, isFromCache: false));
    });

    Widget createStockScreen() {
      return TestHelpers.createTestApp(
        home: BlocProvider<SMRReportCubit>.value(
          value: mockSMRReportCubit,
          child: StockScreen(),
        ),
      );
    }

    testWidgets('should render stock screen', (tester) async {
      await TestHelpers.pumpAndSettle(tester, createStockScreen());

      // Verify basic structure
      expect(find.byType(Scaffold), findsOneWidget);
      expect(find.byType(StockScreen), findsOneWidget);
    });

    testWidgets('should show loading state', (tester) async {
      // Set loading state
      when(() => mockSMRReportCubit.state).thenReturn(SMRReportLoading());

      await tester.pumpWidget(createStockScreen());
      await tester.pump();

      // Verify loading indicator
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('should show error state', (tester) async {
      // Set error state before creating the widget
      when(
        () => mockSMRReportCubit.state,
      ).thenReturn(SMRReportError('Failed to load stock data'));

      await tester.pumpWidget(createStockScreen());
      await tester.pump();

      // Verify error state UI elements are displayed
      expect(find.byIcon(Icons.error_outline), findsOneWidget);
      expect(find.byType(ElevatedButton), findsOneWidget);

      // Verify the retry button is present
      final retryButton = find.byType(ElevatedButton);
      expect(retryButton, findsOneWidget);

      // Test that the retry button can be tapped (basic interaction test)
      await tester.tap(retryButton);
      await tester.pump();

      // The test passes if we can find the error UI elements and interact with the retry button
    });
  });
}
