import 'package:firebase_performance/firebase_performance.dart';
import '../utils/logger.dart';

/// A service for monitoring app performance using Firebase Performance Monitoring.
///
/// This service provides methods to:
/// - Track custom traces for specific operations
/// - Monitor HTTP network requests
/// - Track UI rendering performance
class PerformanceMonitoringService {
  final FirebasePerformance _performance;
  final AppLogger _logger;
  final Map<String, Trace> _activeTraces = {};
  final bool _isEnabled;

  PerformanceMonitoringService(this._performance, this._logger)
    : _isEnabled = true {
    _initialize();
  }

  /// Initialize the performance monitoring service
  Future<void> _initialize() async {
    try {
      final isEnabled = await _performance.isPerformanceCollectionEnabled();
      if (!isEnabled) {
        await _performance.setPerformanceCollectionEnabled(true);
        _logger.i('Firebase Performance Monitoring enabled');
      }
    } catch (e) {
      _logger.e('Error initializing performance monitoring', e);
    }
  }

  /// Start a custom trace for a specific operation
  ///
  /// [traceName] should be a unique identifier for the operation
  /// Returns true if the trace was started successfully
  bool startTrace(String traceName) {
    if (!_isEnabled) return false;

    try {
      if (_activeTraces.containsKey(traceName)) {
        _logger.w(
          'Trace "$traceName" is already running. Stopping previous trace.',
        );
        stopTrace(traceName);
      }

      final trace = _performance.newTrace(traceName);
      trace.start();
      _activeTraces[traceName] = trace;
      _logger.d('Started trace: $traceName');
      return true;
    } catch (e) {
      _logger.e('Error starting trace "$traceName"', e);
      return false;
    }
  }

  /// Stop a custom trace and record its metrics
  ///
  /// [traceName] should match a previously started trace
  /// [attributes] optional key-value pairs to add to the trace
  /// Returns the duration in milliseconds if successful, null otherwise
  Future<int?> stopTrace(
    String traceName, {
    Map<String, String>? attributes,
  }) async {
    if (!_isEnabled) return null;

    try {
      final trace = _activeTraces.remove(traceName);
      if (trace == null) {
        _logger.w('Attempted to stop non-existent trace: $traceName');
        return null;
      }

      // Add any custom attributes
      if (attributes != null) {
        attributes.forEach((key, value) {
          trace.putAttribute(key, value);
        });
      }

      // Stop the trace
      await trace.stop();

      // Get metrics if available
      int? durationMs;
      try {
        // In newer versions of Firebase Performance, we would use:
        // durationMs = trace.metrics[Metric.traceRuntimeMs]?.toInt();
        // But for compatibility, we'll just log the stop event
        durationMs = 0; // We can't get the actual duration
        _logger.d('Stopped trace: $traceName');
      } catch (e) {
        _logger.w('Could not get duration for trace: $traceName');
      }

      return durationMs;
    } catch (e) {
      _logger.e('Error stopping trace "$traceName"', e);
      return null;
    }
  }

  /// Increment a counter metric within a trace
  ///
  /// [traceName] the name of an active trace
  /// [counterName] the name of the counter to increment
  /// [incrementBy] the amount to increment by (default: 1)
  bool incrementMetric(
    String traceName,
    String counterName, [
    int incrementBy = 1,
  ]) {
    if (!_isEnabled) return false;

    try {
      final trace = _activeTraces[traceName];
      if (trace == null) {
        _logger.w(
          'Attempted to increment metric on non-existent trace: $traceName',
        );
        return false;
      }

      trace.incrementMetric(counterName, incrementBy);
      return true;
    } catch (e) {
      _logger.e(
        'Error incrementing metric "$counterName" for trace "$traceName"',
        e,
      );
      return false;
    }
  }

  /// Create and start an HTTP metric for tracking network requests
  ///
  /// [url] the URL being requested
  /// [httpMethod] the HTTP method (GET, POST, etc.)
  /// Returns an HttpMetric object that must be stopped when the request completes
  HttpMetric? startHttpMetric(String url, HttpMethod httpMethod) {
    if (!_isEnabled) return null;

    try {
      final metric = _performance.newHttpMetric(url, httpMethod);
      metric.start();
      return metric;
    } catch (e) {
      _logger.e('Error starting HTTP metric for $httpMethod $url', e);
      return null;
    }
  }

  /// Stop an HTTP metric and record its performance data
  ///
  /// [metric] the HttpMetric object returned from startHttpMetric
  /// [responseCode] the HTTP response code
  /// [requestSize] the size of the request in bytes
  /// [responseSize] the size of the response in bytes
  /// [contentType] the content type of the response
  Future<void> stopHttpMetric(
    HttpMetric? metric, {
    int? responseCode,
    int? requestSize,
    int? responseSize,
    String? contentType,
  }) async {
    if (!_isEnabled || metric == null) return;

    try {
      if (responseCode != null) {
        metric.httpResponseCode = responseCode;
      }

      if (requestSize != null) {
        metric.requestPayloadSize = requestSize;
      }

      if (responseSize != null) {
        metric.responsePayloadSize = responseSize;
      }

      if (contentType != null) {
        metric.responseContentType = contentType;
      }

      await metric.stop();
    } catch (e) {
      _logger.e('Error stopping HTTP metric', e);
    }
  }

  /// Stop all active traces
  ///
  /// This is useful when the app is going to background or being terminated
  Future<void> stopAllTraces() async {
    if (!_isEnabled || _activeTraces.isEmpty) return;

    try {
      final traces = Map<String, Trace>.from(_activeTraces);
      _activeTraces.clear();

      for (final entry in traces.entries) {
        try {
          await entry.value.stop();
          _logger.d('Stopped trace during cleanup: ${entry.key}');
        } catch (e) {
          _logger.w('Error stopping trace "${entry.key}" during cleanup');
        }
      }
    } catch (e) {
      _logger.e('Error stopping all traces', e);
    }
  }
}
