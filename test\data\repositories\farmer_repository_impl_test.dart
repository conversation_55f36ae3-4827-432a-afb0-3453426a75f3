import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:dartz/dartz.dart';
import 'package:aquapartner/data/repositories/farmer_repository_impl.dart';
import 'package:aquapartner/data/datasources/local/farmer_local_datasource.dart';
import 'package:aquapartner/data/datasources/remote/farmer_remote_datasource.dart';
import 'package:aquapartner/core/utils/logger.dart';
import 'package:aquapartner/core/error/failures.dart';
import 'package:aquapartner/core/error/exceptions.dart';
import 'package:aquapartner/domain/entities/farmer_visit/farmer.dart';
import 'package:aquapartner/data/entities/farmer_visits/farmer_entity.dart';
import 'package:aquapartner/data/models/farmer_visits/farmer_data_model.dart';

// Mock classes
class MockFarmerLocalDataSource extends Mock implements FarmerLocalDataSource {}

class MockFarmerRemoteDataSource extends Mock
    implements FarmerRemoteDataSource {}

class MockAppLogger extends Mock implements AppLogger {}

void main() {
  group('FarmerRepositoryImpl Tests', () {
    late FarmerRepositoryImpl repository;
    late MockFarmerLocalDataSource mockLocalDataSource;
    late MockFarmerRemoteDataSource mockRemoteDataSource;
    late MockAppLogger mockLogger;

    late List<FarmerEntity> testFarmerEntities;
    late List<Farmer> testFarmers;
    late FarmerDataModel testFarmerDataModel;
    const testCustomerId = 'customer_123';

    setUpAll(() {
      // Register fallback values for mocktail
      registerFallbackValue(
        FarmerEntity(name: 'Test Farmer', mobileNumber: '+919999999999'),
      );
      registerFallbackValue(FarmerDataModel(results: []));
      registerFallbackValue(<FarmerEntity>[]);
    });

    setUp(() {
      mockLocalDataSource = MockFarmerLocalDataSource();
      mockRemoteDataSource = MockFarmerRemoteDataSource();
      mockLogger = MockAppLogger();

      repository = FarmerRepositoryImpl(
        localDataSource: mockLocalDataSource,
        remoteDataSource: mockRemoteDataSource,
        logger: mockLogger,
      );

      testFarmerEntities = [
        FarmerEntity(name: 'John Farmer', mobileNumber: '+919999999999'),
        FarmerEntity(name: 'Jane Farmer', mobileNumber: '+919876543211'),
      ];

      testFarmers = [
        const Farmer(
          id: 1,
          name: 'John Farmer',
          mobileNumber: '+919999999999',
          visits: [],
        ),
        const Farmer(
          id: 2,
          name: 'Jane Farmer',
          mobileNumber: '+919876543211',
          visits: [],
        ),
      ];

      testFarmerDataModel = FarmerDataModel(results: []);
    });

    group('getAllFarmers Tests', () {
      test(
        'should return farmers from local data source when available',
        () async {
          // Arrange
          when(
            () => mockLocalDataSource.getAllFarmers(),
          ).thenAnswer((_) async => testFarmerEntities);

          // Act
          final result = await repository.getAllFarmers(testCustomerId);

          // Assert
          expect(result, isA<Right<Failure, List<Farmer>>>());
          result.fold(
            (failure) => fail('Expected Right but got Left: $failure'),
            (farmers) {
              expect(farmers.length, equals(2));
              expect(farmers[0].name, equals('John Farmer'));
              expect(farmers[1].name, equals('Jane Farmer'));
            },
          );
          verify(() => mockLocalDataSource.getAllFarmers()).called(1);
        },
      );

      test(
        'should fetch from remote when local data source is empty',
        () async {
          // Arrange
          when(
            () => mockLocalDataSource.getAllFarmers(),
          ).thenAnswer((_) async => []);
          when(
            () => mockRemoteDataSource.getFarmerData(testCustomerId),
          ).thenAnswer((_) async => testFarmerDataModel);
          when(
            () => mockLocalDataSource.storeFarmersAndVisits(any()),
          ).thenAnswer((_) async {});

          // Act
          final result = await repository.getAllFarmers(testCustomerId);

          // Assert
          expect(result, isA<Right<Failure, List<Farmer>>>());
          verify(() => mockLocalDataSource.getAllFarmers()).called(1);
          verify(
            () => mockRemoteDataSource.getFarmerData(testCustomerId),
          ).called(1);
          verify(
            () => mockLocalDataSource.storeFarmersAndVisits(any()),
          ).called(1);
        },
      );

      test('should return ServerFailure when remote fetch fails', () async {
        // Arrange
        when(
          () => mockLocalDataSource.getAllFarmers(),
        ).thenAnswer((_) async => []);
        when(
          () => mockRemoteDataSource.getFarmerData(testCustomerId),
        ).thenThrow(ServerException());

        // Act
        final result = await repository.getAllFarmers(testCustomerId);

        // Assert
        expect(result, equals(Left(ServerFailure())));
        verify(() => mockLocalDataSource.getAllFarmers()).called(1);
        verify(
          () => mockRemoteDataSource.getFarmerData(testCustomerId),
        ).called(1);
      });

      test(
        'should return CacheFailure when local storage fails after remote fetch',
        () async {
          // Arrange
          when(
            () => mockLocalDataSource.getAllFarmers(),
          ).thenAnswer((_) async => []);
          when(
            () => mockRemoteDataSource.getFarmerData(testCustomerId),
          ).thenAnswer((_) async => testFarmerDataModel);
          when(
            () => mockLocalDataSource.storeFarmersAndVisits(any()),
          ).thenThrow(CacheException());

          // Act
          final result = await repository.getAllFarmers(testCustomerId);

          // Assert
          expect(result, equals(Left(CacheFailure())));
          verify(
            () => mockRemoteDataSource.getFarmerData(testCustomerId),
          ).called(1);
          verify(
            () => mockLocalDataSource.storeFarmersAndVisits(any()),
          ).called(1);
        },
      );
    });

    group('storeFarmerData Tests', () {
      test('should store farmer data successfully', () async {
        // Arrange
        when(
          () => mockLocalDataSource.storeFarmersAndVisits(any()),
        ).thenAnswer((_) async {});

        // Act
        final result = await repository.storeFarmerData(testFarmers);

        // Assert
        expect(result, equals(const Right(null)));
        verify(
          () => mockLocalDataSource.storeFarmersAndVisits(any()),
        ).called(1);
      });

      test('should return CacheFailure when storage fails', () async {
        // Arrange
        when(
          () => mockLocalDataSource.storeFarmersAndVisits(any()),
        ).thenThrow(CacheException());

        // Act
        final result = await repository.storeFarmerData(testFarmers);

        // Assert
        expect(result, equals(Left(CacheFailure())));
        verify(
          () => mockLocalDataSource.storeFarmersAndVisits(any()),
        ).called(1);
      });
    });

    group('syncFarmers Tests', () {
      test(
        'should sync farmers successfully with one-way synchronization',
        () async {
          // Arrange
          when(
            () => mockRemoteDataSource.getFarmerData(testCustomerId),
          ).thenAnswer((_) async => testFarmerDataModel);
          when(
            () => mockLocalDataSource.clearAllFarmersAndVisits(),
          ).thenAnswer((_) async {});
          when(
            () => mockLocalDataSource.storeFarmersAndVisits(any()),
          ).thenAnswer((_) async {});

          // Act
          final result = await repository.syncFarmers(testCustomerId);

          // Assert
          expect(result, isA<Right<Failure, List<Farmer>>>());
          verify(
            () => mockRemoteDataSource.getFarmerData(testCustomerId),
          ).called(1);
        },
      );

      test('should return ServerFailure when remote sync fails', () async {
        // Arrange
        when(
          () => mockRemoteDataSource.getFarmerData(testCustomerId),
        ).thenThrow(ServerException());
        when(
          () => mockLocalDataSource.getAllFarmers(),
        ).thenAnswer((_) async => []);

        // Act
        final result = await repository.syncFarmers(testCustomerId);

        // Assert
        expect(result, equals(Left(ServerFailure())));
        verify(
          () => mockRemoteDataSource.getFarmerData(testCustomerId),
        ).called(1);
      });

      test('should return CacheFailure when local clear fails', () async {
        // Arrange
        // Create a FarmerDataModel with actual data to trigger the clear operation
        final farmerDataModelWithData = FarmerDataModel(
          results: [
            FarmerModel(
              name: 'Test Farmer',
              visits: [], // Empty visits but farmer exists
            ),
          ],
        );

        when(
          () => mockRemoteDataSource.getFarmerData(testCustomerId),
        ).thenAnswer((_) async => farmerDataModelWithData);
        when(
          () => mockLocalDataSource.clearAllFarmersAndVisits(),
        ).thenThrow(CacheException());

        // Act
        final result = await repository.syncFarmers(testCustomerId);

        // Assert
        expect(result, equals(Left(CacheFailure())));
        verify(
          () => mockRemoteDataSource.getFarmerData(testCustomerId),
        ).called(1);
      });
    });
  });
}
