/// Test configuration for payment integration tests
class PaymentTestConfig {
  // API Configuration
  static const String currentApiBaseUrl = 'https://partner.aquaconnect.blue/api';
  static const String testApiKey = 'test_api_key_placeholder';
  
  // Test Amounts
  static const double testAmountSmall = 100.0;
  static const double testAmountMedium = 500.0;
  static const double testAmountLarge = 1000.0;
  
  // Test Customer Data
  static const String testCustomerId = 'TEST_CUSTOMER_001';
  static const String testCustomerName = 'Test Customer';
  static const String testCustomerEmail = '<EMAIL>';
  static const String testInvoiceDescription = 'Test payment for integration testing';
  
  // Test Invoice Configuration
  static const String testInvoicePrefix = 'TEST_INV_';
  
  /// Generates a unique test invoice number
  static String generateTestInvoiceNumber() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return '$testInvoicePrefix$timestamp';
  }
  
  /// Generates a unique test customer ID
  static String generateTestCustomerId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return 'TEST_CUST_$timestamp';
  }
  
  /// Generates a unique test session ID
  static String generateTestSessionId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return 'TEST_SESSION_$timestamp';
  }
  
  // Test Timeouts
  static const Duration defaultTimeout = Duration(seconds: 30);
  static const Duration longTimeout = Duration(minutes: 2);
  
  // Test Environment Flags
  static const bool enableRealApiCalls = false; // Set to true for real API testing
  static const bool enableMockResponses = true;
  static const bool enableDetailedLogging = true;
  
  // Mock Response Data
  static const Map<String, dynamic> mockPaymentSessionResponse = {
    'success': true,
    'data': {
      'payment_session': {
        'payments_session_id': 'PS_TEST_123456789',
        'payment_url': 'https://payments.zoho.in/checkout/PS_TEST_123456789',
        'amount': 100.0,
        'currency': 'INR',
        'status': 'created',
        'created_time': '2024-01-01T00:00:00Z',
        'expires_at': '2024-01-01T01:00:00Z',
      },
      'invoiceNo': 'TEST_INV_123456789',
      'customerId': 'TEST_CUSTOMER_001',
      'description': 'Test payment for integration testing',
      'customerName': 'Test Customer',
      'customerEmail': '<EMAIL>',
    },
  };
  
  static const Map<String, dynamic> mockPaymentStatusResponse = {
    'success': true,
    'data': {
      'payment_session': {
        'payments_session_id': 'PS_TEST_123456789',
        'payment_url': 'https://payments.zoho.in/checkout/PS_TEST_123456789',
        'amount': 100.0,
        'currency': 'INR',
        'status': 'pending',
        'created_time': '2024-01-01T00:00:00Z',
        'expires_at': '2024-01-01T01:00:00Z',
      },
    },
  };
  
  static const Map<String, dynamic> mockPaymentVerificationResponse = {
    'success': true,
    'data': {
      'transaction': {
        'transaction_id': 'TXN_TEST_123456789',
        'session_id': 'PS_TEST_123456789',
        'invoice_number': 'TEST_INV_123456789',
        'customer_id': 'TEST_CUSTOMER_001',
        'amount': 100.0,
        'currency': 'INR',
        'status': 'success',
        'payment_method': 'credit_card',
        'transaction_date': '2024-01-01T00:00:00Z',
      },
    },
  };
  
  // Test Validation Rules
  static bool isValidTestAmount(double amount) {
    return amount > 0 && amount <= 10000;
  }
  
  static bool isValidTestInvoiceNumber(String invoiceNumber) {
    return invoiceNumber.isNotEmpty && invoiceNumber.startsWith(testInvoicePrefix);
  }
  
  static bool isValidTestCustomerId(String customerId) {
    return customerId.isNotEmpty && customerId.contains('TEST');
  }
  
  // Test Data Cleanup
  static List<String> createdTestSessions = [];
  static List<String> createdTestInvoices = [];
  
  static void trackTestSession(String sessionId) {
    createdTestSessions.add(sessionId);
  }
  
  static void trackTestInvoice(String invoiceNumber) {
    createdTestInvoices.add(invoiceNumber);
  }
  
  static void clearTestData() {
    createdTestSessions.clear();
    createdTestInvoices.clear();
  }
}
