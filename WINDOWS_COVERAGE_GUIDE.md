# Windows Coverage Analysis Guide

This guide provides comprehensive instructions for analyzing Flutter test coverage on Windows, since `genhtml` is not available on Windows systems.

## 🚀 Quick Start

### Option 1: Use the Automated Script
```bash
# Run the automated coverage analysis
run_coverage_analysis.bat
```

### Option 2: Manual PowerShell Execution
```powershell
# Run with PowerShell directly
powershell -ExecutionPolicy Bypass -File "scripts/coverage_analysis_windows.ps1" -GenerateHtml -Verbose
```

### Option 3: Basic Flutter Commands
```bash
# Run tests with coverage
flutter test --coverage

# View basic coverage info
dart pub global activate coverage
dart pub global run coverage:format_coverage --lcov --in=coverage --out=coverage/lcov.info --packages=.dart_tool/package_config.json --report-on=lib
```

## 📊 Coverage Analysis Tools for Windows

### 1. VS Code Extensions (Recommended)

#### Coverage Gutters Extension
- **Installation**: Search "Coverage Gutters" in VS Code extensions
- **Usage**: 
  1. Run tests with coverage: `flutter test --coverage`
  2. Open VS Code in your project
  3. Press `Ctrl+Shift+P` → "Coverage Gutters: Display Coverage"
  4. View coverage directly in your code editor

#### Flutter Coverage Helper
- **Installation**: Search "Flutter Coverage Helper" in VS Code extensions
- **Features**: Flutter-specific coverage visualization

### 2. Online LCOV Viewers

#### LCOV Viewer (Recommended)
- **URL**: https://lcov-viewer.netlify.app/
- **Usage**: Upload your `coverage/lcov.info` file
- **Features**: Interactive HTML reports, file-by-file analysis

#### Online GenHTML Alternative
- **URL**: https://genhtml.netlify.app/
- **Usage**: Upload LCOV file for HTML generation

### 3. PowerShell Analysis Script

Our custom PowerShell script provides:
- ✅ Comprehensive coverage analysis
- ✅ Production readiness assessment
- ✅ Detailed recommendations
- ✅ Windows-compatible execution

## 📈 Understanding Coverage Reports

### Coverage Metrics

#### Line Coverage
- **Target**: 70%+ for production
- **Meaning**: Percentage of code lines executed during tests
- **Critical**: Authentication (90%+), Dashboard (90%+)

#### Function Coverage
- **Target**: 80%+ for production
- **Meaning**: Percentage of functions called during tests
- **Important**: Business logic functions

#### Branch Coverage
- **Target**: 65%+ for production
- **Meaning**: Percentage of decision branches tested
- **Critical**: Error handling, conditional logic

### Production Readiness Scoring

Our PowerShell script calculates a production readiness score:

- **85%+**: ✅ Ready for Production
- **70-84%**: ⚠️ Approaching Readiness
- **<70%**: ❌ Not Ready

## 🛠️ Troubleshooting

### Common Issues

#### 1. "genhtml not recognized"
**Solution**: Use our Windows-compatible alternatives above

#### 2. Empty LCOV File
```bash
# Ensure tests run successfully first
flutter test --coverage

# Check if coverage directory exists
dir coverage

# Verify LCOV file has content
type coverage\lcov.info
```

#### 3. PowerShell Execution Policy
```powershell
# Allow script execution
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# Or run with bypass
powershell -ExecutionPolicy Bypass -File "scripts/coverage_analysis_windows.ps1"
```

#### 4. Flutter Not Found
```bash
# Verify Flutter installation
flutter doctor

# Add Flutter to PATH if needed
# Add C:\flutter\bin to your system PATH
```

### Coverage Collection Issues

#### Tests Not Generating Coverage
```bash
# Run specific test file with coverage
flutter test test/specific_test.dart --coverage

# Run all tests with verbose output
flutter test --coverage --verbose

# Check for test failures
flutter test --reporter=expanded
```

#### LCOV File Format Issues
```bash
# Regenerate LCOV file
dart pub global run coverage:format_coverage --lcov --in=coverage --out=coverage/lcov.info --packages=.dart_tool/package_config.json --report-on=lib
```

## 📋 Step-by-Step Coverage Analysis

### Step 1: Run Tests
```bash
flutter test --coverage
```

### Step 2: Verify Coverage File
```bash
# Check if file exists and has content
dir coverage
type coverage\lcov.info | findstr /C:"SF:" | find /C "SF:"
```

### Step 3: Analyze with PowerShell Script
```bash
run_coverage_analysis.bat
```

### Step 4: View in VS Code
1. Install Coverage Gutters extension
2. Open project in VS Code
3. Press `Ctrl+Shift+P`
4. Type "Coverage Gutters: Display Coverage"
5. View coverage highlights in your code

### Step 5: Upload to Online Viewer (Optional)
1. Go to https://lcov-viewer.netlify.app/
2. Upload `coverage/lcov.info`
3. Explore interactive coverage report

## 🎯 Coverage Targets by Component

### Critical Components (90%+ Coverage Required)
- Authentication flows (`lib/presentation/cubit/auth/`)
- Dashboard functionality (`lib/presentation/cubit/dashboard/`)
- Data synchronization (`lib/data/repositories/`)

### High Priority (80%+ Coverage)
- Business logic (`lib/domain/usecases/`)
- Core services (`lib/core/services/`)
- Data models (`lib/data/models/`)

### Medium Priority (70%+ Coverage)
- UI screens (`lib/presentation/screens/`)
- Widgets (`lib/presentation/widgets/`)
- Utilities (`lib/core/utils/`)

## 🔄 CI/CD Integration

### GitHub Actions Example
```yaml
name: Test Coverage
on: [push, pull_request]
jobs:
  test:
    runs-on: windows-latest
    steps:
      - uses: actions/checkout@v3
      - uses: subosito/flutter-action@v2
      - run: flutter test --coverage
      - uses: codecov/codecov-action@v3
        with:
          file: coverage/lcov.info
```

### Coverage Gates
- Minimum 70% line coverage
- No coverage decrease >5%
- All tests must pass
- Critical components 90%+

## 📚 Additional Resources

### Documentation
- [Flutter Testing Guide](https://docs.flutter.dev/testing)
- [Dart Coverage Package](https://pub.dev/packages/coverage)
- [VS Code Coverage Gutters](https://marketplace.visualstudio.com/items?itemName=ryanluker.vscode-coverage-gutters)

### Tools
- **LCOV Viewer**: https://lcov-viewer.netlify.app/
- **Coverage Gutters**: VS Code extension
- **Flutter Coverage Helper**: VS Code extension

### Commands Reference
```bash
# Basic coverage
flutter test --coverage

# Specific test path
flutter test test/data/ --coverage

# With custom coverage path
flutter test --coverage --coverage-path=coverage/custom.info

# Format coverage
dart pub global run coverage:format_coverage --lcov --in=coverage --out=coverage/lcov.info --packages=.dart_tool/package_config.json --report-on=lib
```

## ✨ Best Practices

1. **Run coverage regularly** during development
2. **Use VS Code extensions** for immediate feedback
3. **Upload to online viewers** for detailed analysis
4. **Set up CI/CD gates** to maintain coverage
5. **Focus on critical paths** first (auth, dashboard)
6. **Review uncovered lines** regularly
7. **Add tests for edge cases** and error scenarios

This guide ensures you can effectively analyze and improve test coverage on Windows without relying on Linux-specific tools like `genhtml`.
