import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../utils/logger.dart';
import '../constants/app_constants.dart';

/// Debug helper for payment-related issues
class PaymentDebugHelper {
  static final AppLogger _logger = AppLogger();

  /// Test payment API connectivity and configuration
  static Future<PaymentDebugResult> testPaymentAPI({
    String? customBaseUrl,
    String? authToken,
  }) async {
    final result = PaymentDebugResult();
    final baseUrl = customBaseUrl ?? AppConstants.baseUrl;
    
    _logger.i('Starting payment API debug test...');
    _logger.i('Base URL: $baseUrl');
    
    try {
      // Test 1: Basic connectivity
      result.connectivityTest = await _testConnectivity(baseUrl);
      
      // Test 2: Authentication
      result.authTest = await _testAuthentication(baseUrl, authToken);
      
      // Test 3: Payment endpoint availability
      result.endpointTest = await _testPaymentEndpoint(baseUrl, authToken);
      
      // Test 4: App Check status
      result.appCheckTest = await _testAppCheckStatus();
      
      result.overallSuccess = result.connectivityTest.success &&
                             result.authTest.success &&
                             result.endpointTest.success;
      
      _logger.i('Payment API debug test completed');
      _logger.i('Overall result: ${result.overallSuccess ? "SUCCESS" : "FAILED"}');
      
    } catch (e) {
      _logger.e('Error during payment API debug test: $e');
      result.overallSuccess = false;
    }
    
    return result;
  }

  static Future<DebugTestResult> _testConnectivity(String baseUrl) async {
    try {
      _logger.i('Testing connectivity to: $baseUrl');
      
      final dio = Dio(BaseOptions(
        baseUrl: baseUrl,
        connectTimeout: const Duration(seconds: 10),
        receiveTimeout: const Duration(seconds: 10),
      ));
      
      final response = await dio.get('/health');
      
      if (response.statusCode == 200) {
        return DebugTestResult(
          success: true,
          message: 'Connectivity test passed',
          details: 'Server responded with status ${response.statusCode}',
        );
      } else {
        return DebugTestResult(
          success: false,
          message: 'Connectivity test failed',
          details: 'Server responded with status ${response.statusCode}',
        );
      }
    } catch (e) {
      return DebugTestResult(
        success: false,
        message: 'Connectivity test failed',
        details: 'Error: ${e.toString()}',
      );
    }
  }

  static Future<DebugTestResult> _testAuthentication(
    String baseUrl,
    String? authToken,
  ) async {
    try {
      _logger.i('Testing authentication...');
      
      final dio = Dio(BaseOptions(
        baseUrl: baseUrl,
        connectTimeout: const Duration(seconds: 10),
        receiveTimeout: const Duration(seconds: 10),
      ));
      
      final headers = <String, String>{
        'Content-Type': 'application/json',
        if (authToken != null) 'Authorization': 'Bearer $authToken',
      };
      
      final response = await dio.get(
        '/auth/verify',
        options: Options(headers: headers),
      );
      
      if (response.statusCode == 200) {
        return DebugTestResult(
          success: true,
          message: 'Authentication test passed',
          details: 'Token is valid',
        );
      } else {
        return DebugTestResult(
          success: false,
          message: 'Authentication test failed',
          details: 'Status: ${response.statusCode}',
        );
      }
    } catch (e) {
      if (e is DioException && e.response?.statusCode == 401) {
        return DebugTestResult(
          success: false,
          message: 'Authentication failed',
          details: 'Invalid or expired token',
        );
      }
      
      return DebugTestResult(
        success: false,
        message: 'Authentication test error',
        details: 'Error: ${e.toString()}',
      );
    }
  }

  static Future<DebugTestResult> _testPaymentEndpoint(
    String baseUrl,
    String? authToken,
  ) async {
    try {
      _logger.i('Testing payment endpoint availability...');
      
      final dio = Dio(BaseOptions(
        baseUrl: baseUrl,
        connectTimeout: const Duration(seconds: 10),
        receiveTimeout: const Duration(seconds: 10),
      ));
      
      final headers = <String, String>{
        'Content-Type': 'application/json',
        if (authToken != null) 'Authorization': 'Bearer $authToken',
      };
      
      // Test with a minimal request to see if endpoint exists
      final testData = {
        'amount': 1.0,
        'currency': 'INR',
        'invoice_number': 'DEBUG_TEST_001',
        'customer_id': 'DEBUG_CUSTOMER',
        'description': 'Debug test payment',
      };
      
      final response = await dio.post(
        '/zoho/payments/create-session',
        data: testData,
        options: Options(headers: headers),
      );
      
      // Any response (even error) means endpoint exists
      return DebugTestResult(
        success: true,
        message: 'Payment endpoint is available',
        details: 'Status: ${response.statusCode}, Response: ${response.data}',
      );
      
    } catch (e) {
      if (e is DioException) {
        if (e.response?.statusCode == 404) {
          return DebugTestResult(
            success: false,
            message: 'Payment endpoint not found',
            details: 'The /zoho/payments/create-session endpoint does not exist',
          );
        } else if (e.response?.statusCode == 500) {
          return DebugTestResult(
            success: false,
            message: 'Payment endpoint has internal error',
            details: 'Server error: ${e.response?.data}',
          );
        } else {
          return DebugTestResult(
            success: true,
            message: 'Payment endpoint exists but returned error',
            details: 'Status: ${e.response?.statusCode}, Error: ${e.response?.data}',
          );
        }
      }
      
      return DebugTestResult(
        success: false,
        message: 'Payment endpoint test failed',
        details: 'Error: ${e.toString()}',
      );
    }
  }

  static Future<DebugTestResult> _testAppCheckStatus() async {
    try {
      _logger.i('Testing App Check status...');
      
      // In debug mode, App Check might not be properly configured
      if (kDebugMode) {
        return DebugTestResult(
          success: true,
          message: 'App Check bypassed in debug mode',
          details: 'App Check is disabled for payment endpoints in debug builds',
        );
      }
      
      return DebugTestResult(
        success: true,
        message: 'App Check status unknown',
        details: 'App Check testing requires production build',
      );
    } catch (e) {
      return DebugTestResult(
        success: false,
        message: 'App Check test failed',
        details: 'Error: ${e.toString()}',
      );
    }
  }

  /// Generate debug report
  static String generateDebugReport(PaymentDebugResult result) {
    final buffer = StringBuffer();
    buffer.writeln('=== PAYMENT DEBUG REPORT ===');
    buffer.writeln('Generated: ${DateTime.now()}');
    buffer.writeln('Overall Status: ${result.overallSuccess ? "✅ PASS" : "❌ FAIL"}');
    buffer.writeln();
    
    buffer.writeln('1. Connectivity Test:');
    buffer.writeln('   Status: ${result.connectivityTest.success ? "✅ PASS" : "❌ FAIL"}');
    buffer.writeln('   Message: ${result.connectivityTest.message}');
    buffer.writeln('   Details: ${result.connectivityTest.details}');
    buffer.writeln();
    
    buffer.writeln('2. Authentication Test:');
    buffer.writeln('   Status: ${result.authTest.success ? "✅ PASS" : "❌ FAIL"}');
    buffer.writeln('   Message: ${result.authTest.message}');
    buffer.writeln('   Details: ${result.authTest.details}');
    buffer.writeln();
    
    buffer.writeln('3. Payment Endpoint Test:');
    buffer.writeln('   Status: ${result.endpointTest.success ? "✅ PASS" : "❌ FAIL"}');
    buffer.writeln('   Message: ${result.endpointTest.message}');
    buffer.writeln('   Details: ${result.endpointTest.details}');
    buffer.writeln();
    
    buffer.writeln('4. App Check Test:');
    buffer.writeln('   Status: ${result.appCheckTest.success ? "✅ PASS" : "❌ FAIL"}');
    buffer.writeln('   Message: ${result.appCheckTest.message}');
    buffer.writeln('   Details: ${result.appCheckTest.details}');
    buffer.writeln();
    
    buffer.writeln('=== END REPORT ===');
    
    return buffer.toString();
  }
}

class PaymentDebugResult {
  bool overallSuccess = false;
  DebugTestResult connectivityTest = DebugTestResult();
  DebugTestResult authTest = DebugTestResult();
  DebugTestResult endpointTest = DebugTestResult();
  DebugTestResult appCheckTest = DebugTestResult();
}

class DebugTestResult {
  bool success = false;
  String message = '';
  String details = '';
  
  DebugTestResult({
    this.success = false,
    this.message = '',
    this.details = '',
  });
}
