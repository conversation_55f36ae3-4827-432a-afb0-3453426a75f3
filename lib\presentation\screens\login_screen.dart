import 'package:aqua_ui/aqua_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import '../../core/constants/app_constants.dart';
import '../../domain/validators/input_validators.dart';
import '../cubit/auth/auth_cubit.dart';
import '../cubit/auth/auth_state.dart';
import '../../core/utils/logger.dart';
import '../cubit/connectivity/connectivity_cubit.dart';
import '../cubit/connectivity/connectivity_state.dart';
import '../../core/mixins/analytics_mixin.dart';

class LoginScreen extends StatefulWidget {
  final String? initialPhoneNumber;

  const LoginScreen({super.key, this.initialPhoneNumber});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen>
    with AnalyticsMixin<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _phoneController;
  final AppLogger _logger = AppLogger();
  bool _isOtpSent = false; // Add this flag to track OTP state

  @override
  String get screenName => 'login';

  @override
  void initState() {
    _phoneController = TextEditingController(text: widget.initialPhoneNumber);
    super.initState();
  }

  @override
  void dispose() {
    _phoneController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocListener<AuthCubit, AuthState>(
        listener: (context, state) {
          _logger.i("Auth state in listener: ${state.runtimeType}");

          if (state is PhoneNumberError) {
            _logger.e("Phone number error: ${state.message}");

            // Track authentication error
            trackError(
              'phone_number_validation_error',
              state.message,
              additionalParams: {
                'phone_number_length': _phoneController.text.length.toString(),
                'error_type': 'validation',
              },
            );

            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  "Invalid phone number. Please check and try again.",
                ),
              ),
            );
            // Reset the flag if there's an error
            setState(() {
              _isOtpSent = false;
            });
          } else if (state is OtpSent) {
            _logger.i("OTP sent, navigating to verification screen");
            _logger.i("Verification ID: ${state.verificationId}");
            _logger.i("Phone Number: ${state.phoneNumber}");

            // Track successful OTP send
            trackUserFlow(
              flowName: 'authentication',
              stepName: 'otp_sent',
              status: 'completed',
              additionalParams: {
                'phone_number': state.phoneNumber,
                'has_initial_phone':
                    widget.initialPhoneNumber != null ? 'true' : 'false',
              },
            );

            // Set the flag when OTP is sent
            setState(() {
              _isOtpSent = true;
            });

            Future.microtask(() {
              Navigator.pushNamed(
                context,
                '/verify-otp',
                arguments: {
                  'verificationId': state.verificationId,
                  'phoneNumber': state.phoneNumber,
                },
              );
            });
          }
        },
        child: SafeArea(
          child: Container(
            color: acWhiteColor,
            padding: EdgeInsets.only(
              top: screenHeightPercentage(context, percentage: 0.1),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: SingleChildScrollView(
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SvgPicture.asset(AppConstants.imgPartnerLogo, height: 35),
                      vSpace8,
                      AquaText.heading(
                        'Login with',
                        weight: AquaFontWeight.semibold,
                        color: Colors.grey,
                      ),
                      vSpace6,
                      AquaText.subheadline(
                        "Mobile Number",
                        color: acBlackColor,
                      ),
                      vSpace3,
                      BlocBuilder<AuthCubit, AuthState>(
                        builder: (context, state) {
                          return AquaTextFormField(
                            floatingLabelBehavior: FloatingLabelBehavior.always,
                            prefixText: "+91 ",
                            controller: _phoneController,
                            isDense: true,
                            maxLength: 10,
                            readOnly:
                                _isOtpSent ||
                                (state
                                    is AuthLoading), // Make it read-only when OTP is being sent
                            enabled:
                                !_isOtpSent &&
                                !(state
                                    is AuthLoading), // Disable when loading or OTP sent
                            keyboardType: TextInputType.numberWithOptions(
                              decimal: false,
                              signed: false,
                            ),
                            maxLines: 1,
                            prefixTextStyle:
                                AquaText.subheadline(
                                  "",
                                  color: acTextSecondaryColor,
                                ).style,
                            labelText: "",
                            hintText: "Mobile Number",
                            validator: InputValidators.validatePhoneNumber,
                          );
                        },
                      ),
                      vSpace8,

                      BlocBuilder<AuthCubit, AuthState>(
                        builder: (context, state) {
                          final isLoading = state is AuthLoading;
                          final connectivityState =
                              context.watch<ConnectivityCubit>().state;
                          final isOffline =
                              connectivityState.status ==
                              ConnectionStatus.disconnected;

                          return AquaButton(
                            title: "Send OTP", // Restore title
                            isLoading: isLoading,
                            onTap:
                                isOffline ||
                                        isLoading // Disable button when loading or offline
                                    ? null
                                    : () {
                                      _logger.d("Need to Validating Form");

                                      // Track send OTP button tap
                                      trackUserInteraction(
                                        'send_otp_button_tapped',
                                        'button',
                                        additionalParams: {
                                          'phone_number_length':
                                              _phoneController.text.length
                                                  .toString(),
                                          'has_initial_phone':
                                              widget.initialPhoneNumber != null
                                                  ? 'true'
                                                  : 'false',
                                          'is_offline': isOffline.toString(),
                                        },
                                      );

                                      if (_formKey.currentState?.validate() ??
                                          false) {
                                        _logger.d(
                                          "Validation Success, need to send OTP",
                                        );

                                        // Track start of authentication flow
                                        trackUserFlow(
                                          flowName: 'authentication',
                                          stepName: 'otp_request_started',
                                          status: 'started',
                                          additionalParams: {
                                            'phone_number':
                                                _phoneController.text,
                                          },
                                        );

                                        context.read<AuthCubit>().sendOtp(
                                          _phoneController.text,
                                          initialPhoneNumber:
                                              widget.initialPhoneNumber,
                                        );
                                      } else {
                                        // Track validation failure
                                        trackEvent(
                                          'form_validation_failed',
                                          params: {
                                            'screen': 'login',
                                            'field': 'phone_number',
                                            'phone_number_length':
                                                _phoneController.text.length
                                                    .toString(),
                                          },
                                        );
                                      }
                                    },
                          );
                        },
                      ),

                      BlocBuilder<ConnectivityCubit, ConnectivityState>(
                        builder: (context, connectivityState) {
                          if (connectivityState.status ==
                              ConnectionStatus.disconnected) {
                            return Padding(
                              padding: EdgeInsets.only(bottom: 16.0),
                              child: AquaText.body(
                                'No internet connection. You need to be online to login.',
                                color: acRedColor,
                                textAlign: TextAlign.center,
                              ),
                            );
                          }
                          return const SizedBox.shrink();
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
