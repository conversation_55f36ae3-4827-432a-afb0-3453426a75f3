import 'dart:async';
import '../../core/error/exceptions.dart';
import '../../core/network/network_info.dart';
import '../../core/utils/logger.dart';
import '../services/mongodb_service.dart';

class MongoDbConnectionManager {
  final NetworkInfo networkInfo;
  final AppLogger logger;
  final MongoDBService _mongoDBService;

  // Connection settings
  final Duration connectionTimeout = const Duration(seconds: 30);
  final Duration healthCheckInterval = const Duration(minutes: 5);

  // Health check timer
  Timer? _healthCheckTimer;

  // Connection state
  bool _isInitialized = false;

  MongoDbConnectionManager({
    required this.networkInfo,
    required this.logger,
    required MongoDBService mongoDBService,
  }) : _mongoDBService = mongoDBService {
    // Initialize health check timer
    _startHealthCheckTimer();
  }

  // Start a periodic health check to detect disconnections early
  void _startHealthCheckTimer() {
    _healthCheckTimer?.cancel();
    _healthCheckTimer = Timer.periodic(healthCheckInterval, (_) {
      _performHealthCheck();
    });
  }

  // Perform a health check on the MongoDB connection
  Future<void> _performHealthCheck() async {
    if (!_isInitialized) return;

    try {
      if (!_mongoDBService.isConnected) {
        logger.w(
          "Health check detected disconnected MongoDB connection, reconnecting...",
        );
        await _mongoDBService.connect();
      }
    } catch (e) {
      logger.w("Health check error: $e");
      // Don't throw, this is a background check
    }
  }

  Future<MongoDBService> getConnection() async {
    // Initialize the connection manager if not already done
    if (!_isInitialized) {
      _isInitialized = true;
    }

    // Check network connectivity
    if (!await networkInfo.isConnected) {
      logger.e("Cannot connect to MongoDB: No internet connection");
      throw ServerException();
    }

    try {
      // Ensure the MongoDB service is connected
      await _mongoDBService.ensureConnected();

      // Return the MongoDB service
      return _mongoDBService;
    } catch (e) {
      logger.e("Failed to connect to MongoDB", e);
      throw ServerException();
    }
  }

  Future<void> closeConnection() async {
    try {
      logger.i("Closing MongoDB connection...");
      await _mongoDBService.disconnect();
      logger.i("MongoDB connection closed successfully");
    } catch (e) {
      logger.e("Error closing MongoDB connection", e);
    }
  }

  // Dispose method to clean up resources
  void dispose() {
    _healthCheckTimer?.cancel();
    _healthCheckTimer = null;
  }
}
