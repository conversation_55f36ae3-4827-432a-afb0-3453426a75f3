import 'package:flutter_test/flutter_test.dart';
import 'package:aquapartner/data/models/customer_model.dart';
import 'package:aquapartner/domain/entities/customer.dart';

void main() {
  group('CustomerModel Tests', () {
    late CustomerModel testCustomerModel;
    late Customer testCustomerEntity;
    late Map<String, dynamic> testJson;

    setUp(() {
      testCustomerModel = CustomerModel(
        id: 'mongo_id_123',
        customerId: 'customer_123',
        customerName: '<PERSON>',
        email: '<EMAIL>',
        mobileNumber: '+919999999999',
        companyName: 'Aqua Farms Ltd',
        gstNo: '29ABCDE1234F1Z5',
        businessVertical: 'Aquaculture',
        customerCode: 'CUST001',
        billingAddress: '123 Main St, City, State 12345',
        isSynced: true,
      );

      testCustomerEntity = Customer(
        id: 'mongo_id_123',
        customerId: 'customer_123',
        customerName: '<PERSON>',
        email: '<EMAIL>',
        mobileNumber: '+919999999999',
        companyName: 'Aqua Farms Ltd',
        gstNo: '29ABCDE1234F1Z5',
        businessVertical: 'Aquaculture',
        customerCode: 'CUST001',
        billingAddress: '123 Main St, City, State 12345',
      );

      testJson = {
        '_id': 'mongo_id_123',
        'customerId': 'customer_123',
        'customerName': 'John Doe',
        'email': '<EMAIL>',
        'mobileNumber': '+919999999999',
        'companyName': 'Aqua Farms Ltd',
        'gstNo': '29ABCDE1234F1Z5',
        'businessVertical': 'Aquaculture',
        'customerCode': 'CUST001',
        'billingAddress': '123 Main St, City, State 12345',
      };
    });

    group('Constructor Tests', () {
      test('should create CustomerModel with all required fields', () {
        expect(testCustomerModel.id, equals('mongo_id_123'));
        expect(testCustomerModel.customerId, equals('customer_123'));
        expect(testCustomerModel.customerName, equals('John Doe'));
        expect(testCustomerModel.email, equals('<EMAIL>'));
        expect(testCustomerModel.mobileNumber, equals('+919999999999'));
        expect(testCustomerModel.companyName, equals('Aqua Farms Ltd'));
        expect(testCustomerModel.gstNo, equals('29ABCDE1234F1Z5'));
        expect(testCustomerModel.businessVertical, equals('Aquaculture'));
        expect(testCustomerModel.customerCode, equals('CUST001'));
        expect(
          testCustomerModel.billingAddress,
          equals('123 Main St, City, State 12345'),
        );
        expect(testCustomerModel.isSynced, isTrue);
      });

      test('should create CustomerModel with default isSynced value', () {
        final customerModel = CustomerModel(
          customerId: 'customer_456',
          customerName: 'Jane Smith',
          email: '<EMAIL>',
          mobileNumber: '+919876543211',
          companyName: 'Fish Farm Co',
          gstNo: '29FGHIJ5678K2L6',
          businessVertical: 'Fisheries',
          customerCode: 'CUST002',
          billingAddress: '456 Oak Ave, Town, State 67890',
        );

        expect(customerModel.isSynced, isFalse);
        expect(customerModel.id, isNull);
      });

      test('should create CustomerModel with null id', () {
        final customerModel = CustomerModel(
          id: null,
          customerId: 'customer_789',
          customerName: 'Bob Johnson',
          email: '<EMAIL>',
          mobileNumber: '+919876543212',
          companyName: 'Marine Solutions',
          gstNo: '29KLMNO9012P3Q7',
          businessVertical: 'Marine',
          customerCode: 'CUST003',
          billingAddress: '789 Pine Rd, Village, State 13579',
          isSynced: false,
        );

        expect(customerModel.id, isNull);
        expect(customerModel.customerId, equals('customer_789'));
        expect(customerModel.isSynced, isFalse);
      });
    });

    group('Entity Conversion Tests', () {
      test(
        'should convert from Customer entity to CustomerModel correctly',
        () {
          final customerModel = CustomerModel.fromEntity(testCustomerEntity);

          expect(customerModel.id, equals(testCustomerEntity.id));
          expect(
            customerModel.customerId,
            equals(testCustomerEntity.customerId),
          );
          expect(
            customerModel.customerName,
            equals(testCustomerEntity.customerName),
          );
          expect(customerModel.email, equals(testCustomerEntity.email));
          expect(
            customerModel.mobileNumber,
            equals(testCustomerEntity.mobileNumber),
          );
          expect(
            customerModel.companyName,
            equals(testCustomerEntity.companyName),
          );
          expect(customerModel.gstNo, equals(testCustomerEntity.gstNo));
          expect(
            customerModel.businessVertical,
            equals(testCustomerEntity.businessVertical),
          );
          expect(
            customerModel.customerCode,
            equals(testCustomerEntity.customerCode),
          );
          expect(
            customerModel.billingAddress,
            equals(testCustomerEntity.billingAddress),
          );
          expect(customerModel.isSynced, isFalse); // Default value
        },
      );

      test(
        'should convert from CustomerModel to Customer entity correctly',
        () {
          final customerEntity = testCustomerModel.toEntity();

          expect(customerEntity.id, equals(testCustomerModel.id));
          expect(
            customerEntity.customerId,
            equals(testCustomerModel.customerId),
          );
          expect(
            customerEntity.customerName,
            equals(testCustomerModel.customerName),
          );
          expect(customerEntity.email, equals(testCustomerModel.email));
          expect(
            customerEntity.mobileNumber,
            equals(testCustomerModel.mobileNumber),
          );
          expect(
            customerEntity.companyName,
            equals(testCustomerModel.companyName),
          );
          expect(customerEntity.gstNo, equals(testCustomerModel.gstNo));
          expect(
            customerEntity.businessVertical,
            equals(testCustomerModel.businessVertical),
          );
          expect(
            customerEntity.customerCode,
            equals(testCustomerModel.customerCode),
          );
          expect(
            customerEntity.billingAddress,
            equals(testCustomerModel.billingAddress),
          );
        },
      );

      test('should maintain data integrity during entity conversions', () {
        final convertedModel = CustomerModel.fromEntity(testCustomerEntity);
        final convertedEntity = convertedModel.toEntity();

        expect(convertedEntity.id, equals(testCustomerEntity.id));
        expect(
          convertedEntity.customerId,
          equals(testCustomerEntity.customerId),
        );
        expect(
          convertedEntity.customerName,
          equals(testCustomerEntity.customerName),
        );
        expect(convertedEntity.email, equals(testCustomerEntity.email));
        expect(
          convertedEntity.mobileNumber,
          equals(testCustomerEntity.mobileNumber),
        );
        expect(
          convertedEntity.companyName,
          equals(testCustomerEntity.companyName),
        );
        expect(convertedEntity.gstNo, equals(testCustomerEntity.gstNo));
        expect(
          convertedEntity.businessVertical,
          equals(testCustomerEntity.businessVertical),
        );
        expect(
          convertedEntity.customerCode,
          equals(testCustomerEntity.customerCode),
        );
        expect(
          convertedEntity.billingAddress,
          equals(testCustomerEntity.billingAddress),
        );
      });
    });

    group('JSON Serialization Tests', () {
      test('should convert CustomerModel to JSON correctly', () {
        final json = testCustomerModel.toJson();

        expect(json['_id'], equals('mongo_id_123'));
        expect(json['customerId'], equals('customer_123'));
        expect(json['customerName'], equals('John Doe'));
        expect(json['email'], equals('<EMAIL>'));
        expect(json['mobileNumber'], equals('+919999999999'));
        expect(json['companyName'], equals('Aqua Farms Ltd'));
        expect(json['gstNo'], equals('29ABCDE1234F1Z5'));
        expect(json['businessVertical'], equals('Aquaculture'));
        expect(json['customerCode'], equals('CUST001'));
        expect(
          json['billingAddress'],
          equals('123 Main St, City, State 12345'),
        );
      });

      test('should convert CustomerModel to JSON without id when null', () {
        final customerModelWithoutId = CustomerModel(
          customerId: 'customer_456',
          customerName: 'Jane Smith',
          email: '<EMAIL>',
          mobileNumber: '+919876543211',
          companyName: 'Fish Farm Co',
          gstNo: '29FGHIJ5678K2L6',
          businessVertical: 'Fisheries',
          customerCode: 'CUST002',
          billingAddress: '456 Oak Ave, Town, State 67890',
        );

        final json = customerModelWithoutId.toJson();

        expect(json.containsKey('_id'), isFalse);
        expect(json['customerId'], equals('customer_456'));
        expect(json['customerName'], equals('Jane Smith'));
      });

      test('should create CustomerModel from JSON correctly', () {
        final customerModel = CustomerModel.fromJson(testJson);

        expect(customerModel.id, equals('mongo_id_123'));
        expect(customerModel.customerId, equals('customer_123'));
        expect(customerModel.customerName, equals('John Doe'));
        expect(customerModel.email, equals('<EMAIL>'));
        expect(customerModel.mobileNumber, equals('+919999999999'));
        expect(customerModel.companyName, equals('Aqua Farms Ltd'));
        expect(customerModel.gstNo, equals('29ABCDE1234F1Z5'));
        expect(customerModel.businessVertical, equals('Aquaculture'));
        expect(customerModel.customerCode, equals('CUST001'));
        expect(
          customerModel.billingAddress,
          equals('123 Main St, City, State 12345'),
        );
        expect(customerModel.isSynced, isFalse); // Default value from JSON
      });

      test('should handle missing _id field in JSON', () {
        final jsonWithoutId = Map<String, dynamic>.from(testJson);
        jsonWithoutId.remove('_id');

        final customerModel = CustomerModel.fromJson(jsonWithoutId);

        expect(customerModel.id, isNull);
        expect(customerModel.customerId, equals('customer_123'));
        expect(customerModel.customerName, equals('John Doe'));
      });

      test('should maintain data integrity during JSON conversions', () {
        final json = testCustomerModel.toJson();
        final recreatedModel = CustomerModel.fromJson(json);

        expect(recreatedModel.id, equals(testCustomerModel.id));
        expect(recreatedModel.customerId, equals(testCustomerModel.customerId));
        expect(
          recreatedModel.customerName,
          equals(testCustomerModel.customerName),
        );
        expect(recreatedModel.email, equals(testCustomerModel.email));
        expect(
          recreatedModel.mobileNumber,
          equals(testCustomerModel.mobileNumber),
        );
        expect(
          recreatedModel.companyName,
          equals(testCustomerModel.companyName),
        );
        expect(recreatedModel.gstNo, equals(testCustomerModel.gstNo));
        expect(
          recreatedModel.businessVertical,
          equals(testCustomerModel.businessVertical),
        );
        expect(
          recreatedModel.customerCode,
          equals(testCustomerModel.customerCode),
        );
        expect(
          recreatedModel.billingAddress,
          equals(testCustomerModel.billingAddress),
        );
      });
    });

    group('CopyWith Tests', () {
      test('should create copy with updated fields', () {
        final updatedModel = testCustomerModel.copyWith(
          customerName: 'John Smith',
          email: '<EMAIL>',
          isSynced: false,
        );

        expect(updatedModel.id, equals(testCustomerModel.id));
        expect(updatedModel.customerId, equals(testCustomerModel.customerId));
        expect(updatedModel.customerName, equals('John Smith'));
        expect(updatedModel.email, equals('<EMAIL>'));
        expect(
          updatedModel.mobileNumber,
          equals(testCustomerModel.mobileNumber),
        );
        expect(updatedModel.companyName, equals(testCustomerModel.companyName));
        expect(updatedModel.gstNo, equals(testCustomerModel.gstNo));
        expect(
          updatedModel.businessVertical,
          equals(testCustomerModel.businessVertical),
        );
        expect(
          updatedModel.customerCode,
          equals(testCustomerModel.customerCode),
        );
        expect(
          updatedModel.billingAddress,
          equals(testCustomerModel.billingAddress),
        );
        expect(updatedModel.isSynced, isFalse);
      });

      test(
        'should create copy with same values when no parameters provided',
        () {
          final copiedModel = testCustomerModel.copyWith();

          expect(copiedModel.id, equals(testCustomerModel.id));
          expect(copiedModel.customerId, equals(testCustomerModel.customerId));
          expect(
            copiedModel.customerName,
            equals(testCustomerModel.customerName),
          );
          expect(copiedModel.email, equals(testCustomerModel.email));
          expect(
            copiedModel.mobileNumber,
            equals(testCustomerModel.mobileNumber),
          );
          expect(
            copiedModel.companyName,
            equals(testCustomerModel.companyName),
          );
          expect(copiedModel.gstNo, equals(testCustomerModel.gstNo));
          expect(
            copiedModel.businessVertical,
            equals(testCustomerModel.businessVertical),
          );
          expect(
            copiedModel.customerCode,
            equals(testCustomerModel.customerCode),
          );
          expect(
            copiedModel.billingAddress,
            equals(testCustomerModel.billingAddress),
          );
          expect(copiedModel.isSynced, equals(testCustomerModel.isSynced));
        },
      );

      test('should update id and sync status for database operations', () {
        final syncedModel = testCustomerModel.copyWith(
          id: 'new_mongo_id_456',
          isSynced: true,
        );

        expect(syncedModel.id, equals('new_mongo_id_456'));
        expect(syncedModel.isSynced, isTrue);
        expect(syncedModel.customerId, equals(testCustomerModel.customerId));
      });
    });

    group('Edge Cases and Validation', () {
      test('should handle empty string values', () {
        final customerModel = CustomerModel(
          customerId: '',
          customerName: '',
          email: '',
          mobileNumber: '',
          companyName: '',
          gstNo: '',
          businessVertical: '',
          customerCode: '',
          billingAddress: '',
        );

        expect(customerModel.customerId, equals(''));
        expect(customerModel.customerName, equals(''));
        expect(customerModel.email, equals(''));
        expect(customerModel.mobileNumber, equals(''));
        expect(customerModel.companyName, equals(''));
        expect(customerModel.gstNo, equals(''));
        expect(customerModel.businessVertical, equals(''));
        expect(customerModel.customerCode, equals(''));
        expect(customerModel.billingAddress, equals(''));
      });

      test('should handle very long string values', () {
        final longString = 'A' * 1000;
        final customerModel = CustomerModel(
          customerId: longString,
          customerName: longString,
          email: longString,
          mobileNumber: longString,
          companyName: longString,
          gstNo: longString,
          businessVertical: longString,
          customerCode: longString,
          billingAddress: longString,
        );

        expect(customerModel.customerId, equals(longString));
        expect(customerModel.customerName, equals(longString));
        expect(customerModel.email, equals(longString));
      });

      test('should handle special characters in fields', () {
        final customerModel = CustomerModel(
          customerId: 'customer@#\$%^&*()',
          customerName: 'John Döe',
          email: '<EMAIL>',
          mobileNumber: '+91-9876-543-210',
          companyName: 'Aqua & Fish Farms Ltd.',
          gstNo: '29ABCDE1234F1Z5',
          businessVertical: 'Aquaculture/Fisheries',
          customerCode: 'CUST-001',
          billingAddress: '123 Main St., Apt #456, City, State - 12345',
        );

        expect(customerModel.customerId, equals('customer@#\$%^&*()'));
        expect(customerModel.customerName, equals('John Döe'));
        expect(customerModel.email, equals('<EMAIL>'));
        expect(customerModel.mobileNumber, equals('+91-9876-543-210'));
        expect(customerModel.companyName, equals('Aqua & Fish Farms Ltd.'));
        expect(customerModel.businessVertical, equals('Aquaculture/Fisheries'));
        expect(customerModel.customerCode, equals('CUST-001'));
        expect(
          customerModel.billingAddress,
          equals('123 Main St., Apt #456, City, State - 12345'),
        );
      });

      test('should handle invalid GST number format', () {
        final customerModel = CustomerModel(
          customerId: 'customer_123',
          customerName: 'John Doe',
          email: '<EMAIL>',
          mobileNumber: '+919999999999',
          companyName: 'Aqua Farms Ltd',
          gstNo: 'INVALID_GST_123',
          businessVertical: 'Aquaculture',
          customerCode: 'CUST001',
          billingAddress: '123 Main St, City, State 12345',
        );

        expect(customerModel.gstNo, equals('INVALID_GST_123'));
      });

      test('should handle invalid email format', () {
        final customerModel = CustomerModel(
          customerId: 'customer_123',
          customerName: 'John Doe',
          email: 'invalid-email-format',
          mobileNumber: '+919999999999',
          companyName: 'Aqua Farms Ltd',
          gstNo: '29ABCDE1234F1Z5',
          businessVertical: 'Aquaculture',
          customerCode: 'CUST001',
          billingAddress: '123 Main St, City, State 12345',
        );

        expect(customerModel.email, equals('invalid-email-format'));
      });

      test('should handle invalid phone number format', () {
        final customerModel = CustomerModel(
          customerId: 'customer_123',
          customerName: 'John Doe',
          email: '<EMAIL>',
          mobileNumber: 'invalid-phone-123',
          companyName: 'Aqua Farms Ltd',
          gstNo: '29ABCDE1234F1Z5',
          businessVertical: 'Aquaculture',
          customerCode: 'CUST001',
          billingAddress: '123 Main St, City, State 12345',
        );

        expect(customerModel.mobileNumber, equals('invalid-phone-123'));
      });
    });
  });
}
