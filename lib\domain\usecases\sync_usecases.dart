import 'package:dartz/dartz.dart';
import '../../core/network/network_info.dart';
import '../../core/utils/logger.dart';
import '../repositories/user_repository.dart';
import '../../core/error/failures.dart';
import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;

class SyncUserUseCase {
  final UserRepository repository;
  final NetworkInfo networkInfo;
  final AppLogger logger;

  SyncUserUseCase({
    required this.repository,
    required this.networkInfo,
    required this.logger,
  });

  Future<Either<Failure, void>> call() async {
    try {
      if (!await networkInfo.isConnected) {
        logger.i("No internet connection, skipping sync");
        return const Right(null);
      }

      return repository.syncUser();
    } catch (e) {
      logger.e("Error in SyncUserUseCase", e);
      return Left(ServerFailure());
    }
  }
}

class GetSyncStatusUseCase {
  final UserRepository repository;
  final AppLogger logger;

  GetSyncStatusUseCase({required this.repository, required this.logger});

  Stream<bool> call() {
    try {
      logger.i("Getting sync status stream");
      return repository.getSyncStatus();
    } catch (e) {
      logger.e("Error setting up sync status stream", e);
      // Return an empty stream with an error
      return Stream.error(ServerFailure());
    }
  }
}

class CheckAuthStatusUseCase {
  final UserRepository repository;
  final AppLogger logger;

  CheckAuthStatusUseCase({required this.repository, required this.logger});

  Future<Either<Failure, bool>> call() async {
    try {
      logger.i("Checking authentication status");

      final hasUserResult = await repository.hasUser();

      if (hasUserResult.isLeft()) {
        return hasUserResult.fold(
          (failure) => Left(failure),
          (_) => const Right(false), // This line should never be reached
        );
      }

      final hasUser = hasUserResult.getOrElse(() => false);

      if (!hasUser) {
        logger.i("No user found, not authenticated");
        return const Right(false);
      }

      // If a user exists, get the user to check if they're verified
      final userResult = await repository.getUser();

      return userResult.fold(
        (failure) {
          logger.e("Error getting user", failure);
          return Left(failure);
        },
        (user) {
          final isAuthenticated = user.isVerified;
          logger.i("User found, authenticated: $isAuthenticated");
          return Right(isAuthenticated);
        },
      );
    } catch (e) {
      logger.e("Unexpected error in CheckAuthStatusUseCase", e);
      return Left(ServerFailure());
    }
  }

  Future<firebase_auth.User?> getCurrentUser() async {
    try {
      return firebase_auth.FirebaseAuth.instance.currentUser;
    } catch (e) {
      logger.e("Error getting current Firebase user", e);
      return null;
    }
  }
}
