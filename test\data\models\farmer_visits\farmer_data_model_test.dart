import 'package:flutter_test/flutter_test.dart';
import 'package:aquapartner/data/models/farmer_visits/farmer_data_model.dart';
import 'package:aquapartner/data/models/farmer_visits/visit_model.dart';
import 'package:aquapartner/domain/entities/farmer_visit/farmer.dart';
import 'package:aquapartner/domain/entities/farmer_visit/visit.dart';

void main() {
  group('FarmerDataModel Tests', () {
    late FarmerDataModel testFarmerDataModel;
    late FarmerModel testFarmerModel;
    late VisitModel testVisitModel;
    late Map<String, dynamic> testJson;
    late DateTime testDateTime;

    setUp(() {
      testDateTime = DateTime(2024, 1, 15, 10, 30, 0);

      testVisitModel = VisitModel(
        createdDateTime: testDateTime,
        doc: 30,
        pondId: 'POND001',
        mobileNumber: '+919999999999',
        productUsed: 'Test Product',
        farmerId: 'farmer_123',
        farmerName: 'Test Farmer',
      );

      testFarmerModel = FarmerModel(
        visits: [testVisitModel],
        name: 'Test Farmer',
      );

      testFarmerDataModel = FarmerDataModel(results: [testFarmerModel]);

      testJson = {
        'results': [
          {
            'name': 'Test Farmer',
            'visits': [
              {
                'createdDateTime': testDateTime.toIso8601String(),
                'doc': 30,
                'pondId': 'POND001',
                'mobileNumber': '+919999999999',
                'productUsed': 'Test Product',
                'farmerId': 'farmer_123',
                'farmerName': 'Test Farmer',
              },
            ],
          },
        ],
      };
    });

    group('Constructor Tests', () {
      test('should create FarmerDataModel with all required fields', () {
        expect(testFarmerDataModel.results, hasLength(1));
        expect(testFarmerDataModel.results.first, isA<FarmerModel>());
        expect(testFarmerDataModel.results.first.name, equals('Test Farmer'));
        expect(testFarmerDataModel.results.first.visits, hasLength(1));
        expect(
          testFarmerDataModel.results.first.visits.first,
          isA<VisitModel>(),
        );
      });

      test('should create FarmerDataModel with empty results', () {
        final emptyFarmerDataModel = FarmerDataModel(results: []);

        expect(emptyFarmerDataModel.results, isEmpty);
      });

      test('should create FarmerDataModel with multiple farmers', () {
        final multipleFarmersModel = FarmerDataModel(
          results: [testFarmerModel, testFarmerModel],
        );

        expect(multipleFarmersModel.results, hasLength(2));
        expect(multipleFarmersModel.results[0].name, equals('Test Farmer'));
        expect(multipleFarmersModel.results[1].name, equals('Test Farmer'));
      });
    });

    group('JSON Serialization Tests', () {
      test('should convert FarmerDataModel to JSON correctly', () {
        final json = testFarmerDataModel.toJson();

        expect(json['results'], isA<List>());
        expect(json['results'], hasLength(1));
        expect(json['results'][0]['name'], equals('Test Farmer'));
        expect(json['results'][0]['visits'], isA<List>());
        expect(json['results'][0]['visits'], hasLength(1));
        expect(
          json['results'][0]['visits'][0]['createdDateTime'],
          equals(testDateTime.toIso8601String()),
        );
        expect(json['results'][0]['visits'][0]['doc'], equals(30));
        expect(json['results'][0]['visits'][0]['pondId'], equals('POND001'));
        expect(
          json['results'][0]['visits'][0]['mobileNumber'],
          equals('+919999999999'),
        );
        expect(
          json['results'][0]['visits'][0]['productUsed'],
          equals('Test Product'),
        );
        expect(
          json['results'][0]['visits'][0]['farmerId'],
          equals('farmer_123'),
        );
        expect(
          json['results'][0]['visits'][0]['farmerName'],
          equals('Test Farmer'),
        );
      });

      test('should create FarmerDataModel from JSON correctly', () {
        final farmerDataModel = FarmerDataModel.fromJson(testJson);

        expect(farmerDataModel.results, hasLength(1));
        expect(farmerDataModel.results.first.name, equals('Test Farmer'));
        expect(farmerDataModel.results.first.visits, hasLength(1));
        expect(
          farmerDataModel.results.first.visits.first.createdDateTime,
          equals(testDateTime),
        );
        expect(farmerDataModel.results.first.visits.first.doc, equals(30));
        expect(
          farmerDataModel.results.first.visits.first.pondId,
          equals('POND001'),
        );
        expect(
          farmerDataModel.results.first.visits.first.mobileNumber,
          equals('+919999999999'),
        );
        expect(
          farmerDataModel.results.first.visits.first.productUsed,
          equals('Test Product'),
        );
        expect(farmerDataModel.results.first.visits.first.farmerId, equals(''));
        expect(
          farmerDataModel.results.first.visits.first.farmerName,
          equals('Test Farmer'),
        );
      });

      test('should handle empty results array in JSON', () {
        final emptyJson = {'results': []};

        final farmerDataModel = FarmerDataModel.fromJson(emptyJson);

        expect(farmerDataModel.results, isEmpty);
      });

      test('should handle round-trip JSON serialization', () {
        final originalJson = testFarmerDataModel.toJson();
        final recreatedModel = FarmerDataModel.fromJson(originalJson);
        final finalJson = recreatedModel.toJson();

        expect(
          finalJson['results'].length,
          equals(originalJson['results'].length),
        );
        expect(
          finalJson['results'][0]['name'],
          equals(originalJson['results'][0]['name']),
        );
        expect(
          finalJson['results'][0]['visits'].length,
          equals(originalJson['results'][0]['visits'].length),
        );
      });
    });

    group('Domain Entity Conversion Tests', () {
      test(
        'should convert FarmerDataModel to list of Farmer entities correctly',
        () {
          final farmers = testFarmerDataModel.toDomain();

          expect(farmers, hasLength(1));
          expect(farmers.first, isA<Farmer>());
          expect(farmers.first.name, equals('Test Farmer'));
          expect(farmers.first.mobileNumber, equals('+919999999999'));
          expect(farmers.first.visits, hasLength(1));
          expect(farmers.first.visits.first, isA<Visit>());
          expect(
            farmers.first.visits.first.createdDateTime,
            equals(testDateTime),
          );
          expect(farmers.first.visits.first.doc, equals(30));
          expect(farmers.first.visits.first.pondId, equals('POND001'));
          expect(
            farmers.first.visits.first.mobileNumber,
            equals('+919999999999'),
          );
          expect(
            farmers.first.visits.first.productUsed,
            equals('Test Product'),
          );
          expect(farmers.first.visits.first.farmerId, equals('farmer_123'));
          expect(farmers.first.visits.first.farmerName, equals('Test Farmer'));
        },
      );

      test(
        'should create FarmerDataModel from list of Farmer entities correctly',
        () {
          final farmers = [
            Farmer(
              id: 1,
              name: 'Domain Farmer',
              mobileNumber: '+919876543211',
              visits: [
                Visit(
                  id: 1,
                  createdDateTime: testDateTime,
                  doc: 45,
                  pondId: 'POND002',
                  mobileNumber: '+919876543211',
                  productUsed: 'Domain Product',
                  farmerId: 'farmer_456',
                  farmerName: 'Domain Farmer',
                ),
              ],
            ),
          ];

          final farmerDataModel = FarmerDataModel.fromDomain(farmers);

          expect(farmerDataModel.results, hasLength(1));
          expect(farmerDataModel.results.first.name, equals('Domain Farmer'));
          expect(farmerDataModel.results.first.visits, hasLength(1));
          expect(
            farmerDataModel.results.first.visits.first.createdDateTime,
            equals(testDateTime),
          );
          expect(farmerDataModel.results.first.visits.first.doc, equals(45));
          expect(
            farmerDataModel.results.first.visits.first.pondId,
            equals('POND002'),
          );
          expect(
            farmerDataModel.results.first.visits.first.mobileNumber,
            equals('+919876543211'),
          );
          expect(
            farmerDataModel.results.first.visits.first.productUsed,
            equals('Domain Product'),
          );
          expect(
            farmerDataModel.results.first.visits.first.farmerId,
            equals('farmer_456'),
          );
          expect(
            farmerDataModel.results.first.visits.first.farmerName,
            equals('Domain Farmer'),
          );
        },
      );

      test('should maintain data integrity during domain conversion', () {
        final originalFarmers = testFarmerDataModel.toDomain();
        final backToModel = FarmerDataModel.fromDomain(originalFarmers);
        final finalFarmers = backToModel.toDomain();

        expect(finalFarmers.length, equals(originalFarmers.length));
        expect(finalFarmers.first.name, equals(originalFarmers.first.name));
        expect(
          finalFarmers.first.visits.length,
          equals(originalFarmers.first.visits.length),
        );
        expect(
          finalFarmers.first.visits.first.doc,
          equals(originalFarmers.first.visits.first.doc),
        );
        expect(
          finalFarmers.first.visits.first.pondId,
          equals(originalFarmers.first.visits.first.pondId),
        );
      });
    });

    group('Edge Cases and Validation Tests', () {
      test('should handle special characters in farmer names', () {
        final specialCharJson = {
          'results': [
            {
              'name': 'Test Farmer & Co. (Special)',
              'visits': [
                {
                  'createdDateTime': testDateTime.toIso8601String(),
                  'doc': 30,
                  'pondId': 'POND001',
                  'mobileNumber': '+919999999999',
                  'productUsed': 'Test Product',
                },
              ],
            },
          ],
        };

        final farmerDataModel = FarmerDataModel.fromJson(specialCharJson);

        expect(
          farmerDataModel.results.first.name,
          equals('Test Farmer & Co. (Special)'),
        );
      });

      test('should handle farmers with no visits', () {
        final noVisitsJson = {
          'results': [
            {'name': 'Farmer Without Visits', 'visits': []},
          ],
        };

        final farmerDataModel = FarmerDataModel.fromJson(noVisitsJson);

        expect(
          farmerDataModel.results.first.name,
          equals('Farmer Without Visits'),
        );
        expect(farmerDataModel.results.first.visits, isEmpty);
      });

      test('should handle farmers with multiple visits', () {
        final multipleVisitsJson = {
          'results': [
            {
              'name': 'Farmer With Multiple Visits',
              'visits': [
                {
                  'createdDateTime': testDateTime.toIso8601String(),
                  'doc': 30,
                  'pondId': 'POND001',
                  'mobileNumber': '+919999999999',
                  'productUsed': 'Product 1',
                },
                {
                  'createdDateTime':
                      testDateTime.add(Duration(days: 1)).toIso8601String(),
                  'doc': 45,
                  'pondId': 'POND002',
                  'mobileNumber': '+919999999999',
                  'productUsed': 'Product 2',
                },
              ],
            },
          ],
        };

        final farmerDataModel = FarmerDataModel.fromJson(multipleVisitsJson);

        expect(farmerDataModel.results.first.visits, hasLength(2));
        expect(
          farmerDataModel.results.first.visits[0].productUsed,
          equals('Product 1'),
        );
        expect(
          farmerDataModel.results.first.visits[1].productUsed,
          equals('Product 2'),
        );
      });

      test('should handle unicode characters in farmer names', () {
        final unicodeJson = {
          'results': [
            {
              'name': 'Test Farmer 测试农民',
              'visits': [
                {
                  'createdDateTime': testDateTime.toIso8601String(),
                  'doc': 30,
                  'pondId': 'POND001',
                  'mobileNumber': '+919999999999',
                  'productUsed': 'Test Product 测试产品',
                },
              ],
            },
          ],
        };

        final farmerDataModel = FarmerDataModel.fromJson(unicodeJson);

        expect(farmerDataModel.results.first.name, equals('Test Farmer 测试农民'));
        expect(
          farmerDataModel.results.first.visits.first.productUsed,
          equals('Test Product 测试产品'),
        );
      });

      test('should handle very long farmer names', () {
        final longName = 'A' * 1000;
        final longNameJson = {
          'results': [
            {
              'name': longName,
              'visits': [
                {
                  'createdDateTime': testDateTime.toIso8601String(),
                  'doc': 30,
                  'pondId': 'POND001',
                  'mobileNumber': '+919999999999',
                  'productUsed': 'Test Product',
                },
              ],
            },
          ],
        };

        final farmerDataModel = FarmerDataModel.fromJson(longNameJson);

        expect(farmerDataModel.results.first.name.length, equals(1000));
      });
    });
  });

  group('FarmerModel Tests', () {
    late FarmerModel testFarmerModel;
    late VisitModel testVisitModel;
    late DateTime testDateTime;

    setUp(() {
      testDateTime = DateTime(2024, 1, 15, 10, 30, 0);

      testVisitModel = VisitModel(
        createdDateTime: testDateTime,
        doc: 30,
        pondId: 'POND001',
        mobileNumber: '+919999999999',
        productUsed: 'Test Product',
        farmerId: 'farmer_123',
        farmerName: 'Test Farmer',
      );

      testFarmerModel = FarmerModel(
        visits: [testVisitModel],
        name: 'Test Farmer',
      );
    });

    group('Constructor Tests', () {
      test('should create FarmerModel with all required fields', () {
        expect(testFarmerModel.name, equals('Test Farmer'));
        expect(testFarmerModel.visits, hasLength(1));
        expect(testFarmerModel.visits.first, isA<VisitModel>());
      });

      test('should create FarmerModel with empty visits', () {
        final emptyVisitsFarmer = FarmerModel(
          visits: [],
          name: 'Farmer Without Visits',
        );

        expect(emptyVisitsFarmer.name, equals('Farmer Without Visits'));
        expect(emptyVisitsFarmer.visits, isEmpty);
      });
    });

    group('JSON Serialization Tests', () {
      test('should convert FarmerModel to JSON correctly', () {
        final json = testFarmerModel.toJson();

        expect(json['name'], equals('Test Farmer'));
        expect(json['visits'], isA<List>());
        expect(json['visits'], hasLength(1));
        expect(
          json['visits'][0]['createdDateTime'],
          equals(testDateTime.toIso8601String()),
        );
        expect(json['visits'][0]['doc'], equals(30));
        expect(json['visits'][0]['pondId'], equals('POND001'));
        expect(json['visits'][0]['mobileNumber'], equals('+919999999999'));
        expect(json['visits'][0]['productUsed'], equals('Test Product'));
        expect(json['visits'][0]['farmerId'], equals('farmer_123'));
        expect(json['visits'][0]['farmerName'], equals('Test Farmer'));
      });

      test('should create FarmerModel from JSON correctly', () {
        final json = {
          'name': 'JSON Farmer',
          'visits': [
            {
              'createdDateTime': testDateTime.toIso8601String(),
              'doc': 45,
              'pondId': 'POND002',
              'mobileNumber': '+919876543211',
              'productUsed': 'JSON Product',
            },
          ],
        };

        final farmerModel = FarmerModel.fromJson(json);

        expect(farmerModel.name, equals('JSON Farmer'));
        expect(farmerModel.visits, hasLength(1));
        expect(farmerModel.visits.first.createdDateTime, equals(testDateTime));
        expect(farmerModel.visits.first.doc, equals(45));
        expect(farmerModel.visits.first.pondId, equals('POND002'));
        expect(farmerModel.visits.first.mobileNumber, equals('+919876543211'));
        expect(farmerModel.visits.first.productUsed, equals('JSON Product'));
        expect(farmerModel.visits.first.farmerId, equals(''));
        expect(farmerModel.visits.first.farmerName, equals('JSON Farmer'));
      });

      test('should handle round-trip JSON serialization', () {
        final originalJson = testFarmerModel.toJson();
        final recreatedModel = FarmerModel.fromJson(originalJson);
        final finalJson = recreatedModel.toJson();

        expect(finalJson['name'], equals(originalJson['name']));
        expect(
          finalJson['visits'].length,
          equals(originalJson['visits'].length),
        );
      });
    });

    group('Domain Entity Conversion Tests', () {
      test('should convert FarmerModel to Farmer entity correctly', () {
        final farmerEntity = testFarmerModel.toDomain();

        expect(farmerEntity, isA<Farmer>());
        expect(farmerEntity.id, isNull);
        expect(farmerEntity.name, equals('Test Farmer'));
        expect(farmerEntity.mobileNumber, equals('+919999999999'));
        expect(farmerEntity.visits, hasLength(1));
        expect(farmerEntity.visits.first, isA<Visit>());
        expect(farmerEntity.visits.first.createdDateTime, equals(testDateTime));
        expect(farmerEntity.visits.first.doc, equals(30));
        expect(farmerEntity.visits.first.pondId, equals('POND001'));
        expect(farmerEntity.visits.first.mobileNumber, equals('+919999999999'));
        expect(farmerEntity.visits.first.productUsed, equals('Test Product'));
        expect(farmerEntity.visits.first.farmerId, equals('farmer_123'));
        expect(farmerEntity.visits.first.farmerName, equals('Test Farmer'));
      });

      test('should create FarmerModel from Farmer entity correctly', () {
        final farmerEntity = Farmer(
          id: 1,
          name: 'Domain Farmer',
          mobileNumber: '+919876543211',
          visits: [
            Visit(
              id: 1,
              createdDateTime: testDateTime,
              doc: 45,
              pondId: 'POND002',
              mobileNumber: '+919876543211',
              productUsed: 'Domain Product',
              farmerId: 'farmer_456',
              farmerName: 'Domain Farmer',
            ),
          ],
        );

        final farmerModel = FarmerModel.fromDomain(farmerEntity);

        expect(farmerModel.name, equals('Domain Farmer'));
        expect(farmerModel.visits, hasLength(1));
        expect(farmerModel.visits.first.createdDateTime, equals(testDateTime));
        expect(farmerModel.visits.first.doc, equals(45));
        expect(farmerModel.visits.first.pondId, equals('POND002'));
        expect(farmerModel.visits.first.mobileNumber, equals('+919876543211'));
        expect(farmerModel.visits.first.productUsed, equals('Domain Product'));
        expect(farmerModel.visits.first.farmerId, equals('farmer_456'));
        expect(farmerModel.visits.first.farmerName, equals('Domain Farmer'));
      });

      test('should handle farmer with no visits during domain conversion', () {
        final noVisitsFarmer = FarmerModel(
          visits: [],
          name: 'No Visits Farmer',
        );

        final farmerEntity = noVisitsFarmer.toDomain();

        expect(farmerEntity.name, equals('No Visits Farmer'));
        expect(farmerEntity.mobileNumber, isEmpty);
        expect(farmerEntity.visits, isEmpty);
      });

      test('should maintain data integrity during domain conversion', () {
        final originalEntity = testFarmerModel.toDomain();
        final backToModel = FarmerModel.fromDomain(originalEntity);
        final finalEntity = backToModel.toDomain();

        expect(finalEntity.name, equals(originalEntity.name));
        expect(finalEntity.mobileNumber, equals(originalEntity.mobileNumber));
        expect(finalEntity.visits.length, equals(originalEntity.visits.length));
        expect(
          finalEntity.visits.first.doc,
          equals(originalEntity.visits.first.doc),
        );
        expect(
          finalEntity.visits.first.pondId,
          equals(originalEntity.visits.first.pondId),
        );
      });
    });
  });
}
