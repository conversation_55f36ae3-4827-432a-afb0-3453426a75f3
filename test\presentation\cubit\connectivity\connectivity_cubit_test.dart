import 'dart:async';

import 'package:aquapartner/core/utils/logger.dart';
import 'package:aquapartner/presentation/cubit/connectivity/connectivity_cubit.dart';
import 'package:aquapartner/presentation/cubit/connectivity/connectivity_state.dart';
import 'package:bloc_test/bloc_test.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// Mock classes
class MockConnectivity extends Mock implements Connectivity {}

class MockAppLogger extends Mock implements AppLogger {}

void main() {
  group('ConnectivityCubit Tests', () {
    late ConnectivityCubit connectivityCubit;
    late MockConnectivity mockConnectivity;
    late MockAppLogger mockLogger;
    late StreamController<List<ConnectivityResult>> connectivityController;

    setUp(() {
      mockConnectivity = MockConnectivity();
      mockLogger = MockAppLogger();
      connectivityController =
          StreamController<List<ConnectivityResult>>.broadcast();

      // Setup default mock behaviors
      when(
        () => mockConnectivity.onConnectivityChanged,
      ).thenAnswer((_) => connectivityController.stream);
      when(
        () => mockConnectivity.checkConnectivity(),
      ).thenAnswer((_) async => [ConnectivityResult.none]);

      connectivityCubit = ConnectivityCubit(
        connectivity: mockConnectivity,
        logger: mockLogger,
      );
    });

    tearDown(() {
      connectivityController.close();
      connectivityCubit.close();
    });

    group('Initial State', () {
      test('should have initial state as disconnected', () {
        expect(connectivityCubit.state, isA<ConnectivityState>());
        expect(
          connectivityCubit.state.status,
          equals(ConnectionStatus.disconnected),
        );
      });

      test('should initialize connectivity on creation', () async {
        // Wait for initialization to complete
        await Future.delayed(Duration(milliseconds: 100));

        verify(() => mockConnectivity.checkConnectivity()).called(1);
      });
    });

    group('Connectivity State Changes', () {
      blocTest<ConnectivityCubit, ConnectivityState>(
        'should emit connected state when wifi connection is available',
        build: () {
          when(
            () => mockConnectivity.checkConnectivity(),
          ).thenAnswer((_) async => [ConnectivityResult.wifi]);
          return ConnectivityCubit(
            connectivity: mockConnectivity,
            logger: mockLogger,
          );
        },
        act: (cubit) async {
          // Wait for initialization
          await Future.delayed(Duration(milliseconds: 100));
        },
        expect:
            () => [
              predicate<ConnectivityState>(
                (state) => state.status == ConnectionStatus.connected,
              ),
            ],
      );

      blocTest<ConnectivityCubit, ConnectivityState>(
        'should emit connected state when mobile connection is available',
        build: () {
          when(
            () => mockConnectivity.checkConnectivity(),
          ).thenAnswer((_) async => [ConnectivityResult.mobile]);
          return ConnectivityCubit(
            connectivity: mockConnectivity,
            logger: mockLogger,
          );
        },
        act: (cubit) async {
          await Future.delayed(Duration(milliseconds: 100));
        },
        expect:
            () => [
              predicate<ConnectivityState>(
                (state) => state.status == ConnectionStatus.connected,
              ),
            ],
      );

      blocTest<ConnectivityCubit, ConnectivityState>(
        'should emit connected state when ethernet connection is available',
        build: () {
          when(
            () => mockConnectivity.checkConnectivity(),
          ).thenAnswer((_) async => [ConnectivityResult.ethernet]);
          return ConnectivityCubit(
            connectivity: mockConnectivity,
            logger: mockLogger,
          );
        },
        act: (cubit) async {
          await Future.delayed(Duration(milliseconds: 100));
        },
        expect:
            () => [
              predicate<ConnectivityState>(
                (state) => state.status == ConnectionStatus.connected,
              ),
            ],
      );

      blocTest<ConnectivityCubit, ConnectivityState>(
        'should emit disconnected state when no connection is available',
        build: () {
          when(
            () => mockConnectivity.checkConnectivity(),
          ).thenAnswer((_) async => [ConnectivityResult.none]);
          return ConnectivityCubit(
            connectivity: mockConnectivity,
            logger: mockLogger,
          );
        },
        act: (cubit) async {
          await Future.delayed(Duration(milliseconds: 100));
        },
        expect:
            () => [
              predicate<ConnectivityState>(
                (state) => state.status == ConnectionStatus.disconnected,
              ),
            ],
      );

      blocTest<ConnectivityCubit, ConnectivityState>(
        'should emit connected state when multiple connections are available',
        build: () {
          when(() => mockConnectivity.checkConnectivity()).thenAnswer(
            (_) async => [ConnectivityResult.wifi, ConnectivityResult.mobile],
          );
          return ConnectivityCubit(
            connectivity: mockConnectivity,
            logger: mockLogger,
          );
        },
        act: (cubit) async {
          await Future.delayed(Duration(milliseconds: 100));
        },
        expect:
            () => [
              predicate<ConnectivityState>(
                (state) => state.status == ConnectionStatus.connected,
              ),
            ],
      );
    });

    group('Stream Subscription', () {
      blocTest<ConnectivityCubit, ConnectivityState>(
        'should listen to connectivity changes and update state accordingly',
        build: () => connectivityCubit,
        act: (cubit) async {
          // Simulate connectivity changes
          connectivityController.add([ConnectivityResult.wifi]);
          await Future.delayed(Duration(milliseconds: 50));

          connectivityController.add([ConnectivityResult.none]);
          await Future.delayed(Duration(milliseconds: 50));

          connectivityController.add([ConnectivityResult.mobile]);
          await Future.delayed(Duration(milliseconds: 50));
        },
        expect:
            () => [
              predicate<ConnectivityState>(
                (state) => state.status == ConnectionStatus.connected,
              ),
              predicate<ConnectivityState>(
                (state) => state.status == ConnectionStatus.disconnected,
              ),
              predicate<ConnectivityState>(
                (state) => state.status == ConnectionStatus.connected,
              ),
            ],
      );

      blocTest<ConnectivityCubit, ConnectivityState>(
        'should handle rapid connectivity changes',
        build: () => connectivityCubit,
        act: (cubit) async {
          // Simulate rapid changes with delays to ensure proper state emission
          connectivityController.add([ConnectivityResult.wifi]);
          await Future.delayed(Duration(milliseconds: 10));
          connectivityController.add([ConnectivityResult.none]);
          await Future.delayed(Duration(milliseconds: 10));
          connectivityController.add([ConnectivityResult.mobile]);
          await Future.delayed(Duration(milliseconds: 10));
        },
        expect:
            () => [
              predicate<ConnectivityState>(
                (state) => state.status == ConnectionStatus.connected,
              ),
              predicate<ConnectivityState>(
                (state) => state.status == ConnectionStatus.disconnected,
              ),
              predicate<ConnectivityState>(
                (state) => state.status == ConnectionStatus.connected,
              ),
            ],
      );
    });

    group('Error Handling', () {
      test('should handle connectivity check errors gracefully', () async {
        when(
          () => mockConnectivity.checkConnectivity(),
        ).thenThrow(Exception('Connectivity check failed'));

        final cubit = ConnectivityCubit(
          connectivity: mockConnectivity,
          logger: mockLogger,
        );

        // Wait for initialization to complete
        await Future.delayed(Duration(milliseconds: 100));

        // Should emit initial state when error occurs
        expect(cubit.state.status, equals(ConnectionStatus.disconnected));

        await cubit.close();
      });

      test('should handle stream errors without crashing', () async {
        // Create a cubit that will receive stream errors
        final errorController = StreamController<List<ConnectivityResult>>();
        when(
          () => mockConnectivity.onConnectivityChanged,
        ).thenAnswer((_) => errorController.stream);
        when(
          () => mockConnectivity.checkConnectivity(),
        ).thenAnswer((_) async => [ConnectivityResult.none]);

        final cubit = ConnectivityCubit(
          connectivity: mockConnectivity,
          logger: mockLogger,
        );

        // Wait for initialization
        await Future.delayed(Duration(milliseconds: 50));

        // Cubit should still be functional
        expect(cubit.state, isA<ConnectivityState>());

        await errorController.close();
        await cubit.close();
      });
    });

    group('State Management', () {
      test('should create equal states with same connection status', () {
        final state1 = ConnectivityState(status: ConnectionStatus.connected);
        final state2 = ConnectivityState(status: ConnectionStatus.connected);

        expect(state1, equals(state2));
      });

      test(
        'should create different states with different connection status',
        () {
          final state1 = ConnectivityState(status: ConnectionStatus.connected);
          final state2 = ConnectivityState(
            status: ConnectionStatus.disconnected,
          );

          expect(state1, isNot(equals(state2)));
        },
      );

      test('should create new state with copyWith method', () {
        final initialState = ConnectivityState(
          status: ConnectionStatus.disconnected,
        );
        final newState = initialState.copyWith(
          status: ConnectionStatus.connected,
        );

        expect(newState.status, equals(ConnectionStatus.connected));
        expect(initialState.status, equals(ConnectionStatus.disconnected));
      });

      test('should create initial state correctly', () {
        final initialState = ConnectivityState.initial();
        expect(initialState.status, equals(ConnectionStatus.disconnected));
      });
    });

    group('Resource Management', () {
      test('should cancel stream subscription when closed', () async {
        final testController = StreamController<List<ConnectivityResult>>();
        when(
          () => mockConnectivity.onConnectivityChanged,
        ).thenAnswer((_) => testController.stream);
        when(
          () => mockConnectivity.checkConnectivity(),
        ).thenAnswer((_) async => [ConnectivityResult.none]);

        final cubit = ConnectivityCubit(
          connectivity: mockConnectivity,
          logger: mockLogger,
        );

        // Wait for initialization
        await Future.delayed(Duration(milliseconds: 50));

        // Close the cubit
        await cubit.close();

        // Verify that the cubit is closed
        expect(cubit.isClosed, isTrue);

        await testController.close();
      });

      test('should handle multiple close calls gracefully', () async {
        final testController = StreamController<List<ConnectivityResult>>();
        when(
          () => mockConnectivity.onConnectivityChanged,
        ).thenAnswer((_) => testController.stream);
        when(
          () => mockConnectivity.checkConnectivity(),
        ).thenAnswer((_) async => [ConnectivityResult.none]);

        final cubit = ConnectivityCubit(
          connectivity: mockConnectivity,
          logger: mockLogger,
        );

        // Wait for initialization
        await Future.delayed(Duration(milliseconds: 50));

        // Close multiple times
        await cubit.close();
        await cubit.close();
        await cubit.close();

        expect(cubit.isClosed, isTrue);

        await testController.close();
      });
    });

    group('Edge Cases', () {
      test('should handle empty connectivity results list', () async {
        connectivityController.add([]);
        await Future.delayed(Duration(milliseconds: 50));

        // Empty list should be treated as no connection
        expect(
          connectivityCubit.state.status,
          equals(ConnectionStatus.disconnected),
        );
      });

      blocTest<ConnectivityCubit, ConnectivityState>(
        'should handle bluetooth connectivity result',
        build: () => connectivityCubit,
        act: (cubit) async {
          connectivityController.add([ConnectivityResult.bluetooth]);
          await Future.delayed(Duration(milliseconds: 50));
        },
        expect:
            () => [
              predicate<ConnectivityState>(
                (state) => state.status == ConnectionStatus.connected,
              ),
            ],
      );

      blocTest<ConnectivityCubit, ConnectivityState>(
        'should handle VPN connectivity result',
        build: () => connectivityCubit,
        act: (cubit) async {
          connectivityController.add([ConnectivityResult.vpn]);
          await Future.delayed(Duration(milliseconds: 50));
        },
        expect:
            () => [
              predicate<ConnectivityState>(
                (state) => state.status == ConnectionStatus.connected,
              ),
            ],
      );

      test('should handle mixed connectivity results with none', () async {
        connectivityController.add([
          ConnectivityResult.wifi,
          ConnectivityResult.none,
        ]);
        await Future.delayed(Duration(milliseconds: 50));

        // Should be connected because wifi is available
        expect(
          connectivityCubit.state.status,
          equals(ConnectionStatus.connected),
        );
      });
    });
  });
}
