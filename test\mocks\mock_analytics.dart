import 'package:mocktail/mocktail.dart';
import 'package:aquapartner/core/services/analytics_service.dart';
import 'package:firebase_analytics/firebase_analytics.dart';

/// Mock implementation of AnalyticsService for testing
class MockAnalyticsService extends Mo<PERSON> implements AnalyticsService {
  final List<Map<String, dynamic>> _loggedEvents = [];
  final List<Map<String, dynamic>> _userProperties = [];
  final List<String> _screenViews = [];

  /// Get all logged events for verification
  List<Map<String, dynamic>> get loggedEvents =>
      List.unmodifiable(_loggedEvents);

  /// Get all set user properties for verification
  List<Map<String, dynamic>> get userProperties =>
      List.unmodifiable(_userProperties);

  /// Get all screen views for verification
  List<String> get screenViews => List.unmodifiable(_screenViews);

  /// Clear all logged data
  void clearLogs() {
    _loggedEvents.clear();
    _userProperties.clear();
    _screenViews.clear();
  }

  /// Override logScreenView to capture screen views
  @override
  Future<void> logScreenView({
    required String screenName,
    String? screenClass,
  }) async {
    _screenViews.add(screenName);
    _loggedEvents.add({
      'name': 'screen_view',
      'parameters': {
        'screen_name': screenName,
        'screen_class': screenClass ?? '',
      },
    });
  }

  /// Override logEvent to capture events
  @override
  Future<void> logEvent({
    required String name,
    Map<String, Object?>? parameters,
  }) async {
    _loggedEvents.add({'name': name, 'parameters': parameters ?? {}});
  }

  /// Override setUserProperty to capture user properties
  Future<void> setUserProperty({
    required String name,
    required String? value,
  }) async {
    _userProperties.add({'name': name, 'value': value});
  }

  /// Override setCurrentScreen to capture screen views
  Future<void> setCurrentScreen({
    required String? screenName,
    String? screenClassOverride,
  }) async {
    if (screenName != null) {
      _screenViews.add(screenName);
    }
  }

  /// Helper method to verify if an event was logged
  bool hasLoggedEvent(String eventName) {
    return _loggedEvents.any((event) => event['name'] == eventName);
  }

  /// Helper method to get events by name
  List<Map<String, dynamic>> getEventsByName(String eventName) {
    return _loggedEvents.where((event) => event['name'] == eventName).toList();
  }

  /// Helper method to verify event parameters
  bool hasEventWithParameter(
    String eventName,
    String paramKey,
    dynamic paramValue,
  ) {
    return _loggedEvents.any(
      (event) =>
          event['name'] == eventName &&
          event['parameters'][paramKey] == paramValue,
    );
  }

  /// Helper method to get the last logged event
  Map<String, dynamic>? get lastLoggedEvent {
    return _loggedEvents.isNotEmpty ? _loggedEvents.last : null;
  }

  /// Helper method to get event count
  int getEventCount(String eventName) {
    return _loggedEvents.where((event) => event['name'] == eventName).length;
  }

  // Add missing methods from AnalyticsService
  @override
  Future<void> logUserLogin(String userId, String userType) async {
    await logEvent(
      name: 'user_login',
      parameters: {'user_id': userId, 'user_type': userType},
    );
  }

  @override
  Future<void> logUserLogout(String userId) async {
    await logEvent(name: 'user_logout', parameters: {'user_id': userId});
  }

  @override
  Future<void> setUserProperties(String userId, String userRole) async {
    await setUserProperty(name: 'user_id', value: userId);
    await setUserProperty(name: 'user_role', value: userRole);
  }

  @override
  Future<void> logFeatureUsage(String featureName) async {
    await logEvent(
      name: 'feature_used',
      parameters: {'feature_name': featureName},
    );
  }

  @override
  Future<void> logProductView(String productId, String productName) async {
    await logEvent(
      name: 'product_viewed',
      parameters: {'product_id': productId, 'product_name': productName},
    );
  }

  @override
  void setCurrentUser(customer) async {
    // Mock implementation
  }

  @override
  Future<void> logScreenDuration({
    required String screenName,
    required int durationMs,
    String? screenClass,
  }) async {
    await logEvent(
      name: 'screen_duration',
      parameters: {
        'screen_name': screenName,
        'duration_ms': durationMs,
        'screen_class': screenClass ?? '',
      },
    );
  }

  @override
  Future<void> logOrderCreated(String orderId, double amount) async {
    await logEvent(
      name: 'order_created',
      parameters: {'order_id': orderId, 'amount': amount},
    );
  }

  @override
  Future<void> logCustomerInteraction(
    String customerId,
    String interactionType,
  ) async {
    await logEvent(
      name: 'customer_interaction',
      parameters: {
        'customer_id': customerId,
        'interaction_type': interactionType,
      },
    );
  }

  @override
  Future<void> logError({
    required String errorType,
    required String errorMessage,
    String? screenName,
    Map<String, Object> additionalParams = const {},
  }) async {
    await logEvent(
      name: 'app_error',
      parameters: {
        'error_type': errorType,
        'error_message': errorMessage,
        'screen_name': screenName ?? 'unknown',
        ...additionalParams,
      },
    );
  }

  @override
  Future<void> logUserEngagement(int durationMs) async {
    await logEvent(
      name: 'engagement_user',
      parameters: {'duration_ms': durationMs},
    );
  }

  @override
  Future<void> logSessionStart() async {
    await logEvent(name: 'start_session', parameters: {});
  }

  @override
  Future<void> logUserInteraction({
    required String screenName,
    required String actionName,
    required String elementType,
    String? elementId,
    Map<String, Object> additionalParams = const {},
  }) async {
    await logEvent(
      name: 'user_interaction',
      parameters: {
        'screen_name': screenName,
        'action_name': actionName,
        'element_type': elementType,
        'element_id': elementId ?? 'unknown',
        ...additionalParams,
      },
    );
  }

  @override
  Future<void> logUserFlow({
    required String flowName,
    required String stepName,
    required String status,
    Map<String, Object> additionalParams = const {},
  }) async {
    await logEvent(
      name: 'user_flow',
      parameters: {
        'flow_name': flowName,
        'step_name': stepName,
        'status': status,
        ...additionalParams,
      },
    );
  }

  @override
  Future<void> updateUserBehaviorProperties() async {
    // Mock implementation
  }
}

/// Mock Firebase Analytics for testing
class MockFirebaseAnalytics extends Mock implements FirebaseAnalytics {
  final List<Map<String, dynamic>> _events = [];
  final Map<String, String?> _userProperties = {};
  String? _currentScreen;

  List<Map<String, dynamic>> get events => List.unmodifiable(_events);
  Map<String, String?> get userProperties => Map.unmodifiable(_userProperties);
  String? get currentScreen => _currentScreen;

  @override
  Future<void> logEvent({
    AnalyticsCallOptions? callOptions,
    required String name,
    Map<String, Object?>? parameters,
  }) async {
    _events.add({'name': name, 'parameters': parameters ?? {}});
  }

  @override
  Future<void> setUserProperty({
    AnalyticsCallOptions? callOptions,
    required String name,
    required String? value,
  }) async {
    _userProperties[name] = value;
  }

  @override
  Future<void> setCurrentScreen({
    AnalyticsCallOptions? callOptions,
    required String? screenName,
    String? screenClassOverride,
  }) async {
    _currentScreen = screenName;
  }

  @override
  Future<void> logScreenView({
    AnalyticsCallOptions? callOptions,
    String? screenName,
    String? screenClass,
    Map<String, Object>? parameters,
  }) async {
    _currentScreen = screenName;
    if (screenName != null) {
      _events.add({
        'name': 'screen_view',
        'parameters': {
          'screen_name': screenName,
          'screen_class': screenClass ?? '',
          ...?parameters,
        },
      });
    }
  }

  void clearEvents() {
    _events.clear();
    _userProperties.clear();
    _currentScreen = null;
  }
}

/// Mock Analytics Observer for testing
class MockAnalyticsObserver extends Mock implements FirebaseAnalyticsObserver {}

/// Test Analytics Service that extends the mock for easier testing
class TestAnalyticsService extends MockAnalyticsService {
  bool _isInitialized = false;
  bool _shouldThrowError = false;

  bool get isInitialized => _isInitialized;

  void setInitialized(bool initialized) {
    _isInitialized = initialized;
  }

  void setShouldThrowError(bool shouldThrow) {
    _shouldThrowError = shouldThrow;
  }

  @override
  Future<void> logEvent({
    required String name,
    Map<String, Object?>? parameters,
  }) async {
    if (_shouldThrowError) {
      throw Exception('Analytics service error');
    }
    if (!_isInitialized) {
      throw Exception('Analytics service not initialized');
    }
    await super.logEvent(name: name, parameters: parameters);
  }

  @override
  Future<void> setUserProperty({
    required String name,
    required String? value,
  }) async {
    if (_shouldThrowError) {
      throw Exception('Analytics service error');
    }
    if (!_isInitialized) {
      throw Exception('Analytics service not initialized');
    }
    await super.setUserProperty(name: name, value: value);
  }

  @override
  Future<void> setCurrentScreen({
    required String? screenName,
    String? screenClassOverride,
  }) async {
    if (_shouldThrowError) {
      throw Exception('Analytics service error');
    }
    if (!_isInitialized) {
      throw Exception('Analytics service not initialized');
    }
    await super.setCurrentScreen(
      screenName: screenName,
      screenClassOverride: screenClassOverride,
    );
  }
}
