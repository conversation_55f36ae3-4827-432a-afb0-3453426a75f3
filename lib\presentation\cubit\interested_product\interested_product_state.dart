import 'package:equatable/equatable.dart';
import '../../../domain/entities/interested_product.dart';

enum InterestedProductStatus {
  initial,
  loading,
  success,
  error,
  syncing,
  adding,
}

class InterestedProductState extends Equatable {
  final InterestedProductStatus status;
  final List<InterestedProduct> interestedProducts;
  final int unsyncedCount;
  final String? errorMessage;

  const InterestedProductState({
    this.status = InterestedProductStatus.initial,
    this.interestedProducts = const [],
    this.unsyncedCount = 0,
    this.errorMessage,
  });

  InterestedProductState copyWith({
    InterestedProductStatus? status,
    List<InterestedProduct>? interestedProducts,
    int? unsyncedCount,
    String? errorMessage,
  }) {
    return InterestedProductState(
      status: status ?? this.status,
      interestedProducts: interestedProducts ?? this.interestedProducts,
      unsyncedCount: unsyncedCount ?? this.unsyncedCount,
      errorMessage: errorMessage,
    );
  }

  bool get isLoading => status == InterestedProductStatus.loading;
  bool get isAdding => status == InterestedProductStatus.adding;
  bool get isSyncing => status == InterestedProductStatus.syncing;
  bool get isSuccess => status == InterestedProductStatus.success;
  bool get isError => status == InterestedProductStatus.error;

  @override
  List<Object?> get props => [
    status,
    interestedProducts,
    unsyncedCount,
    errorMessage,
  ];
}
