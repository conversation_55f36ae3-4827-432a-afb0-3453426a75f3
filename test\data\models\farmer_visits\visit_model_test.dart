import 'package:flutter_test/flutter_test.dart';
import 'package:aquapartner/data/models/farmer_visits/visit_model.dart';
import 'package:aquapartner/domain/entities/farmer_visit/visit.dart';

void main() {
  group('VisitModel Tests', () {
    late VisitModel testVisitModel;
    late Map<String, dynamic> testJson;
    late DateTime testDateTime;

    setUp(() {
      testDateTime = DateTime(2024, 1, 15, 10, 30, 0);

      testVisitModel = VisitModel(
        createdDateTime: testDateTime,
        doc: 30,
        pondId: 'POND001',
        mobileNumber: '+919999999999',
        productUsed: 'Test Product',
        farmerId: 'farmer_123',
        farmerName: 'Test Farmer',
      );

      testJson = {
        'createdDateTime': testDateTime.toIso8601String(),
        'doc': 30,
        'pondId': 'POND001',
        'mobileNumber': '+919999999999',
        'productUsed': 'Test Product',
        'farmerId': 'farmer_123',
        'farmerName': 'Test Farmer',
      };
    });

    group('Constructor Tests', () {
      test('should create VisitModel with all required fields', () {
        expect(testVisitModel.createdDateTime, equals(testDateTime));
        expect(testVisitModel.doc, equals(30));
        expect(testVisitModel.pondId, equals('POND001'));
        expect(testVisitModel.mobileNumber, equals('+919999999999'));
        expect(testVisitModel.productUsed, equals('Test Product'));
        expect(testVisitModel.farmerId, equals('farmer_123'));
        expect(testVisitModel.farmerName, equals('Test Farmer'));
      });

      test('should create VisitModel with empty string values', () {
        final emptyVisitModel = VisitModel(
          createdDateTime: testDateTime,
          doc: 0,
          pondId: '',
          mobileNumber: '',
          productUsed: '',
          farmerId: '',
          farmerName: '',
        );

        expect(emptyVisitModel.createdDateTime, equals(testDateTime));
        expect(emptyVisitModel.doc, equals(0));
        expect(emptyVisitModel.pondId, isEmpty);
        expect(emptyVisitModel.mobileNumber, isEmpty);
        expect(emptyVisitModel.productUsed, isEmpty);
        expect(emptyVisitModel.farmerId, isEmpty);
        expect(emptyVisitModel.farmerName, isEmpty);
      });
    });

    group('JSON Serialization Tests', () {
      test('should convert VisitModel to JSON correctly', () {
        final json = testVisitModel.toJson();

        expect(json['createdDateTime'], equals(testDateTime.toIso8601String()));
        expect(json['doc'], equals(30));
        expect(json['pondId'], equals('POND001'));
        expect(json['mobileNumber'], equals('+919999999999'));
        expect(json['productUsed'], equals('Test Product'));
        expect(json['farmerId'], equals('farmer_123'));
        expect(json['farmerName'], equals('Test Farmer'));
      });

      test('should create VisitModel from JSON correctly', () {
        final visitModel = VisitModel.fromJson(testJson);

        expect(visitModel.createdDateTime, equals(testDateTime));
        expect(visitModel.doc, equals(30));
        expect(visitModel.pondId, equals('POND001'));
        expect(visitModel.mobileNumber, equals('+919999999999'));
        expect(visitModel.productUsed, equals('Test Product'));
        expect(visitModel.farmerId, equals('farmer_123'));
        expect(visitModel.farmerName, equals('Test Farmer'));
      });

      test('should handle missing JSON fields with defaults', () {
        final incompleteJson = <String, dynamic>{
          'createdDateTime': testDateTime.toIso8601String(),
          'doc': 30,
        };

        final visitModel = VisitModel.fromJson(incompleteJson);

        expect(visitModel.createdDateTime, equals(testDateTime));
        expect(visitModel.doc, equals(30));
        expect(visitModel.pondId, isEmpty);
        expect(visitModel.mobileNumber, isEmpty);
        expect(visitModel.productUsed, isEmpty);
        expect(visitModel.farmerId, isEmpty);
        expect(visitModel.farmerName, isEmpty);
      });

      test('should handle null JSON values with defaults', () {
        final nullJson = <String, dynamic>{
          'createdDateTime': testDateTime.toIso8601String(),
          'doc': null,
          'pondId': null,
          'mobileNumber': null,
          'productUsed': null,
          'farmerId': null,
          'farmerName': null,
        };

        final visitModel = VisitModel.fromJson(nullJson);

        expect(visitModel.createdDateTime, equals(testDateTime));
        expect(visitModel.doc, equals(0));
        expect(visitModel.pondId, isEmpty);
        expect(visitModel.mobileNumber, isEmpty);
        expect(visitModel.productUsed, isEmpty);
        expect(visitModel.farmerId, isEmpty);
        expect(visitModel.farmerName, isEmpty);
      });

      test('should handle doc field as string and convert to int', () {
        final stringDocJson = <String, dynamic>{
          'createdDateTime': testDateTime.toIso8601String(),
          'doc': '45',
          'pondId': 'POND001',
          'mobileNumber': '+919999999999',
          'productUsed': 'Test Product',
        };

        final visitModel = VisitModel.fromJson(stringDocJson);

        expect(visitModel.doc, equals(45));
      });

      test('should handle invalid doc field and default to 0', () {
        final invalidDocJson = <String, dynamic>{
          'createdDateTime': testDateTime.toIso8601String(),
          'doc': 'invalid_number',
          'pondId': 'POND001',
          'mobileNumber': '+919999999999',
          'productUsed': 'Test Product',
        };

        final visitModel = VisitModel.fromJson(invalidDocJson);

        expect(visitModel.doc, equals(0));
      });

      test('should handle farmerId and farmerName parameters in fromJson', () {
        final jsonWithoutIds = <String, dynamic>{
          'createdDateTime': testDateTime.toIso8601String(),
          'doc': 30,
          'pondId': 'POND001',
          'mobileNumber': '+919999999999',
          'productUsed': 'Test Product',
        };

        final visitModel = VisitModel.fromJson(
          jsonWithoutIds,
          farmerId: 'param_farmer_123',
          farmerName: 'Param Farmer',
        );

        expect(visitModel.farmerId, equals('param_farmer_123'));
        expect(visitModel.farmerName, equals('Param Farmer'));
      });

      test(
        'should prefer parameters over JSON values when both are present',
        () {
          final jsonWithIds = <String, dynamic>{
            'createdDateTime': testDateTime.toIso8601String(),
            'doc': 30,
            'pondId': 'POND001',
            'mobileNumber': '+919999999999',
            'productUsed': 'Test Product',
            'farmerId': 'json_farmer_123',
            'farmerName': 'JSON Farmer',
          };

          final visitModel = VisitModel.fromJson(
            jsonWithIds,
            farmerId: 'param_farmer_123',
            farmerName: 'Param Farmer',
          );

          expect(visitModel.farmerId, equals('param_farmer_123'));
          expect(visitModel.farmerName, equals('Param Farmer'));
        },
      );

      test('should handle round-trip JSON serialization', () {
        final originalJson = testVisitModel.toJson();
        final recreatedModel = VisitModel.fromJson(originalJson);
        final finalJson = recreatedModel.toJson();

        expect(finalJson, equals(originalJson));
      });
    });

    group('Domain Entity Conversion Tests', () {
      test('should convert VisitModel to Visit entity correctly', () {
        final visitEntity = testVisitModel.toDomain();

        expect(visitEntity, isA<Visit>());
        expect(visitEntity.id, isNull);
        expect(
          visitEntity.createdDateTime,
          equals(testVisitModel.createdDateTime),
        );
        expect(visitEntity.doc, equals(testVisitModel.doc));
        expect(visitEntity.pondId, equals(testVisitModel.pondId));
        expect(visitEntity.mobileNumber, equals(testVisitModel.mobileNumber));
        expect(visitEntity.productUsed, equals(testVisitModel.productUsed));
        expect(visitEntity.farmerId, equals(testVisitModel.farmerId));
        expect(visitEntity.farmerName, equals(testVisitModel.farmerName));
      });

      test('should create VisitModel from Visit entity correctly', () {
        final visitEntity = Visit(
          id: 1,
          createdDateTime: testDateTime,
          doc: 45,
          pondId: 'POND002',
          mobileNumber: '+919876543211',
          productUsed: 'Domain Product',
          farmerId: 'farmer_456',
          farmerName: 'Domain Farmer',
        );

        final visitModel = VisitModel.fromDomain(visitEntity);

        expect(visitModel.createdDateTime, equals(testDateTime));
        expect(visitModel.doc, equals(45));
        expect(visitModel.pondId, equals('POND002'));
        expect(visitModel.mobileNumber, equals('+919876543211'));
        expect(visitModel.productUsed, equals('Domain Product'));
        expect(visitModel.farmerId, equals('farmer_456'));
        expect(visitModel.farmerName, equals('Domain Farmer'));
      });

      test('should maintain data integrity during entity conversion', () {
        final originalEntity = testVisitModel.toDomain();
        final backToModel = VisitModel.fromDomain(originalEntity);
        final finalEntity = backToModel.toDomain();

        expect(
          finalEntity.createdDateTime,
          equals(originalEntity.createdDateTime),
        );
        expect(finalEntity.doc, equals(originalEntity.doc));
        expect(finalEntity.pondId, equals(originalEntity.pondId));
        expect(finalEntity.mobileNumber, equals(originalEntity.mobileNumber));
        expect(finalEntity.productUsed, equals(originalEntity.productUsed));
        expect(finalEntity.farmerId, equals(originalEntity.farmerId));
        expect(finalEntity.farmerName, equals(originalEntity.farmerName));
      });
    });

    group('Edge Cases and Validation Tests', () {
      test('should handle special characters in string fields', () {
        final specialCharJson = {
          'createdDateTime': testDateTime.toIso8601String(),
          'doc': 30,
          'pondId': 'POND001 & Special',
          'mobileNumber': '+91-9999999999 (primary)',
          'productUsed': 'Test Product & Supplements',
          'farmerId': 'farmer_123-special',
          'farmerName': 'Test Farmer & Co.',
        };

        final visitModel = VisitModel.fromJson(specialCharJson);

        expect(visitModel.pondId, equals('POND001 & Special'));
        expect(visitModel.mobileNumber, equals('+91-9999999999 (primary)'));
        expect(visitModel.productUsed, equals('Test Product & Supplements'));
        expect(visitModel.farmerId, equals('farmer_123-special'));
        expect(visitModel.farmerName, equals('Test Farmer & Co.'));
      });

      test('should handle extreme numeric values for doc', () {
        final extremeDocJson = {
          'createdDateTime': testDateTime.toIso8601String(),
          'doc': 999999,
          'pondId': 'POND001',
          'mobileNumber': '+919999999999',
          'productUsed': 'Test Product',
        };

        final visitModel = VisitModel.fromJson(extremeDocJson);

        expect(visitModel.doc, equals(999999));
      });

      test('should handle negative doc values', () {
        final negativeDocJson = {
          'createdDateTime': testDateTime.toIso8601String(),
          'doc': -30,
          'pondId': 'POND001',
          'mobileNumber': '+919999999999',
          'productUsed': 'Test Product',
        };

        final visitModel = VisitModel.fromJson(negativeDocJson);

        expect(visitModel.doc, equals(-30));
      });

      test('should handle very long string values', () {
        final longString = 'A' * 1000;
        final longStringJson = {
          'createdDateTime': testDateTime.toIso8601String(),
          'doc': 30,
          'pondId': longString,
          'mobileNumber': longString,
          'productUsed': longString,
          'farmerId': longString,
          'farmerName': longString,
        };

        final visitModel = VisitModel.fromJson(longStringJson);

        expect(visitModel.pondId.length, equals(1000));
        expect(visitModel.mobileNumber.length, equals(1000));
        expect(visitModel.productUsed.length, equals(1000));
        expect(visitModel.farmerId.length, equals(1000));
        expect(visitModel.farmerName.length, equals(1000));
      });

      test('should handle unicode characters', () {
        final unicodeJson = {
          'createdDateTime': testDateTime.toIso8601String(),
          'doc': 30,
          'pondId': 'POND001 池塘',
          'mobileNumber': '+919999999999',
          'productUsed': 'Test Product 测试产品',
          'farmerId': 'farmer_123',
          'farmerName': 'Test Farmer 测试农民',
        };

        final visitModel = VisitModel.fromJson(unicodeJson);

        expect(visitModel.pondId, equals('POND001 池塘'));
        expect(visitModel.productUsed, equals('Test Product 测试产品'));
        expect(visitModel.farmerName, equals('Test Farmer 测试农民'));
      });

      test('should handle different date formats', () {
        final differentDateJson = {
          'createdDateTime': '2024-01-15T10:30:00.000Z',
          'doc': 30,
          'pondId': 'POND001',
          'mobileNumber': '+919999999999',
          'productUsed': 'Test Product',
        };

        final visitModel = VisitModel.fromJson(differentDateJson);

        expect(visitModel.createdDateTime, isA<DateTime>());
        expect(visitModel.createdDateTime.year, equals(2024));
        expect(visitModel.createdDateTime.month, equals(1));
        expect(visitModel.createdDateTime.day, equals(15));
      });

      test('should handle zero doc value', () {
        final zeroDocJson = {
          'createdDateTime': testDateTime.toIso8601String(),
          'doc': 0,
          'pondId': 'POND001',
          'mobileNumber': '+919999999999',
          'productUsed': 'Test Product',
        };

        final visitModel = VisitModel.fromJson(zeroDocJson);

        expect(visitModel.doc, equals(0));
      });

      test('should handle empty string mobile number', () {
        final emptyMobileJson = {
          'createdDateTime': testDateTime.toIso8601String(),
          'doc': 30,
          'pondId': 'POND001',
          'mobileNumber': '',
          'productUsed': 'Test Product',
        };

        final visitModel = VisitModel.fromJson(emptyMobileJson);

        expect(visitModel.mobileNumber, isEmpty);
      });

      test('should handle empty string product used', () {
        final emptyProductJson = {
          'createdDateTime': testDateTime.toIso8601String(),
          'doc': 30,
          'pondId': 'POND001',
          'mobileNumber': '+919999999999',
          'productUsed': '',
        };

        final visitModel = VisitModel.fromJson(emptyProductJson);

        expect(visitModel.productUsed, isEmpty);
      });
    });
  });
}
