import 'package:equatable/equatable.dart';

/// Represents a completed payment transaction entity
class PaymentTransaction extends Equatable {
  final String transactionId;
  final String sessionId;
  final String invoiceNumber;
  final String customerId;
  final double amount;
  final String currency;
  final PaymentTransactionStatus status;
  final PaymentMethod paymentMethod;
  final DateTime transactionDate;
  final String? gatewayTransactionId;
  final String? gatewayResponse;
  final String? failureReason;
  final Map<String, dynamic>? metadata;

  const PaymentTransaction({
    required this.transactionId,
    required this.sessionId,
    required this.invoiceNumber,
    required this.customerId,
    required this.amount,
    required this.currency,
    required this.status,
    required this.paymentMethod,
    required this.transactionDate,
    this.gatewayTransactionId,
    this.gatewayResponse,
    this.failureReason,
    this.metadata,
  });

  @override
  List<Object?> get props => [
        transactionId,
        sessionId,
        invoiceNumber,
        customerId,
        amount,
        currency,
        status,
        paymentMethod,
        transactionDate,
        gatewayTransactionId,
        gatewayResponse,
        failureReason,
        metadata,
      ];

  PaymentTransaction copyWith({
    String? transactionId,
    String? sessionId,
    String? invoiceNumber,
    String? customerId,
    double? amount,
    String? currency,
    PaymentTransactionStatus? status,
    PaymentMethod? paymentMethod,
    DateTime? transactionDate,
    String? gatewayTransactionId,
    String? gatewayResponse,
    String? failureReason,
    Map<String, dynamic>? metadata,
  }) {
    return PaymentTransaction(
      transactionId: transactionId ?? this.transactionId,
      sessionId: sessionId ?? this.sessionId,
      invoiceNumber: invoiceNumber ?? this.invoiceNumber,
      customerId: customerId ?? this.customerId,
      amount: amount ?? this.amount,
      currency: currency ?? this.currency,
      status: status ?? this.status,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      transactionDate: transactionDate ?? this.transactionDate,
      gatewayTransactionId: gatewayTransactionId ?? this.gatewayTransactionId,
      gatewayResponse: gatewayResponse ?? this.gatewayResponse,
      failureReason: failureReason ?? this.failureReason,
      metadata: metadata ?? this.metadata,
    );
  }

  bool get isSuccessful => status == PaymentTransactionStatus.success;
  bool get isFailed => status == PaymentTransactionStatus.failed;
  bool get isPending => status == PaymentTransactionStatus.pending;
}

/// Payment transaction status enumeration
enum PaymentTransactionStatus {
  pending,
  success,
  failed,
  cancelled,
  refunded,
}

extension PaymentTransactionStatusExtension on PaymentTransactionStatus {
  String get displayName {
    switch (this) {
      case PaymentTransactionStatus.pending:
        return 'Pending';
      case PaymentTransactionStatus.success:
        return 'Success';
      case PaymentTransactionStatus.failed:
        return 'Failed';
      case PaymentTransactionStatus.cancelled:
        return 'Cancelled';
      case PaymentTransactionStatus.refunded:
        return 'Refunded';
    }
  }
}

/// Payment method enumeration
enum PaymentMethod {
  creditCard,
  debitCard,
  netBanking,
  upi,
  wallet,
  unknown,
}

extension PaymentMethodExtension on PaymentMethod {
  String get displayName {
    switch (this) {
      case PaymentMethod.creditCard:
        return 'Credit Card';
      case PaymentMethod.debitCard:
        return 'Debit Card';
      case PaymentMethod.netBanking:
        return 'Net Banking';
      case PaymentMethod.upi:
        return 'UPI';
      case PaymentMethod.wallet:
        return 'Wallet';
      case PaymentMethod.unknown:
        return 'Unknown';
    }
  }
}
