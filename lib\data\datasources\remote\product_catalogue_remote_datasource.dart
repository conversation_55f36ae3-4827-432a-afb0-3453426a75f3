import 'dart:async';
import 'package:mongo_dart/mongo_dart.dart';
import '../../../core/error/exceptions.dart';
import '../../../core/network/mongodb_connection_manager.dart';
import '../../../core/utils/logger.dart';
import '../../models/product_catalogue_model.dart';

abstract class ProductCatalogueRemoteDataSource {
  /// Gets all product catalogues from the remote server
  Future<List<ProductCatalogueModel>> getProductCatalogues();

  /// Gets a specific product catalogue by ID from the remote server
  Future<ProductCatalogueModel?> getProductCatalogueById(String id);
}

class ProductCatalogueRemoteDataSourceImpl
    implements ProductCatalogueRemoteDataSource {
  final MongoDbConnectionManager connectionManager;
  final AppLogger logger;

  // Add a timeout for operations
  final Duration operationTimeout = const Duration(seconds: 15);

  ProductCatalogueRemoteDataSourceImpl({
    required this.connectionManager,
    required this.logger,
  });

  @override
  Future<List<ProductCatalogueModel>> getProductCatalogues() async {
    try {
      logger.i("Getting product catalogues from MongoDB");

      final mongoService = await connectionManager.getConnection();

      // Get the collection with automatic reconnection
      final collection = await mongoService.productCatalogueCollection;

      // Get all product catalogues with timeout
      final cursor = collection.find();
      final result = await cursor.toList().timeout(
        operationTimeout,
        onTimeout: () {
          logger.e(
            "MongoDB operation timed out while fetching product catalogues",
          );
          throw TimeoutException("MongoDB operation timed out");
        },
      );

      if (result.isEmpty) {
        logger.w("No product catalogues found in MongoDB");
        return [];
      }

      // Process the results with error handling for each document
      final productCatalogues = <ProductCatalogueModel>[];
      for (var json in result) {
        try {
          final model = ProductCatalogueModel.fromJson(json);
          productCatalogues.add(model);
        } catch (e) {
          // Log the error but continue processing other documents
          logger.e("Error parsing product catalogue document: $e", e);
          logger.e("Problematic document: $json");
        }
      }

      logger.i(
        "Successfully processed ${productCatalogues.length} product catalogues from MongoDB",
      );
      return productCatalogues;
    } on TimeoutException catch (e) {
      logger.e("MongoDB operation timed out", e);
      throw ServerException();
    } on ServerException {
      rethrow;
    } catch (e) {
      logger.e("Error getting product catalogues from MongoDB: $e", e);
      throw ServerException();
    }
  }

  @override
  Future<ProductCatalogueModel?> getProductCatalogueById(String id) async {
    try {
      logger.i("Getting product catalogue by ID: $id from MongoDB");

      final mongoService = await connectionManager.getConnection();

      // Get the collection with automatic reconnection
      final collection = await mongoService.productCatalogueCollection;

      // Try to convert the string ID to ObjectId if it's in the correct format
      dynamic queryId = id;
      try {
        // Check if the ID is a valid 24-character hex string (ObjectId format)
        if (id.length == 24 && RegExp(r'^[0-9a-fA-F]{24}$').hasMatch(id)) {
          queryId = ObjectId.parse(id);
          logger.i("Converted string ID to ObjectId: $id");
        }
      } catch (e) {
        // If conversion fails, use the string ID as is
        logger.w("Could not convert ID to ObjectId, using string ID: $id");
      }

      // Find product catalogue by ID with timeout
      final result = await collection
          .findOne(where.eq('_id', queryId))
          .timeout(
            operationTimeout,
            onTimeout: () {
              logger.e(
                "MongoDB operation timed out while fetching product catalogue by ID",
              );
              throw TimeoutException("MongoDB operation timed out");
            },
          );

      if (result == null) {
        logger.w("No product catalogue found with ID: $id in MongoDB");
        return null;
      }

      logger.i("Found product catalogue with ID: $id in MongoDB");
      return ProductCatalogueModel.fromJson(result);
    } on TimeoutException catch (e) {
      logger.e("MongoDB operation timed out", e);
      throw ServerException();
    } on ServerException {
      rethrow;
    } catch (e) {
      logger.e("Error getting product catalogue by ID from MongoDB: $e", e);
      throw ServerException();
    }
  }
}
