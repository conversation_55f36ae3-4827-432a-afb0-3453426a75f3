# Simple LCOV Coverage Parser for Windows
# Parses LCOV file and provides coverage statistics

param(
    [string]$LcovPath = "coverage/lcov.info"
)

Write-Host "🚀 Flutter Test Coverage Analysis" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green
Write-Host ""

if (!(Test-Path $LcovPath)) {
    Write-Host "❌ LCOV file not found: $LcovPath" -ForegroundColor Red
    Write-Host "Please run 'flutter test --coverage' first" -ForegroundColor Yellow
    exit 1
}

Write-Host "📊 Analyzing coverage data from: $LcovPath" -ForegroundColor Cyan
Write-Host ""

# Initialize counters
$totalFiles = 0
$totalLines = 0
$coveredLines = 0
$totalFunctions = 0
$coveredFunctions = 0
$totalBranches = 0
$coveredBranches = 0

# File-specific tracking
$fileStats = @()
$currentFile = ""
$currentFileLines = 0
$currentFileCovered = 0

# Read and parse LCOV file
$lcovContent = Get-Content $LcovPath

foreach ($line in $lcovContent) {
    if ($line.StartsWith("SF:")) {
        # Start of new file
        if ($currentFile -ne "") {
            # Save previous file stats
            $coverage = if ($currentFileLines -gt 0) { ($currentFileCovered / $currentFileLines) * 100 } else { 0 }
            $fileStats += [PSCustomObject]@{
                File         = $currentFile
                TotalLines   = $currentFileLines
                CoveredLines = $currentFileCovered
                Coverage     = $coverage
            }
        }
        
        $totalFiles++
        $currentFile = $line.Substring(3).Replace('\', '/')
        $currentFileLines = 0
        $currentFileCovered = 0
    }
    elseif ($line.StartsWith("LF:")) {
        $currentFileLines = [int]($line.Split(":")[1])
        $totalLines += $currentFileLines
    }
    elseif ($line.StartsWith("LH:")) {
        $currentFileCovered = [int]($line.Split(":")[1])
        $coveredLines += $currentFileCovered
    }
    elseif ($line.StartsWith("FNF:")) {
        $totalFunctions += [int]($line.Split(":")[1])
    }
    elseif ($line.StartsWith("FNH:")) {
        $coveredFunctions += [int]($line.Split(":")[1])
    }
    elseif ($line.StartsWith("BRF:")) {
        $totalBranches += [int]($line.Split(":")[1])
    }
    elseif ($line.StartsWith("BRH:")) {
        $coveredBranches += [int]($line.Split(":")[1])
    }
}

# Handle last file
if ($currentFile -ne "") {
    $coverage = if ($currentFileLines -gt 0) { ($currentFileCovered / $currentFileLines) * 100 } else { 0 }
    $fileStats += [PSCustomObject]@{
        File         = $currentFile
        TotalLines   = $currentFileLines
        CoveredLines = $currentFileCovered
        Coverage     = $coverage
    }
}

# Calculate overall coverage
$lineCoverage = if ($totalLines -gt 0) { ($coveredLines / $totalLines) * 100 } else { 0 }
$functionCoverage = if ($totalFunctions -gt 0) { ($coveredFunctions / $totalFunctions) * 100 } else { 0 }
$branchCoverage = if ($totalBranches -gt 0) { ($coveredBranches / $totalBranches) * 100 } else { 0 }

# Display overall statistics
Write-Host "📈 OVERALL COVERAGE STATISTICS" -ForegroundColor Green
Write-Host "==============================" -ForegroundColor Green
Write-Host ""

Write-Host "📁 Files Analyzed: $totalFiles" -ForegroundColor White
Write-Host ""

# Line Coverage
$lineColor = if ($lineCoverage -ge 70) { "Green" } 
elseif ($lineCoverage -ge 50) { "Yellow" } 
else { "Red" }
Write-Host "📏 Line Coverage: $($lineCoverage.ToString('F2'))% ($coveredLines/$totalLines)" -ForegroundColor $lineColor

# Function Coverage
if ($totalFunctions -gt 0) {
    $funcColor = if ($functionCoverage -ge 80) { "Green" } 
    elseif ($functionCoverage -ge 60) { "Yellow" } 
    else { "Red" }
    Write-Host "🔧 Function Coverage: $($functionCoverage.ToString('F2'))% ($coveredFunctions/$totalFunctions)" -ForegroundColor $funcColor
}

# Branch Coverage
if ($totalBranches -gt 0) {
    $branchColor = if ($branchCoverage -ge 65) { "Green" } 
    elseif ($branchCoverage -ge 45) { "Yellow" } 
    else { "Red" }
    Write-Host "🌿 Branch Coverage: $($branchCoverage.ToString('F2'))% ($coveredBranches/$totalBranches)" -ForegroundColor $branchColor
}

Write-Host ""

# Production Readiness Assessment
Write-Host "🎯 PRODUCTION READINESS ASSESSMENT" -ForegroundColor Cyan
Write-Host "===================================" -ForegroundColor Cyan
Write-Host ""

$readinessScore = 0

# Line coverage scoring (40 points max)
if ($lineCoverage -ge 70) { $readinessScore += 40 }
elseif ($lineCoverage -ge 50) { $readinessScore += 25 }
elseif ($lineCoverage -ge 30) { $readinessScore += 15 }

# Function coverage scoring (30 points max)
if ($totalFunctions -gt 0) {
    if ($functionCoverage -ge 80) { $readinessScore += 30 }
    elseif ($functionCoverage -ge 60) { $readinessScore += 20 }
    elseif ($functionCoverage -ge 40) { $readinessScore += 10 }
}
else {
    $readinessScore += 15  # Partial credit if no function data
}

# Branch coverage scoring (20 points max)
if ($totalBranches -gt 0) {
    if ($branchCoverage -ge 65) { $readinessScore += 20 }
    elseif ($branchCoverage -ge 45) { $readinessScore += 12 }
    elseif ($branchCoverage -ge 25) { $readinessScore += 6 }
}
else {
    $readinessScore += 10  # Partial credit if no branch data
}

# File coverage scoring (10 points max)
if ($totalFiles -ge 50) { $readinessScore += 10 }
elseif ($totalFiles -ge 30) { $readinessScore += 7 }
elseif ($totalFiles -ge 15) { $readinessScore += 4 }

$readinessColor = if ($readinessScore -ge 85) { "Green" } 
elseif ($readinessScore -ge 70) { "Yellow" } 
else { "Red" }

Write-Host "🏆 Production Readiness Score: $readinessScore%" -ForegroundColor $readinessColor
Write-Host ""

if ($readinessScore -ge 85) {
    Write-Host "✅ READY FOR PRODUCTION" -ForegroundColor Green
    Write-Host "   Your app has excellent test coverage and is ready for CI/CD implementation." -ForegroundColor Green
}
elseif ($readinessScore -ge 70) {
    Write-Host "⚠️ APPROACHING PRODUCTION READINESS" -ForegroundColor Yellow
    Write-Host "   Your app has good coverage but needs improvement in some areas." -ForegroundColor Yellow
}
else {
    Write-Host "❌ NOT READY FOR PRODUCTION" -ForegroundColor Red
    Write-Host "   Your app needs significant improvement in test coverage." -ForegroundColor Red
}

Write-Host ""

# Coverage by Layer Analysis
Write-Host "📊 COVERAGE BY LAYER" -ForegroundColor Magenta
Write-Host "====================" -ForegroundColor Magenta
Write-Host ""

# Group files by layer
$layers = @{
    "Data Layer"         = @()
    "Domain Layer"       = @()
    "Presentation Layer" = @()
    "Core Services"      = @()
    "Other"              = @()
}

foreach ($file in $fileStats) {
    $path = $file.File.ToLower()
    if ($path.Contains("data/")) {
        $layers["Data Layer"] += $file
    }
    elseif ($path.Contains("domain/")) {
        $layers["Domain Layer"] += $file
    }
    elseif ($path.Contains("presentation/")) {
        $layers["Presentation Layer"] += $file
    }
    elseif ($path.Contains("core/")) {
        $layers["Core Services"] += $file
    }
    else {
        $layers["Other"] += $file
    }
}

foreach ($layerName in $layers.Keys) {
    $layerFiles = $layers[$layerName]
    if ($layerFiles.Count -gt 0) {
        $layerTotalLines = ($layerFiles | Measure-Object -Property TotalLines -Sum).Sum
        $layerCoveredLines = ($layerFiles | Measure-Object -Property CoveredLines -Sum).Sum
        $layerCoverage = if ($layerTotalLines -gt 0) { ($layerCoveredLines / $layerTotalLines) * 100 } else { 0 }
        
        $layerColor = if ($layerCoverage -ge 70) { "Green" } 
        elseif ($layerCoverage -ge 50) { "Yellow" } 
        else { "Red" }
        
        Write-Host "$layerName`: $($layerCoverage.ToString('F2'))% ($layerCoveredLines/$layerTotalLines) - $($layerFiles.Count) files" -ForegroundColor $layerColor
    }
}

Write-Host ""

# Show files with low coverage
Write-Host "⚠️ FILES NEEDING ATTENTION (< 70% coverage)" -ForegroundColor Yellow
Write-Host "=============================================" -ForegroundColor Yellow
Write-Host ""

$lowCoverageFiles = $fileStats | Where-Object { $_.Coverage -lt 70 } | Sort-Object Coverage
if ($lowCoverageFiles.Count -gt 0) {
    foreach ($file in $lowCoverageFiles | Select-Object -First 10) {
        $color = if ($file.Coverage -lt 30) { "Red" } else { "Yellow" }
        Write-Host "$($file.Coverage.ToString('F1'))% - $($file.File)" -ForegroundColor $color
    }
    if ($lowCoverageFiles.Count -gt 10) {
        Write-Host "... and $($lowCoverageFiles.Count - 10) more files" -ForegroundColor Gray
    }
}
else {
    Write-Host "🎉 All files have good coverage (70%+)!" -ForegroundColor Green
}

Write-Host ""

# Recommendations
Write-Host "💡 RECOMMENDATIONS" -ForegroundColor Magenta
Write-Host "==================" -ForegroundColor Magenta
Write-Host ""

if ($lineCoverage -lt 70) {
    Write-Host "• Increase line coverage to at least 70% for production readiness" -ForegroundColor Yellow
}
if ($totalFunctions -gt 0 -and $functionCoverage -lt 80) {
    Write-Host "• Improve function coverage to at least 80%" -ForegroundColor Yellow
}
if ($totalBranches -gt 0 -and $branchCoverage -lt 65) {
    Write-Host "• Add more branch coverage tests (target: 65%+)" -ForegroundColor Yellow
}
if ($totalFiles -lt 30) {
    Write-Host "• Consider adding more comprehensive test files" -ForegroundColor Yellow
}

Write-Host "• Use VS Code Coverage Gutters extension for visual coverage" -ForegroundColor Cyan
Write-Host "• Upload LCOV file to https://lcov-viewer.netlify.app/ for detailed analysis" -ForegroundColor Cyan
Write-Host "• Set up CI/CD pipeline with coverage gates" -ForegroundColor Cyan

Write-Host ""
Write-Host "✨ Coverage analysis complete!" -ForegroundColor Green
Write-Host ""
Write-Host "LCOV file location: $LcovPath" -ForegroundColor Gray
Write-Host "Upload to: https://lcov-viewer.netlify.app/" -ForegroundColor Gray
