#!/usr/bin/env python3
"""
Simple script to calculate test coverage from lcov.info file
"""

import re
import sys

def calculate_coverage(lcov_file_path):
    """Calculate coverage percentage from lcov.info file"""
    total_lines = 0
    covered_lines = 0
    
    try:
        with open(lcov_file_path, 'r') as file:
            content = file.read()
            
        # Find all LF (lines found) and LH (lines hit) entries
        lf_matches = re.findall(r'LF:(\d+)', content)
        lh_matches = re.findall(r'LH:(\d+)', content)
        
        if len(lf_matches) != len(lh_matches):
            print("Error: Mismatch between LF and LH entries")
            return None
            
        for lf, lh in zip(lf_matches, lh_matches):
            total_lines += int(lf)
            covered_lines += int(lh)
            
        if total_lines == 0:
            return 0
            
        coverage_percentage = (covered_lines / total_lines) * 100
        
        print(f"Coverage Analysis:")
        print(f"Total lines: {total_lines}")
        print(f"Covered lines: {covered_lines}")
        print(f"Coverage percentage: {coverage_percentage:.2f}%")
        
        return coverage_percentage
        
    except FileNotFoundError:
        print(f"Error: File {lcov_file_path} not found")
        return None
    except Exception as e:
        print(f"Error reading file: {e}")
        return None

if __name__ == "__main__":
    lcov_path = "coverage/lcov.info"
    coverage = calculate_coverage(lcov_path)
    
    if coverage is not None:
        print(f"\n🎯 Current Coverage: {coverage:.2f}%")
        
        # Compare against milestones
        milestones = {
            "Phase 1 (Data Layer)": 30,
            "Phase 2 (Domain Logic)": 50, 
            "Phase 3 (Presentation)": 70,
            "Production Ready": 70
        }
        
        print("\n📊 Milestone Progress:")
        for milestone, target in milestones.items():
            status = "✅" if coverage >= target else "🔄" if coverage >= target * 0.8 else "❌"
            print(f"   {status} {milestone}: {target}% (Current: {coverage:.1f}%)")
