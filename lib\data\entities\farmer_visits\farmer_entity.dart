import 'package:objectbox/objectbox.dart';

import '../../../domain/entities/farmer_visit/farmer.dart';
import 'visit_entity.dart';

@Entity()
class FarmerEntity {
  @Id()
  int id = 0;

  String name;
  String mobileNumber;

  // Define a ToMany relationship to VisitEntity
  // @Backlink() indicates that VisitEntity has a ToOne relationship back to FarmerEntity
  @Backlink()
  final visits = ToMany<VisitEntity>();

  FarmerEntity({required this.name, required this.mobileNumber});

  // Factory constructor to create a FarmerEntity from a Farmer domain entity
  factory FarmerEntity.fromDomain(Farmer farmer) {
    FarmerEntity entity = FarmerEntity(
      name: farmer.name,
      mobileNumber: farmer.mobileNumber,
    );

    // Set the ID if it exists in the domain entity
    if (farmer.id != null) {
      entity.id = farmer.id!;
    }

    return entity;
  }

  // Method to convert a FarmerEntity back to a Farmer domain entity
  Farmer toDomain() {
    return Farmer(
      id: id,
      name: name,
      mobileNumber: mobileNumber,
      // Map the related VisitEntities back to Visit domain entities
      visits: visits.map((v) => v.toDomain()).toList(),
    );
  }
}
