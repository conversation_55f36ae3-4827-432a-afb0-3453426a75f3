#!/bin/bash

echo "Attempting to fix iOS device connection issues..."

# Try to kill any potentially stuck processes
echo "Terminating potentially stuck processes..."
killall -9 usbd mDNSResponder

# Restart usbmuxd service
echo "Restarting usbmuxd service..."
sudo killall -9 usbmuxd

echo "======================="
echo "CONNECTION RESET ATTEMPTED"
echo "Please follow these steps:"
echo "1. Disconnect your iPhone from the Mac"
echo "2. Restart both your Mac and iPhone"
echo "3. Reconnect your iPhone and trust the computer when prompted"
echo "4. Once reconnected, run './run_ios.sh' again"
echo "======================="
