import 'package:aqua_ui/aqua_ui.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

import '../../core/utils/string_utils.dart';
import 'custome_badge.dart';

class ProductCardWidget extends StatelessWidget {
  final String brand;
  final String product;
  final String image;
  final String description;
  final String badge;
  final VoidCallback? onTap;
  const ProductCardWidget({
    super.key,
    required this.brand,
    required this.product,
    required this.image,
    required this.description,
    required this.badge,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Card(
        color: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
          side: BorderSide(color: Color(0xFFE2E2E2), width: 1),
        ),
        elevation: 0,
        child: Container(
          padding: const EdgeInsets.all(0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Product Image
              SizedBox(
                width: 100,
                height: 100,
                child: Padding(
                  padding: EdgeInsets.all(12),
                  child: CachedNetworkImage(
                    imageUrl: image,
                    fit: BoxFit.contain,
                    placeholder:
                        (context, url) => Center(
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: acPrimaryBlue,
                          ),
                        ),
                    errorWidget:
                        (context, url, error) =>
                            Icon(Icons.error, color: Colors.red),
                    // Add caching options
                    memCacheWidth: 200, // Adjust based on your needs
                    memCacheHeight: 300, // Adjust based on your needs
                    maxWidthDiskCache: 200,
                    maxHeightDiskCache: 300,
                  ),
                ),
              ),

              Expanded(
                child: Stack(
                  children: [
                    // Badge at top-right
                    Positioned(
                      top: 0,
                      right: 0,
                      child:
                          badge.isEmpty
                              ? Container()
                              : CustomBadge(
                                title: badge,
                                titleColor:
                                    badge == 'NEW ARRIVAL'
                                        ? acPrimaryBlue
                                        : acGreenColor,
                              ),
                    ),

                    // Content Column
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              AquaText.subheadline(
                                brand,
                                color: acGrey300,
                                weight: AquaFontWeight.bold,
                              ),
                              AquaText.headline(
                                StringUtils.decodeUnicodeEscapes(product),
                                weight: AquaFontWeight.bold,
                                color: acBlackColor,
                              ),
                              Padding(
                                padding: EdgeInsets.only(right: 5),
                                child: AquaText.subheadline(
                                  maxLines: 1,
                                  description,
                                  color: acTextSecondaryColor,
                                  weight: AquaFontWeight.regular,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}


/*

                    Align(
                      alignment: Alignment.topRight,
                      child:
                          badge.isEmpty
                              ? Container()
                              : Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 6,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color:
                                      badge == 'NEW ARRIVAL'
                                          ? Color.fromARGB(255, 190, 255, 229)
                                          : Color.fromARGB(255, 255, 243, 192),
                                  borderRadius: BorderRadius.only(
                                    topRight: Radius.circular(4),
                                  ),
                                ),
                                child: AquaText.caption(badge),
                              ),
                    ),


*/