import 'package:mongo_dart/mongo_dart.dart';

import '../../domain/entities/price_list.dart';

class PriceItemModel extends PriceItem {
  PriceItemModel({
    required super.count,
    required super.price,
  });

  factory PriceItemModel.fromJson(Map<String, dynamic> json) {
    return PriceItemModel(
      count: json['count'],
      price: json['price'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'count': count,
      'price': price,
    };
  }
PriceItem toDomain() {
    return PriceItem(
      count: count,
      price: price,
    );
  }

}

class PriceListModel extends PriceList {
  PriceListModel({
    super.id,
    required super.state,
    required super.date,
    required List<PriceItemModel> super.prices,
  });

  factory PriceListModel.fromJson(Map<String, dynamic> json) {
    // Handle ObjectId conversion to String
    String? idString;
    if (json['_id'] != null) {
      if (json['_id'] is ObjectId) {
        idString = (json['_id'] as ObjectId).oid;
      } else if (json['_id'] is String) {
        idString = json['_id'];
      }
    }

    return PriceListModel(
      id: idString, // Now optional
      state: json['state'],
      date: json['date'],
      prices: (json['prices'] as List)
          .map((item) => PriceItemModel.fromJson(item))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    final map = {
      'state': state,
      'date': date,
      'prices': (prices as List<PriceItemModel>).map((item) => item.toJson()).toList(),
    };
    
    // Only include _id if it exists
    if (id != null) {
      map['_id'] = id!;
    }
    
    return map;
  }

PriceList toDomain() {
    return PriceList(
      id: id,
      state: state,
      date: date,
      prices: prices.map((item) {
        if (item is PriceItemModel) {
          return item.toDomain();
        }
        return item;
      }).toList(),
    );
  }

}