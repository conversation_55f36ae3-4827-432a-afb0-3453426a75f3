# AquaPartner Flutter Payment Integration Guide

## Complete Zoho Payment API Documentation for AI Implementation

### Document Overview

This is a comprehensive, standalone guide for implementing Zoho Payment integration in the AquaPartner Flutter application. This document contains everything needed for immediate implementation without requiring additional context.

---

## 1. Business Context & Use Cases

### AquaPartner Platform Overview

AquaPartner is an aquaculture management platform serving retailers, farmers, and distributors in the aquaculture industry. The platform manages:

- Product orders and invoicing
- Customer due management with aging (16-30, 31-60, 61-90, >90 days)
- Account statements and transaction history
- Customer relationship management

### Payment Integration Use Cases

1. **Invoice Payments**: Pay outstanding invoices from billing section
2. **Due Settlements**: Pay aged dues with filtering by aging periods
3. **Bulk Payments**: Pay multiple invoices in single transaction
4. **Partial Payments**: Make partial payments on large invoices
5. **Emergency Orders**: Quick payment for urgent aquaculture supplies
6. **Seasonal Payments**: Handle aquaculture seasonal payment cycles
7. **Harvest Settlements**: Large payments during harvest seasons

### Customer Types & Payment Patterns

- **Retailers**: Frequent small-medium payments, mobile-first users
- **Distributors**: High-value transactions, bulk payment needs
- **Farmers**: Occasional payments, simple flows required

---

## 2. Complete API Reference

### Base Configuration

- **Base URL**: `https://partner.aquaconnect.blue`
- **Authentication**: Server-side OAuth 2.0 (no client-side token management required)
- **Content-Type**: `application/json`
- **Timeout**: 30 seconds for API calls
- **Currency**: INR (Indian Rupees)

### Core API Endpoints

#### 2.1 Health Check

**Endpoint**: `GET /api/zoho/health`
**Purpose**: Verify system status and connectivity
**Authentication**: None required

**Response Format**:

```json
{
  "timestamp": "2025-06-17T10:22:14.870Z",
  "service": "Zoho Payment Integration",
  "version": "1.0.0",
  "status": "healthy",
  "checks": {
    "database": { "status": "healthy", "message": "Database connection successful" },
    "environment": { "status": "healthy", "message": "All required environment variables are set" },
    "zoho_auth": { "status": "healthy", "message": "Zoho authentication token is available" },
    "zoho_api": { "status": "healthy", "message": "Zoho API is accessible" }
  },
  "summary": { "total_checks": 4, "healthy_checks": 4, "unhealthy_checks": 0 }
}
```

#### 2.2 Create Payment Session

**Endpoint**: `POST /api/zoho/payments/create-session`
**Purpose**: Create new payment session for processing payments
**Authentication**: Server-side handled

**Request Body**:

```json
{
  "amount": 1500.5,
  "currency": "INR",
  "description": "Payment for aquaculture products - Invoice INV-12345",
  "invoice_number": "INV-12345",
  "customer_id": "CUST-001",
  "customer_name": "ABC Aqua Farm",
  "customer_email": "<EMAIL>",
  "customer_phone": "+919876543210",
  "redirect_url": "https://yourapp.com/payment/success",
  "reference_id": "REF-12345",
  "meta_data": [
    { "key": "product_type", "value": "aquaculture" },
    { "key": "order_type", "value": "feed_supplies" },
    { "key": "payment_type", "value": "invoice" }
  ]
}
```

**Required Fields**: `amount`, `description`, `invoice_number`, `customer_id`

**Success Response (200)**:

```json
{
  "success": true,
  "message": "Payment session created successfully",
  "data": {
    "payment_session": {
      "payments_session_id": "PS_123456789",
      "amount": 1500.5,
      "currency": "INR",
      "status": "created",
      "created_time": 1640995200,
      "expires_at": 1640996100,
      "payment_url": "https://payments.zoho.in/checkout/PS_123456789"
    },
    "transaction_id": "64a1b2c3d4e5f6789012345",
    "expires_in": "15 minutes"
  }
}
```

#### 2.3 Check Payment Status

**Endpoint**: `GET /api/zoho/payments/status/{sessionId}`
**Purpose**: Get current payment status and details
**Authentication**: Server-side handled

**Success Response (200)**:

```json
{
  "success": true,
  "message": "Payment status retrieved successfully",
  "data": {
    "payment_session": {
      "payments_session_id": "PS_123456789",
      "amount": 1500.5,
      "currency": "INR",
      "status": "succeeded",
      "payment_id": "PAY_987654321",
      "payment_method": "upi",
      "created_time": 1640995200,
      "completed_time": 1640995800
    },
    "transaction": {
      "id": "64a1b2c3d4e5f6789012345",
      "customer_id": "CUST-001",
      "customer_name": "ABC Aqua Farm",
      "reference_id": "REF-12345",
      "created_at": "2024-01-01T10:00:00.000Z",
      "updated_at": "2024-01-01T10:10:00.000Z"
    }
  }
}
```

**Payment Status Values**:

- `created`: Payment session created, awaiting payment
- `pending`: Payment being processed
- `succeeded`: Payment completed successfully
- `failed`: Payment failed
- `cancelled`: Payment cancelled by user
- `expired`: Session expired (15 minutes timeout)

#### 2.4 List Customer Payments

**Endpoint**: `GET /api/zoho/payments/list`
**Purpose**: Get paginated list of customer payments with filtering
**Authentication**: Server-side handled

**Query Parameters**:

- `customer_id` (required): Customer identifier
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10, max: 100)
- `status` (optional): Filter by payment status
- `from_date` (optional): Start date (YYYY-MM-DD)
- `to_date` (optional): End date (YYYY-MM-DD)

**Example Request**:

```
GET /api/zoho/payments/list?customer_id=CUST-001&page=1&limit=20&status=succeeded
```

**Success Response (200)**:

```json
{
  "success": true,
  "message": "Payments retrieved successfully",
  "data": {
    "transactions": [
      {
        "_id": "64a1b2c3d4e5f6789012345",
        "payments_session_id": "PS_123456789",
        "amount": 1500.5,
        "currency": "INR",
        "status": "succeeded",
        "invoice_number": "INV-12345",
        "customer_id": "CUST-001",
        "payment_method": "upi",
        "created_at": "2024-01-01T10:00:00.000Z",
        "completed_at": "2024-01-01T10:10:00.000Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 45,
      "pages": 3
    }
  }
}
```

#### 2.5 Legacy Payment Endpoint (Backward Compatibility)

**Endpoint**: `POST /api/initiatePayment`
**Purpose**: Legacy endpoint for existing integrations
**Authentication**: Server-side handled

**Request Body**:

```json
{
  "amount": 1500.5,
  "invoiceNo": "INV-12345",
  "customerId": "CUST-001",
  "customerName": "ABC Aqua Farm",
  "customerEmail": "<EMAIL>",
  "customerPhone": "+919876543210",
  "redirectUrl": "https://yourapp.com/payment/success",
  "referenceId": "REF-12345"
}
```

**Success Response (200)**:

```json
{
  "result": "success",
  "paymentSession": {
    "payments_session_id": "PS_123456789",
    "amount": 1500.5,
    "currency": "INR",
    "status": "created",
    "payment_url": "https://payments.zoho.in/checkout/PS_123456789"
  },
  "transaction_id": "64a1b2c3d4e5f6789012345",
  "payment_session_id": "PS_123456789",
  "expires_in": "15 minutes"
}
```

#### 2.6 Create Refund

**Endpoint**: `POST /api/zoho/refunds/create`
**Purpose**: Process refunds for completed payments
**Authentication**: Server-side handled

**Request Body**:

```json
{
  "payment_id": "PAY_987654321",
  "amount": 750.25,
  "reason": "Customer request - partial order cancellation",
  "reference_id": "REF-REFUND-001"
}
```

**Success Response (200)**:

```json
{
  "success": true,
  "message": "Refund created successfully",
  "data": {
    "refund": {
      "refund_id": "REF_123456789",
      "payment_id": "PAY_987654321",
      "amount": 750.25,
      "status": "pending",
      "created_time": 1640995200,
      "reason": "Customer request - partial order cancellation"
    }
  }
}
```

### Error Response Format

All endpoints return consistent error format:

**Error Response (400/404/500)**:

```json
{
  "error": "Error Type",
  "message": "Detailed error description",
  "details": "Additional context or troubleshooting info"
}
```

**Common Error Codes**:

- `400`: Bad Request - Invalid input data
- `401`: Unauthorized - Authentication failed
- `404`: Not Found - Resource not found
- `500`: Internal Server Error - System error

---

## 3. Flutter Implementation Guide

### 3.1 Dependencies

Add to `pubspec.yaml`:

```yaml
dependencies:
  http: ^1.1.0
  url_launcher: ^6.2.1
  shared_preferences: ^2.2.2
  provider: ^6.1.1 # or riverpod for state management
```

### 3.2 Data Models

#### Payment Request Model

```dart
class PaymentRequest {
  final double amount;
  final String description;
  final String invoiceNumber;
  final String customerId;
  final String? customerName;
  final String? customerEmail;
  final String? customerPhone;
  final String? redirectUrl;
  final String? referenceId;
  final String currency;
  final List<MetaData>? metaData;

  PaymentRequest({
    required this.amount,
    required this.description,
    required this.invoiceNumber,
    required this.customerId,
    this.customerName,
    this.customerEmail,
    this.customerPhone,
    this.redirectUrl,
    this.referenceId,
    this.currency = 'INR',
    this.metaData,
  });

  Map<String, dynamic> toJson() => {
    'amount': amount,
    'description': description,
    'invoice_number': invoiceNumber,
    'customer_id': customerId,
    'customer_name': customerName,
    'customer_email': customerEmail,
    'customer_phone': customerPhone,
    'redirect_url': redirectUrl,
    'reference_id': referenceId,
    'currency': currency,
    'meta_data': metaData?.map((e) => e.toJson()).toList(),
  };
}

class MetaData {
  final String key;
  final String value;

  MetaData({required this.key, required this.value});

  Map<String, dynamic> toJson() => {'key': key, 'value': value};
}
```

#### Payment Session Model

```dart
class PaymentSession {
  final String paymentSessionId;
  final double amount;
  final String currency;
  final String status;
  final String paymentUrl;
  final int createdTime;
  final int expiresAt;
  final String transactionId;

  PaymentSession({
    required this.paymentSessionId,
    required this.amount,
    required this.currency,
    required this.status,
    required this.paymentUrl,
    required this.createdTime,
    required this.expiresAt,
    required this.transactionId,
  });

  factory PaymentSession.fromJson(Map<String, dynamic> json) => PaymentSession(
    paymentSessionId: json['payment_session']['payments_session_id'],
    amount: json['payment_session']['amount'].toDouble(),
    currency: json['payment_session']['currency'],
    status: json['payment_session']['status'],
    paymentUrl: json['payment_session']['payment_url'],
    createdTime: json['payment_session']['created_time'],
    expiresAt: json['payment_session']['expires_at'],
    transactionId: json['transaction_id'],
  );

  bool get isExpired => DateTime.now().millisecondsSinceEpoch > (expiresAt * 1000);
  bool get isActive => status == 'created' && !isExpired;
}
```

#### Payment Status Model

```dart
enum PaymentStatusType { created, pending, succeeded, failed, cancelled, expired }

class PaymentStatus {
  final String paymentSessionId;
  final PaymentStatusType status;
  final double amount;
  final String currency;
  final String? paymentId;
  final String? paymentMethod;
  final int? completedTime;
  final String? errorCode;
  final String? errorMessage;

  PaymentStatus({
    required this.paymentSessionId,
    required this.status,
    required this.amount,
    required this.currency,
    this.paymentId,
    this.paymentMethod,
    this.completedTime,
    this.errorCode,
    this.errorMessage,
  });

  factory PaymentStatus.fromJson(Map<String, dynamic> json) {
    final statusStr = json['payment_session']['status'] as String;
    final status = PaymentStatusType.values.firstWhere(
      (e) => e.name == statusStr,
      orElse: () => PaymentStatusType.created,
    );

    return PaymentStatus(
      paymentSessionId: json['payment_session']['payments_session_id'],
      status: status,
      amount: json['payment_session']['amount'].toDouble(),
      currency: json['payment_session']['currency'],
      paymentId: json['payment_session']['payment_id'],
      paymentMethod: json['payment_session']['payment_method'],
      completedTime: json['payment_session']['completed_time'],
      errorCode: json['payment_session']['error_code'],
      errorMessage: json['payment_session']['error_message'],
    );
  }

  bool get isCompleted => status == PaymentStatusType.succeeded;
  bool get isFailed => status == PaymentStatusType.failed;
  bool get isPending => status == PaymentStatusType.pending;
  bool get isCancelled => status == PaymentStatusType.cancelled;
  bool get isExpired => status == PaymentStatusType.expired;
  bool get isFinalized => isCompleted || isFailed || isCancelled || isExpired;
}
```

#### Payment Transaction Model

```dart
class PaymentTransaction {
  final String id;
  final String paymentSessionId;
  final double amount;
  final String currency;
  final PaymentStatusType status;
  final String invoiceNumber;
  final String customerId;
  final String? paymentMethod;
  final DateTime createdAt;
  final DateTime? completedAt;

  PaymentTransaction({
    required this.id,
    required this.paymentSessionId,
    required this.amount,
    required this.currency,
    required this.status,
    required this.invoiceNumber,
    required this.customerId,
    this.paymentMethod,
    required this.createdAt,
    this.completedAt,
  });

  factory PaymentTransaction.fromJson(Map<String, dynamic> json) {
    final statusStr = json['status'] as String;
    final status = PaymentStatusType.values.firstWhere(
      (e) => e.name == statusStr,
      orElse: () => PaymentStatusType.created,
    );

    return PaymentTransaction(
      id: json['_id'],
      paymentSessionId: json['payments_session_id'],
      amount: json['amount'].toDouble(),
      currency: json['currency'],
      status: status,
      invoiceNumber: json['invoice_number'],
      customerId: json['customer_id'],
      paymentMethod: json['payment_method'],
      createdAt: DateTime.parse(json['created_at']),
      completedAt: json['completed_at'] != null ? DateTime.parse(json['completed_at']) : null,
    );
  }
}
```

#### Payment Exception Model

```dart
class PaymentException implements Exception {
  final String message;
  final int? statusCode;
  final Map<String, dynamic>? details;

  PaymentException(this.message, {this.statusCode, this.details});

  factory PaymentException.fromResponse(http.Response response) {
    try {
      final data = jsonDecode(response.body);
      return PaymentException(
        data['message'] ?? 'Payment operation failed',
        statusCode: response.statusCode,
        details: data,
      );
    } catch (e) {
      return PaymentException(
        'Failed to parse error response',
        statusCode: response.statusCode,
      );
    }
  }

  @override
  String toString() => 'PaymentException: $message';
}
```

### 3.3 Payment Service Class

```dart
import 'dart:convert';
import 'dart:async';
import 'package:http/http.dart' as http;

class AquaPartnerPaymentService {
  static const String baseUrl = 'https://partner.aquaconnect.blue/api';
  static const Duration defaultTimeout = Duration(seconds: 30);

  // Create payment session for AquaPartner invoices/dues
  Future<PaymentSession> createPaymentSession(PaymentRequest request) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/zoho/payments/create-session'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(request.toJson()),
      ).timeout(defaultTimeout);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success'] == true) {
          return PaymentSession.fromJson(data['data']);
        } else {
          throw PaymentException(data['message'] ?? 'Payment session creation failed');
        }
      } else {
        throw PaymentException.fromResponse(response);
      }
    } on TimeoutException {
      throw PaymentException('Request timeout - please check your connection');
    } catch (e) {
      if (e is PaymentException) rethrow;
      throw PaymentException('Network error: ${e.toString()}');
    }
  }

  // Get payment status with polling support
  Future<PaymentStatus> getPaymentStatus(String sessionId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/zoho/payments/status/$sessionId'),
      ).timeout(defaultTimeout);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success'] == true) {
          return PaymentStatus.fromJson(data['data']);
        } else {
          throw PaymentException(data['message'] ?? 'Failed to get payment status');
        }
      } else {
        throw PaymentException.fromResponse(response);
      }
    } on TimeoutException {
      throw PaymentException('Request timeout - please check your connection');
    } catch (e) {
      if (e is PaymentException) rethrow;
      throw PaymentException('Network error: ${e.toString()}');
    }
  }

  // Poll payment status until completion or timeout
  Future<PaymentStatus> pollPaymentStatus(
    String sessionId, {
    Duration interval = const Duration(seconds: 3),
    Duration timeout = const Duration(minutes: 5),
  }) async {
    final startTime = DateTime.now();

    while (DateTime.now().difference(startTime) < timeout) {
      try {
        final status = await getPaymentStatus(sessionId);

        if (status.isFinalized) {
          return status;
        }

        await Future.delayed(interval);
      } catch (e) {
        // Continue polling on errors, but log them
        print('Error polling payment status: $e');
        await Future.delayed(interval);
      }
    }

    throw PaymentException('Payment status polling timeout');
  }

  // Get customer payment history for AquaPartner customers
  Future<List<PaymentTransaction>> getCustomerPayments({
    required String customerId,
    int page = 1,
    int limit = 20,
    PaymentStatusType? status,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      final queryParams = <String, String>{
        'customer_id': customerId,
        'page': page.toString(),
        'limit': limit.toString(),
      };

      if (status != null) queryParams['status'] = status.name;
      if (fromDate != null) queryParams['from_date'] = fromDate.toIso8601String().split('T')[0];
      if (toDate != null) queryParams['to_date'] = toDate.toIso8601String().split('T')[0];

      final uri = Uri.parse('$baseUrl/zoho/payments/list').replace(queryParameters: queryParams);
      final response = await http.get(uri).timeout(defaultTimeout);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success'] == true) {
          final transactions = (data['data']['transactions'] as List)
              .map((json) => PaymentTransaction.fromJson(json))
              .toList();
          return transactions;
        } else {
          throw PaymentException(data['message'] ?? 'Failed to get payment history');
        }
      } else {
        throw PaymentException.fromResponse(response);
      }
    } on TimeoutException {
      throw PaymentException('Request timeout - please check your connection');
    } catch (e) {
      if (e is PaymentException) rethrow;
      throw PaymentException('Network error: ${e.toString()}');
    }
  }

  // Legacy payment method for backward compatibility
  Future<PaymentSession> createLegacyPayment({
    required double amount,
    required String invoiceNo,
    required String customerId,
    String? customerName,
    String? customerEmail,
    String? customerPhone,
    String? redirectUrl,
    String? referenceId,
  }) async {
    try {
      final requestBody = {
        'amount': amount,
        'invoiceNo': invoiceNo,
        'customerId': customerId,
        'customerName': customerName,
        'customerEmail': customerEmail,
        'customerPhone': customerPhone,
        'redirectUrl': redirectUrl,
        'referenceId': referenceId,
      };

      final response = await http.post(
        Uri.parse('$baseUrl/initiatePayment'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(requestBody),
      ).timeout(defaultTimeout);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['result'] == 'success') {
          // Convert legacy response to PaymentSession format
          return PaymentSession(
            paymentSessionId: data['payment_session_id'],
            amount: data['paymentSession']['amount'].toDouble(),
            currency: data['paymentSession']['currency'],
            status: data['paymentSession']['status'],
            paymentUrl: data['paymentSession']['payment_url'],
            createdTime: data['paymentSession']['created_time'],
            expiresAt: data['paymentSession']['expires_at'],
            transactionId: data['transaction_id'],
          );
        } else {
          throw PaymentException(data['message'] ?? 'Legacy payment creation failed');
        }
      } else {
        throw PaymentException.fromResponse(response);
      }
    } on TimeoutException {
      throw PaymentException('Request timeout - please check your connection');
    } catch (e) {
      if (e is PaymentException) rethrow;
      throw PaymentException('Network error: ${e.toString()}');
    }
  }

  // Check system health
  Future<bool> checkSystemHealth() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/zoho/health'),
      ).timeout(defaultTimeout);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data['status'] == 'healthy';
      }
      return false;
    } catch (e) {
      return false;
    }
  }
}
```

### 3.4 AquaPartner-Specific Payment Flows

#### Invoice Payment Flow

```dart
class InvoicePaymentFlow {
  final AquaPartnerPaymentService _paymentService = AquaPartnerPaymentService();

  Future<PaymentSession> payInvoice({
    required String invoiceNumber,
    required double amount,
    required String customerId,
    required String customerName,
    required String customerEmail,
    String? customerPhone,
  }) async {
    final request = PaymentRequest(
      amount: amount,
      description: 'Payment for Invoice $invoiceNumber - Aquaculture Products',
      invoiceNumber: invoiceNumber,
      customerId: customerId,
      customerName: customerName,
      customerEmail: customerEmail,
      customerPhone: customerPhone,
      metaData: [
        MetaData(key: 'payment_type', value: 'invoice'),
        MetaData(key: 'business_type', value: 'aquaculture'),
        MetaData(key: 'invoice_number', value: invoiceNumber),
      ],
    );

    return await _paymentService.createPaymentSession(request);
  }
}
```

#### Due Settlement Flow

```dart
class DueSettlementFlow {
  final AquaPartnerPaymentService _paymentService = AquaPartnerPaymentService();

  Future<PaymentSession> settleDues({
    required List<String> dueIds,
    required double totalAmount,
    required String customerId,
    required String customerName,
    required String customerEmail,
    required String agingCategory, // "16-30", "31-60", "61-90", ">90"
  }) async {
    final request = PaymentRequest(
      amount: totalAmount,
      description: 'Due Settlement - $agingCategory days aging (${dueIds.length} items)',
      invoiceNumber: 'DUE-SETTLEMENT-${DateTime.now().millisecondsSinceEpoch}',
      customerId: customerId,
      customerName: customerName,
      customerEmail: customerEmail,
      metaData: [
        MetaData(key: 'payment_type', value: 'due_settlement'),
        MetaData(key: 'aging_category', value: agingCategory),
        MetaData(key: 'due_count', value: dueIds.length.toString()),
        MetaData(key: 'due_ids', value: dueIds.join(',')),
      ],
    );

    return await _paymentService.createPaymentSession(request);
  }
}
```

#### Bulk Payment Flow

```dart
class BulkPaymentFlow {
  final AquaPartnerPaymentService _paymentService = AquaPartnerPaymentService();

  Future<PaymentSession> payMultipleInvoices({
    required List<String> invoiceNumbers,
    required double totalAmount,
    required String customerId,
    required String customerName,
    required String customerEmail,
  }) async {
    final request = PaymentRequest(
      amount: totalAmount,
      description: 'Bulk Payment - ${invoiceNumbers.length} invoices',
      invoiceNumber: 'BULK-${DateTime.now().millisecondsSinceEpoch}',
      customerId: customerId,
      customerName: customerName,
      customerEmail: customerEmail,
      metaData: [
        MetaData(key: 'payment_type', value: 'bulk_payment'),
        MetaData(key: 'invoice_count', value: invoiceNumbers.length.toString()),
        MetaData(key: 'invoice_numbers', value: invoiceNumbers.join(',')),
      ],
    );

    return await _paymentService.createPaymentSession(request);
  }
}
```

### 3.5 Complete Payment Widget Example

```dart
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

class AquaPartnerPaymentWidget extends StatefulWidget {
  final PaymentRequest paymentRequest;
  final Function(PaymentStatus) onPaymentComplete;
  final VoidCallback? onCancel;

  const AquaPartnerPaymentWidget({
    Key? key,
    required this.paymentRequest,
    required this.onPaymentComplete,
    this.onCancel,
  }) : super(key: key);

  @override
  _AquaPartnerPaymentWidgetState createState() => _AquaPartnerPaymentWidgetState();
}

class _AquaPartnerPaymentWidgetState extends State<AquaPartnerPaymentWidget> {
  final AquaPartnerPaymentService _paymentService = AquaPartnerPaymentService();

  PaymentSession? _paymentSession;
  PaymentStatus? _currentStatus;
  bool _isLoading = false;
  String? _error;
  Timer? _statusTimer;

  @override
  void initState() {
    super.initState();
    _createPaymentSession();
  }

  @override
  void dispose() {
    _statusTimer?.cancel();
    super.dispose();
  }

  Future<void> _createPaymentSession() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final session = await _paymentService.createPaymentSession(widget.paymentRequest);
      setState(() {
        _paymentSession = session;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _launchPayment() async {
    if (_paymentSession == null) return;

    try {
      final uri = Uri.parse(_paymentSession!.paymentUrl);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
        _startStatusPolling();
      } else {
        setState(() {
          _error = 'Cannot launch payment URL';
        });
      }
    } catch (e) {
      setState(() {
        _error = 'Failed to launch payment: $e';
      });
    }
  }

  void _startStatusPolling() {
    _statusTimer = Timer.periodic(Duration(seconds: 3), (timer) async {
      if (_paymentSession == null) return;

      try {
        final status = await _paymentService.getPaymentStatus(_paymentSession!.paymentSessionId);
        setState(() {
          _currentStatus = status;
        });

        if (status.isFinalized) {
          timer.cancel();
          widget.onPaymentComplete(status);
        }
      } catch (e) {
        print('Error polling payment status: $e');
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: EdgeInsets.all(16),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'AquaPartner Payment',
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16),

            if (_isLoading) ...[
              Center(child: CircularProgressIndicator()),
              SizedBox(height: 8),
              Text('Creating payment session...', textAlign: TextAlign.center),
            ],

            if (_error != null) ...[
              Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red.shade200),
                ),
                child: Column(
                  children: [
                    Icon(Icons.error, color: Colors.red),
                    SizedBox(height: 8),
                    Text(_error!, style: TextStyle(color: Colors.red.shade700)),
                    SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: _createPaymentSession,
                      child: Text('Retry'),
                    ),
                  ],
                ),
              ),
            ],

            if (_paymentSession != null && _error == null) ...[
              _buildPaymentDetails(),
              SizedBox(height: 16),

              if (_currentStatus == null) ...[
                ElevatedButton.icon(
                  onPressed: _launchPayment,
                  icon: Icon(Icons.payment),
                  label: Text('Proceed to Payment'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ],

              if (_currentStatus != null) ...[
                _buildStatusIndicator(),
              ],
            ],

            SizedBox(height: 8),
            TextButton(
              onPressed: widget.onCancel,
              child: Text('Cancel'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentDetails() {
    return Container(
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Payment Details', style: TextStyle(fontWeight: FontWeight.bold)),
          SizedBox(height: 8),
          _buildDetailRow('Amount', '₹${_paymentSession!.amount.toStringAsFixed(2)}'),
          _buildDetailRow('Invoice', widget.paymentRequest.invoiceNumber),
          _buildDetailRow('Customer', widget.paymentRequest.customerName ?? 'N/A'),
          _buildDetailRow('Session ID', _paymentSession!.paymentSessionId),
          if (_paymentSession!.isExpired)
            Padding(
              padding: EdgeInsets.only(top: 8),
              child: Text(
                'Session Expired',
                style: TextStyle(color: Colors.red, fontWeight: FontWeight.bold),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: TextStyle(color: Colors.grey.shade600)),
          Flexible(child: Text(value, style: TextStyle(fontWeight: FontWeight.w500))),
        ],
      ),
    );
  }

  Widget _buildStatusIndicator() {
    final status = _currentStatus!;
    Color statusColor;
    IconData statusIcon;
    String statusText;

    switch (status.status) {
      case PaymentStatusType.pending:
        statusColor = Colors.orange;
        statusIcon = Icons.hourglass_empty;
        statusText = 'Processing Payment...';
        break;
      case PaymentStatusType.succeeded:
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        statusText = 'Payment Successful!';
        break;
      case PaymentStatusType.failed:
        statusColor = Colors.red;
        statusIcon = Icons.error;
        statusText = 'Payment Failed';
        break;
      case PaymentStatusType.cancelled:
        statusColor = Colors.grey;
        statusIcon = Icons.cancel;
        statusText = 'Payment Cancelled';
        break;
      case PaymentStatusType.expired:
        statusColor = Colors.red;
        statusIcon = Icons.timer_off;
        statusText = 'Payment Expired';
        break;
      default:
        statusColor = Colors.blue;
        statusIcon = Icons.info;
        statusText = 'Payment Created';
    }

    return Container(
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: statusColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: statusColor.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(statusIcon, color: statusColor, size: 32),
          SizedBox(height: 8),
          Text(
            statusText,
            style: TextStyle(
              color: statusColor,
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
          if (status.paymentMethod != null) ...[
            SizedBox(height: 4),
            Text('Method: ${status.paymentMethod}'),
          ],
          if (status.errorMessage != null) ...[
            SizedBox(height: 4),
            Text(
              'Error: ${status.errorMessage}',
              style: TextStyle(color: Colors.red.shade700),
            ),
          ],
        ],
      ),
    );
  }
}
```

---

## 4. Testing Instructions

### 4.1 API Testing with PowerShell (Windows)

#### Test System Health

```powershell
Invoke-RestMethod -Uri "https://partner.aquaconnect.blue/api/zoho/health" -Method GET
```

#### Test Payment Session Creation

```powershell
$paymentData = @{
    amount = 1500.50
    description = "Test Payment - Aquaculture Products"
    invoice_number = "TEST-INV-$(Get-Date -Format 'yyyyMMddHHmmss')"
    customer_id = "TEST-CUST-001"
    customer_name = "Test Aqua Farm"
    customer_email = "<EMAIL>"
    customer_phone = "+919876543210"
    meta_data = @(
        @{ key = "product_type"; value = "aquaculture" }
        @{ key = "test_mode"; value = "true" }
    )
} | ConvertTo-Json -Depth 3

$response = Invoke-RestMethod -Uri "https://partner.aquaconnect.blue/api/zoho/payments/create-session" -Method POST -Body $paymentData -ContentType "application/json"
$sessionId = $response.data.payment_session.payments_session_id
Write-Host "Payment Session Created: $sessionId"
Write-Host "Payment URL: $($response.data.payment_session.payment_url)"
```

#### Test Payment Status Check

```powershell
# Replace SESSION_ID with actual session ID from previous test
$sessionId = "PS_YOUR_SESSION_ID"
Invoke-RestMethod -Uri "https://partner.aquaconnect.blue/api/zoho/payments/status/$sessionId" -Method GET
```

#### Test Customer Payment History

```powershell
Invoke-RestMethod -Uri "https://partner.aquaconnect.blue/api/zoho/payments/list?customer_id=TEST-CUST-001&limit=10" -Method GET
```

### 4.2 Flutter Testing

#### Unit Tests for Payment Service

```dart
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:http/http.dart' as http;

void main() {
  group('AquaPartnerPaymentService Tests', () {
    late AquaPartnerPaymentService paymentService;
    late MockClient mockClient;

    setUp(() {
      mockClient = MockClient();
      paymentService = AquaPartnerPaymentService();
    });

    test('should create payment session successfully', () async {
      // Arrange
      final request = PaymentRequest(
        amount: 1500.50,
        description: 'Test Payment',
        invoiceNumber: 'TEST-INV-001',
        customerId: 'TEST-CUST-001',
      );

      final mockResponse = {
        'success': true,
        'data': {
          'payment_session': {
            'payments_session_id': 'PS_123456789',
            'amount': 1500.50,
            'currency': 'INR',
            'status': 'created',
            'payment_url': 'https://payments.zoho.in/checkout/PS_123456789',
            'created_time': 1640995200,
            'expires_at': 1640996100,
          },
          'transaction_id': 'TXN_123456789',
        }
      };

      when(mockClient.post(any, headers: anyNamed('headers'), body: anyNamed('body')))
          .thenAnswer((_) async => http.Response(jsonEncode(mockResponse), 200));

      // Act
      final session = await paymentService.createPaymentSession(request);

      // Assert
      expect(session.paymentSessionId, equals('PS_123456789'));
      expect(session.amount, equals(1500.50));
      expect(session.status, equals('created'));
      expect(session.paymentUrl, contains('zoho.in'));
    });

    test('should handle payment session creation failure', () async {
      // Arrange
      final request = PaymentRequest(
        amount: -100.0, // Invalid amount
        description: 'Test Payment',
        invoiceNumber: 'TEST-INV-001',
        customerId: 'TEST-CUST-001',
      );

      when(mockClient.post(any, headers: anyNamed('headers'), body: anyNamed('body')))
          .thenAnswer((_) async => http.Response('{"error": "Invalid amount"}', 400));

      // Act & Assert
      expect(
        () => paymentService.createPaymentSession(request),
        throwsA(isA<PaymentException>()),
      );
    });

    test('should poll payment status until completion', () async {
      // Arrange
      final sessionId = 'PS_123456789';
      var callCount = 0;

      when(mockClient.get(any)).thenAnswer((_) async {
        callCount++;
        final status = callCount < 3 ? 'pending' : 'succeeded';
        final mockResponse = {
          'success': true,
          'data': {
            'payment_session': {
              'payments_session_id': sessionId,
              'status': status,
              'amount': 1500.50,
              'currency': 'INR',
            }
          }
        };
        return http.Response(jsonEncode(mockResponse), 200);
      });

      // Act
      final finalStatus = await paymentService.pollPaymentStatus(
        sessionId,
        interval: Duration(milliseconds: 100),
        timeout: Duration(seconds: 5),
      );

      // Assert
      expect(finalStatus.status, equals(PaymentStatusType.succeeded));
      expect(callCount, greaterThanOrEqualTo(3));
    });
  });
}
```

#### Widget Tests for Payment UI

```dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('AquaPartnerPaymentWidget Tests', () {
    testWidgets('should display payment details correctly', (WidgetTester tester) async {
      // Arrange
      final paymentRequest = PaymentRequest(
        amount: 1500.50,
        description: 'Test Payment',
        invoiceNumber: 'TEST-INV-001',
        customerId: 'TEST-CUST-001',
        customerName: 'Test Customer',
      );

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AquaPartnerPaymentWidget(
              paymentRequest: paymentRequest,
              onPaymentComplete: (status) {},
            ),
          ),
        ),
      );

      // Assert
      expect(find.text('AquaPartner Payment'), findsOneWidget);
      expect(find.text('₹1500.50'), findsOneWidget);
      expect(find.text('TEST-INV-001'), findsOneWidget);
      expect(find.text('Test Customer'), findsOneWidget);
    });

    testWidgets('should show loading state during session creation', (WidgetTester tester) async {
      // Arrange
      final paymentRequest = PaymentRequest(
        amount: 1500.50,
        description: 'Test Payment',
        invoiceNumber: 'TEST-INV-001',
        customerId: 'TEST-CUST-001',
      );

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AquaPartnerPaymentWidget(
              paymentRequest: paymentRequest,
              onPaymentComplete: (status) {},
            ),
          ),
        ),
      );

      // Assert
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text('Creating payment session...'), findsOneWidget);
    });
  });
}
```

### 4.3 Integration Testing

#### End-to-End Payment Flow Test

```dart
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Payment Integration Tests', () {
    testWidgets('complete payment flow', (WidgetTester tester) async {
      // 1. Launch app
      await tester.pumpWidget(MyApp());
      await tester.pumpAndSettle();

      // 2. Navigate to payment screen
      await tester.tap(find.text('Make Payment'));
      await tester.pumpAndSettle();

      // 3. Fill payment details
      await tester.enterText(find.byKey(Key('amount_field')), '1500.50');
      await tester.enterText(find.byKey(Key('invoice_field')), 'TEST-INV-001');
      await tester.enterText(find.byKey(Key('customer_field')), 'TEST-CUST-001');

      // 4. Create payment session
      await tester.tap(find.text('Create Payment'));
      await tester.pumpAndSettle();

      // 5. Verify payment session created
      expect(find.text('Proceed to Payment'), findsOneWidget);
      expect(find.text('₹1500.50'), findsOneWidget);

      // 6. Launch payment (this would open external browser in real scenario)
      await tester.tap(find.text('Proceed to Payment'));
      await tester.pumpAndSettle();

      // 7. Verify status polling starts
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });
  });
}
```

---

## 5. Implementation Guidelines

### 5.1 Quick Start Checklist

#### Prerequisites

- [ ] Flutter SDK 3.0+ installed
- [ ] HTTP package added to pubspec.yaml
- [ ] URL launcher package added
- [ ] Internet permissions configured

#### Implementation Steps

1. **Copy Data Models**: Add all model classes to your project
2. **Add Payment Service**: Implement AquaPartnerPaymentService class
3. **Create Payment Flows**: Add AquaPartner-specific payment flows
4. **Build UI Components**: Implement payment widgets
5. **Add Error Handling**: Implement comprehensive error handling
6. **Test Integration**: Run tests to verify functionality

### 5.2 AquaPartner Integration Points

#### Billing & Payments Section Integration

```dart
// In your existing billing screen
class BillingScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Billing & Payments')),
      body: Column(
        children: [
          // Existing billing content

          // Add payment integration
          ElevatedButton(
            onPressed: () => _navigateToPayment(context),
            child: Text('Pay Outstanding Invoices'),
          ),
        ],
      ),
    );
  }

  void _navigateToPayment(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PaymentSelectionScreen(),
      ),
    );
  }
}
```

#### Customer Context Integration

```dart
// Use existing customer data
class PaymentContextProvider {
  static PaymentRequest createFromInvoice({
    required Invoice invoice,
    required Customer customer,
  }) {
    return PaymentRequest(
      amount: invoice.amount,
      description: 'Payment for ${invoice.description}',
      invoiceNumber: invoice.invoiceNumber,
      customerId: customer.id,
      customerName: customer.name,
      customerEmail: customer.email,
      customerPhone: customer.phone,
      metaData: [
        MetaData(key: 'invoice_id', value: invoice.id),
        MetaData(key: 'customer_type', value: customer.type),
        MetaData(key: 'business_type', value: 'aquaculture'),
      ],
    );
  }
}
```

### 5.3 Error Handling Best Practices

#### Network Error Handling

```dart
class PaymentErrorHandler {
  static String getErrorMessage(PaymentException error) {
    switch (error.statusCode) {
      case 400:
        return 'Invalid payment details. Please check and try again.';
      case 401:
        return 'Authentication failed. Please contact support.';
      case 404:
        return 'Payment session not found. Please create a new payment.';
      case 500:
        return 'Server error. Please try again later.';
      default:
        return 'Payment failed. Please check your connection and try again.';
    }
  }

  static void showErrorDialog(BuildContext context, PaymentException error) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Payment Error'),
        content: Text(getErrorMessage(error)),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('OK'),
          ),
        ],
      ),
    );
  }
}
```

### 5.4 State Management Integration

#### Using Provider

```dart
class PaymentProvider extends ChangeNotifier {
  final AquaPartnerPaymentService _paymentService = AquaPartnerPaymentService();

  PaymentSession? _currentSession;
  PaymentStatus? _currentStatus;
  bool _isLoading = false;
  String? _error;

  PaymentSession? get currentSession => _currentSession;
  PaymentStatus? get currentStatus => _currentStatus;
  bool get isLoading => _isLoading;
  String? get error => _error;

  Future<void> createPaymentSession(PaymentRequest request) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      _currentSession = await _paymentService.createPaymentSession(request);
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> checkPaymentStatus() async {
    if (_currentSession == null) return;

    try {
      _currentStatus = await _paymentService.getPaymentStatus(_currentSession!.paymentSessionId);
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      notifyListeners();
    }
  }
}
```

### 5.5 Security Considerations

#### Data Protection

- Never store sensitive payment data locally
- Use HTTPS for all API communications
- Validate all user inputs before sending to API
- Implement proper session timeout handling

#### Error Information

- Don't expose sensitive error details to users
- Log errors securely for debugging
- Provide user-friendly error messages
- Implement proper retry mechanisms

---

## 6. Production Deployment

### 6.1 Environment Configuration

- Update base URL to production: `https://partner.aquaconnect.blue`
- Ensure all environment variables are properly configured
- Test with production Zoho Payment account
- Verify webhook endpoints are accessible

### 6.2 Performance Optimization

- Implement proper caching for payment history
- Use connection pooling for HTTP requests
- Optimize UI rendering for payment screens
- Implement proper memory management

### 6.3 Monitoring & Analytics

- Track payment success/failure rates
- Monitor API response times
- Log payment flow events
- Implement crash reporting

---

## 7. Support & Troubleshooting

### 7.1 Common Issues

**Payment Session Creation Fails**

- Check network connectivity
- Verify customer data is valid
- Ensure amount is positive
- Check API endpoint availability

**Payment Status Not Updating**

- Verify webhook configuration
- Check status polling implementation
- Ensure session ID is correct
- Test manual status check

**Payment URL Not Opening**

- Check URL launcher configuration
- Verify payment URL format
- Test with different browsers
- Check device permissions

### 7.2 Debug Mode

Enable debug logging for development:

```dart
class PaymentDebugger {
  static bool isDebugMode = true; // Set to false for production

  static void log(String message) {
    if (isDebugMode) {
      print('[PaymentDebug] $message');
    }
  }
}
```

---

## 8. API Reference Summary

### Quick Reference Table

| Endpoint                            | Method | Purpose                        | Required Fields                                  |
| ----------------------------------- | ------ | ------------------------------ | ------------------------------------------------ |
| `/api/zoho/health`                  | GET    | System health check            | None                                             |
| `/api/zoho/payments/create-session` | POST   | Create payment session         | amount, description, invoice_number, customer_id |
| `/api/zoho/payments/status/{id}`    | GET    | Get payment status             | sessionId (path)                                 |
| `/api/zoho/payments/list`           | GET    | List customer payments         | customer_id (query)                              |
| `/api/zoho/refunds/create`          | POST   | Create refund                  | payment_id, amount                               |
| `/api/initiatePayment`              | POST   | Legacy payment (compatibility) | amount, invoiceNo, customerId                    |

### Response Status Codes

- **200**: Success
- **400**: Bad Request (invalid input)
- **401**: Unauthorized (authentication failed)
- **404**: Not Found (resource not found)
- **500**: Internal Server Error (system error)

---

**Document Version**: 1.0.0
**Last Updated**: June 17, 2025
**API Base URL**: https://partner.aquaconnect.blue
**Support**: Contact AquaPartner development team

This documentation provides everything needed to implement Zoho Payment integration in the AquaPartner Flutter application. The code examples are production-ready and follow Flutter best practices for the aquaculture business context.
