import 'package:equatable/equatable.dart';

import '../../../domain/entities/scheme/customer_scheme.dart';

abstract class SchemeState extends Equatable {
  const SchemeState();

  @override
  List<Object?> get props => [];
}

class SchemeInitial extends SchemeState {}

class SchemeLoading extends SchemeState {
  final CustomerScheme? previousScheme;
  final DateTime? lastSyncTime;

  const SchemeLoading({this.previousScheme, this.lastSyncTime});

  @override
  List<Object?> get props => [previousScheme, lastSyncTime];
}

class SchemeLoaded extends SchemeState {
  final CustomerScheme scheme;
  final bool isSyncing;
  final DateTime? lastSyncTime;
  final bool hasError;
  final String? errorMessage;

  const SchemeLoaded({
    required this.scheme,
    required this.isSyncing,
    this.lastSyncTime,
    this.hasError = false,
    this.errorMessage,
  });

  @override
  List<Object?> get props => [
    scheme,
    isSyncing,
    lastSyncTime,
    hasError,
    errorMessage,
  ];

  SchemeLoaded copyWith({
    CustomerScheme? scheme,
    bool? isSyncing,
    DateTime? lastSyncTime,
    bool? hasError,
    String? errorMessage,
  }) {
    return SchemeLoaded(
      scheme: scheme ?? this.scheme,
      isSyncing: isSyncing ?? this.isSyncing,
      lastSyncTime: lastSyncTime ?? this.lastSyncTime,
      hasError: hasError ?? this.hasError,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}

class SchemeError extends SchemeState {
  final String message;

  const SchemeError(this.message);

  @override
  List<Object> get props => [message];
}
