import 'package:flutter_test/flutter_test.dart';
import 'package:aquapartner/data/models/payments/customer_payment_model.dart';
import 'package:aquapartner/domain/entities/payments/customer_payment.dart';

void main() {
  group('CustomerPaymentModel Tests', () {
    late CustomerPaymentModel testCustomerPaymentModel;
    late Map<String, dynamic> testJson;
    late DateTime testDateTime;

    setUp(() {
      testDateTime = DateTime(2024, 1, 15, 10, 30, 0);
      
      testCustomerPaymentModel = CustomerPaymentModel(
        id: 'payment_123',
        customerId: 'customer_123',
        customerName: 'Test Customer',
        categoryType: 'Feed',
        amount: '5000.00',
        paymentsDate: testDateTime.toIso8601String(),
        paymentId: 'pay_123',
        paymentsMode: 'Bank Transfer',
        paymentsNumber: 'TXN123456789',
      );

      testJson = {
        'id': 'payment_123',
        'customerId': 'customer_123',
        'customerName': 'Test Customer',
        'categoryType': 'Feed',
        'amount': '5000.00',
        'paymentsDate': testDateTime.toIso8601String(),
        'paymentId': 'pay_123',
        'paymentsMode': 'Bank Transfer',
        'paymentsNumber': 'TXN123456789',
      };
    });

    group('Constructor Tests', () {
      test('should create CustomerPaymentModel with all required fields', () {
        expect(testCustomerPaymentModel.id, equals('payment_123'));
        expect(testCustomerPaymentModel.customerId, equals('customer_123'));
        expect(testCustomerPaymentModel.customerName, equals('Test Customer'));
        expect(testCustomerPaymentModel.categoryType, equals('Feed'));
        expect(testCustomerPaymentModel.amount, equals('5000.00'));
        expect(testCustomerPaymentModel.paymentsDate, equals(testDateTime.toIso8601String()));
        expect(testCustomerPaymentModel.paymentId, equals('pay_123'));
        expect(testCustomerPaymentModel.paymentsMode, equals('Bank Transfer'));
        expect(testCustomerPaymentModel.paymentsNumber, equals('TXN123456789'));
        expect(testCustomerPaymentModel.dbId, equals(0));
      });

      test('should create CustomerPaymentModel with empty string values', () {
        final emptyPaymentModel = CustomerPaymentModel(
          id: '',
          customerId: '',
          customerName: '',
          categoryType: '',
          amount: '',
          paymentsDate: '',
          paymentId: '',
          paymentsMode: '',
          paymentsNumber: '',
        );

        expect(emptyPaymentModel.id, isEmpty);
        expect(emptyPaymentModel.customerId, isEmpty);
        expect(emptyPaymentModel.customerName, isEmpty);
        expect(emptyPaymentModel.categoryType, isEmpty);
        expect(emptyPaymentModel.amount, isEmpty);
        expect(emptyPaymentModel.paymentsDate, isEmpty);
        expect(emptyPaymentModel.paymentId, isEmpty);
        expect(emptyPaymentModel.paymentsMode, isEmpty);
        expect(emptyPaymentModel.paymentsNumber, isEmpty);
      });
    });

    group('JSON Serialization Tests', () {
      test('should create CustomerPaymentModel from JSON correctly', () {
        final customerPaymentModel = CustomerPaymentModel.fromJson(testJson);

        expect(customerPaymentModel.id, equals('payment_123'));
        expect(customerPaymentModel.customerId, equals('customer_123'));
        expect(customerPaymentModel.customerName, equals('Test Customer'));
        expect(customerPaymentModel.categoryType, equals('Feed'));
        expect(customerPaymentModel.amount, equals('5000.00'));
        expect(customerPaymentModel.paymentsDate, equals(testDateTime.toIso8601String()));
        expect(customerPaymentModel.paymentId, equals('pay_123'));
        expect(customerPaymentModel.paymentsMode, equals('Bank Transfer'));
        expect(customerPaymentModel.paymentsNumber, equals('TXN123456789'));
      });

      test('should handle missing JSON fields with defaults', () {
        final incompleteJson = <String, dynamic>{
          'id': 'payment_123',
          'customerId': 'customer_123',
        };

        final customerPaymentModel = CustomerPaymentModel.fromJson(incompleteJson);

        expect(customerPaymentModel.id, equals('payment_123'));
        expect(customerPaymentModel.customerId, equals('customer_123'));
        expect(customerPaymentModel.customerName, isEmpty);
        expect(customerPaymentModel.categoryType, isEmpty);
        expect(customerPaymentModel.amount, isEmpty);
        expect(customerPaymentModel.paymentsDate, isEmpty);
        expect(customerPaymentModel.paymentId, isEmpty);
        expect(customerPaymentModel.paymentsMode, isEmpty);
        expect(customerPaymentModel.paymentsNumber, isEmpty);
      });

      test('should handle null JSON values with defaults', () {
        final nullJson = <String, dynamic>{
          'id': null,
          'customerId': null,
          'customerName': null,
          'categoryType': null,
          'amount': null,
          'paymentsDate': null,
          'paymentId': null,
          'paymentsMode': null,
          'paymentsNumber': null,
        };

        final customerPaymentModel = CustomerPaymentModel.fromJson(nullJson);

        expect(customerPaymentModel.id, isEmpty);
        expect(customerPaymentModel.customerId, isEmpty);
        expect(customerPaymentModel.customerName, isEmpty);
        expect(customerPaymentModel.categoryType, isEmpty);
        expect(customerPaymentModel.amount, isEmpty);
        expect(customerPaymentModel.paymentsDate, isEmpty);
        expect(customerPaymentModel.paymentId, isEmpty);
        expect(customerPaymentModel.paymentsMode, isEmpty);
        expect(customerPaymentModel.paymentsNumber, isEmpty);
      });

      test('should handle _id field as id', () {
        final jsonWithId = <String, dynamic>{
          '_id': 'payment_from_id',
          'customerId': 'customer_123',
        };

        final customerPaymentModel = CustomerPaymentModel.fromJson(jsonWithId);

        expect(customerPaymentModel.id, equals('payment_from_id'));
        expect(customerPaymentModel.customerId, equals('customer_123'));
      });

      test('should prefer id over _id when both are present', () {
        final jsonWithBoth = <String, dynamic>{
          'id': 'payment_id',
          '_id': 'payment_from_id',
          'customerId': 'customer_123',
        };

        final customerPaymentModel = CustomerPaymentModel.fromJson(jsonWithBoth);

        expect(customerPaymentModel.id, equals('payment_id'));
        expect(customerPaymentModel.customerId, equals('customer_123'));
      });
    });

    group('Entity Conversion Tests', () {
      test('should convert CustomerPaymentModel to CustomerPayment entity correctly', () {
        final customerPaymentEntity = testCustomerPaymentModel.toEntity();

        expect(customerPaymentEntity, isA<CustomerPayment>());
        expect(customerPaymentEntity.id, equals(testCustomerPaymentModel.id));
        expect(customerPaymentEntity.customerId, equals(testCustomerPaymentModel.customerId));
        expect(customerPaymentEntity.customerName, equals(testCustomerPaymentModel.customerName));
        expect(customerPaymentEntity.categoryType, equals(testCustomerPaymentModel.categoryType));
        expect(customerPaymentEntity.amount, equals(testCustomerPaymentModel.amount));
        expect(customerPaymentEntity.paymentsDate, equals(testDateTime));
        expect(customerPaymentEntity.paymentId, equals(testCustomerPaymentModel.paymentId));
        expect(customerPaymentEntity.paymentsMode, equals(testCustomerPaymentModel.paymentsMode));
        expect(customerPaymentEntity.paymentsNumber, equals(testCustomerPaymentModel.paymentsNumber));
      });

      test('should handle date parsing during entity conversion', () {
        final paymentModelWithDate = CustomerPaymentModel(
          id: 'payment_123',
          customerId: 'customer_123',
          customerName: 'Test Customer',
          categoryType: 'Feed',
          amount: '5000.00',
          paymentsDate: '2024-01-15T10:30:00.000Z',
          paymentId: 'pay_123',
          paymentsMode: 'Bank Transfer',
          paymentsNumber: 'TXN123456789',
        );

        final entity = paymentModelWithDate.toEntity();

        expect(entity.paymentsDate, isA<DateTime>());
        expect(entity.paymentsDate.year, equals(2024));
        expect(entity.paymentsDate.month, equals(1));
        expect(entity.paymentsDate.day, equals(15));
      });

      test('should maintain data integrity during entity conversion', () {
        final convertedEntity = testCustomerPaymentModel.toEntity();
        
        expect(convertedEntity.id, equals(testJson['id']));
        expect(convertedEntity.customerId, equals(testJson['customerId']));
        expect(convertedEntity.customerName, equals(testJson['customerName']));
        expect(convertedEntity.categoryType, equals(testJson['categoryType']));
        expect(convertedEntity.amount, equals(testJson['amount']));
        expect(convertedEntity.paymentId, equals(testJson['paymentId']));
        expect(convertedEntity.paymentsMode, equals(testJson['paymentsMode']));
        expect(convertedEntity.paymentsNumber, equals(testJson['paymentsNumber']));
      });
    });

    group('Edge Cases and Validation Tests', () {
      test('should handle special characters in string fields', () {
        final specialCharJson = {
          'id': 'payment_123 & special',
          'customerId': 'customer_123-special',
          'customerName': 'Test Customer & Co.',
          'categoryType': 'Feed & Supplements',
          'amount': '5,000.00',
          'paymentsDate': testDateTime.toIso8601String(),
          'paymentId': 'pay_123 (revised)',
          'paymentsMode': 'Bank Transfer & Online',
          'paymentsNumber': 'TXN123456789-special',
        };

        final customerPaymentModel = CustomerPaymentModel.fromJson(specialCharJson);

        expect(customerPaymentModel.id, equals('payment_123 & special'));
        expect(customerPaymentModel.customerId, equals('customer_123-special'));
        expect(customerPaymentModel.customerName, equals('Test Customer & Co.'));
        expect(customerPaymentModel.categoryType, equals('Feed & Supplements'));
        expect(customerPaymentModel.amount, equals('5,000.00'));
        expect(customerPaymentModel.paymentId, equals('pay_123 (revised)'));
        expect(customerPaymentModel.paymentsMode, equals('Bank Transfer & Online'));
        expect(customerPaymentModel.paymentsNumber, equals('TXN123456789-special'));
      });

      test('should handle very long string values', () {
        final longString = 'A' * 1000;
        final longStringJson = {
          'id': longString,
          'customerId': longString,
          'customerName': longString,
          'categoryType': longString,
          'amount': longString,
          'paymentsDate': testDateTime.toIso8601String(),
          'paymentId': longString,
          'paymentsMode': longString,
          'paymentsNumber': longString,
        };

        final customerPaymentModel = CustomerPaymentModel.fromJson(longStringJson);

        expect(customerPaymentModel.id.length, equals(1000));
        expect(customerPaymentModel.customerId.length, equals(1000));
        expect(customerPaymentModel.customerName.length, equals(1000));
        expect(customerPaymentModel.categoryType.length, equals(1000));
        expect(customerPaymentModel.amount.length, equals(1000));
        expect(customerPaymentModel.paymentId.length, equals(1000));
        expect(customerPaymentModel.paymentsMode.length, equals(1000));
        expect(customerPaymentModel.paymentsNumber.length, equals(1000));
      });

      test('should handle unicode characters', () {
        final unicodeJson = {
          'id': 'payment_123',
          'customerId': 'customer_123',
          'customerName': 'Test Customer 测试客户',
          'categoryType': 'Feed 饲料',
          'amount': '5000.00',
          'paymentsDate': testDateTime.toIso8601String(),
          'paymentId': 'pay_123',
          'paymentsMode': 'Bank Transfer 银行转账',
          'paymentsNumber': 'TXN123456789',
        };

        final customerPaymentModel = CustomerPaymentModel.fromJson(unicodeJson);

        expect(customerPaymentModel.customerName, equals('Test Customer 测试客户'));
        expect(customerPaymentModel.categoryType, equals('Feed 饲料'));
        expect(customerPaymentModel.paymentsMode, equals('Bank Transfer 银行转账'));
      });

      test('should handle different amount formats', () {
        final differentAmountFormats = [
          {'amount': '5000'},
          {'amount': '5000.0'},
          {'amount': '5000.00'},
          {'amount': '5,000.00'},
          {'amount': '₹5000.00'},
          {'amount': '5000.50'},
        ];

        for (var amountJson in differentAmountFormats) {
          final json = Map<String, dynamic>.from(testJson);
          json['amount'] = amountJson['amount'];

          final customerPaymentModel = CustomerPaymentModel.fromJson(json);

          expect(customerPaymentModel.amount, equals(amountJson['amount']));
        }
      });

      test('should handle different payment modes', () {
        final paymentModes = [
          'Cash',
          'Bank Transfer',
          'Credit Card',
          'Debit Card',
          'UPI',
          'Net Banking',
          'Cheque',
          'DD',
          'RTGS',
          'NEFT',
        ];

        for (var mode in paymentModes) {
          final json = Map<String, dynamic>.from(testJson);
          json['paymentsMode'] = mode;

          final customerPaymentModel = CustomerPaymentModel.fromJson(json);

          expect(customerPaymentModel.paymentsMode, equals(mode));
        }
      });

      test('should handle different category types', () {
        final categoryTypes = [
          'Feed',
          'Chemical',
          'Equipment',
          'Healthcare',
          'Harvest',
          'Service',
          'Consultation',
          'Other',
        ];

        for (var category in categoryTypes) {
          final json = Map<String, dynamic>.from(testJson);
          json['categoryType'] = category;

          final customerPaymentModel = CustomerPaymentModel.fromJson(json);

          expect(customerPaymentModel.categoryType, equals(category));
        }
      });

      test('should handle different date formats in paymentsDate', () {
        final dateFormats = [
          '2024-01-15T10:30:00.000Z',
          '2024-01-15T10:30:00Z',
          '2024-01-15T10:30:00',
          '2024-01-15 10:30:00',
        ];

        for (var dateFormat in dateFormats) {
          final json = Map<String, dynamic>.from(testJson);
          json['paymentsDate'] = dateFormat;

          final customerPaymentModel = CustomerPaymentModel.fromJson(json);

          expect(customerPaymentModel.paymentsDate, equals(dateFormat));
          
          // Test entity conversion with different date formats
          final entity = customerPaymentModel.toEntity();
          expect(entity.paymentsDate, isA<DateTime>());
        }
      });

      test('should handle empty payment number', () {
        final emptyPaymentNumberJson = Map<String, dynamic>.from(testJson);
        emptyPaymentNumberJson['paymentsNumber'] = '';

        final customerPaymentModel = CustomerPaymentModel.fromJson(emptyPaymentNumberJson);

        expect(customerPaymentModel.paymentsNumber, isEmpty);
      });

      test('should handle zero amount', () {
        final zeroAmountJson = Map<String, dynamic>.from(testJson);
        zeroAmountJson['amount'] = '0.00';

        final customerPaymentModel = CustomerPaymentModel.fromJson(zeroAmountJson);

        expect(customerPaymentModel.amount, equals('0.00'));
      });

      test('should handle negative amount', () {
        final negativeAmountJson = Map<String, dynamic>.from(testJson);
        negativeAmountJson['amount'] = '-1000.00';

        final customerPaymentModel = CustomerPaymentModel.fromJson(negativeAmountJson);

        expect(customerPaymentModel.amount, equals('-1000.00'));
      });

      test('should handle very large amount', () {
        final largeAmountJson = Map<String, dynamic>.from(testJson);
        largeAmountJson['amount'] = '999999999999.99';

        final customerPaymentModel = CustomerPaymentModel.fromJson(largeAmountJson);

        expect(customerPaymentModel.amount, equals('999999999999.99'));
      });
    });
  });
}
