import 'package:aquapartner/core/utils/logger.dart';

import '../../../core/error/exceptions.dart';
import '../../../objectbox.g.dart';
import '../../models/objectbox_smr_report_model.dart';
import '../../models/smr_report_model.dart';

abstract class SMRReportLocalDataSource {
  Future<List<SMRReportModel>> getLastSMRReports(String customerId);

  /// Caches the [SMRReportModel] list
  Future<void> cacheSMRReports(List<SMRReportModel> reports, String customerId);
}

class SMRReportLocalDataSourceImpl implements SMRReportLocalDataSource {
  final Box<ObjectBoxSMRReportModel> smrReportBox;
  final AppLogger logger;

  SMRReportLocalDataSourceImpl({
    required this.smrReportBox,
    required this.logger,
  });

  @override
  Future<List<SMRReportModel>> getLastSMRReports(String customerId) async {
    try {
      final query =
          smrReportBox
              .query(ObjectBoxSMRReportModel_.partnerId.equals(customerId))
              .build();

      final reports = query.find();
      query.close();

      if (reports.isEmpty) {
        throw CacheException();
      }

      return reports.map((objectBoxModel) {
        final entity = objectBoxModel.toEntity();
        return SMRReportModel(
          id: entity.id,
          so: entity.so,
          partner: entity.partner,
          partnerId: entity.partnerId,
          productName: entity.productName,
          startDate: entity.startDate,
          lastDate: entity.lastDate,
          openingBalance: entity.openingBalance,
          invoice: entity.invoice,
          srn: entity.srn,
          closingBalance: entity.closingBalance,
          sales: entity.sales,
          status: entity.status,
        );
      }).toList();
    } catch (e) {
      throw CacheException();
    }
  }

  @override
  Future<void> cacheSMRReports(
    List<SMRReportModel?> reports,
    String customerId,
  ) async {
    try {
      // Delete existing reports for this customer
      final query =
          smrReportBox
              .query(ObjectBoxSMRReportModel_.partnerId.equals(customerId))
              .build();
      final existingReports = query.find();
      smrReportBox.removeMany(existingReports.map((r) => r.dbId).toList());
      query.close();

      // Save new reports
      final objectBoxModels =
          reports.map((report) {
            return ObjectBoxSMRReportModel(
              id: report!.id,
              so: report.so,
              partner: report.partner,
              partnerId: report.partnerId,
              productName: report.productName,
              startDateMillis: report.startDate.millisecondsSinceEpoch,
              lastDateMillis: report.lastDate.millisecondsSinceEpoch,
              openingBalance: report.openingBalance,
              invoice: report.invoice,
              srn: report.srn,
              closingBalance: report.closingBalance,
              sales: report.sales,
              status: report.status,
            );
          }).toList();

      smrReportBox.putMany(objectBoxModels);
    } catch (e) {
      throw CacheException();
    }
  }
}
