import 'dart:convert';
import '../../../core/constants/app_constants.dart';
import '../../../core/error/exceptions.dart';
import '../../../core/network/api_client.dart';
import '../../../core/utils/logger.dart';
import '../../models/account_statement/account_statement_models.dart';

abstract class AccountStatementRemoteDataSource {
  /// Get account statement from remote API
  Future<AccountStatementModel?> getAccountStatement(String customerId);
}

class AccountStatementRemoteDataSourceImpl
    implements AccountStatementRemoteDataSource {
  final ApiClient apiClient;
  final AppLogger logger;

  // Add a timeout for operations
  final Duration operationTimeout = const Duration(seconds: 30);

  AccountStatementRemoteDataSourceImpl({
    required this.apiClient,
    required this.logger,
  });

  @override
  Future<AccountStatementModel?> getAccountStatement(String customerId) async {
    try {
      logger.i(
        'Fetching account statement data from API for customer: $customerId',
      );

      final response = await apiClient
          .get('${AppConstants.baseUrl}/accountStatementFinal/$customerId')
          .timeout(operationTimeout);

      if (response.statusCode == 200) {
        // Handle the response data
        final dynamic responseData = response.data;
        Map<String, dynamic> data;

        if (responseData is String) {
          data = jsonDecode(responseData);
        } else if (responseData is Map<String, dynamic>) {
          data = responseData;
        } else {
          logger.e('Unexpected response format: ${responseData.runtimeType}');
          throw ServerException();
        }

        // Check if result is null or empty
        if (data.isEmpty) {
          logger.w('Empty account statement data received from API');
          return null;
        }

        // Log the structure of the response for debugging
        logger.i(
          'Account statement response structure: ${data.keys.join(', ')}',
        );
        if (data.containsKey('results')) {
          final results = data['results'] as List;
          if (results.isNotEmpty) {
            final firstEntry = results.first as Map<String, dynamic>;
            logger.i('First entry fields: ${firstEntry.keys.join(', ')}');
          }
        }

        // Create account statement model from response
        final accountStatementModel = AccountStatementModel.fromJson(
          data,
          customerId,
        );

        logger.i(
          'Successfully fetched account statement with ${accountStatementModel.entries.length} entries',
        );

        return accountStatementModel;
      } else {
        logger.e(
          'API error: ${response.statusCode} - ${response.statusMessage}',
        );
        throw ServerException();
      }
    } catch (e) {
      logger.e('Error fetching account statement: ${e.toString()}');
      throw ServerException();
    }
  }
}
