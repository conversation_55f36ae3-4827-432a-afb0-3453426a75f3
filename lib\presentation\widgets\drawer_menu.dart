import 'package:aqua_ui/aqua_ui.dart';
import 'package:aquapartner/core/utils/logger.dart';
import 'package:aquapartner/presentation/cubit/customer/customer_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../cubit/auth/auth_cubit.dart';
import '../cubit/auth/auth_state.dart';
import '../cubit/home/<USER>';
import '../cubit/home/<USER>';

class DrawerMenu extends StatelessWidget {
  final AppLogger logger = AppLogger();

  DrawerMenu({super.key});

  /// Show confirmation dialog BEFORE logout
  void _showLogoutConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: AquaText.body('Confirm Logout'),
          content: AquaText.body('Are you sure you want to logout?'),
          actions: [
            TextButton(
              child: AquaText.body('No'),
              onPressed: () => Navigator.of(context).pop(),
            ),
            TextButton(
              child: AquaText.body('Yes'),
              onPressed: () {
                Navigator.of(context).pop(); // dismiss dialog
                context.read<HomeCubit>().gotoDashboardView();
                context.read<AuthCubit>().signOut(); // perform logout
                Navigator.pop(context); // close the drawer
              },
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final authState = context.watch<AuthCubit>().state;
    final homeState = context.watch<HomeCubit>().state;

    return Drawer(
      child: SafeArea(
        child: Container(
          color: acWhiteColor,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 0),
            child: BlocListener<AuthCubit, AuthState>(
              listener: (context, state) {
                if (state is AuthInitial) {
                  Navigator.of(context).pushReplacementNamed('/login');
                }
              },
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (authState is AuthSuccess)
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 16.0),
                      child: BlocConsumer<CustomerCubit, CustomerState>(
                        builder:
                            (context, state) => Padding(
                              padding: const EdgeInsets.only(left: 16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  AquaText.body(
                                    state is CustomerLoaded
                                        ? state.customer.companyName
                                        : 'Guest',
                                    color: acBlackColor,
                                    weight: AquaFontWeight.bold,
                                  ),
                                  const SizedBox(height: 4),
                                  AquaText.body(
                                    state is CustomerLoaded
                                        ? state.customer.mobileNumber
                                        : 'Guest',
                                    color: acBlackColor,
                                  ),
                                ],
                              ),
                            ),
                        listener: (context, state) {},
                      ),
                    )
                  else
                    const Padding(
                      padding: EdgeInsets.symmetric(vertical: 24.0),
                      child: Center(
                        child: CircularProgressIndicator(color: acPrimaryBlue),
                      ),
                    ),
                  Expanded(
                    child: ListView(
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: EdgeInsets.zero,
                      children: [
                        _buildMenuItem(
                          context,
                          icon: Icons.dashboard_outlined,
                          title: "Dashboard",
                          isSelected: homeState.viewType == ViewType.dashboard,
                          onTap: () {
                            logger.w("Goto Dashboard");
                            context.read<HomeCubit>().gotoDashboardView();
                            Navigator.pop(context);
                          },
                        ),
                        _buildMenuItem(
                          context,
                          icon: Icons.receipt_long_outlined,
                          title: "Account Statement",
                          isSelected:
                              homeState.viewType == ViewType.accountStatement,
                          onTap: () {
                            context
                                .read<HomeCubit>()
                                .gotoAccountStatementView();
                            Navigator.pop(context);
                          },
                        ),
                        _buildMenuItem(
                          context,
                          icon: Icons.payment_outlined,
                          title: "Billing & Payments",
                          isSelected:
                              homeState.viewType == ViewType.billingAndPayments,
                          onTap: () {
                            logger.w("Goto Billing and Payments");
                            context
                                .read<HomeCubit>()
                                .gotoBillingAndPaymentsView();
                            Navigator.pop(context);
                          },
                        ),
                        _buildMenuItem(
                          context,
                          icon: Icons.list_alt_outlined,
                          title: "Price List",
                          isSelected: homeState.viewType == ViewType.priceList,
                          onTap: () {
                            logger.w("Goto PriceList");
                            context.read<HomeCubit>().gotoPriceListView();
                            Navigator.pop(context);
                          },
                        ),
                        _buildMenuItem(
                          context,
                          icon: Icons.people_outline,
                          title: "My Farmers",
                          isSelected: homeState.viewType == ViewType.myFarmers,
                          onTap: () {
                            logger.w("Goto My Farmers");
                            context.read<HomeCubit>().gotoMyFarmersView();
                            Navigator.pop(context);
                          },
                        ),
                        _buildMenuItem(
                          context,
                          icon: Icons.inventory_2_outlined,
                          title: "Products",
                          isSelected:
                              homeState.viewType == ViewType.productList,
                          onTap: () {
                            logger.w("Goto Products");
                            context.read<HomeCubit>().gotoProductsView();
                            Navigator.pop(context);
                          },
                        ),
                        _buildMenuItem(
                          context,
                          icon: Icons.stacked_bar_chart,
                          title: "Stocks",
                          isSelected: homeState.viewType == ViewType.stocks,
                          onTap: () {
                            logger.w("Goto Stocks");
                            context.read<HomeCubit>().gotoStocksView();
                            Navigator.pop(context);
                          },
                        ),
                        if (homeState.customer != null) ...[
                          const Divider(height: 32),
                          Padding(
                            padding: const EdgeInsets.only(bottom: 16.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                AquaText.headline(
                                  "Current Customer",
                                  weight: AquaFontWeight.bold,
                                  color: acPrimaryBlue,
                                ),
                                const SizedBox(height: 8),
                                _buildCustomerInfo(
                                  "Name",
                                  homeState.customer!.customerName,
                                ),
                                _buildCustomerInfo(
                                  "Company",
                                  homeState.customer!.companyName,
                                ),
                                _buildCustomerInfo(
                                  "Mobile",
                                  homeState.customer!.mobileNumber,
                                ),
                              ],
                            ),
                          ),
                          ListTile(
                            leading: const Icon(Icons.person_remove_outlined),
                            title: AquaText.body('Clear Customer'),
                            contentPadding: EdgeInsets.zero,
                            onTap: () {
                              context.read<HomeCubit>().clearCustomerData();
                              Navigator.pop(context);
                            },
                          ),
                        ],
                      ],
                    ),
                  ),
                  _buildMenuItem(
                    context,
                    icon: Icons.help_outline,
                    title: "Help / Support",
                    isSelected: homeState.viewType == ViewType.helpAndSupport,
                    onTap: () {
                      logger.w("Goto Help / Support");
                      context.read<HomeCubit>().gotoHelpAndSupportScreen();
                      Navigator.pop(context);
                    },
                  ),
                  // LOGOUT with confirmation
                  _buildMenuItem(
                    context,
                    icon: Icons.logout,
                    title: "Logout",
                    onTap: () {
                      _showLogoutConfirmation(context);
                    },
                  ),
                  const SizedBox(height: 16),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMenuItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    bool isSelected = false,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 0),
      decoration: BoxDecoration(
        color:
            isSelected
                ? acPrimaryBlue.withValues(alpha: 0.1)
                : Colors.transparent,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: const EdgeInsets.only(left: 16),
        child: ListTile(
          leading: Icon(
            icon,
            color: isSelected ? acPrimaryBlue : acTextSecondaryColor,
            size: 22,
          ),
          title: AquaText.subheadline(
            title,
            color: isSelected ? acPrimaryBlue : acBlackColor,
            weight:
                isSelected ? AquaFontWeight.semibold : AquaFontWeight.regular,
          ),
          contentPadding: const EdgeInsets.symmetric(horizontal: 0),
          visualDensity: const VisualDensity(horizontal: 0, vertical: -1),
          onTap: onTap,
        ),
      ),
    );
  }

  Widget _buildCustomerInfo(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(width: 80, child: AquaText.body("$label:")),
          Expanded(child: AquaText.body(value)),
        ],
      ),
    );
  }
}
