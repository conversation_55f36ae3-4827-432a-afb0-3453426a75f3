import 'package:aqua_ui/aqua_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../widgets/scheme_q3_rules_card.dart';

class Q3SchemeRulesScreen extends StatelessWidget {
  const Q3SchemeRulesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.white,
          statusBarIconBrightness: Brightness.dark,
        ),
        backgroundColor: acWhiteColor,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          color: acTextSecondaryColor,
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: AquaText.subheadline(
          "Rules",
          color: acTextSecondaryColor,
          weight: AquaFontWeight.semibold,
        ),
        centerTitle: false,
      ),
      // Use a Stack to position the floating button at the bottom
      body: SchemeQ3RulesCard(),
    );
  }
}
