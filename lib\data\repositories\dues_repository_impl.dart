import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import '../../domain/entities/dues/dues.dart';
import '../../domain/repositories/dues_repository.dart';
import '../../core/error/failures.dart';
import '../../core/network/network_info.dart';
import '../datasources/local/dues_local_data_source.dart';
import '../datasources/remote/dues_remote_data_source.dart';

class DuesRepositoryImpl implements DuesRepository {
  final DuesLocalDataSource localDataSource;
  final DuesRemoteDataSource remoteDataSource;
  final NetworkInfo networkInfo;

  DuesRepositoryImpl({
    required this.localDataSource,
    required this.remoteDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, DuesSummary>> getDues(String customerId) async {
    try {
      final localDues = await localDataSource.getDues(customerId);
      return Right(localDues);
    } catch (e) {
      return Left(CacheFailure());
    }
  }

  @override
  Future<Either<Failure, DuesSummary>> syncDues(String customerId) async {
    if (await networkInfo.isConnected) {
      try {
        final remoteDues = await remoteDataSource.getDues(customerId);
        await localDataSource.cacheDues(remoteDues, customerId);
        return Right(remoteDues);
      } on DioException catch (_) {
        return Left(ServerFailure());
      } catch (e) {
        return Left(ServerFailure());
      }
    } else {
      try {
        final localDues = await localDataSource.getDues(customerId);
        return Right(localDues);
      } catch (e) {
        return Left(CacheFailure());
      }
    }
  }

  @override
  Future<Either<Failure, bool>> checkIfSyncNeeded(String customerId) async {
    try {
      final lastSyncTime = await localDataSource.getLastSyncTime(customerId);
      final currentTime = DateTime.now().millisecondsSinceEpoch;
      // Sync if last sync was more than 1 hour ago
      final syncNeeded = (currentTime - lastSyncTime) > 3600000;
      return Right(syncNeeded);
    } catch (e) {
      // If there's an error, assume sync is needed
      return const Right(true);
    }
  }
}
