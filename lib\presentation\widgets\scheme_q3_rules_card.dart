import 'package:aqua_ui/aqua_ui.dart';
import 'package:flutter/material.dart';

class SchemeQ3RulesCard extends StatelessWidget {
  const SchemeQ3RulesCard({super.key});

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isSmallScreen = constraints.maxWidth < 400;

        return Container(
          width: double.infinity,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.2),
                spreadRadius: 1,
                blurRadius: 3,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Ticket sections
              _buildTicketSection('1st Ticket', '₹6,00,000.00', isSmallScreen),
              const AquaDivider(color: acGrey200),
              _buildTicketSection('2nd Ticket', '₹12,00,000.00', isSmallScreen),
              const AquaDivider(color: acGrey200),
              _buildTicketSection('3rd Ticket', '₹18,00,000.00', isSmallScreen),

              // Note section
              Padding(
                padding: const EdgeInsets.all(16),
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.symmetric(
                    vertical: 16,
                    horizontal: 16,
                  ),
                  decoration: BoxDecoration(
                    color: acGrey100, // Light yellow color
                    borderRadius: const BorderRadius.all(Radius.circular(8)),
                  ),
                  child: AquaText.body(
                    'Note: Discounts upto 45% are available for other healthcare products',
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTicketSection(String title, String amount, bool isSmallScreen) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AquaText.headline(title, color: acTextPrimaryColor),
          const SizedBox(height: 12),
          _buildInfoRow('Total :', amount, isSmallScreen),
          const SizedBox(height: 6),
          _buildInfoRow('Payment for Non Feed :', amount, isSmallScreen),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, bool isSmallScreen) {
    if (isSmallScreen) {
      // Stack layout for small screens
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AquaText.body(label, color: acTextSecondaryColor),
          const SizedBox(height: 4),
          Padding(
            padding: const EdgeInsets.only(left: 0),
            child: AquaText.body(value),
          ),
        ],
      );
    } else {
      // Row layout for larger screens
      return Padding(
        padding: const EdgeInsets.only(left: 0),
        child: Row(
          children: [
            SizedBox(
              width: 160,
              child: AquaText.body(label, color: acTextSecondaryColor),
            ),
            AquaText.body(
              value,
              color: acBlackColor,
              weight: AquaFontWeight.bold,
            ),
          ],
        ),
      );
    }
  }
}
