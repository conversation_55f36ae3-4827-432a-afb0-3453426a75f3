import '../../../domain/entities/farmer_visit/farmer.dart';
import 'visit_model.dart'; // Import visit model

/// Model for the top-level JSON response containing a list of farmer results.
class FarmerDataModel {
  final List<FarmerModel> results;

  FarmerDataModel({required this.results});

  factory FarmerDataModel.fromJson(Map<String, dynamic> json) {
    return FarmerDataModel(
      results: List<FarmerModel>.from(
        json['results'].map((x) => FarmerModel.fromJson(x)),
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {'results': List<dynamic>.from(results.map((x) => x.toJson()))};
  }

  // Method to map the entire data model to a list of domain Farmers
  List<Farmer> toDomain() {
    return results.map((farmerModel) => farmerModel.toDomain()).toList();
  }

  // Factory constructor to create a FarmerDataModel from a list of domain Farmers
  factory FarmerDataModel.fromDomain(List<Farmer> farmers) {
    return FarmerDataModel(
      results: farmers.map((farmer) => FarmerModel.fromDomain(farmer)).toList(),
    );
  }
}

/// Model for each farmer entry within the JSON 'results' list.
class FarmerModel {
  final List<VisitModel> visits;
  final String name;
  FarmerModel({required this.visits, required this.name});

  factory FarmerModel.fromJson(Map<String, dynamic> json) {
    final name = json['name'] as String? ?? '';
    // Generate a farmerId if needed (could be based on name or other unique identifier)
    final farmerId = ''; // Leave empty as it's not in the JSON

    return FarmerModel(
      visits: List<VisitModel>.from(
        json['visits'].map(
          (x) => VisitModel.fromJson(x, farmerId: farmerId, farmerName: name),
        ),
      ),
      name: name,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'visits': List<dynamic>.from(visits.map((x) => x.toJson())),
      'name': name,
    };
  }

  // Method to map the FarmerModel to a Farmer domain entity
  Farmer toDomain() {
    return Farmer(
      id: null, // API models don't have IDs until stored in the database
      name: name,
      // Extract mobile number from the first visit, assuming consistency
      mobileNumber: visits.isNotEmpty ? visits.first.mobileNumber : '',
      // Map visit models to visit domain entities
      visits: visits.map((v) => v.toDomain()).toList(),
    );
  }

  // Factory constructor to create a FarmerModel from a domain Farmer
  factory FarmerModel.fromDomain(Farmer farmer) {
    return FarmerModel(
      name: farmer.name,
      visits:
          farmer.visits.map((visit) => VisitModel.fromDomain(visit)).toList(),
    );
  }
}
