part of 'farmer_visits_cubit.dart';

abstract class FarmerVisitsState extends Equatable {
  const FarmerVisitsState();

  @override
  List<Object> get props => [];
}

class FarmerVisitsInitial extends FarmerVisitsState {}

class FarmerVisitsLoading extends FarmerVisitsState {}

class FarmerVisitsEmpty extends FarmerVisitsState {}

class FarmerVisitsLoaded extends FarmerVisitsState {
  final List<Farmer> farmers;

  const FarmerVisitsLoaded(this.farmers);

  @override
  List<Object> get props => [farmers];
}

class FarmerVisitsError extends FarmerVisitsState {
  final String message;

  const FarmerVisitsError(this.message);

  @override
  List<Object> get props => [message];
}

class FarmerVisitsSaving extends FarmerVisitsState {}

class FarmerVisitsSaveSuccess extends FarmerVisitsState {}

class FarmerVisitsSaveError extends FarmerVisitsState {
  final String message;

  const FarmerVisitsSaveError(this.message);

  @override
  List<Object> get props => [message];
}

class FarmerVisitsSyncing extends FarmerVisitsState {
  final List<Farmer>? farmers;

  const FarmerVisitsSyncing([this.farmers]);

  @override
  List<Object> get props => farmers != null ? [farmers!] : [];
}
