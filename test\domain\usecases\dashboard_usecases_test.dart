import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import 'package:aquapartner/core/error/failures.dart';
import 'package:aquapartner/core/network/network_info.dart';
import 'package:aquapartner/core/utils/logger.dart';
import 'package:aquapartner/domain/repositories/dashboard_repository.dart';
import 'package:aquapartner/domain/usecases/dashboard_usecases.dart';
import 'package:aquapartner/domain/entities/dashboard/dashboard_entity.dart';

// Mock classes
class MockDashboardRepository extends Mock implements DashboardRepository {}
class MockNetworkInfo extends Mock implements NetworkInfo {}
class MockAppLogger extends Mock implements AppLogger {}

void main() {
  setUpAll(() {
    // Register fallback values for mocktail
    registerFallbackValue('test_customer_id');
  });

  group('GetDashboardUseCase', () {
    late GetDashboardUseCase useCase;
    late MockDashboardRepository mockRepository;
    late MockAppLogger mockLogger;

    setUp(() {
      mockRepository = MockDashboardRepository();
      mockLogger = MockAppLogger();
      useCase = GetDashboardUseCase(
        repository: mockRepository,
        logger: mockLogger,
      );
    });

    group('Successful Operations', () {
      test('should get dashboard data for valid customer ID', () async {
        // arrange
        const customerId = 'test_customer_123';
        final testDashboard = _createTestDashboard(customerId);
        when(() => mockRepository.getDashboard(customerId))
            .thenAnswer((_) async => Right(testDashboard));

        // act
        final result = await useCase.call(customerId);

        // assert
        expect(result, Right(testDashboard));
        verify(() => mockLogger.i('GetDashboardUseCase called for customer: $customerId')).called(1);
        verify(() => mockRepository.getDashboard(customerId)).called(1);
      });

      test('should handle empty customer ID', () async {
        // arrange
        const customerId = '';
        when(() => mockRepository.getDashboard(customerId))
            .thenAnswer((_) async => Left(ValidationFailure('Invalid customer ID')));

        // act
        final result = await useCase.call(customerId);

        // assert
        expect(result, Left(ValidationFailure('Invalid customer ID')));
        verify(() => mockLogger.i('GetDashboardUseCase called for customer: $customerId')).called(1);
      });
    });

    group('Error Handling', () {
      test('should return failure when repository fails', () async {
        // arrange
        const customerId = 'test_customer_123';
        when(() => mockRepository.getDashboard(customerId))
            .thenAnswer((_) async => Left(ServerFailure()));

        // act
        final result = await useCase.call(customerId);

        // assert
        expect(result, Left(ServerFailure()));
        verify(() => mockRepository.getDashboard(customerId)).called(1);
      });

      test('should handle network failure', () async {
        // arrange
        const customerId = 'test_customer_123';
        when(() => mockRepository.getDashboard(customerId))
            .thenAnswer((_) async => Left(NetworkFailure()));

        // act
        final result = await useCase.call(customerId);

        // assert
        expect(result, Left(NetworkFailure()));
      });

      test('should handle cache failure', () async {
        // arrange
        const customerId = 'test_customer_123';
        when(() => mockRepository.getDashboard(customerId))
            .thenAnswer((_) async => Left(CacheFailure()));

        // act
        final result = await useCase.call(customerId);

        // assert
        expect(result, Left(CacheFailure()));
      });
    });

    group('Edge Cases', () {
      test('should handle special characters in customer ID', () async {
        // arrange
        const customerId = 'test_customer_@#\$%';
        final testDashboard = _createTestDashboard(customerId);
        when(() => mockRepository.getDashboard(customerId))
            .thenAnswer((_) async => Right(testDashboard));

        // act
        final result = await useCase.call(customerId);

        // assert
        expect(result, Right(testDashboard));
      });

      test('should handle very long customer ID', () async {
        // arrange
        final customerId = 'test_customer_' + 'a' * 1000;
        when(() => mockRepository.getDashboard(customerId))
            .thenAnswer((_) async => Left(ValidationFailure('Customer ID too long')));

        // act
        final result = await useCase.call(customerId);

        // assert
        expect(result, Left(ValidationFailure('Customer ID too long')));
      });
    });
  });

  group('SyncDashboardUseCase', () {
    late SyncDashboardUseCase useCase;
    late MockDashboardRepository mockRepository;
    late MockNetworkInfo mockNetworkInfo;
    late MockAppLogger mockLogger;

    setUp(() {
      mockRepository = MockDashboardRepository();
      mockNetworkInfo = MockNetworkInfo();
      mockLogger = MockAppLogger();
      useCase = SyncDashboardUseCase(
        repository: mockRepository,
        networkInfo: mockNetworkInfo,
        logger: mockLogger,
      );
    });

    group('Successful Sync', () {
      test('should sync dashboard when network is connected', () async {
        // arrange
        const customerId = 'test_customer_123';
        when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        when(() => mockRepository.syncDashboard(customerId))
            .thenAnswer((_) async => const Right(true));

        // act
        final result = await useCase.call(customerId);

        // assert
        expect(result, const Right(true));
        verify(() => mockLogger.i('SyncDashboardUseCase called for customer: $customerId')).called(1);
        verify(() => mockNetworkInfo.isConnected).called(1);
        verify(() => mockRepository.syncDashboard(customerId)).called(1);
      });

      test('should handle successful sync with data clearing', () async {
        // arrange
        const customerId = 'test_customer_123';
        when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        when(() => mockRepository.syncDashboard(customerId))
            .thenAnswer((_) async => const Right(true));

        // act
        final result = await useCase.call(customerId);

        // assert
        expect(result, const Right(true));
        verify(() => mockRepository.syncDashboard(customerId)).called(1);
      });
    });

    group('Network Connectivity', () {
      test('should return NetworkFailure when network is not connected', () async {
        // arrange
        const customerId = 'test_customer_123';
        when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => false);

        // act
        final result = await useCase.call(customerId);

        // assert
        expect(result, Left(NetworkFailure()));
        verify(() => mockLogger.w('Cannot sync dashboard: No network connection')).called(1);
        verify(() => mockNetworkInfo.isConnected).called(1);
        verifyNever(() => mockRepository.syncDashboard(any()));
      });

      test('should handle network check failure', () async {
        // arrange
        const customerId = 'test_customer_123';
        when(() => mockNetworkInfo.isConnected).thenThrow(Exception('Network check failed'));

        // act & assert
        expect(() => useCase.call(customerId), throwsException);
      });
    });

    group('Error Handling', () {
      test('should return repository failure when sync fails', () async {
        // arrange
        const customerId = 'test_customer_123';
        when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        when(() => mockRepository.syncDashboard(customerId))
            .thenAnswer((_) async => Left(ServerFailure()));

        // act
        final result = await useCase.call(customerId);

        // assert
        expect(result, Left(ServerFailure()));
      });

      test('should handle authentication failure during sync', () async {
        // arrange
        const customerId = 'test_customer_123';
        when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        when(() => mockRepository.syncDashboard(customerId))
            .thenAnswer((_) async => Left(AuthFailure()));

        // act
        final result = await useCase.call(customerId);

        // assert
        expect(result, Left(AuthFailure()));
      });
    });

    group('Edge Cases', () {
      test('should handle concurrent sync requests', () async {
        // arrange
        const customerId = 'test_customer_123';
        when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        when(() => mockRepository.syncDashboard(customerId))
            .thenAnswer((_) async => const Right(true));

        // act
        final results = await Future.wait([
          useCase.call(customerId),
          useCase.call(customerId),
          useCase.call(customerId),
        ]);

        // assert
        for (final result in results) {
          expect(result, const Right(true));
        }
        verify(() => mockRepository.syncDashboard(customerId)).called(3);
      });

      test('should handle sync timeout gracefully', () async {
        // arrange
        const customerId = 'test_customer_123';
        when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        when(() => mockRepository.syncDashboard(customerId)).thenAnswer(
          (_) async => Future.delayed(Duration(seconds: 1), () => Left(ServerFailure())),
        );

        // act
        final result = await useCase.call(customerId);

        // assert
        expect(result, Left(ServerFailure()));
      });
    });
  });
}

// Helper function to create test dashboard entity
DashboardEntity _createTestDashboard(String customerId) {
  return DashboardEntity(
    customerId: customerId,
    sales: const SalesEntity(yearlyData: {}),
    payments: const PaymentsEntity(yearlyData: {}),
    dues: const [],
    salesReturn: 0.0,
    categoryTypeSales: const [],
    liquidation: const LiquidationEntity(
      totalLiquidation: 0.0,
      liquidationByYear: [],
    ),
    myFarmers: const MyFarmersEntity(totalFarmers: [], potentialFarmers: 0),
  );
}
