// lib/data/datasources/remote/customer_remote_data_source.dart
import 'dart:convert';

import 'package:aquapartner/core/error/exceptions.dart';
import 'package:aquapartner/core/utils/logger.dart';
import 'package:aquapartner/data/models/customer_model.dart';
import 'package:aquapartner/core/network/api_client.dart';

import '../../../core/constants/app_constants.dart';

abstract class CustomerRemoteDataSource {
  Future<CustomerModel?> getCustomerByMobileNumber(String mobileNumber);
  Future<CustomerModel?> getCustomerByCustomerId(String customerId);
}

class CustomerRemoteDataSourceImpl implements CustomerRemoteDataSource {
  final ApiClient apiClient;
  final AppLogger logger;

  CustomerRemoteDataSourceImpl({required this.apiClient, required this.logger});

  @override
  Future<CustomerModel?> getCustomerByMobileNumber(String mobileNumber) async {
    try {
      final response = await apiClient.get(
        '${AppConstants.baseUrl}/validateRetailer/$mobileNumber',
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = jsonDecode(response.data);

        // Check if result is null
        if (data['result'] == null) {
          return null;
        }

        return CustomerModel.fromJson(data['result']);
      }
      return null;
    } catch (e) {
      throw ServerException();
    }
  }

  @override
  Future<CustomerModel?> getCustomerByCustomerId(String customerId) async {
    try {
      final response = await apiClient.get(
        '${AppConstants.baseUrl}/customer/$customerId',
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = jsonDecode(response.data);

        return CustomerModel.fromJson(data);
      }
      return null;
    } catch (e) {
      throw ServerException();
    }
  }
}
