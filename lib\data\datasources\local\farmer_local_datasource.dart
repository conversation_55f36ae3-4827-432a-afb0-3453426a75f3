import 'package:aquapartner/core/utils/logger.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../objectbox.g.dart';
import '../../entities/farmer_visits/farmer_entity.dart';
import '../../entities/farmer_visits/visit_entity.dart'; // Generated file for querying

/// Abstract interface for the local data source (ObjectBox).
abstract class FarmerLocalDataSource {
  /// Gets all Farmer entities from the local database.
  /// Throws [CacheException] on error.
  Future<List<FarmerEntity>> getAllFarmers();

  /// Stores a list of Farmer entities (and their related visits) in the local database.
  /// Throws [CacheException] on error.
  Future<void> storeFarmersAndVisits(List<FarmerEntity> farmers);

  /// Clears all farmer and visit data from the local database.
  /// Throws [CacheException] on error.
  Future<void> clearAllFarmersAndVisits();

  // Add other local data source methods as needed (e.g., get farmer by ID, update visit)
}

/// Implementation of the [FarmerLocalDataSource] using ObjectBox.
class FarmerLocalDataSourceImpl implements FarmerLocalDataSource {
  final Store store;
  final Box<FarmerEntity> farmerBox;
  final Box<VisitEntity> visitBox;
  final AppLogger logger;

  FarmerLocalDataSourceImpl({
    required this.store,
    required this.logger,
    required this.farmerBox,
    required this.visitBox,
  });

  @override
  Future<List<FarmerEntity>> getAllFarmers() async {
    try {
      // You might want to order or filter results here
      return farmerBox.getAll();
    } catch (e) {
      // Log the error if necessary
      logger.i('ObjectBox Error getting all farmers: $e');
      throw CacheException(); // Throw a specific cache exception
    }
  }

  @override
  Future<void> storeFarmersAndVisits(List<FarmerEntity> farmersToStore) async {
    try {
      // Use a transaction for atomicity and performance when performing multiple operations
      store.runInTransaction(TxMode.write, () {
        for (final farmerEntity in farmersToStore) {
          // Store each farmer entity without checking for duplicates
          // ObjectBox will assign a new ID to farmerEntity after this
          farmerBox.put(farmerEntity);
          logger.i('Storing farmer: ${farmerEntity.name}');

          // Process the visits for this farmer
          final visitsToPut = <VisitEntity>[];
          for (final visitEntity in farmerEntity.visits) {
            // Clear any existing ToOne target
            visitEntity.farmer.target = null;
            // Set the ToOne relationship to the farmer entity
            visitEntity.farmer.target = farmerEntity;
            visitsToPut.add(visitEntity);
          }

          // Put all visits for the farmer
          if (visitsToPut.isNotEmpty) {
            visitBox.putMany(visitsToPut);
            logger.i(
              'Stored ${visitsToPut.length} visits for farmer: ${farmerEntity.name}',
            );
          }
        }
      });
    } catch (e) {
      // Log the error if necessary
      logger.e('ObjectBox Error storing farmers and visits: $e');
      throw CacheException(); // Throw a specific cache exception
    }
  }

  @override
  Future<void> clearAllFarmersAndVisits() async {
    try {
      // Use a transaction for atomicity
      store.runInTransaction(TxMode.write, () {
        // Remove all visits first (to maintain referential integrity)
        final visitCount = visitBox.count();
        visitBox.removeAll();

        // Then remove all farmers
        final farmerCount = farmerBox.count();
        farmerBox.removeAll();

        logger.i(
          'Cleared all farmer and visit data: $farmerCount farmers and $visitCount visits removed',
        );
      });
    } catch (e) {
      logger.e('ObjectBox Error clearing farmers and visits: $e');
      throw CacheException();
    }
  }
}
