import 'package:aqua_ui/aqua_ui.dart';
import 'package:flutter/material.dart';

class ColumnConfig<T> {
  final String title;
  final double width;
  final Alignment? titleAlignment;
  final Alignment? bodyAlignment;
  final Widget Function(T item) cellBuilder;

  ColumnConfig({
    this.titleAlignment,
    this.bodyAlignment,
    required this.title,
    required this.width,
    required this.cellBuilder,
  });
}

class StyledGenericTable<T> extends StatelessWidget {
  final List<T> items;
  final List<ColumnConfig<T>> columns;
  final bool showDividers;
  final EdgeInsetsGeometry padding;
  final void Function(T item)? onRowTap;
  final Color? rowHoverColor;
  final Color dividerColor;

  const StyledGenericTable({
    super.key,
    required this.items,
    required this.columns,
    this.showDividers = true,
    this.padding = const EdgeInsets.all(0.0),
    this.onRowTap,
    this.rowHoverColor,
    this.dividerColor = acGrey200,
  });

  @override
  Widget build(BuildContext context) {
    // Calculate total width of all columns
    double totalWidth = columns.fold(0, (sum, column) => sum + column.width);

    return Padding(
      padding: padding,
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: SizedBox(
          width: totalWidth, // Set a fixed width for the column
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Table header
              Row(
                children:
                    columns
                        .map(
                          (column) => _buildHeaderCell(
                            column.title,
                            column.width,
                            column.titleAlignment,
                          ),
                        )
                        .toList(),
              ),
              // Header divider
              if (showDividers)
                Divider(color: dividerColor, height: 1, thickness: 1),
              // Table rows with dividers
              for (int i = 0; i < items.length; i++) ...[
                _buildDataRow(items[i], context),
                // Add divider after each row except the last one
                if (showDividers && i < items.length - 1)
                  Divider(color: dividerColor, height: 1, thickness: 1),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeaderCell(String text, double width, Alignment? alignment) {
    return Container(
      width: width,
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 0),
      child: Align(
        alignment: alignment ?? Alignment.centerLeft,
        child: AquaText.body(
          text,
          color: acTextSecondaryColor,
          weight: AquaFontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildDataRow(T item, BuildContext context) {
    final row = Row(
      children:
          columns
              .map(
                (column) => Container(
                  width: column.width,
                  padding: const EdgeInsets.symmetric(
                    vertical: 12,
                    horizontal: 0,
                  ),
                  child: Container(
                    alignment: column.bodyAlignment ?? Alignment.centerLeft,
                    padding: EdgeInsets.only(
                      right: column.bodyAlignment != null ? 8 : 0,
                    ),
                    child: column.cellBuilder(item),
                  ),
                ),
              )
              .toList(),
    );

    if (onRowTap != null) {
      return Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => onRowTap!(item),
          hoverColor: rowHoverColor ?? Theme.of(context).hoverColor,
          child: row,
        ),
      );
    } else {
      return row;
    }
  }
}
