import 'package:mongo_dart/mongo_dart.dart';

import '../../../core/error/exceptions.dart';
import '../../../core/services/mongodb_service.dart';
import '../../models/user_model.dart';

abstract class MongoDBDataSource {
  Future<void> saveUser(UserModel user);
  Future<UserModel?> getUser(String phoneNumber);
  Future<void> updateUser(UserModel user);
  Future<void> deleteUser(String phoneNumber);
  Future<List<UserModel>> getAllUsers();
  Future<void> clearUserSession();
}

class MongoDBDataSourceImpl implements MongoDBDataSource {
  final MongoDBService _mongoService;

  MongoDBDataSourceImpl(this._mongoService);

  @override
  Future<void> saveUser(UserModel user) async {
    try {
      // Get the collection with automatic reconnection if needed
      final collection = await _mongoService.userCollection;

      final existingUser = await getUser(user.phoneNumber);
      if (existingUser != null) {
        throw DatabaseException();
      }

      await collection.insertOne({
        'phoneNumber': user.phoneNumber,
        'isVerified': user.isVerified,
        'createdAt': user.createdAt.toIso8601String(),
        'updatedAt': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      throw DatabaseException();
    }
  }

  @override
  Future<UserModel?> getUser(String phoneNumber) async {
    try {
      // Get the collection with automatic reconnection if needed
      final collection = await _mongoService.userCollection;

      final result = await collection.findOne(
        where.eq('phoneNumber', phoneNumber),
      );

      if (result == null) return null;

      return UserModel(
        phoneNumber: result['phoneNumber'] as String,
        isVerified: result['isVerified'] as bool,
        createdAt: DateTime.parse(result['createdAt'] as String),
      );
    } catch (e) {
      throw DatabaseException();
    }
  }

  @override
  Future<void> updateUser(UserModel user) async {
    try {
      // Get the collection with automatic reconnection if needed
      final collection = await _mongoService.userCollection;

      final result = await collection.updateOne(
        where.eq('phoneNumber', user.phoneNumber),
        modify
            .set('isVerified', user.isVerified)
            .set('updatedAt', DateTime.now().toIso8601String()),
      );

      if (result.isSuccess) {
        if (result.nModified == 0) {
          throw DatabaseException();
        }
      } else {
        throw DatabaseException();
      }
    } catch (e) {
      throw DatabaseException();
    }
  }

  @override
  Future<void> deleteUser(String phoneNumber) async {
    try {
      // Get the collection with automatic reconnection if needed
      final collection = await _mongoService.userCollection;

      final result = await collection.deleteOne(
        where.eq('phoneNumber', phoneNumber),
      );

      if (result.isSuccess) {
        if (result.nRemoved == 0) {
          throw DatabaseException();
        }
      } else {
        throw DatabaseException();
      }
    } catch (e) {
      throw DatabaseException();
    }
  }

  @override
  Future<void> clearUserSession() async {
    try {
      // Close the current MongoDB connection
      await _mongoService.disconnect();
    } catch (e) {
      throw Exception('Failed to clear MongoDB session: ${e.toString()}');
    }
  }

  @override
  Future<List<UserModel>> getAllUsers() async {
    try {
      // Get the collection with automatic reconnection if needed
      final collection = await _mongoService.userCollection;

      final cursor = collection.find();
      final documents = await cursor.toList();

      return documents
          .map(
            (doc) => UserModel(
              phoneNumber: doc['phoneNumber'] as String,
              isVerified: doc['isVerified'] as bool,
              createdAt: DateTime.parse(doc['createdAt'] as String),
            ),
          )
          .toList();
    } catch (e) {
      throw DatabaseException();
    }
  }
}
