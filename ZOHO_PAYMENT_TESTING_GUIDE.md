# Zoho Payment API Testing Guide

## Overview

This guide provides comprehensive instructions for testing the Zoho payment API integration in the AquaPartner Flutter application.

## Prerequisites

### 1. Environment Setup

#### Backend Requirements
- Backend server running with Zoho payment endpoints
- Valid API authentication tokens
- Test database with sample invoices
- Network connectivity to Zoho sandbox environment

#### Flutter App Requirements
- Flutter development environment set up
- AquaPartner app compiled and running
- Test dependencies installed
- Debug mode enabled for detailed logging

### 2. Zoho Payments Setup

#### Sandbox Account
1. Create Zoho Payments sandbox account
2. Obtain test API credentials
3. Configure webhook URLs (if applicable)
4. Set up test payment methods

#### Test Credentials
```dart
// Add to your test configuration
static const String testOrganizationId = 'your_test_org_id';
static const String testApiKey = 'your_test_api_key';
```

## Testing Approaches

### 1. Unit Testing

#### Run Unit Tests
```bash
# Run all payment-related unit tests
flutter test test/domain/usecases/payments/
flutter test test/presentation/cubit/payments/
flutter test test/data/repositories/payment_repository_impl_test.dart

# Run specific test file
flutter test test/domain/usecases/payments/create_payment_session_usecase_test.dart

# Run with coverage
flutter test --coverage
genhtml coverage/lcov.info -o coverage/html
```

#### Key Unit Test Areas
- ✅ Payment request validation
- ✅ Use case business logic
- ✅ State management (PaymentCubit)
- ✅ Error handling scenarios
- ✅ Data model serialization

### 2. Widget Testing

#### Run Widget Tests
```bash
# Run payment widget tests
flutter test test/presentation/widgets/zoho_payment_button_test.dart

# Run with verbose output
flutter test --verbose test/presentation/widgets/
```

#### Key Widget Test Areas
- ✅ Button state changes
- ✅ Loading indicators
- ✅ Error message display
- ✅ User interaction handling
- ✅ Payment completion callbacks

### 3. Integration Testing

#### Setup Integration Tests
```bash
# Run integration tests
flutter test integration_test/payment_api_integration_test.dart

# Run on specific device
flutter test integration_test/ -d <device_id>
```

#### Environment Configuration
```bash
# Test environment
flutter test --dart-define=ENVIRONMENT=test

# Staging environment
flutter test --dart-define=ENVIRONMENT=staging

# Development environment
flutter test --dart-define=ENVIRONMENT=dev
```

### 4. Manual Testing

#### Using Test Utilities
```dart
import 'test/manual_testing/payment_test_utils.dart';

void main() async {
  // Initialize test utilities
  PaymentTestUtils.initialize(
    customBaseUrl: 'https://staging-partner.aquaconnect.blue/api',
    authToken: 'your_test_token',
  );

  // Print test configuration
  PaymentTestUtils.printTestConfiguration();

  // Run comprehensive tests
  await PaymentTestUtils.runComprehensiveTests();

  // Generate test report
  await PaymentTestUtils.generateTestReport();
}
```

#### Manual Test Scenarios

##### Scenario 1: Successful Payment Flow
1. **Setup**: Create test invoice with overdue status
2. **Action**: Tap "Pay Now" button
3. **Expected**: Payment session created, WebView opens
4. **Action**: Complete payment in WebView
5. **Expected**: Payment success, invoice status updated

##### Scenario 2: Failed Payment Flow
1. **Setup**: Use test card that fails (****************)
2. **Action**: Attempt payment
3. **Expected**: Payment failure, appropriate error message

##### Scenario 3: Cancelled Payment
1. **Setup**: Start payment process
2. **Action**: Close WebView before completion
3. **Expected**: Payment cancelled, no charge applied

##### Scenario 4: Expired Session
1. **Setup**: Create payment session
2. **Action**: Wait for session timeout (30 minutes)
3. **Expected**: Session expires, new session required

## Test Data

### Test Card Numbers (Zoho Sandbox)

#### Successful Payments
```
Card Number: ****************
Expiry: 12/2025
CVV: 123
Name: Test User
```

#### Failed Payments
```
Card Number: ****************
Expiry: 12/2025
CVV: 123
Name: Test User Failure
```

#### Insufficient Funds
```
Card Number: ****************
Expiry: 12/2025
CVV: 123
Name: Test User Insufficient
```

### Test UPI IDs
- Success: `test@paytm`
- Failure: `fail@paytm`

### Test Amounts
- Small: ₹1.00
- Medium: ₹100.00
- Large: ₹1,000.00
- Maximum: ₹50,000.00

## API Testing

### 1. Direct API Testing

#### Create Payment Session
```bash
curl -X POST https://staging-partner.aquaconnect.blue/api/zoho/payments/create-session \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "amount": 100.0,
    "currency": "INR",
    "invoice_number": "TEST_INV_001",
    "customer_id": "TEST_CUST_001",
    "description": "Test payment",
    "customer_name": "Test Customer",
    "customer_email": "<EMAIL>"
  }'
```

#### Check Payment Status
```bash
curl -X GET https://staging-partner.aquaconnect.blue/api/zoho/payments/status/PS_123456789 \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 2. Postman Collection

Create a Postman collection with the following requests:

1. **Create Payment Session**
   - Method: POST
   - URL: `{{base_url}}/zoho/payments/create-session`
   - Headers: Authorization, Content-Type
   - Body: Payment request JSON

2. **Check Payment Status**
   - Method: GET
   - URL: `{{base_url}}/zoho/payments/status/{{session_id}}`
   - Headers: Authorization

3. **Verify Payment**
   - Method: GET
   - URL: `{{base_url}}/zoho/payments/status/{{session_id}}`
   - Headers: Authorization

## Performance Testing

### Load Testing
```dart
// Test concurrent payment sessions
Future<void> testConcurrentPayments() async {
  const concurrentRequests = 10;
  final futures = <Future>[];

  for (int i = 0; i < concurrentRequests; i++) {
    futures.add(PaymentTestUtils.testCreatePaymentSession(
      invoiceNumber: 'LOAD_TEST_$i',
    ));
  }

  final results = await Future.wait(futures);
  print('Created ${results.length} concurrent payment sessions');
}
```

### Response Time Testing
```dart
// Measure API response times
Future<void> testResponseTimes() async {
  final stopwatch = Stopwatch()..start();
  
  await PaymentTestUtils.testCreatePaymentSession();
  
  stopwatch.stop();
  print('Payment session created in ${stopwatch.elapsed.inMilliseconds}ms');
}
```

## Error Testing

### Network Error Simulation
1. Disable network connectivity
2. Attempt payment creation
3. Verify error handling and user feedback

### Authentication Error Testing
1. Use invalid/expired token
2. Attempt API calls
3. Verify proper error handling

### Validation Error Testing
1. Send invalid payment data
2. Verify validation errors
3. Check user-friendly error messages

## Monitoring and Debugging

### Enable Debug Logging
```dart
// In main.dart or test setup
AppLogger.setLogLevel(LogLevel.debug);
```

### View Payment Logs
```bash
# Flutter logs
flutter logs

# Filter payment-related logs
flutter logs | grep -i payment
```

### Analytics Verification
1. Check payment event tracking
2. Verify success/failure metrics
3. Monitor user interaction analytics

## Test Automation

### CI/CD Integration
```yaml
# .github/workflows/payment_tests.yml
name: Payment API Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: subosito/flutter-action@v2
      - run: flutter test test/domain/usecases/payments/
      - run: flutter test test/presentation/cubit/payments/
      - run: flutter test test/presentation/widgets/zoho_payment_button_test.dart
```

### Scheduled Testing
```yaml
# Run integration tests daily
name: Daily Payment Integration Tests
on:
  schedule:
    - cron: '0 2 * * *'  # 2 AM daily

jobs:
  integration_test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: subosito/flutter-action@v2
      - run: flutter test integration_test/payment_api_integration_test.dart
```

## Troubleshooting

### Common Issues

#### 1. Payment Session Creation Fails
**Symptoms**: API returns 400/500 error
**Solutions**:
- Check authentication token validity
- Verify request payload format
- Check network connectivity
- Review API endpoint URL

#### 2. Payment Status Not Updating
**Symptoms**: Status remains "created" or "pending"
**Solutions**:
- Check polling mechanism
- Verify webhook configuration
- Check Zoho sandbox status
- Review session timeout settings

#### 3. WebView Not Loading
**Symptoms**: Blank WebView or loading error
**Solutions**:
- Verify payment URL format
- Check WebView permissions
- Test network connectivity
- Review CORS settings

#### 4. Authentication Errors
**Symptoms**: 401 Unauthorized responses
**Solutions**:
- Refresh authentication token
- Check token expiration
- Verify API key configuration
- Review authentication headers

### Debug Commands
```bash
# Check network connectivity
ping payments-sandbox.zoho.in

# Test API endpoint
curl -I https://staging-partner.aquaconnect.blue/api/health

# View detailed logs
flutter logs --verbose
```

## Best Practices

### 1. Test Data Management
- Use consistent test data
- Clean up test sessions after testing
- Avoid using production data in tests
- Implement test data factories

### 2. Test Environment Isolation
- Use separate test databases
- Configure test-specific API endpoints
- Implement feature flags for testing
- Use mock services when appropriate

### 3. Continuous Testing
- Run tests on every code change
- Implement automated regression testing
- Monitor test coverage metrics
- Set up alerting for test failures

### 4. Documentation
- Document test scenarios
- Maintain test data specifications
- Update testing procedures regularly
- Share testing knowledge with team

## Conclusion

This comprehensive testing guide ensures the Zoho payment integration is thoroughly tested across all scenarios. Regular testing using these procedures will help maintain payment system reliability and user experience quality.

For additional support or questions, refer to the main integration documentation or contact the development team.

---

**Last Updated**: January 2024
**Version**: 1.0.0
**Maintainer**: AquaPartner Development Team
