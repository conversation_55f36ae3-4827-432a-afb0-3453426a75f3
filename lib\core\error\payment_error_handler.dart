import 'dart:async';
import 'package:flutter/material.dart';

import '../services/zoho_payment_service.dart';

/// Error handling patterns for payment operations
class PaymentErrorHandler {
  /// Handle payment errors with retry logic
  static Future<T> handleWithRetry<T>(
    Future<T> Function() operation, {
    int maxRetries = 3,
    Duration delay = const Duration(seconds: 2),
  }) async {
    int attempts = 0;
    
    while (attempts < maxRetries) {
      try {
        return await operation();
      } catch (e) {
        attempts++;
        
        if (e is PaymentException) {
          // Don't retry client errors (4xx)
          if (e.isClientError) {
            rethrow;
          }
          
          // Retry network errors and server errors
          if (attempts < maxRetries && e.isNetworkError) {
            await Future.delayed(delay * attempts); // Exponential backoff
            continue;
          }
        }
        
        rethrow;
      }
    }
    
    throw PaymentException('Operation failed after $maxRetries attempts');
  }
  
  /// Show user-friendly error dialog
  static void showErrorDialog(BuildContext context, PaymentException error) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Payment Error'),
        content: Text(error.userFriendlyMessage),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// Show error snackbar
  static void showErrorSnackBar(BuildContext context, PaymentException error) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(error.userFriendlyMessage),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 5),
        action: SnackBarAction(
          label: 'Dismiss',
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }

  /// Handle payment errors with user feedback
  static Future<T?> handleWithUserFeedback<T>(
    BuildContext context,
    Future<T> Function() operation, {
    bool showDialog = true,
    bool showSnackBar = false,
    int maxRetries = 3,
  }) async {
    try {
      return await handleWithRetry(
        operation,
        maxRetries: maxRetries,
      );
    } catch (e) {
      if (e is PaymentException) {
        if (showDialog) {
          showErrorDialog(context, e);
        } else if (showSnackBar) {
          showErrorSnackBar(context, e);
        }
      }
      return null;
    }
  }

  /// Get appropriate error message for different error types
  static String getErrorMessage(dynamic error) {
    if (error is PaymentException) {
      return error.userFriendlyMessage;
    } else if (error is TimeoutException) {
      return 'Request timed out. Please check your internet connection and try again.';
    } else if (error is FormatException) {
      return 'Invalid response format. Please try again.';
    } else {
      return 'An unexpected error occurred. Please try again.';
    }
  }

  /// Check if error is retryable
  static bool isRetryable(dynamic error) {
    if (error is PaymentException) {
      return error.isNetworkError;
    } else if (error is TimeoutException) {
      return true;
    } else {
      return false;
    }
  }

  /// Log payment errors for debugging
  static void logError(dynamic error, StackTrace? stackTrace) {
    debugPrint('Payment Error: $error');
    if (stackTrace != null) {
      debugPrint('Stack Trace: $stackTrace');
    }
  }
}

/// Security utilities for payment operations
class PaymentSecurity {
  /// Validate payment amounts to prevent manipulation
  static bool validateAmount(double amount) {
    // Check for reasonable limits
    if (amount <= 0 || amount > 1000000) {
      return false;
    }
    
    // Check for precision (max 2 decimal places)
    final rounded = double.parse(amount.toStringAsFixed(2));
    return (amount - rounded).abs() < 0.001;
  }
  
  /// Sanitize user input
  static String sanitizeInput(String input) {
    return input.trim().replaceAll(RegExp(r'[<>"\']'), '');
  }
  
  /// Generate secure invoice numbers
  static String generateInvoiceNumber(String prefix) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = (DateTime.now().microsecond % 9999).toString().padLeft(4, '0');
    return '${prefix}_${timestamp}_$random';
  }

  /// Validate email format
  static bool isValidEmail(String email) {
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    return emailRegex.hasMatch(email);
  }

  /// Validate phone number format
  static bool isValidPhone(String phone) {
    final phoneRegex = RegExp(r'^\+?[1-9]\d{1,14}$');
    return phoneRegex.hasMatch(phone.replaceAll(RegExp(r'[-\s]'), ''));
  }

  /// Mask sensitive data for logging
  static String maskSensitiveData(String data) {
    if (data.length <= 4) return '****';
    return '${data.substring(0, 2)}${'*' * (data.length - 4)}${data.substring(data.length - 2)}';
  }
}

/// Cache utilities for payment operations
class PaymentCache {
  static final Map<String, PaymentStatusResponse> _statusCache = {};
  static const Duration _cacheExpiry = Duration(minutes: 5);
  
  /// Cache payment status with expiry
  static void cacheStatus(String sessionId, PaymentStatusResponse status) {
    _statusCache[sessionId] = status;
    
    // Auto-expire cache
    Timer(_cacheExpiry, () => _statusCache.remove(sessionId));
  }
  
  /// Get cached status if available and not expired
  static PaymentStatusResponse? getCachedStatus(String sessionId) {
    return _statusCache[sessionId];
  }
  
  /// Clear all cached data
  static void clearCache() {
    _statusCache.clear();
  }
}

/// Analytics utilities for payment operations
class PaymentAnalytics {
  /// Log payment events for analytics
  static void logPaymentEvent(String event, Map<String, dynamic> parameters) {
    // Remove sensitive data
    final sanitizedParams = Map<String, dynamic>.from(parameters);
    sanitizedParams.remove('customer_email');
    sanitizedParams.remove('customer_phone');
    
    // Log to your analytics service
    debugPrint('Payment Event: $event - $sanitizedParams');
  }
  
  /// Track payment funnel
  static void trackPaymentStep(String step, String sessionId) {
    logPaymentEvent('payment_step', {
      'step': step,
      'session_id': sessionId,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  /// Track payment completion
  static void trackPaymentCompletion(String sessionId, bool success, double amount) {
    logPaymentEvent('payment_completion', {
      'session_id': sessionId,
      'success': success,
      'amount': amount,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  /// Track payment errors
  static void trackPaymentError(String sessionId, String errorType, String errorMessage) {
    logPaymentEvent('payment_error', {
      'session_id': sessionId,
      'error_type': errorType,
      'error_message': errorMessage,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }
}
