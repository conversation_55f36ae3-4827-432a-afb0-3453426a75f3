import 'package:dartz/dartz.dart';
import '../../../core/error/failures.dart';
import '../../../core/utils/logger.dart';
import '../../entities/account_statement/account_statement.dart';
import '../../repositories/account_statement_repository.dart';

class SyncAccountStatementUseCase {
  final AccountStatementRepository repository;
  final AppLogger logger;

  SyncAccountStatementUseCase({
    required this.repository,
    required this.logger,
  });

  Future<Either<Failure, AccountStatement?>> call(String customerId) async {
    logger.i('SyncAccountStatementUseCase called for customer: $customerId');
    return repository.syncAccountStatement(customerId);
  }
}