# Windows PowerShell Script for Flutter Test Coverage Analysis
# This script provides comprehensive coverage analysis for Windows development environment

param(
    [string]$TestPath = "test/",
    [string]$CoverageDir = "coverage",
    [switch]$GenerateHtml,
    [switch]$OpenReport,
    [switch]$Verbose
)

Write-Host "🚀 Flutter Test Coverage Analysis for Windows" -ForegroundColor Green
Write-Host "=============================================" -ForegroundColor Green

# Create coverage directory if it doesn't exist
if (!(Test-Path $CoverageDir)) {
    New-Item -ItemType Directory -Path $CoverageDir -Force | Out-Null
    Write-Host "📁 Created coverage directory: $CoverageDir" -ForegroundColor Yellow
}

# Function to run tests with coverage
function Run-TestsWithCoverage {
    param([string]$Path, [string]$CoverageDir)

    Write-Host "🧪 Running tests with coverage..." -ForegroundColor Cyan
    Write-Host "Test path: $Path" -ForegroundColor Gray

    $startTime = Get-Date

    # Run Flutter tests with coverage
    try {
        $testResult = flutter test $Path --coverage --coverage-path="$CoverageDir/lcov.info"
        $exitCode = $LASTEXITCODE
    }
    catch {
        Write-Host "❌ Error running tests: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }

    $endTime = Get-Date
    $duration = $endTime - $startTime

    if ($exitCode -eq 0) {
        Write-Host "✅ Tests completed successfully in $($duration.TotalSeconds.ToString('F2')) seconds" -ForegroundColor Green
        return $true
    }
    else {
        Write-Host "❌ Tests failed with exit code: $exitCode" -ForegroundColor Red
        return $false
    }
}

# Function to analyze LCOV file
function Analyze-LcovFile {
    param([string]$LcovPath)
    
    if (!(Test-Path $LcovPath)) {
        Write-Host "⚠️ LCOV file not found: $LcovPath" -ForegroundColor Yellow
        return $null
    }
    
    Write-Host "📊 Analyzing coverage data..." -ForegroundColor Cyan
    
    $lcovContent = Get-Content $LcovPath
    $totalLines = 0
    $coveredLines = 0
    $totalFunctions = 0
    $coveredFunctions = 0
    $totalBranches = 0
    $coveredBranches = 0
    $fileCount = 0
    
    foreach ($line in $lcovContent) {
        if ($line.StartsWith("SF:")) {
            $fileCount++
        }
        elseif ($line.StartsWith("LF:")) {
            $totalLines += [int]($line.Split(":")[1])
        }
        elseif ($line.StartsWith("LH:")) {
            $coveredLines += [int]($line.Split(":")[1])
        }
        elseif ($line.StartsWith("FNF:")) {
            $totalFunctions += [int]($line.Split(":")[1])
        }
        elseif ($line.StartsWith("FNH:")) {
            $coveredFunctions += [int]($line.Split(":")[1])
        }
        elseif ($line.StartsWith("BRF:")) {
            $totalBranches += [int]($line.Split(":")[1])
        }
        elseif ($line.StartsWith("BRH:")) {
            $coveredBranches += [int]($line.Split(":")[1])
        }
    }
    
    $lineCoverage = if ($totalLines -gt 0) { ($coveredLines / $totalLines) * 100 } else { 0 }
    $functionCoverage = if ($totalFunctions -gt 0) { ($coveredFunctions / $totalFunctions) * 100 } else { 0 }
    $branchCoverage = if ($totalBranches -gt 0) { ($coveredBranches / $totalBranches) * 100 } else { 0 }
    
    return @{
        FileCount        = $fileCount
        TotalLines       = $totalLines
        CoveredLines     = $coveredLines
        LineCoverage     = $lineCoverage
        TotalFunctions   = $totalFunctions
        CoveredFunctions = $coveredFunctions
        FunctionCoverage = $functionCoverage
        TotalBranches    = $totalBranches
        CoveredBranches  = $coveredBranches
        BranchCoverage   = $branchCoverage
    }
}

# Function to generate coverage report
function Generate-CoverageReport {
    param([hashtable]$CoverageData)
    
    Write-Host ""
    Write-Host "📈 COVERAGE ANALYSIS REPORT" -ForegroundColor Green
    Write-Host "============================" -ForegroundColor Green
    Write-Host ""
    
    Write-Host "📁 Files Analyzed: $($CoverageData.FileCount)" -ForegroundColor White
    Write-Host ""
    
    # Line Coverage
    $lineColor = if ($CoverageData.LineCoverage -ge 70) { "Green" } 
    elseif ($CoverageData.LineCoverage -ge 50) { "Yellow" } 
    else { "Red" }
    Write-Host "📏 Line Coverage: $($CoverageData.LineCoverage.ToString('F2'))% ($($CoverageData.CoveredLines)/$($CoverageData.TotalLines))" -ForegroundColor $lineColor
    
    # Function Coverage
    $funcColor = if ($CoverageData.FunctionCoverage -ge 80) { "Green" } 
    elseif ($CoverageData.FunctionCoverage -ge 60) { "Yellow" } 
    else { "Red" }
    Write-Host "🔧 Function Coverage: $($CoverageData.FunctionCoverage.ToString('F2'))% ($($CoverageData.CoveredFunctions)/$($CoverageData.TotalFunctions))" -ForegroundColor $funcColor
    
    # Branch Coverage
    if ($CoverageData.TotalBranches -gt 0) {
        $branchColor = if ($CoverageData.BranchCoverage -ge 65) { "Green" } 
        elseif ($CoverageData.BranchCoverage -ge 45) { "Yellow" } 
        else { "Red" }
        Write-Host "🌿 Branch Coverage: $($CoverageData.BranchCoverage.ToString('F2'))% ($($CoverageData.CoveredBranches)/$($CoverageData.TotalBranches))" -ForegroundColor $branchColor
    }
    
    Write-Host ""
    
    # Production Readiness Assessment
    Write-Host "🎯 PRODUCTION READINESS ASSESSMENT" -ForegroundColor Cyan
    Write-Host "===================================" -ForegroundColor Cyan
    
    $readinessScore = 0
    $maxScore = 100
    
    # Line coverage scoring (40 points max)
    if ($CoverageData.LineCoverage -ge 70) { $readinessScore += 40 }
    elseif ($CoverageData.LineCoverage -ge 50) { $readinessScore += 25 }
    elseif ($CoverageData.LineCoverage -ge 30) { $readinessScore += 15 }
    
    # Function coverage scoring (30 points max)
    if ($CoverageData.FunctionCoverage -ge 80) { $readinessScore += 30 }
    elseif ($CoverageData.FunctionCoverage -ge 60) { $readinessScore += 20 }
    elseif ($CoverageData.FunctionCoverage -ge 40) { $readinessScore += 10 }
    
    # Branch coverage scoring (20 points max)
    if ($CoverageData.TotalBranches -gt 0) {
        if ($CoverageData.BranchCoverage -ge 65) { $readinessScore += 20 }
        elseif ($CoverageData.BranchCoverage -ge 45) { $readinessScore += 12 }
        elseif ($CoverageData.BranchCoverage -ge 25) { $readinessScore += 6 }
    }
    else {
        $readinessScore += 10  # Partial credit if no branch data
    }
    
    # File coverage scoring (10 points max)
    if ($CoverageData.FileCount -ge 50) { $readinessScore += 10 }
    elseif ($CoverageData.FileCount -ge 30) { $readinessScore += 7 }
    elseif ($CoverageData.FileCount -ge 15) { $readinessScore += 4 }
    
    $readinessPercentage = $readinessScore
    $readinessColor = if ($readinessPercentage -ge 85) { "Green" } 
    elseif ($readinessPercentage -ge 70) { "Yellow" } 
    else { "Red" }
    
    Write-Host "🏆 Production Readiness Score: $readinessPercentage%" -ForegroundColor $readinessColor
    
    if ($readinessPercentage -ge 85) {
        Write-Host "✅ READY FOR PRODUCTION" -ForegroundColor Green
        Write-Host "   Your app has excellent test coverage and is ready for CI/CD implementation." -ForegroundColor Green
    }
    elseif ($readinessPercentage -ge 70) {
        Write-Host "⚠️ APPROACHING PRODUCTION READINESS" -ForegroundColor Yellow
        Write-Host "   Your app has good coverage but needs improvement in some areas." -ForegroundColor Yellow
    }
    else {
        Write-Host "❌ NOT READY FOR PRODUCTION" -ForegroundColor Red
        Write-Host "   Your app needs significant improvement in test coverage." -ForegroundColor Red
    }
    
    Write-Host ""
    
    # Recommendations
    Write-Host "💡 RECOMMENDATIONS" -ForegroundColor Magenta
    Write-Host "==================" -ForegroundColor Magenta
    
    if ($CoverageData.LineCoverage -lt 70) {
        Write-Host "• Increase line coverage to at least 70% for production readiness" -ForegroundColor Yellow
    }
    if ($CoverageData.FunctionCoverage -lt 80) {
        Write-Host "• Improve function coverage to at least 80%" -ForegroundColor Yellow
    }
    if ($CoverageData.TotalBranches -gt 0 -and $CoverageData.BranchCoverage -lt 65) {
        Write-Host "• Add more branch coverage tests (target: 65%+)" -ForegroundColor Yellow
    }
    if ($CoverageData.FileCount -lt 30) {
        Write-Host "• Consider adding more comprehensive test files" -ForegroundColor Yellow
    }
    
    Write-Host "• Use VS Code Coverage Gutters extension for visual coverage" -ForegroundColor Cyan
    Write-Host "• Consider online LCOV viewers for detailed analysis" -ForegroundColor Cyan
    Write-Host "• Set up CI/CD pipeline with coverage gates" -ForegroundColor Cyan
}

# Function to suggest Windows-compatible tools
function Show-WindowsTools {
    Write-Host ""
    Write-Host "🛠️ WINDOWS-COMPATIBLE COVERAGE TOOLS" -ForegroundColor Blue
    Write-Host "=====================================" -ForegroundColor Blue
    Write-Host ""
    Write-Host "VS Code Extensions:" -ForegroundColor Cyan
    Write-Host "• Coverage Gutters - Visual coverage in editor" -ForegroundColor White
    Write-Host "• Flutter Coverage Helper - Flutter-specific coverage" -ForegroundColor White
    Write-Host ""
    Write-Host "Online Tools:" -ForegroundColor Cyan
    Write-Host "• https://lcov-viewer.netlify.app/ - Upload LCOV files" -ForegroundColor White
    Write-Host "• https://genhtml.netlify.app/ - Online genhtml alternative" -ForegroundColor White
    Write-Host ""
    Write-Host "PowerShell Commands:" -ForegroundColor Cyan
    Write-Host "• dart pub global activate coverage" -ForegroundColor White
    Write-Host "• dart pub global run coverage:format_coverage" -ForegroundColor White
}

# Main execution
Write-Host "Starting coverage analysis..." -ForegroundColor White

# Run tests
$testsSuccessful = Run-TestsWithCoverage -Path $TestPath -CoverageDir $CoverageDir

if ($testsSuccessful) {
    # Analyze coverage
    $lcovPath = "$CoverageDir/lcov.info"
    $coverageData = Analyze-LcovFile -LcovPath $lcovPath
    
    if ($coverageData) {
        Generate-CoverageReport -CoverageData $coverageData
        
        if ($GenerateHtml) {
            Write-Host ""
            Write-Host "🌐 Attempting to generate HTML report..." -ForegroundColor Cyan
            
            # Try using dart coverage tool
            try {
                dart pub global run coverage:format_coverage --lcov --in=$CoverageDir --out=$CoverageDir/lcov.info --packages=.dart_tool/package_config.json --report-on=lib
                Write-Host "✅ LCOV file updated successfully" -ForegroundColor Green
            }
            catch {
                Write-Host "⚠️ Could not update LCOV file: $($_.Exception.Message)" -ForegroundColor Yellow
            }
        }
        
        Show-WindowsTools
        
        if ($OpenReport -and (Test-Path "$CoverageDir/lcov.info")) {
            Write-Host ""
            Write-Host "🌐 Opening LCOV file location..." -ForegroundColor Cyan
            Start-Process explorer.exe -ArgumentList (Resolve-Path $CoverageDir).Path
        }
    }
    else {
        Write-Host "❌ Could not analyze coverage data" -ForegroundColor Red
        Show-WindowsTools
    }
}
else {
    Write-Host "❌ Tests failed. Cannot generate coverage report." -ForegroundColor Red
}

Write-Host ""
Write-Host "✨ Coverage analysis complete!" -ForegroundColor Green
