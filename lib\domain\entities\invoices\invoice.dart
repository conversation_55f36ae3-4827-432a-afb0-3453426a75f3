import 'package:equatable/equatable.dart';
import 'invoice_item.dart';

class Invoice extends Equatable {
  final String invoiceId;
  final String addressId;
  final int ageInDays;
  final String ageTier;
  final double balance;
  final String customerId;
  final String deliveryMode;
  final String deliveryStatus;
  final DateTime dueDate;
  final DateTime invoiceDate;
  final String invoiceNumber;
  final String invoiceStatus;
  final double subTotal;
  final double total;
  final List<InvoiceItem> items;

  const Invoice({
    required this.invoiceId,
    required this.addressId,
    required this.ageInDays,
    required this.ageTier,
    required this.balance,
    required this.customerId,
    required this.deliveryMode,
    required this.deliveryStatus,
    required this.dueDate,
    required this.invoiceDate,
    required this.invoiceNumber,
    required this.invoiceStatus,
    required this.subTotal,
    required this.total,
    required this.items,
  });

  @override
  List<Object?> get props => [
    invoiceId,
    addressId,
    ageInDays,
    ageTier,
    balance,
    customerId,
    deliveryMode,
    deliveryStatus,
    dueDate,
    invoiceDate,
    invoiceNumber,
    invoiceStatus,
    subTotal,
    total,
    items,
  ];
}
