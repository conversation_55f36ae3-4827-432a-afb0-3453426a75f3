import '../../../core/error/exceptions.dart';
import '../../../core/utils/logger.dart';
import '../../../objectbox.g.dart';
import '../../models/objectbox_product_catalogue_model.dart';
import '../../models/product_catalogue_model.dart';
import 'package:objectbox/objectbox.dart';

abstract class ProductCatalogueLocalDataSource {
  /// Gets all product catalogues from local storage
  Future<List<ProductCatalogueModel>> getProductCatalogues();

  /// Gets a specific product catalogue by name
  Future<ProductCatalogueModel?> getProductCatalogueByName(String name);

  /// Saves product catalogues to local storage
  Future<void> saveProductCatalogues(
    List<ProductCatalogueModel> productCatalogues,
  );

  /// Checks if there are any product catalogues in local storage
  Future<bool> hasProductCatalogues();
}

class ProductCatalogueLocalDataSourceImpl
    implements ProductCatalogueLocalDataSource {
  final Box<ObjectBoxProductCatalogueModel> productCatalogueBox;
  final AppLogger logger;

  ProductCatalogueLocalDataSourceImpl({
    required this.productCatalogueBox,
    required this.logger,
  });

  @override
  Future<bool> hasProductCatalogues() async {
    try {
      logger.i("Checking if product catalogues exist in local storage");
      final count = productCatalogueBox.count();
      logger.i("Product catalogue count: $count");
      return count > 0;
    } catch (e) {
      logger.e(
        "Error checking if product catalogues exist in local storage",
        e,
      );
      throw CacheException();
    }
  }

  @override
  Future<List<ProductCatalogueModel>> getProductCatalogues() async {
    try {
      logger.i("Getting product catalogues from local storage");

      final objectBoxModels = productCatalogueBox.getAll();

      if (objectBoxModels.isEmpty) {
        logger.w("No product catalogues found in local storage");
        return [];
      }

      final productCatalogues =
          objectBoxModels
              .map((model) => model.toProductCatalogueModel())
              .toList();

      logger.i(
        "Found ${productCatalogues.length} product catalogues in local storage",
      );
      return productCatalogues;
    } catch (e) {
      logger.e("Error getting product catalogues from local storage", e);
      throw CacheException();
    }
  }

  @override
  Future<ProductCatalogueModel?> getProductCatalogueByName(String name) async {
    try {
      logger.i("Getting product catalogue by name: $name from local storage");

      final query =
          productCatalogueBox
              .query(ObjectBoxProductCatalogueModel_.name.equals(name))
              .build();

      final results = query.find();
      query.close();

      if (results.isEmpty) {
        logger.w(
          "No product catalogue found with name: $name in local storage",
        );
        return null;
      }

      logger.i("Found product catalogue with name: $name in local storage");
      return results.first.toProductCatalogueModel();
    } catch (e) {
      logger.e("Error getting product catalogue by name from local storage", e);
      throw CacheException();
    }
  }

  @override
  Future<void> saveProductCatalogues(
    List<ProductCatalogueModel> productCatalogues,
  ) async {
    try {
      logger.i(
        "Saving ${productCatalogues.length} product catalogues to local storage",
      );

      // Create a map of names to entities for quick lookup
      final productCatalogueMap = {
        for (var pc in productCatalogues) pc.name: pc,
      };

      // Get all existing product catalogues
      final query = productCatalogueBox.query().build();
      final existingModels = query.find();
      query.close();

      // Create maps for updates and inserts
      final modelsToUpdate = <ObjectBoxProductCatalogueModel>[];
      final modelsToInsert = <ObjectBoxProductCatalogueModel>[];

      // Process existing models - update if in the new data
      final processedNames = <String>{};

      for (var existingModel in existingModels) {
        if (productCatalogueMap.containsKey(existingModel.name)) {
          // Update existing model
          final entity = productCatalogueMap[existingModel.name]!;
          final objectBoxModel =
              ObjectBoxProductCatalogueModel.fromProductCatalogueModel(entity);

          final updatedModel = existingModel.copyWith(
            image: objectBoxModel.image,
            sortOrder: objectBoxModel.sortOrder,
            status: objectBoxModel.status,
            productsJson: objectBoxModel.productsJson,
          );

          modelsToUpdate.add(updatedModel);
          processedNames.add(existingModel.name);
        }
      }

      // Process new models - insert if not already processed
      for (var entity in productCatalogues) {
        if (!processedNames.contains(entity.name)) {
          modelsToInsert.add(
            ObjectBoxProductCatalogueModel.fromProductCatalogueModel(entity),
          );
        }
      }

      // Batch update and insert
      if (modelsToUpdate.isNotEmpty) {
        productCatalogueBox.putMany(modelsToUpdate);
      }

      if (modelsToInsert.isNotEmpty) {
        productCatalogueBox.putMany(modelsToInsert);
      }

      logger.i(
        "Successfully saved product catalogues to local storage " +
            "(Updated: ${modelsToUpdate.length}, Inserted: ${modelsToInsert.length})",
      );
    } catch (e) {
      logger.e("Error saving product catalogues to local storage", e);
      throw CacheException();
    }
  }
}
