import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:aquapartner/core/cache/cache_manager.dart';
import 'package:aquapartner/core/utils/logger.dart';

class MockSharedPreferences extends Mock implements SharedPreferences {}

class MockAppLogger extends Mock implements AppLogger {}

void main() {
  group('CacheManager Tests', () {
    late CacheManager cacheManager;
    late MockSharedPreferences mockSharedPreferences;
    late MockAppLogger mockLogger;

    setUp(() {
      mockSharedPreferences = MockSharedPreferences();
      mockLogger = MockAppLogger();
      cacheManager = CacheManager(
        sharedPreferences: mockSharedPreferences,
        logger: mockLogger,
      );
    });

    group('getLastSyncTime Tests', () {
      test('should return DateTime when timestamp exists', () async {
        // Arrange
        const entityId = 'test_entity_123';
        final expectedDateTime = DateTime(2024, 1, 1, 12, 0, 0);
        final timestamp = expectedDateTime.millisecondsSinceEpoch;

        when(
          () => mockSharedPreferences.getInt('last_sync_time_$entityId'),
        ).thenReturn(timestamp);

        // Act
        final result = await cacheManager.getLastSyncTime(entityId);

        // Assert
        expect(result, equals(expectedDateTime));
        verify(
          () => mockSharedPreferences.getInt('last_sync_time_$entityId'),
        ).called(1);
      });

      test('should return null when timestamp does not exist', () async {
        // Arrange
        const entityId = 'nonexistent_entity';

        when(
          () => mockSharedPreferences.getInt('last_sync_time_$entityId'),
        ).thenReturn(null);

        // Act
        final result = await cacheManager.getLastSyncTime(entityId);

        // Assert
        expect(result, isNull);
        verify(
          () => mockSharedPreferences.getInt('last_sync_time_$entityId'),
        ).called(1);
      });

      test('should return null and log error when exception occurs', () async {
        // Arrange
        const entityId = 'error_entity';

        when(
          () => mockSharedPreferences.getInt('last_sync_time_$entityId'),
        ).thenThrow(Exception('SharedPreferences error'));

        // Act
        final result = await cacheManager.getLastSyncTime(entityId);

        // Assert
        expect(result, isNull);
        verify(
          () => mockLogger.e(
            'Error getting last sync time: Exception: SharedPreferences error',
          ),
        ).called(1);
      });

      test('should use entity type in key when provided', () async {
        // Arrange
        const entityId = 'test_entity_123';
        const entityType = 'dashboard';
        const expectedKey = 'last_sync_time_dashboard_test_entity_123';

        when(
          () => mockSharedPreferences.getInt(expectedKey),
        ).thenReturn(1704110400000);

        // Act
        await cacheManager.getLastSyncTime(entityId, entityType: entityType);

        // Assert
        verify(() => mockSharedPreferences.getInt(expectedKey)).called(1);
      });
    });

    group('saveLastSyncTime Tests', () {
      test('should save timestamp successfully', () async {
        // Arrange
        const entityId = 'test_entity_123';
        final dateTime = DateTime(2024, 1, 1, 12, 0, 0);
        final expectedTimestamp = dateTime.millisecondsSinceEpoch;

        when(
          () => mockSharedPreferences.setInt(
            'last_sync_time_$entityId',
            expectedTimestamp,
          ),
        ).thenAnswer((_) async => true);

        // Act
        final result = await cacheManager.saveLastSyncTime(entityId, dateTime);

        // Assert
        expect(result, isTrue);
        verify(
          () => mockSharedPreferences.setInt(
            'last_sync_time_$entityId',
            expectedTimestamp,
          ),
        ).called(1);
      });

      test('should return false and log error when save fails', () async {
        // Arrange
        const entityId = 'test_entity_123';
        final dateTime = DateTime(2024, 1, 1, 12, 0, 0);

        when(
          () => mockSharedPreferences.setInt(any(), any()),
        ).thenThrow(Exception('Save failed'));

        // Act
        final result = await cacheManager.saveLastSyncTime(entityId, dateTime);

        // Assert
        expect(result, isFalse);
        verify(
          () => mockLogger.e(
            'Error saving last sync time: Exception: Save failed',
          ),
        ).called(1);
      });

      test('should use entity type in key when provided', () async {
        // Arrange
        const entityId = 'test_entity_123';
        const entityType = 'user';
        const expectedKey = 'last_sync_time_user_test_entity_123';
        final dateTime = DateTime(2024, 1, 1, 12, 0, 0);

        when(
          () => mockSharedPreferences.setInt(expectedKey, any()),
        ).thenAnswer((_) async => true);

        // Act
        await cacheManager.saveLastSyncTime(
          entityId,
          dateTime,
          entityType: entityType,
        );

        // Assert
        verify(
          () => mockSharedPreferences.setInt(
            expectedKey,
            dateTime.millisecondsSinceEpoch,
          ),
        ).called(1);
      });
    });

    group('isSyncNeeded Tests', () {
      test('should return true when entity never synced', () async {
        // Arrange
        const entityId = 'never_synced_entity';

        when(
          () => mockSharedPreferences.getInt('last_sync_time_$entityId'),
        ).thenReturn(null);

        // Act
        final result = await cacheManager.isSyncNeeded(entityId);

        // Assert
        expect(result, isTrue);
        verify(
          () => mockLogger.i('Entity $entityId never synced, sync needed'),
        ).called(1);
      });

      test('should return true when data is stale', () async {
        // Arrange
        const entityId = 'stale_entity';
        final oldDateTime = DateTime.now().subtract(const Duration(hours: 3));
        final oldTimestamp = oldDateTime.millisecondsSinceEpoch;

        when(
          () => mockSharedPreferences.getInt('last_sync_time_$entityId'),
        ).thenReturn(oldTimestamp);

        // Act
        final result = await cacheManager.isSyncNeeded(
          entityId,
          freshnessThreshold: const Duration(hours: 2),
        );

        // Assert
        expect(result, isTrue);
        verify(() => mockLogger.i(any())).called(1);
      });

      test('should return false when data is fresh', () async {
        // Arrange
        const entityId = 'fresh_entity';
        final recentDateTime = DateTime.now().subtract(
          const Duration(minutes: 30),
        );
        final recentTimestamp = recentDateTime.millisecondsSinceEpoch;

        when(
          () => mockSharedPreferences.getInt('last_sync_time_$entityId'),
        ).thenReturn(recentTimestamp);

        // Act
        final result = await cacheManager.isSyncNeeded(
          entityId,
          freshnessThreshold: const Duration(hours: 2),
        );

        // Assert
        expect(result, isFalse);
        verify(() => mockLogger.i(any())).called(1);
      });

      test('should return true and log error when exception occurs', () async {
        // Arrange
        const entityId = 'error_entity';

        when(
          () => mockSharedPreferences.getInt('last_sync_time_$entityId'),
        ).thenThrow(Exception('Error checking sync'));

        // Act
        final result = await cacheManager.isSyncNeeded(entityId);

        // Assert
        expect(result, isTrue);
        // The exception is caught in getLastSyncTime, so it logs the getLastSyncTime error
        // and then treats it as never synced
        verify(
          () => mockLogger.e(
            'Error getting last sync time: Exception: Error checking sync',
          ),
        ).called(1);
        verify(
          () => mockLogger.i('Entity $entityId never synced, sync needed'),
        ).called(1);
      });

      test('should use custom freshness threshold', () async {
        // Arrange
        const entityId = 'custom_threshold_entity';
        final dateTime = DateTime.now().subtract(const Duration(minutes: 45));
        final timestamp = dateTime.millisecondsSinceEpoch;

        when(
          () => mockSharedPreferences.getInt('last_sync_time_$entityId'),
        ).thenReturn(timestamp);

        // Act
        final result = await cacheManager.isSyncNeeded(
          entityId,
          freshnessThreshold: const Duration(minutes: 30),
        );

        // Assert
        expect(result, isTrue);
      });
    });

    group('getCacheStatus Tests', () {
      test('should return CacheStatus.notFound when never synced', () async {
        // Arrange
        const entityId = 'never_synced_entity';

        when(
          () => mockSharedPreferences.getInt('last_sync_time_$entityId'),
        ).thenReturn(null);

        // Act
        final result = await cacheManager.getCacheStatus(entityId);

        // Assert
        expect(result, equals(CacheStatus.notFound));
      });

      test('should return CacheStatus.stale when data is old', () async {
        // Arrange
        const entityId = 'stale_entity';
        final oldDateTime = DateTime.now().subtract(const Duration(hours: 3));
        final oldTimestamp = oldDateTime.millisecondsSinceEpoch;

        when(
          () => mockSharedPreferences.getInt('last_sync_time_$entityId'),
        ).thenReturn(oldTimestamp);

        // Act
        final result = await cacheManager.getCacheStatus(
          entityId,
          freshnessThreshold: const Duration(hours: 2),
        );

        // Assert
        expect(result, equals(CacheStatus.stale));
      });

      test('should return CacheStatus.fresh when data is recent', () async {
        // Arrange
        const entityId = 'fresh_entity';
        final recentDateTime = DateTime.now().subtract(
          const Duration(minutes: 30),
        );
        final recentTimestamp = recentDateTime.millisecondsSinceEpoch;

        when(
          () => mockSharedPreferences.getInt('last_sync_time_$entityId'),
        ).thenReturn(recentTimestamp);

        // Act
        final result = await cacheManager.getCacheStatus(
          entityId,
          freshnessThreshold: const Duration(hours: 2),
        );

        // Assert
        expect(result, equals(CacheStatus.fresh));
      });

      test(
        'should return CacheStatus.notFound when exception occurs in getLastSyncTime',
        () async {
          // Arrange
          const entityId = 'error_entity';

          when(
            () => mockSharedPreferences.getInt('last_sync_time_$entityId'),
          ).thenThrow(Exception('Cache error'));

          // Act
          final result = await cacheManager.getCacheStatus(entityId);

          // Assert
          expect(result, equals(CacheStatus.notFound));
          verify(
            () => mockLogger.e(
              'Error getting last sync time: Exception: Cache error',
            ),
          ).called(1);
        },
      );
    });

    group('invalidateCache Tests', () {
      test('should invalidate cache successfully', () async {
        // Arrange
        const entityId = 'test_entity_123';

        when(
          () => mockSharedPreferences.setInt('last_sync_time_$entityId', 0),
        ).thenAnswer((_) async => true);

        // Act
        final result = await cacheManager.invalidateCache(entityId);

        // Assert
        expect(result, isTrue);
        verify(
          () => mockSharedPreferences.setInt('last_sync_time_$entityId', 0),
        ).called(1);
      });

      test(
        'should return false and log error when invalidation fails',
        () async {
          // Arrange
          const entityId = 'test_entity_123';

          when(
            () => mockSharedPreferences.setInt('last_sync_time_$entityId', 0),
          ).thenThrow(Exception('Invalidation failed'));

          // Act
          final result = await cacheManager.invalidateCache(entityId);

          // Assert
          expect(result, isFalse);
          verify(
            () => mockLogger.e(
              'Error invalidating cache: Exception: Invalidation failed',
            ),
          ).called(1);
        },
      );

      test('should use entity type in key when provided', () async {
        // Arrange
        const entityId = 'test_entity_123';
        const entityType = 'farmer';
        const expectedKey = 'last_sync_time_farmer_test_entity_123';

        when(
          () => mockSharedPreferences.setInt(expectedKey, 0),
        ).thenAnswer((_) async => true);

        // Act
        await cacheManager.invalidateCache(entityId, entityType: entityType);

        // Assert
        verify(() => mockSharedPreferences.setInt(expectedKey, 0)).called(1);
      });
    });

    group('Edge Cases and Integration Tests', () {
      test('should handle multiple entities with different types', () async {
        // Arrange
        const entityId = 'multi_entity_123';
        const userType = 'user';
        const dashboardType = 'dashboard';
        final dateTime = DateTime.now();

        when(
          () => mockSharedPreferences.setInt(any(), any()),
        ).thenAnswer((_) async => true);

        // Act
        await cacheManager.saveLastSyncTime(
          entityId,
          dateTime,
          entityType: userType,
        );
        await cacheManager.saveLastSyncTime(
          entityId,
          dateTime,
          entityType: dashboardType,
        );

        // Assert
        verify(
          () => mockSharedPreferences.setInt(
            'last_sync_time_user_multi_entity_123',
            any(),
          ),
        ).called(1);
        verify(
          () => mockSharedPreferences.setInt(
            'last_sync_time_dashboard_multi_entity_123',
            any(),
          ),
        ).called(1);
      });

      test('should handle concurrent operations gracefully', () async {
        // Arrange
        const entityId = 'concurrent_entity';
        final dateTime = DateTime.now();

        when(
          () => mockSharedPreferences.setInt(any(), any()),
        ).thenAnswer((_) async => true);
        when(
          () => mockSharedPreferences.getInt(any()),
        ).thenReturn(dateTime.millisecondsSinceEpoch);

        // Act
        final futures = [
          cacheManager.saveLastSyncTime(entityId, dateTime),
          cacheManager.getLastSyncTime(entityId),
          cacheManager.isSyncNeeded(entityId),
          cacheManager.getCacheStatus(entityId),
        ];

        final results = await Future.wait(futures);

        // Assert
        expect(results[0], isTrue); // saveLastSyncTime
        expect(results[1], isA<DateTime>()); // getLastSyncTime
        expect(results[2], isA<bool>()); // isSyncNeeded
        expect(results[3], isA<CacheStatus>()); // getCacheStatus
      });

      test('should handle very old timestamps', () async {
        // Arrange
        const entityId = 'old_entity';
        final veryOldDateTime = DateTime(1970, 1, 1);
        final veryOldTimestamp = veryOldDateTime.millisecondsSinceEpoch;

        when(
          () => mockSharedPreferences.getInt('last_sync_time_$entityId'),
        ).thenReturn(veryOldTimestamp);

        // Act
        final lastSyncTime = await cacheManager.getLastSyncTime(entityId);
        final isSyncNeeded = await cacheManager.isSyncNeeded(entityId);
        final cacheStatus = await cacheManager.getCacheStatus(entityId);

        // Assert
        expect(lastSyncTime, equals(veryOldDateTime));
        expect(isSyncNeeded, isTrue);
        expect(cacheStatus, equals(CacheStatus.stale));
      });

      test('should handle future timestamps', () async {
        // Arrange
        const entityId = 'future_entity';
        final futureDateTime = DateTime.now().add(const Duration(days: 1));
        final futureTimestamp = futureDateTime.millisecondsSinceEpoch;

        when(
          () => mockSharedPreferences.getInt('last_sync_time_$entityId'),
        ).thenReturn(futureTimestamp);

        // Act
        final lastSyncTime = await cacheManager.getLastSyncTime(entityId);
        final isSyncNeeded = await cacheManager.isSyncNeeded(entityId);
        final cacheStatus = await cacheManager.getCacheStatus(entityId);

        // Assert
        expect(
          lastSyncTime!.millisecondsSinceEpoch,
          equals(futureDateTime.millisecondsSinceEpoch),
        );
        expect(isSyncNeeded, isFalse); // Future timestamp means fresh
        expect(cacheStatus, equals(CacheStatus.fresh));
      });

      test(
        'should use default freshness threshold when not specified',
        () async {
          // Arrange
          const entityId = 'default_threshold_entity';
          final dateTime = DateTime.now().subtract(const Duration(hours: 1));
          final timestamp = dateTime.millisecondsSinceEpoch;

          when(
            () => mockSharedPreferences.getInt('last_sync_time_$entityId'),
          ).thenReturn(timestamp);

          // Act
          final isSyncNeeded = await cacheManager.isSyncNeeded(entityId);
          final cacheStatus = await cacheManager.getCacheStatus(entityId);

          // Assert
          expect(isSyncNeeded, isFalse); // 1 hour < 2 hours default threshold
          expect(cacheStatus, equals(CacheStatus.fresh));
        },
      );
    });
  });
}
