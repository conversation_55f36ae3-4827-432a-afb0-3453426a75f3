import 'package:aquapartner/core/error/failures.dart';
import 'package:aquapartner/core/utils/logger.dart';
import 'package:aquapartner/domain/entities/customer.dart';
import 'package:aquapartner/domain/entities/sales_order/sales_order.dart';
import 'package:aquapartner/domain/entities/sales_order/sales_order_item.dart';
import 'package:aquapartner/domain/services/auth_service.dart';
import 'package:aquapartner/domain/usecases/sales_order/check_if_sync_needed_usecase.dart';
import 'package:aquapartner/domain/usecases/sales_order/get_sales_orders_usecase.dart';
import 'package:aquapartner/domain/usecases/sales_order/sync_sales_orders_usecase.dart';
import 'package:aquapartner/presentation/cubit/sales_order/sales_order_cubit.dart';
import 'package:aquapartner/presentation/cubit/sales_order/sales_order_state.dart';
import 'package:bloc_test/bloc_test.dart';
import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// Mock classes
class MockGetSalesOrdersUseCase extends Mock implements GetSalesOrdersUseCase {}

class MockSyncSalesOrdersUseCase extends Mock
    implements SyncSalesOrdersUseCase {}

class MockCheckIfSyncNeededUseCase extends Mock
    implements CheckIfSalesOrdersSyncNeededUseCase {}

class MockAuthService extends Mock implements AuthService {}

class MockAppLogger extends Mock implements AppLogger {}

// Test data
final testCustomer = Customer(
  customerId: 'test_customer_123',
  customerName: 'Test Customer',
  email: '<EMAIL>',
  mobileNumber: '+919999999999',
  companyName: 'Test Company',
  gstNo: 'TEST123456789',
  businessVertical: 'Aquaculture',
  customerCode: 'TC001',
  billingAddress: 'Test Address, Test City, Test State - 123456',
);

final testSalesOrderItems = [
  SalesOrderItem(
    itemId: 'item_001',
    salesOrderId: 'so_001',
    productId: 'prod_001',
    createdTime: DateTime.now(),
    entityDiscountPercent: '10%',
    hsnSac: 'HSN001',
    invoicedQuantityCancelled: '0',
    itemName: 'Test Product 1',
    itemPrice: 1000.0,
    lastModifiedTime: DateTime.now(),
    manuallyFulfilledQuantity: '0',
    nonPackageQuantity: '0',
    placeOfSupply: 'Test State',
    quantityDelivered: '2',
    quantityDropshipped: '0',
    quantityPacked: '2',
    salesVertices: 'vertex1',
    sno: '1',
    total: 2000.0,
  ),
  SalesOrderItem(
    itemId: 'item_002',
    salesOrderId: 'so_001',
    productId: 'prod_002',
    createdTime: DateTime.now(),
    entityDiscountPercent: '5%',
    hsnSac: 'HSN002',
    invoicedQuantityCancelled: '0',
    itemName: 'Test Product 2',
    itemPrice: 500.0,
    lastModifiedTime: DateTime.now(),
    manuallyFulfilledQuantity: '0',
    nonPackageQuantity: '0',
    placeOfSupply: 'Test State',
    quantityDelivered: '1',
    quantityDropshipped: '0',
    quantityPacked: '1',
    salesVertices: 'vertex2',
    sno: '2',
    total: 500.0,
  ),
];

final testSalesOrders = [
  SalesOrder(
    salesOrderId: 'so_001',
    addressId: 'addr_001',
    createdTime: DateTime.now(),
    customerId: 'test_customer_123',
    invoicedStatus: 'Pending',
    lastModifiedTime: DateTime.now(),
    orderSource: 'Mobile App',
    paidStatus: 'Unpaid',
    paymentTermsLabel: 'Net 30',
    saleOrderDate: DateTime.now().toIso8601String(),
    salesChannel: 'Direct',
    salesOrderNumber: 'SO001',
    subTotal: 2250.0,
    total: 2500.0,
    items: testSalesOrderItems,
  ),
  SalesOrder(
    salesOrderId: 'so_002',
    addressId: 'addr_002',
    createdTime: DateTime.now().subtract(Duration(days: 1)),
    customerId: 'test_customer_123',
    invoicedStatus: 'Completed',
    lastModifiedTime: DateTime.now().subtract(Duration(days: 1)),
    orderSource: 'Web Portal',
    paidStatus: 'Paid',
    paymentTermsLabel: 'Net 15',
    saleOrderDate: DateTime.now().subtract(Duration(days: 1)).toIso8601String(),
    salesChannel: 'Online',
    salesOrderNumber: 'SO002',
    subTotal: 1350.0,
    total: 1500.0,
    items: [testSalesOrderItems.first],
  ),
];

void main() {
  group('SalesOrderCubit Tests', () {
    late SalesOrderCubit salesOrderCubit;
    late MockGetSalesOrdersUseCase mockGetSalesOrdersUseCase;
    late MockSyncSalesOrdersUseCase mockSyncSalesOrdersUseCase;
    late MockCheckIfSyncNeededUseCase mockCheckIfSyncNeededUseCase;
    late MockAuthService mockAuthService;
    late MockAppLogger mockLogger;

    setUp(() {
      mockGetSalesOrdersUseCase = MockGetSalesOrdersUseCase();
      mockSyncSalesOrdersUseCase = MockSyncSalesOrdersUseCase();
      mockCheckIfSyncNeededUseCase = MockCheckIfSyncNeededUseCase();
      mockAuthService = MockAuthService();
      mockLogger = MockAppLogger();

      salesOrderCubit = SalesOrderCubit(
        getSalesOrdersUseCase: mockGetSalesOrdersUseCase,
        syncSalesOrdersUseCase: mockSyncSalesOrdersUseCase,
        checkIfSyncNeededUseCase: mockCheckIfSyncNeededUseCase,
        authService: mockAuthService,
        logger: mockLogger,
      );

      // Setup default mock behaviors
      when(
        () => mockAuthService.getCurrentCustomer(),
      ).thenAnswer((_) async => Right(testCustomer));
    });

    tearDown(() {
      salesOrderCubit.close();
    });

    group('Initial State', () {
      test('should have initial state as SalesOrderInitial', () {
        expect(salesOrderCubit.state, isA<SalesOrderInitial>());
      });
    });

    group('Load Sales Orders - Caching First Approach', () {
      blocTest<SalesOrderCubit, SalesOrderState>(
        'should emit [SalesOrderLoading, SalesOrdersLoaded] when loading from cache successfully',
        build: () {
          when(
            () => mockGetSalesOrdersUseCase(testCustomer.customerId),
          ).thenAnswer((_) async => Right(testSalesOrders));
          return salesOrderCubit;
        },
        act: (cubit) => cubit.loadSalesOrders(),
        expect:
            () => [
              isA<SalesOrderLoading>(),
              predicate<SalesOrdersLoaded>(
                (state) =>
                    state.salesOrders == testSalesOrders &&
                    state.isFromCache == true &&
                    state.isCacheStale == false &&
                    state.isBackgroundSyncInProgress == false,
              ),
            ],
        verify: (_) {
          verify(
            () => mockLogger.i(
              'Loading sales orders for customer: ${testCustomer.customerId}',
            ),
          ).called(1);
          verify(
            () => mockGetSalesOrdersUseCase(testCustomer.customerId),
          ).called(1);
        },
      );

      blocTest<SalesOrderCubit, SalesOrderState>(
        'should emit [SalesOrderLoading, SalesOrderError] when cache fails',
        build: () {
          when(
            () => mockGetSalesOrdersUseCase(testCustomer.customerId),
          ).thenAnswer((_) async => Left(CacheFailure()));
          // Mock sync to also fail to prevent additional states
          when(
            () => mockSyncSalesOrdersUseCase(testCustomer.customerId),
          ).thenAnswer((_) async => Left(NetworkFailure()));
          return salesOrderCubit;
        },
        act: (cubit) => cubit.loadSalesOrders(),
        expect:
            () => [
              isA<SalesOrderLoading>(),
              predicate<SalesOrderError>(
                (state) =>
                    state.message.contains('Failed to load sales orders'),
              ),
              isA<SalesOrderSyncing>(),
              predicate<SalesOrderSyncError>(
                (state) =>
                    state.message.contains('Failed to sync sales orders'),
              ),
            ],
        verify: (_) {
          verify(
            () => mockLogger.e(
              'Error loading sales orders from cache: ${CacheFailure()}',
            ),
          ).called(1);
        },
      );

      blocTest<SalesOrderCubit, SalesOrderState>(
        'should emit [SalesOrderLoading, SalesOrderError] when auth service fails',
        build: () {
          when(
            () => mockAuthService.getCurrentCustomer(),
          ).thenAnswer((_) async => Left(ServerFailure()));
          return salesOrderCubit;
        },
        act: (cubit) => cubit.loadSalesOrders(),
        expect: () => [isA<SalesOrderLoading>(), isA<SalesOrderError>()],
      );
    });

    group('Sync Sales Orders', () {
      blocTest<SalesOrderCubit, SalesOrderState>(
        'should emit [SalesOrderSyncing, SalesOrderSyncSuccess, SalesOrdersLoaded] when sync is successful',
        build: () {
          when(
            () => mockSyncSalesOrdersUseCase(testCustomer.customerId),
          ).thenAnswer((_) async => Right(testSalesOrders));
          return salesOrderCubit;
        },
        act: (cubit) => cubit.syncSalesOrders(),
        expect:
            () => [
              isA<SalesOrderSyncing>(),
              predicate<SalesOrderSyncSuccess>(
                (state) => state.salesOrders == testSalesOrders,
              ),
              predicate<SalesOrdersLoaded>(
                (state) =>
                    state.salesOrders == testSalesOrders &&
                    state.isFromCache == false &&
                    state.isCacheStale == false &&
                    state.isBackgroundSyncInProgress == false,
              ),
            ],
        verify: (_) {
          verify(
            () => mockLogger.i(
              'Syncing sales orders for customer: ${testCustomer.customerId}',
            ),
          ).called(1);
          verify(
            () => mockSyncSalesOrdersUseCase(testCustomer.customerId),
          ).called(1);
          verify(
            () => mockLogger.i(
              'Successfully synced ${testSalesOrders.length} sales orders with ${testSalesOrders.fold<int>(0, (sum, order) => sum + order.items.length)} items',
            ),
          ).called(1);
        },
      );

      blocTest<SalesOrderCubit, SalesOrderState>(
        'should emit [SalesOrderSyncing, SalesOrderSyncError] when sync fails',
        build: () {
          when(
            () => mockSyncSalesOrdersUseCase(testCustomer.customerId),
          ).thenAnswer((_) async => Left(NetworkFailure()));
          return salesOrderCubit;
        },
        act: (cubit) => cubit.syncSalesOrders(),
        expect:
            () => [
              isA<SalesOrderSyncing>(),
              predicate<SalesOrderSyncError>(
                (state) =>
                    state.message.contains('Failed to sync sales orders'),
              ),
            ],
        verify: (_) {
          verify(
            () =>
                mockLogger.e('Error syncing sales orders: ${NetworkFailure()}'),
          ).called(1);
        },
      );

      blocTest<SalesOrderCubit, SalesOrderState>(
        'should emit [SalesOrderSyncing, SalesOrderSyncError] when auth service fails during sync',
        build: () {
          when(
            () => mockAuthService.getCurrentCustomer(),
          ).thenAnswer((_) async => Left(ServerFailure()));
          return salesOrderCubit;
        },
        act: (cubit) => cubit.syncSalesOrders(),
        expect: () => [isA<SalesOrderSyncing>(), isA<SalesOrderSyncError>()],
      );
    });

    group('Background Sync', () {
      blocTest<SalesOrderCubit, SalesOrderState>(
        'should perform background sync when cache is stale',
        build: () {
          // First load from cache
          when(
            () => mockGetSalesOrdersUseCase(testCustomer.customerId),
          ).thenAnswer((_) async => Right(testSalesOrders));

          // Then background sync
          when(
            () => mockSyncSalesOrdersUseCase(testCustomer.customerId),
          ).thenAnswer((_) async => Right(testSalesOrders));

          return salesOrderCubit;
        },
        act: (cubit) async {
          await cubit.loadSalesOrders();
          // Simulate background sync trigger
          await cubit.syncSalesOrders();
        },
        expect:
            () => [
              isA<SalesOrderLoading>(),
              isA<SalesOrdersLoaded>(),
              isA<SalesOrderSyncing>(),
              isA<SalesOrderSyncSuccess>(),
              isA<SalesOrdersLoaded>(),
            ],
      );
    });

    group('Edge Cases', () {
      test('should handle null customer gracefully', () async {
        when(
          () => mockAuthService.getCurrentCustomer(),
        ).thenAnswer((_) async => const Right(null));

        salesOrderCubit.loadSalesOrders();
        await Future.delayed(Duration(milliseconds: 100));

        expect(salesOrderCubit.state, isA<SalesOrderError>());
      });

      blocTest<SalesOrderCubit, SalesOrderState>(
        'should handle empty sales orders list',
        build: () {
          when(
            () => mockGetSalesOrdersUseCase(testCustomer.customerId),
          ).thenAnswer((_) async => const Right([]));
          return salesOrderCubit;
        },
        act: (cubit) => cubit.loadSalesOrders(),
        expect:
            () => [
              isA<SalesOrderLoading>(),
              predicate<SalesOrdersLoaded>(
                (state) =>
                    state.salesOrders.isEmpty && state.isFromCache == true,
              ),
            ],
      );

      blocTest<SalesOrderCubit, SalesOrderState>(
        'should handle multiple sequential load calls',
        build: () {
          when(
            () => mockGetSalesOrdersUseCase(testCustomer.customerId),
          ).thenAnswer((_) async => Right(testSalesOrders));
          return salesOrderCubit;
        },
        act: (cubit) async {
          await cubit.loadSalesOrders();
          await cubit.loadSalesOrders();
        },
        expect:
            () => [
              isA<SalesOrderLoading>(),
              isA<SalesOrdersLoaded>(),
              isA<SalesOrderLoading>(),
              isA<SalesOrdersLoaded>(),
            ],
      );
    });

    group('State Transitions', () {
      test('should maintain state consistency during transitions', () {
        expect(salesOrderCubit.state, isA<SalesOrderInitial>());

        // Test that state changes are properly managed
        salesOrderCubit.emit(SalesOrderLoading());
        expect(salesOrderCubit.state, isA<SalesOrderLoading>());

        salesOrderCubit.emit(SalesOrdersLoaded(testSalesOrders));
        expect(salesOrderCubit.state, isA<SalesOrdersLoaded>());
      });

      test('should handle state equality correctly', () {
        final state1 = SalesOrdersLoaded(testSalesOrders);
        final state2 = SalesOrdersLoaded(testSalesOrders);
        final state3 = SalesOrdersLoaded([]);

        expect(state1, equals(state2));
        expect(state1, isNot(equals(state3)));
      });
    });
  });
}
