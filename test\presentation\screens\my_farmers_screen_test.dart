import 'package:aquapartner/domain/entities/farmer_visit/farmer.dart';
import 'package:aquapartner/presentation/cubit/my_farmers/farmer_visits_cubit.dart';
import 'package:aquapartner/presentation/screens/my_farmers_screen.dart';
import 'package:bloc_test/bloc_test.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import '../../helpers/test_helpers.dart';

// Mock classes
class MockFarmerVisitsCubit extends MockCubit<FarmerVisitsState>
    implements FarmerVisitsCubit {
  @override
  Future<void> getFarmerVisits() async {
    // Mock implementation
  }

  @override
  Future<void> syncFarmerVisits() async {
    // Mock implementation
  }
}

// Helper methods to create test data
List<Farmer> createTestFarmers() {
  return [
    const Farmer(
      id: 1,
      name: 'Test Farmer 1',
      mobileNumber: '+919999999999',
      visits: [],
    ),
    const Farmer(
      id: 2,
      name: 'Test Farmer 2',
      mobileNumber: '+919876543211',
      visits: [],
    ),
  ];
}

void main() {
  group('MyFarmersScreen Tests', () {
    late MockFarmerVisitsCubit mockFarmerVisitsCubit;

    setUp(() {
      mockFarmerVisitsCubit = MockFarmerVisitsCubit();

      // Set up GetIt services for testing
      TestHelpers.setupGetItServices();

      // Set up default states
      when(
        () => mockFarmerVisitsCubit.state,
      ).thenReturn(FarmerVisitsLoaded(createTestFarmers()));
    });

    tearDown(() {
      // Clean up GetIt services after testing
      TestHelpers.cleanupGetItServices();
    });

    Widget createMyFarmersScreen() {
      return TestHelpers.createTestApp(
        home: BlocProvider<FarmerVisitsCubit>.value(
          value: mockFarmerVisitsCubit,
          child: MyFarmersScreen(),
        ),
      );
    }

    group('Widget Rendering', () {
      testWidgets('should render my farmers screen with main components', (
        tester,
      ) async {
        await TestHelpers.pumpAndSettle(tester, createMyFarmersScreen());

        // Verify main structure
        expect(find.byType(Scaffold), findsOneWidget);
        expect(find.byType(MyFarmersScreen), findsOneWidget);
      });

      testWidgets('should display farmers list when loaded', (tester) async {
        await TestHelpers.pumpAndSettle(tester, createMyFarmersScreen());

        // Verify farmers content is displayed
        expect(find.byType(RefreshIndicator), findsOneWidget);
      });
    });
  });
}
