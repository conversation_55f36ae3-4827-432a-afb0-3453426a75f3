import 'sales_order_item.dart';

class SalesOrder {
  final int id;
  final String salesOrderId;
  final String addressId;
  final DateTime createdTime;
  final String customerId;
  final String invoicedStatus;
  final DateTime lastModifiedTime;
  final String orderSource;
  final String paidStatus;
  final String paymentTermsLabel;
  final String saleOrderDate;
  final String salesChannel;
  final String salesOrderNumber;
  final double subTotal;
  final double total;
  final bool isSynced;
  final DateTime? lastSyncedAt;
  final List<SalesOrderItem> items;

  SalesOrder({
    this.id = 0,
    required this.salesOrderId,
    required this.addressId,
    required this.createdTime,
    required this.customerId,
    required this.invoicedStatus,
    required this.lastModifiedTime,
    required this.orderSource,
    required this.paidStatus,
    required this.paymentTermsLabel,
    required this.saleOrderDate,
    required this.salesChannel,
    required this.salesOrderNumber,
    required this.subTotal,
    required this.total,
    this.isSynced = false,
    this.lastSyncedAt,
    this.items = const [],
  });

  // Create a copy with updated fields
  SalesOrder copyWith({
    int? id,
    String? salesOrderId,
    String? addressId,
    DateTime? createdTime,
    String? customerId,
    String? invoicedStatus,
    DateTime? lastModifiedTime,
    String? orderSource,
    String? paidStatus,
    String? paymentTermsLabel,
    String? saleOrderDate,
    String? salesChannel,
    String? salesOrderNumber,
    double? subTotal,
    double? total,
    bool? isSynced,
    DateTime? lastSyncedAt,
    List<SalesOrderItem>? items,
  }) {
    return SalesOrder(
      id: id ?? this.id,
      salesOrderId: salesOrderId ?? this.salesOrderId,
      addressId: addressId ?? this.addressId,
      createdTime: createdTime ?? this.createdTime,
      customerId: customerId ?? this.customerId,
      invoicedStatus: invoicedStatus ?? this.invoicedStatus,
      lastModifiedTime: lastModifiedTime ?? this.lastModifiedTime,
      orderSource: orderSource ?? this.orderSource,
      paidStatus: paidStatus ?? this.paidStatus,
      paymentTermsLabel: paymentTermsLabel ?? this.paymentTermsLabel,
      saleOrderDate: saleOrderDate ?? this.saleOrderDate,
      salesChannel: salesChannel ?? this.salesChannel,
      salesOrderNumber: salesOrderNumber ?? this.salesOrderNumber,
      subTotal: subTotal ?? this.subTotal,
      total: total ?? this.total,
      isSynced: isSynced ?? this.isSynced,
      lastSyncedAt: lastSyncedAt ?? this.lastSyncedAt,
      items: items ?? this.items,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SalesOrder &&
          runtimeType == other.runtimeType &&
          salesOrderId == other.salesOrderId;

  @override
  int get hashCode => salesOrderId.hashCode;

  @override
  String toString() {
    return 'SalesOrder{id: $id, salesOrderId: $salesOrderId, salesOrderNumber: $salesOrderNumber, total: $total, items: ${items.length}}';
  }
}
