import 'package:dartz/dartz.dart';
import '../../core/error/exceptions.dart';
import '../../core/error/failures.dart';
import '../../core/network/network_info.dart';
import '../../core/utils/logger.dart';
import '../../domain/entities/interested_product.dart';
import '../../domain/repositories/interested_product_repository.dart';
import '../datasources/local/interested_product_local_datasource.dart';
import '../datasources/remote/interested_product_remote_datasource.dart';
import '../models/interested_product_model.dart';

class InterestedProductRepositoryImpl implements InterestedProductRepository {
  final InterestedProductRemoteDataSource remoteDataSource;
  final InterestedProductLocalDataSource localDataSource;
  final NetworkInfo networkInfo;
  final AppLogger logger;

  InterestedProductRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
    required this.networkInfo,
    required this.logger,
  });

  @override
  Future<Either<Failure, List<InterestedProduct>>>
  getInterestedProducts() async {
    logger.i("InterestedProductRepositoryImpl: Getting interested products");

    try {
      final localInterestedProducts =
          await localDataSource.getInterestedProducts();
      return Right(localInterestedProducts);
    } catch (e) {
      logger.e("Error getting interested products", e);
      return Left(CacheFailure());
    }
  }

  @override
  Future<Either<Failure, InterestedProduct>> addInterestedProduct(
    InterestedProduct interestedProduct,
  ) async {
    logger.i("InterestedProductRepositoryImpl: Adding interested product");

    try {
      // Convert to model
      final interestedProductModel = InterestedProductModel(
        customerId: interestedProduct.customerId,
        mobile: interestedProduct.mobile,
        productName: interestedProduct.productName,
        datetime: interestedProduct.datetime,
        source: interestedProduct.source,
        isSynced: false, // Always start as unsynced
      );

      // Save to local storage
      final savedProduct = await localDataSource.addInterestedProduct(
        interestedProductModel,
      );

      // Try to sync immediately if online
      if (await networkInfo.isConnected) {
        try {
          logger.i("Online, attempting to sync immediately");
          final syncedProduct = await remoteDataSource.addInterestedProduct(
            savedProduct,
          );

          // Mark as synced in local storage
          await localDataSource.markAsSynced(syncedProduct);

          return Right(syncedProduct);
        } on ServerException catch (e) {
          logger.w("Failed to sync immediately: ${e.toString()}");
          // Return the locally saved product even if sync fails
          return Right(savedProduct);
        }
      } else {
        logger.i("Offline, product saved locally and will be synced later");
        return Right(savedProduct);
      }
    } on CacheException catch (e) {
      logger.e("Cache exception adding interested product", e);
      return Left(CacheFailure());
    } catch (e) {
      logger.e("Unexpected error adding interested product", e);
      return Left(CacheFailure());
    }
  }

  @override
  Future<Either<Failure, int>> syncInterestedProducts() async {
    logger.i("InterestedProductRepositoryImpl: Syncing interested products");

    if (!(await networkInfo.isConnected)) {
      logger.w("Cannot sync interested products: No internet connection");
      return Left(ServerFailure());
    }

    try {
      // Get all unsynced interested products
      final unsyncedProducts =
          await localDataSource.getUnsyncedInterestedProducts();

      if (unsyncedProducts.isEmpty) {
        logger.i("No unsynced interested products to sync");
        return const Right(0);
      }

      // Sync with server
      final syncedProducts = await remoteDataSource.addInterestedProducts(
        unsyncedProducts,
      );

      // Mark all as synced in local storage
      for (var product in syncedProducts) {
        await localDataSource.markAsSynced(product);
      }

      logger.i(
        "Successfully synced ${syncedProducts.length} interested products",
      );
      return Right(syncedProducts.length);
    } on ServerException catch (e) {
      logger.w("Server Exception: ${e.toString()}");
      return Left(ServerFailure());
    } on CacheException catch (e) {
      logger.e("Cache exception syncing interested products", e);
      return Left(CacheFailure());
    } catch (e) {
      logger.e("Unexpected error syncing interested products", e);
      return Left(ServerFailure());
    }
  }

  @override
  Future<Either<Failure, int>> getUnsyncedCount() async {
    logger.i("InterestedProductRepositoryImpl: Getting unsynced count");

    try {
      final count = await localDataSource.getUnsyncedCount();
      return Right(count);
    } catch (e) {
      logger.e("Error getting unsynced count", e);
      return Left(CacheFailure());
    }
  }
}
