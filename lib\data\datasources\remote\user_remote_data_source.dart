import 'dart:async';
import 'package:mongo_dart/mongo_dart.dart';

import '../../../core/error/exceptions.dart';
import '../../../core/network/mongodb_connection_manager.dart';
import '../../../core/utils/logger.dart';
import '../../models/user_model.dart';

abstract class UserRemoteDataSource {
  Future<String> createUser(UserModel user);
  Future<void> updateUser(UserModel user);
  Future<UserModel?> getUserByPhone(String phoneNumber);
}

class UserRemoteDataSourceImpl implements UserRemoteDataSource {
  final MongoDbConnectionManager connectionManager;
  final AppLogger logger;

  UserRemoteDataSourceImpl({
    required this.connectionManager,
    required this.logger,
  });

  @override
  Future<String> createUser(UserModel user) async {
    try {
      logger.i("Creating user in MongoDB: ${user.phoneNumber}");

      final mongoService = await connectionManager.getConnection();

      // Get the collection with automatic reconnection
      final collection = await mongoService.userCollection;

      final userData = user.toJson();
      // Remove _id if it exists but is null
      userData.remove('_id');

      // Add to MongoDB with timeout
      final result = await collection
          .insertOne(userData)
          .timeout(
            Duration(seconds: 15),
            onTimeout: () {
              logger.e("MongoDB operation timed out while creating user");
              throw TimeoutException("MongoDB operation timed out");
            },
          );

      if (result.isSuccess) {
        final insertedId = result.id;
        String idString = insertedId.toString();

        // If it's an ObjectId, get the oid (hexadecimal string)
        if (insertedId is ObjectId) {
          idString = insertedId.oid;
        }

        logger.i("User created in MongoDB with ID: $idString");
        return idString;
      } else {
        logger.e("MongoDB insert failed: ${result.writeError?.errmsg}");
        throw ServerException();
      }
    } on TimeoutException {
      logger.e("MongoDB operation timed out while creating user");
      throw ServerException();
    } catch (e) {
      logger.e("Error creating user in MongoDB: $e", e);
      throw ServerException();
    }
  }

  @override
  Future<void> updateUser(UserModel user) async {
    try {
      if (user.mongoId == null) {
        logger.e("Cannot update user without MongoDB ID");
        throw ServerException();
      }

      logger.i("Updating user in MongoDB: ${user.mongoId}");

      final mongoService = await connectionManager.getConnection();

      // Get the collection with automatic reconnection
      final collection = await mongoService.userCollection;

      final userData = user.toJson();

      // Try to convert the string ID to ObjectId if it's in the correct format
      dynamic queryId;
      try {
        // Check if the ID is a valid 24-character hex string (ObjectId format)
        if (user.mongoId!.length == 24 &&
            RegExp(r'^[0-9a-fA-F]{24}$').hasMatch(user.mongoId!)) {
          queryId = ObjectId.parse(user.mongoId!);
          logger.i("Converted string ID to ObjectId: ${user.mongoId}");
        } else {
          queryId = user.mongoId;
        }
      } catch (e) {
        // If conversion fails, use the string ID as is
        logger.w(
          "Could not convert ID to ObjectId, using string ID: ${user.mongoId}",
        );
        queryId = user.mongoId;
      }

      // Add to MongoDB with timeout
      final result = await collection
          .updateOne(where.eq('_id', queryId), {'\$set': userData})
          .timeout(
            Duration(seconds: 15),
            onTimeout: () {
              logger.e("MongoDB operation timed out while updating user");
              throw TimeoutException("MongoDB operation timed out");
            },
          );

      if (result.isSuccess) {
        if (result.nModified > 0) {
          logger.i("User updated in MongoDB");
        } else {
          logger.w(
            "User update in MongoDB had no effect (no documents matched)",
          );
        }
      } else {
        logger.e("MongoDB update failed: ${result.writeError?.errmsg}");
        throw ServerException();
      }
    } on TimeoutException catch (e) {
      logger.e("MongoDB operation timed out while updating user", e);
      throw ServerException();
    } catch (e) {
      logger.e("Error updating user in MongoDB: $e", e);
      throw ServerException();
    }
  }

  @override
  Future<UserModel?> getUserByPhone(String phoneNumber) async {
    try {
      logger.i("Getting user by phone number from MongoDB: $phoneNumber");

      final mongoService = await connectionManager.getConnection();

      // Get the collection with automatic reconnection
      final collection = await mongoService.userCollection;

      // Find user by phone number with timeout
      final result = await collection
          .findOne(where.eq('phoneNumber', phoneNumber))
          .timeout(
            Duration(seconds: 15),
            onTimeout: () {
              logger.e(
                "MongoDB operation timed out while getting user by phone",
              );
              throw TimeoutException("MongoDB operation timed out");
            },
          );

      if (result != null) {
        logger.i("User found in MongoDB with phone: $phoneNumber");
        try {
          return UserModel.fromJson(result);
        } catch (e) {
          logger.e("Error parsing user document: $e", e);
          logger.e("Problematic document: $result");
          throw ServerException();
        }
      } else {
        logger.i("User not found in MongoDB with phone: $phoneNumber");
        return null;
      }
    } on TimeoutException catch (e) {
      logger.e("MongoDB operation timed out while getting user by phone", e);
      throw ServerException();
    } catch (e) {
      logger.e("Error getting user by phone from MongoDB: $e", e);
      throw ServerException();
    }
  }
}
