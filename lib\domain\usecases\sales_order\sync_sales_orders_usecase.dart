import 'package:dartz/dartz.dart';

import '../../../core/error/failures.dart';
import '../../entities/sales_order/sales_order.dart';
import '../../repositories/sales_order_repository.dart';
import '../../../core/utils/logger.dart';
import '../../../core/network/network_info.dart';

class SyncSalesOrdersUseCase {
  final SalesOrderRepository repository;
  final NetworkInfo networkInfo;
  final AppLogger logger;

  SyncSalesOrdersUseCase({
    required this.repository,
    required this.networkInfo,
    required this.logger,
  });

  Future<Either<Failure, List<SalesOrder>>> call(String customerId) async {
    logger.i('SyncSalesOrdersUseCase called with customerId: $customerId');

    // Check if device is online
    if (await networkInfo.isConnected) {
      return await repository.syncSalesOrders(customerId);
    } else {
      logger.w('Device is offline, cannot sync sales orders');
      return Left(NetworkFailure());
    }
  }
}
