// lib/domain/entities/customer_payment.dart
class CustomerPayment {
  final String id;
  final String customerId;
  final String customerName;
  final String categoryType;
  final String amount;
  final DateTime paymentsDate;
  final String paymentId;
  final String paymentsMode;
  final String paymentsNumber;

  CustomerPayment({
    required this.id,
    required this.customerId,
    required this.customerName,
    required this.categoryType,
    required this.amount,
    required this.paymentsDate,
    required this.paymentId,
    required this.paymentsMode,
    required this.paymentsNumber,
  });
}
