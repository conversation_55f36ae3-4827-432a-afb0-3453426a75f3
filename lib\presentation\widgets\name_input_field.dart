import 'package:flutter/material.dart';

class NameInputField extends StatelessWidget {
  final TextEditingController controller;
  final String? error;

  const NameInputField({
    Key? key,
    required this.controller,
    this.error,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextField(
          controller: controller,
          decoration: InputDecoration(
            labelText: 'Full Name',
            prefixIcon: const Icon(Icons.person),
            border: const OutlineInputBorder(),
            errorText: error,
          ),
          keyboardType: TextInputType.name,
          textCapitalization: TextCapitalization.words,
        ),
        if (error != null)
          Padding(
            padding: const EdgeInsets.only(left: 12.0, top: 8.0),
            child: Text(
              error!,
              style: const TextStyle(color: Colors.red, fontSize: 12.0),
            ),
          ),
      ],
    );
  }
}