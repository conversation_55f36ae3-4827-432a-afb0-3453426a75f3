import 'package:dartz/dartz.dart';
import '../../../core/error/failures.dart';
import '../../../core/utils/logger.dart';
import '../../repositories/account_statement_repository.dart';

class CheckIfAccountStatementSyncNeededUseCase {
  final AccountStatementRepository repository;
  final AppLogger logger;

  CheckIfAccountStatementSyncNeededUseCase({
    required this.repository,
    required this.logger,
  });

  Future<Either<Failure, bool>> call(String customerId) async {
    logger.i('CheckIfAccountStatementSyncNeededUseCase called for customer: $customerId');
    return repository.checkIfSyncNeeded(customerId);
  }
}