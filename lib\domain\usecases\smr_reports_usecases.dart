import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../entities/smr_report.dart';
import '../repositories/smr_report_repository.dart';

class GetSMRReportsUseCase {
  final SMRReportRepository repository;

  GetSMRReportsUseCase(this.repository);

  Future<Either<Failure, List<SMRReport>>> call(String customerId) {
    return repository.getSMRReports(customerId);
  }
}

class SyncSMRReportsUseCase {
  final SMRReportRepository repository;

  SyncSMRReportsUseCase(this.repository);

  Future<Either<Failure, bool>> call(String customerId) {
    return repository.syncSMRReports(customerId);
  }
}
