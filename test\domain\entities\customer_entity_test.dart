import 'package:flutter_test/flutter_test.dart';
import 'package:aquapartner/domain/entities/customer.dart';

void main() {
  group('Customer Entity', () {
    group('Constructor and Properties', () {
      test('should create customer with all required properties', () {
        // arrange & act
        final customer = Customer(
          customerId: 'CUST001',
          customerName: '<PERSON>',
          email: '<EMAIL>',
          mobileNumber: '+919999999999',
          companyName: 'Doe Enterprises',
          gstNo: 'GST*********',
          businessVertical: 'Aquaculture',
          customerCode: 'JD001',
          billingAddress: '123 Main Street, City, State, 12345',
        );

        // assert
        expect(customer.id, isNull);
        expect(customer.customerId, 'CUST001');
        expect(customer.customerName, 'John Doe');
        expect(customer.email, '<EMAIL>');
        expect(customer.mobileNumber, '+919999999999');
        expect(customer.companyName, 'Doe Enterprises');
        expect(customer.gstNo, 'GST*********');
        expect(customer.businessVertical, 'Aquaculture');
        expect(customer.customerCode, 'JD001');
        expect(customer.billingAddress, '123 Main Street, City, State, 12345');
      });

      test('should create customer with optional id', () {
        // arrange & act
        final customer = Customer(
          id: 'internal_id_123',
          customerId: 'CUST001',
          customerName: 'John Doe',
          email: '<EMAIL>',
          mobileNumber: '+919999999999',
          companyName: 'Doe Enterprises',
          gstNo: 'GST*********',
          businessVertical: 'Aquaculture',
          customerCode: 'JD001',
          billingAddress: '123 Main Street, City, State, 12345',
        );

        // assert
        expect(customer.id, 'internal_id_123');
        expect(customer.customerId, 'CUST001');
        expect(customer.customerName, 'John Doe');
      });

      test('should create customer with minimal required data', () {
        // arrange & act
        final customer = Customer(
          customerId: 'CUST001',
          customerName: 'John Doe',
          email: '<EMAIL>',
          mobileNumber: '+919999999999',
          companyName: 'Doe Enterprises',
          gstNo: 'GST*********',
          businessVertical: 'Aquaculture',
          customerCode: 'JD001',
          billingAddress: '123 Main Street',
        );

        // assert
        expect(customer.customerId, 'CUST001');
        expect(customer.customerName, 'John Doe');
        expect(customer.email, '<EMAIL>');
        expect(customer.mobileNumber, '+919999999999');
        expect(customer.companyName, 'Doe Enterprises');
        expect(customer.gstNo, 'GST*********');
        expect(customer.businessVertical, 'Aquaculture');
        expect(customer.customerCode, 'JD001');
        expect(customer.billingAddress, '123 Main Street');
      });
    });

    group('Edge Cases', () {
      test('should handle empty strings for all properties', () {
        // arrange & act
        final customer = Customer(
          id: '',
          customerId: '',
          customerName: '',
          email: '',
          mobileNumber: '',
          companyName: '',
          gstNo: '',
          businessVertical: '',
          customerCode: '',
          billingAddress: '',
        );

        // assert
        expect(customer.id, '');
        expect(customer.customerId, '');
        expect(customer.customerName, '');
        expect(customer.email, '');
        expect(customer.mobileNumber, '');
        expect(customer.companyName, '');
        expect(customer.gstNo, '');
        expect(customer.businessVertical, '');
        expect(customer.customerCode, '');
        expect(customer.billingAddress, '');
      });

      test('should handle special characters in properties', () {
        // arrange & act
        final customer = Customer(
          customerId: 'CUST@#\$%001',
          customerName: 'John O\'Doe & Associates',
          email: '<EMAIL>',
          mobileNumber: '+91-9876-543-210',
          companyName: 'Doe & Sons Enterprises (Pvt.) Ltd.',
          gstNo: 'GST@*********',
          businessVertical: 'Aquaculture & Fisheries',
          customerCode: 'JD@001',
          billingAddress: '123 Main Street, Apt #456, City, State - 12345',
        );

        // assert
        expect(customer.customerId, 'CUST@#\$%001');
        expect(customer.customerName, 'John O\'Doe & Associates');
        expect(customer.email, '<EMAIL>');
        expect(customer.mobileNumber, '+91-9876-543-210');
        expect(customer.companyName, 'Doe & Sons Enterprises (Pvt.) Ltd.');
        expect(customer.gstNo, 'GST@*********');
        expect(customer.businessVertical, 'Aquaculture & Fisheries');
        expect(customer.customerCode, 'JD@001');
        expect(
          customer.billingAddress,
          '123 Main Street, Apt #456, City, State - 12345',
        );
      });

      test('should handle very long strings', () {
        // arrange
        final longString = 'A' * 1000;

        // act
        final customer = Customer(
          customerId: longString,
          customerName: longString,
          email: longString,
          mobileNumber: longString,
          companyName: longString,
          gstNo: longString,
          businessVertical: longString,
          customerCode: longString,
          billingAddress: longString,
        );

        // assert
        expect(customer.customerId.length, 1000);
        expect(customer.customerName.length, 1000);
        expect(customer.email.length, 1000);
        expect(customer.mobileNumber.length, 1000);
        expect(customer.companyName.length, 1000);
        expect(customer.gstNo.length, 1000);
        expect(customer.businessVertical.length, 1000);
        expect(customer.customerCode.length, 1000);
        expect(customer.billingAddress.length, 1000);
      });

      test('should handle unicode characters', () {
        // arrange & act
        final customer = Customer(
          customerId: 'CUST001',
          customerName: 'जॉन डो',
          email: '<EMAIL>',
          mobileNumber: '+919999999999',
          companyName: 'डो एंटरप्राइजेज',
          gstNo: 'GST*********',
          businessVertical: 'मत्स्य पालन',
          customerCode: 'JD001',
          billingAddress: '१२३ मुख्य सड़क, शहर, राज्य, १२३४५',
        );

        // assert
        expect(customer.customerName, 'जॉन डो');
        expect(customer.companyName, 'डो एंटरप्राइजेज');
        expect(customer.businessVertical, 'मत्स्य पालन');
        expect(customer.billingAddress, '१२३ मुख्य सड़क, शहर, राज्य, १२३४५');
      });

      test('should handle null id correctly', () {
        // arrange & act
        final customer = Customer(
          id: null,
          customerId: 'CUST001',
          customerName: 'John Doe',
          email: '<EMAIL>',
          mobileNumber: '+919999999999',
          companyName: 'Doe Enterprises',
          gstNo: 'GST*********',
          businessVertical: 'Aquaculture',
          customerCode: 'JD001',
          billingAddress: '123 Main Street',
        );

        // assert
        expect(customer.id, isNull);
      });
    });

    group('Business Logic Validation', () {
      test('should represent a valid business customer', () {
        // arrange & act
        final businessCustomer = Customer(
          customerId: 'CUST001',
          customerName: 'Aqua Farms Ltd.',
          email: '<EMAIL>',
          mobileNumber: '+919999999999',
          companyName: 'Aqua Farms Private Limited',
          gstNo: 'GST27ABCDE1234F1Z5',
          businessVertical: 'Aquaculture',
          customerCode: 'AF001',
          billingAddress:
              'Plot 123, Industrial Area, Hyderabad, Telangana, 500032',
        );

        // assert
        expect(businessCustomer.customerId, isNotEmpty);
        expect(businessCustomer.customerName, isNotEmpty);
        expect(businessCustomer.email, contains('@'));
        expect(businessCustomer.mobileNumber, startsWith('+91'));
        expect(businessCustomer.gstNo, startsWith('GST'));
        expect(businessCustomer.businessVertical, 'Aquaculture');
      });

      test('should represent an individual customer', () {
        // arrange & act
        final individualCustomer = Customer(
          customerId: 'CUST002',
          customerName: 'Rajesh Kumar',
          email: '<EMAIL>',
          mobileNumber: '+919876543211',
          companyName: 'Rajesh Kumar (Individual)',
          gstNo: 'N/A',
          businessVertical: 'Small Scale Farming',
          customerCode: 'RK002',
          billingAddress: 'Village Pond, District Nalgonda, Telangana, 508001',
        );

        // assert
        expect(individualCustomer.customerName, 'Rajesh Kumar');
        expect(individualCustomer.companyName, contains('Individual'));
        expect(individualCustomer.gstNo, 'N/A');
        expect(individualCustomer.businessVertical, 'Small Scale Farming');
      });

      test('should handle different business verticals', () {
        // arrange
        final businessVerticals = [
          'Aquaculture',
          'Fisheries',
          'Shrimp Farming',
          'Fish Feed Manufacturing',
          'Aquaculture Equipment',
          'Fish Processing',
          'Hatchery Operations',
          'Pond Management',
        ];

        // act & assert
        for (final vertical in businessVerticals) {
          final customer = Customer(
            customerId: 'CUST001',
            customerName: 'Test Customer',
            email: '<EMAIL>',
            mobileNumber: '+919999999999',
            companyName: 'Test Company',
            gstNo: 'GST*********',
            businessVertical: vertical,
            customerCode: 'TC001',
            billingAddress: 'Test Address',
          );

          expect(customer.businessVertical, vertical);
        }
      });

      test('should handle different GST number formats', () {
        // arrange
        final gstNumbers = [
          'GST27ABCDE1234F1Z5',
          '27ABCDE1234F1Z5',
          'GST*********',
          'N/A',
          'Not Applicable',
          'PENDING',
        ];

        // act & assert
        for (final gstNo in gstNumbers) {
          final customer = Customer(
            customerId: 'CUST001',
            customerName: 'Test Customer',
            email: '<EMAIL>',
            mobileNumber: '+919999999999',
            companyName: 'Test Company',
            gstNo: gstNo,
            businessVertical: 'Aquaculture',
            customerCode: 'TC001',
            billingAddress: 'Test Address',
          );

          expect(customer.gstNo, gstNo);
        }
      });

      test('should handle different mobile number formats', () {
        // arrange
        final mobileNumbers = [
          '+919999999999',
          '919999999999',
          '9999999999',
          '+91-9876-543-210',
          '+91 9876 543 210',
          '(+91) 9999999999',
        ];

        // act & assert
        for (final mobileNumber in mobileNumbers) {
          final customer = Customer(
            customerId: 'CUST001',
            customerName: 'Test Customer',
            email: '<EMAIL>',
            mobileNumber: mobileNumber,
            companyName: 'Test Company',
            gstNo: 'GST*********',
            businessVertical: 'Aquaculture',
            customerCode: 'TC001',
            billingAddress: 'Test Address',
          );

          expect(customer.mobileNumber, mobileNumber);
        }
      });

      test('should handle different email formats', () {
        // arrange
        final emails = [
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
        ];

        // act & assert
        for (final email in emails) {
          final customer = Customer(
            customerId: 'CUST001',
            customerName: 'Test Customer',
            email: email,
            mobileNumber: '+919999999999',
            companyName: 'Test Company',
            gstNo: 'GST*********',
            businessVertical: 'Aquaculture',
            customerCode: 'TC001',
            billingAddress: 'Test Address',
          );

          expect(customer.email, email);
        }
      });
    });

    group('Data Integrity', () {
      test('should maintain all properties after creation', () {
        // arrange
        final originalData = {
          'id': 'internal_123',
          'customerId': 'CUST001',
          'customerName': 'John Doe',
          'email': '<EMAIL>',
          'mobileNumber': '+919999999999',
          'companyName': 'Doe Enterprises',
          'gstNo': 'GST*********',
          'businessVertical': 'Aquaculture',
          'customerCode': 'JD001',
          'billingAddress': '123 Main Street',
        };

        // act
        final customer = Customer(
          id: originalData['id'],
          customerId: originalData['customerId']!,
          customerName: originalData['customerName']!,
          email: originalData['email']!,
          mobileNumber: originalData['mobileNumber']!,
          companyName: originalData['companyName']!,
          gstNo: originalData['gstNo']!,
          businessVertical: originalData['businessVertical']!,
          customerCode: originalData['customerCode']!,
          billingAddress: originalData['billingAddress']!,
        );

        // assert
        expect(customer.id, originalData['id']);
        expect(customer.customerId, originalData['customerId']);
        expect(customer.customerName, originalData['customerName']);
        expect(customer.email, originalData['email']);
        expect(customer.mobileNumber, originalData['mobileNumber']);
        expect(customer.companyName, originalData['companyName']);
        expect(customer.gstNo, originalData['gstNo']);
        expect(customer.businessVertical, originalData['businessVertical']);
        expect(customer.customerCode, originalData['customerCode']);
        expect(customer.billingAddress, originalData['billingAddress']);
      });
    });
  });
}
