import 'package:equatable/equatable.dart';
import '../../../domain/entities/product_catalogue.dart';

enum ProductCatalogueStatus { initial, loading, success, error, syncing }

class ProductCatalogueState extends Equatable {
  final ProductCatalogueStatus status;
  final List<ProductCatalogue> productCatalogues;
  final String? errorMessage;

  // Cache maps with const keys for better performance
  static const String _keyDrGrow = 'drGrow';
  static const String _keyOthers = 'others';
  static const String _keyDrGrowCategories = 'drGrowCategories';
  static const String _keyOtherCategories = 'otherCategories';

  // Lazy-initialized cache maps
  final Map<String, List<ProductCatalogue>> _filteredCache = {};
  final Map<String, List<String>> _categoryFilterCache = {};

  ProductCatalogueState({
    this.status = ProductCatalogueStatus.initial,
    this.productCatalogues = const [],
    this.errorMessage,
  });

  ProductCatalogueState copyWith({
    ProductCatalogueStatus? status,
    List<ProductCatalogue>? productCatalogues,
    String? errorMessage,
  }) {
    // Clear caches when product catalogues change
    final newState = ProductCatalogueState(
      status: status ?? this.status,
      productCatalogues: productCatalogues ?? this.productCatalogues,
      errorMessage: errorMessage,
    );

    // Only copy caches if product catalogues haven't changed
    if (productCatalogues == null) {
      newState._filteredCache.addAll(_filteredCache);
      newState._categoryFilterCache.addAll(_categoryFilterCache);
    }

    return newState;
  }

  // Status getters
  bool get isLoading => status == ProductCatalogueStatus.loading;
  bool get isSyncing => status == ProductCatalogueStatus.syncing;
  bool get isSuccess => status == ProductCatalogueStatus.success;
  bool get isError => status == ProductCatalogueStatus.error;

  // Default empty Dr.Grow catalogue for fallback
  ProductCatalogue get _emptyDrGrowCatalogue => ProductCatalogue(
    id: '',
    name: 'Dr.Grow',
    image: '',
    sortOrder: "0",
    status: '',
    products: const [],
  );

  // Cached getters for product catalogues
  List<ProductCatalogue> get drGrowProductCatalogue {
    return _getCachedValue<List<ProductCatalogue>>(
      _filteredCache,
      _keyDrGrow,
      () => [
        productCatalogues.firstWhere(
          (catalogue) => catalogue.name == 'Dr.Grow',
          orElse: () => _emptyDrGrowCatalogue,
        ),
      ],
    );
  }

  List<ProductCatalogue> get otherProductsCatelogue {
    return _getCachedValue<List<ProductCatalogue>>(
      _filteredCache,
      _keyOthers,
      () =>
          productCatalogues
              .where((catalogue) => catalogue.name != 'Dr.Grow')
              .toList(),
    );
  }

  // Cached getters for category filters
  List<String> get drGrowCategoryFilter {
    return _getCachedValue<List<String>>(
      _categoryFilterCache,
      _keyDrGrowCategories,
      () {
        final categories =
            drGrowProductCatalogue
                .expand((category) => category.products)
                .map((product) => product.subCategory)
                .where((subCategory) => subCategory.isNotEmpty)
                .toSet()
                .toList();
        categories.sort();
        return categories;
      },
    );
  }

  List<String> get otherCategoryFilter {
    return _getCachedValue<List<String>>(
      _categoryFilterCache,
      _keyOtherCategories,
      () {
        final categories =
            otherProductsCatelogue
                .expand((category) => category.products)
                .map((product) => product.subCategory)
                .where((subCategory) => subCategory.isNotEmpty)
                .toSet()
                .toList();
        categories.sort();
        return categories;
      },
    );
  }

  // Generic cache helper method
  T _getCachedValue<T>(Map<String, T> cache, String key, T Function() compute) {
    if (cache.containsKey(key)) {
      return cache[key]!;
    }

    final result = compute();
    cache[key] = result;
    return result;
  }

  // Filtered products with caching
  List<ProductCatalogue> getFilteredProducts(String filter, bool isDrGrow) {
    final cacheKey = '${isDrGrow ? "DrGrow" : "Other"}_$filter';

    return _getCachedValue<List<ProductCatalogue>>(
      _filteredCache,
      cacheKey,
      () {
        final catalogues =
            isDrGrow ? drGrowProductCatalogue : otherProductsCatelogue;

        if (filter == 'All') {
          return catalogues;
        }

        return catalogues
            .map((catalogue) {
              final filteredProducts =
                  catalogue.products
                      .where((product) => product.subCategory == filter)
                      .toList();

              return filteredProducts.isEmpty
                  ? null
                  : ProductCatalogue(
                    id: catalogue.id,
                    name: catalogue.name,
                    image: catalogue.image,
                    sortOrder: catalogue.sortOrder,
                    status: catalogue.status,
                    products: filteredProducts,
                  );
            })
            .whereType<ProductCatalogue>()
            .toList();
      },
    );
  }

  // Clear specific cache
  void clearFilterCache() {
    _filteredCache.clear();
  }

  // Clear category cache
  void clearCategoryCache() {
    _categoryFilterCache.clear();
  }

  // Clear all caches
  void clearAllCaches() {
    _filteredCache.clear();
    _categoryFilterCache.clear();
  }

  // Check if a specific filter exists
  bool hasFilter(String filter, bool isDrGrow) {
    final categories = isDrGrow ? drGrowCategoryFilter : otherCategoryFilter;
    return filter == 'All' || categories.contains(filter);
  }

  @override
  List<Object?> get props => [status, productCatalogues, errorMessage];
}
