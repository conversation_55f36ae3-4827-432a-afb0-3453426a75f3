import 'package:flutter_test/flutter_test.dart';
import 'package:aquapartner/domain/entities/product.dart';

void main() {
  group('Product Entity Tests', () {
    const testProductData = {
      'id': 'product_123',
      'productName': 'Test Product',
      'category': 'Test Category',
      'productImage': 'https://example.com/image.jpg',
      'content': '<p>Test product content</p>',
      'productTag': 'NEW ARRIVAL',
    };

    group('Product Creation', () {
      test('should create Product with all required fields', () {
        final product = Product(
          productName: testProductData['productName']!,
          category: testProductData['category']!,
          productImage: testProductData['productImage']!,
          content: testProductData['content']!,
          productTag: testProductData['productTag']!,
          categoryType: 'Default',
          subCategory: 'Default',
          tagLine: '',
          sortOrder: '0',
          status: '',
        );

        expect(product.productName, equals(testProductData['productName']));
        expect(product.category, equals(testProductData['category']));
        expect(product.productImage, equals(testProductData['productImage']));
        expect(product.content, equals(testProductData['content']));
        expect(product.productTag, equals(testProductData['productTag']));
      });

      test('should create Product with empty optional fields', () {
        final product = Product(
          productName: 'Test Product',
          category: 'Test Category',
          productImage: '',
          content: '',
          productTag: '',
          categoryType: '',
          subCategory: '',
          tagLine: '',
          sortOrder: '',
          status: '',
        );

        expect(product.productName, equals('Test Product'));
        expect(product.category, equals('Test Category'));
        expect(product.productImage, isEmpty);
        expect(product.content, isEmpty);
        expect(product.productTag, isEmpty);
      });
    });

    group('Product Equality', () {
      test('should be equal when all properties are the same', () {
        final product1 = Product(
          productName: testProductData['productName']!,
          category: testProductData['category']!,
          productImage: testProductData['productImage']!,
          content: testProductData['content']!,
          productTag: testProductData['productTag']!,
          categoryType: 'Default',
          subCategory: 'Default',
          tagLine: '',
          sortOrder: '0',
          status: '',
        );

        final product2 = Product(
          productName: testProductData['productName']!,
          category: testProductData['category']!,
          productImage: testProductData['productImage']!,
          content: testProductData['content']!,
          productTag: testProductData['productTag']!,
          categoryType: 'Default',
          subCategory: 'Default',
          tagLine: '',
          sortOrder: '0',
          status: '',
        );

        expect(product1, equals(product2));
        expect(product1.hashCode, equals(product2.hashCode));
      });

      test('should not be equal when properties differ', () {
        final product1 = Product(
          productName: 'Test Product 1',
          category: 'Category 1',
          productImage: 'image1.jpg',
          content: 'Content 1',
          productTag: 'TAG1',
          categoryType: 'Default',
          subCategory: 'Default',
          tagLine: '',
          sortOrder: '0',
          status: '',
        );

        final product2 = Product(
          productName: 'Test Product 2',
          category: 'Category 2',
          productImage: 'image2.jpg',
          content: 'Content 2',
          productTag: 'TAG2',
          categoryType: 'Default',
          subCategory: 'Default',
          tagLine: '',
          sortOrder: '0',
          status: '',
        );

        expect(product1, isNot(equals(product2)));
        expect(product1.hashCode, isNot(equals(product2.hashCode)));
      });

      test('should not be equal when properties differ', () {
        final product1 = Product(
          productName: 'Test Product',
          category: 'Test Category',
          productImage: 'image.jpg',
          content: 'Content',
          productTag: 'TAG',
          categoryType: 'Default',
          subCategory: 'Default',
          tagLine: '',
          sortOrder: '0',
          status: '',
        );

        final product2 = Product(
          productName: 'Test Product',
          category: 'Test Category',
          productImage: 'image.jpg',
          content: 'Content',
          productTag: 'TAG',
          categoryType: 'Default',
          subCategory: 'Default',
          tagLine: '',
          sortOrder: '1', // Different sort order
          status: '',
        );

        expect(product1, isNot(equals(product2)));
      });
    });

    group('Product String Representation', () {
      test('should return proper string representation', () {
        final product = Product(
          productName: 'Test Product',
          category: 'Test Category',
          productImage: 'image.jpg',
          content: 'Content',
          productTag: 'NEW',
          categoryType: 'Default',
          subCategory: 'Default',
          tagLine: '',
          sortOrder: '0',
          status: '',
        );

        final stringRepresentation = product.toString();

        expect(stringRepresentation, contains('Product'));
        expect(stringRepresentation, contains('Test Product'));
        expect(stringRepresentation, contains('Test Category'));
        expect(stringRepresentation, contains('NEW'));
      });
    });

    group('Product Validation', () {
      test('should handle special characters in product name', () {
        final product = Product(
          productName: 'Test Product & Co. (Special)',
          category: 'Test Category',
          productImage: 'image.jpg',
          content: 'Content',
          productTag: 'NEW',
          categoryType: 'Default',
          subCategory: 'Default',
          tagLine: '',
          sortOrder: '0',
          status: '',
        );

        expect(product.productName, equals('Test Product & Co. (Special)'));
      });

      test('should handle HTML content', () {
        final product = Product(
          productName: 'Test Product',
          category: 'Test Category',
          productImage: 'image.jpg',
          content:
              '<h1>Title</h1><p>Description with <strong>bold</strong> text</p>',
          productTag: 'NEW',
          categoryType: 'Default',
          subCategory: 'Default',
          tagLine: '',
          sortOrder: '0',
          status: '',
        );

        expect(product.content, contains('<h1>'));
        expect(product.content, contains('<strong>'));
        expect(product.content, contains('</p>'));
      });

      test('should handle long product names', () {
        const longName =
            'This is a very long product name that might be used in some cases where the product has a detailed description in its name field';

        final product = Product(
          productName: longName,
          category: 'Test Category',
          productImage: 'image.jpg',
          content: 'Content',
          productTag: 'NEW',
          categoryType: 'Default',
          subCategory: 'Default',
          tagLine: '',
          sortOrder: '0',
          status: '',
        );

        expect(product.productName, equals(longName));
        expect(product.productName.length, greaterThan(50));
      });

      test('should handle Unicode characters', () {
        final product = Product(
          productName: 'Test Product 🐟',
          category: 'Category',
          productImage: 'image.jpg',
          content: 'Content with émojis 🌊🐠',
          productTag: 'NEW',
          categoryType: 'Default',
          subCategory: 'Default',
          tagLine: '',
          sortOrder: '0',
          status: '',
        );

        expect(product.productName, contains('🐟'));
        expect(product.productName, contains('Test Product'));
        expect(product.category, contains('Category'));
        expect(product.content, contains('🌊🐠'));
        expect(product.productTag, contains('NEW'));
      });
    });

    group('Product Edge Cases', () {
      test('should handle empty strings', () {
        final product = Product(
          productName: '',
          category: '',
          productImage: '',
          content: '',
          productTag: '',
          categoryType: 'Default',
          subCategory: 'Default',
          tagLine: '',
          sortOrder: '0',
          status: '',
        );

        expect(product.productName, isEmpty);
        expect(product.category, isEmpty);
        expect(product.productImage, isEmpty);
        expect(product.content, isEmpty);
        expect(product.productTag, isEmpty);
      });

      test('should handle whitespace-only strings', () {
        final product = Product(
          productName: '\t\n',
          category: '  \r\n  ',
          productImage: ' ',
          content: '\t',
          productTag: '\n',
          categoryType: 'Default',
          subCategory: 'Default',
          tagLine: '',
          sortOrder: '0',
          status: '',
        );

        expect(product.productName, equals('\t\n'));
        expect(product.category, equals('  \r\n  '));
      });
    });
  });
}
