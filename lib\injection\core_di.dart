import 'package:aquapartner/core/cache/cache_manager.dart';
import 'package:aquapartner/core/constants/app_constants.dart';
import 'package:aquapartner/core/network/api_client.dart';
import 'package:aquapartner/core/network/app_check_interceptor.dart';
import 'package:aquapartner/core/network/network_info.dart';
import 'package:aquapartner/core/network/performance_interceptor.dart';
import 'package:aquapartner/core/routes/analytics_route_observer.dart';
import 'package:aquapartner/core/services/analytics_service.dart';
import 'package:aquapartner/core/services/app_check_service.dart';
import 'package:aquapartner/core/services/feature_usage_tracker.dart';
import 'package:aquapartner/core/services/mongodb_service.dart';
import 'package:aquapartner/core/services/navigation_service.dart';
import 'package:aquapartner/core/services/performance_monitoring_service.dart';
import 'package:aquapartner/core/services/session_manager.dart';
import 'package:aquapartner/core/utils/logger.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio/dio.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_app_check/firebase_app_check.dart';
import 'package:firebase_performance/firebase_performance.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get_it/get_it.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:pretty_dio_logger/pretty_dio_logger.dart';
import 'package:shared_preferences/shared_preferences.dart';

final sl = GetIt.instance;

/// Initializes core dependencies that are used across multiple features
Future<void> initCoreDependencies() async {
  // External dependencies
  sl.registerLazySingleton(() => const FlutterSecureStorage());

  sl.registerLazySingleton(() => FirebaseAnalytics.instance);
  sl.registerLazySingleton(() => FirebasePerformance.instance);
  sl.registerLazySingleton(() => FirebaseAppCheck.instance);
  sl.registerLazySingleton(() => InternetConnectionChecker.createInstance());
  sl.registerLazySingleton(() => Connectivity());

  final SharedPreferences prefs = await SharedPreferences.getInstance();
  sl.registerLazySingleton(() => prefs);

  // Logger
  sl.registerLazySingleton(() => AppLogger());

  // Dio HTTP client
  sl.registerLazySingleton(() {
    final dio = Dio(
      BaseOptions(
        baseUrl: AppConstants.baseUrl,
        connectTimeout: const Duration(seconds: 15),
        receiveTimeout: const Duration(seconds: 15),
      ),
    );

    // Add interceptors
    dio.interceptors.add(
      PrettyDioLogger(
        requestHeader: true,
        requestBody: true,
        responseBody: true,
        responseHeader: false,
        error: true,
        compact: true,
      ),
    );

    dio.interceptors.add(
      PerformanceInterceptor(
        sl<PerformanceMonitoringService>(),
        sl<AppLogger>(),
      ),
    );

    dio.interceptors.add(
      AppCheckInterceptor(sl<AppCheckService>(), sl<AppLogger>()),
    );

    return dio;
  });

  // Core services
  sl.registerLazySingleton<NetworkInfo>(
    () => NetworkInfoImpl(
      connectivity: sl<Connectivity>(),
      logger: sl<AppLogger>(),
    ),
  );

  sl.registerLazySingleton(
    () =>
        MongoDBService(networkInfo: sl<NetworkInfo>(), logger: sl<AppLogger>()),
  );

  sl.registerLazySingleton(() => NavigationService());
  sl.registerLazySingleton(
    () => ApiClient(sl<Dio>(), sl<FlutterSecureStorage>()),
  );

  sl.registerLazySingleton(
    () => AnalyticsService(sl<FirebaseAnalytics>(), sl<AppLogger>()),
  );

  sl.registerLazySingleton(
    () => AnalyticsRouteObserver(sl<AnalyticsService>()),
  );

  sl.registerLazySingleton(
    () => PerformanceMonitoringService(
      sl<FirebasePerformance>(),
      sl<AppLogger>(),
    ),
  );

  sl.registerLazySingleton(
    () => AppCheckService(sl<FirebaseAppCheck>(), sl<AppLogger>()),
  );

  sl.registerLazySingleton(
    () => CacheManager(
      sharedPreferences: sl<SharedPreferences>(),
      logger: sl<AppLogger>(),
    ),
  );

  sl.registerLazySingleton(() => SessionManager(sl<AnalyticsService>()));
  sl.registerLazySingleton(() => FeatureUsageTracker(sl<AnalyticsService>()));
}
