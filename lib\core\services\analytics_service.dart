import 'package:aquapartner/core/utils/logger.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../domain/entities/customer.dart';

class AnalyticsService {
  final FirebaseAnalytics analytics;
  Customer? _currentUser;
  final AppLogger _appLogger;

  AnalyticsService(this.analytics, this._appLogger);

  // User Session Tracking
  Future<void> logUserLogin(String userId, String userType) async {
    if (kDebugMode) {
      _appLogger.i('🔥 User Login:');
      _appLogger.i('🔥 - User ID: $userId');
      _appLogger.i('🔥 - User Type: $userType');
    }

    // Set user ID for Firebase Analytics
    await analytics.setUserId(id: userId);

    // Get shared preferences to check if this is first login
    final prefs = await SharedPreferences.getInstance();
    final firstLoginKey = 'first_login_$userId';

    // Check if this is the first login for this user
    if (!prefs.containsKey(firstLoginKey)) {
      final now = DateTime.now().toIso8601String();
      await prefs.setString(firstLoginKey, now);
      // Set first login date as user property
      await analytics.setUserProperty(name: 'first_login_date', value: now);
    }

    // Set user properties that will appear in the dashboard
    await analytics.setUserProperty(name: 'user_type', value: userType);
    await analytics.setUserProperty(
      name: 'last_login_date',
      value: DateTime.now().toIso8601String(),
    );

    // Log the login event
    await analytics.logEvent(
      name: 'user_login',
      parameters: <String, Object>{
        'user_id': userId,
        'user_type': userType,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  Future<void> logUserLogout(String userId) async {
    if (kDebugMode) {
      _appLogger.i('🔥 User Logout:');
      _appLogger.i('🔥 - User ID: $userId');
    }

    await analytics.logEvent(
      name: 'user_logout',
      parameters: <String, Object>{
        'user_id': userId,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  Future<void> logEvent({
    required String name,
    required Map<String, Object> parameters,
  }) async {
    // Standardize event names to follow Firebase conventions
    final standardizedName = name.toLowerCase().replaceAll(' ', '_');

    // Enforce Firebase's 40-character limit
    final truncatedName =
        standardizedName.length > 40
            ? standardizedName.substring(0, 40)
            : standardizedName;

    if (kDebugMode) {
      _appLogger.i('🔥 Analytics Event: $truncatedName');
      _appLogger.i('🔥 Parameters: $parameters');
    }

    // Add standard parameters to all events
    final enrichedParameters = {
      'timestamp': DateTime.now().toIso8601String(),
      ...parameters,
    };

    await analytics.logEvent(
      name: truncatedName,
      parameters: enrichedParameters,
    );
  }

  // Screen Tracking
  Future<void> logScreenView({
    required String screenName,
    String? screenClass,
  }) async {
    // Add debug logging in debug mode
    if (kDebugMode) {
      _appLogger.i('🔥 Screen View: $screenName (${screenClass ?? 'unknown'})');
    }

    await analytics.logScreenView(
      screenName: screenName,
      screenClass: screenClass,
    );
  }

  // User Properties
  Future<void> setUserProperties(String userId, String userRole) async {
    // Add debug logging in debug mode
    if (kDebugMode) {
      _appLogger.i('🔥 Setting User Properties:');
      _appLogger.i('🔥 - User ID: $userId');
      _appLogger.i('🔥 - User Role: $userRole');
      _appLogger.i('🔥 - Last Login: ${DateTime.now().toIso8601String()}');
    }

    await Future.wait([
      analytics.setUserId(id: userId),
      analytics.setUserProperty(name: 'user_role', value: userRole),
      analytics.setUserProperty(
        name: 'last_login',
        value: DateTime.now().toIso8601String(),
      ),
    ]);
  }

  // Enhanced feature usage tracking
  Future<void> logFeatureUsage(String featureName) async {
    if (kDebugMode) {
      _appLogger.i('🔥 Feature Used:');
      _appLogger.i('🔥 - Feature Name: $featureName');
    }

    // Get shared preferences to track feature usage frequency
    final prefs = await SharedPreferences.getInstance();

    // Track feature usage count
    final countKey = 'feature_count_$featureName';
    final count = prefs.getInt(countKey) ?? 0;
    await prefs.setInt(countKey, count + 1);

    // Track last usage time
    final lastUsedKey = 'feature_last_used_$featureName';
    final now = DateTime.now().toIso8601String();
    await prefs.setString(lastUsedKey, now);

    // Log as a custom event with count information
    await analytics.logEvent(
      name: 'feature_used',
      parameters: {
        'feature_name': featureName,
        'usage_count': count + 1,
        'timestamp': now,
      },
    );

    // Also log as a standard Firebase Analytics event for better dashboard visibility
    await analytics.logSelectContent(
      contentType: 'feature',
      itemId: featureName,
    );
  }

  // Product Interaction Tracking
  Future<void> logProductView(String productId, String productName) async {
    if (kDebugMode) {
      _appLogger.i('🔥 Product Viewed:');
      _appLogger.i('🔥 - Product ID: $productId');
      _appLogger.i('🔥 - Product Name: $productName');
    }

    await analytics.logEvent(
      name: 'product_viewed',
      parameters: {
        'product_id': productId,
        'product_name': productName,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  void setCurrentUser(Customer? customer) async {
    _currentUser = customer;

    if (customer == null) return;

    await analytics.setUserId(id: customer.customerId);
    await analytics.setUserProperty(
      name: 'user_name',
      value: customer.customerName,
    );
    await analytics.setUserProperty(
      name: 'company_name',
      value: customer.companyName,
    );
    await analytics.setUserProperty(name: 'user_role', value: 'retailer');
    await analytics.setUserProperty(
      name: 'last_active_date',
      value: DateTime.now().toIso8601String(),
    );
  }

  // Enhanced screen duration tracking
  Future<void> logScreenDuration({
    required String screenName,
    required int durationMs,
    String? screenClass,
  }) async {
    final params = {
      'screen_name': screenName,
      'duration_ms': durationMs.toString(),
      'screen_class': screenClass ?? '',
      'timestamp': DateTime.now().toIso8601String(),
    };

    // Add user info if available
    if (_currentUser != null) {
      params['user_id'] = _currentUser!.customerId;
      params['user_name'] = _currentUser!.customerName;
    }

    if (kDebugMode) {
      _appLogger.i('🔥 Screen Duration:');
      _appLogger.i('🔥 - Screen: $screenName');
      _appLogger.i('🔥 - Duration: ${durationMs}ms');
      if (_currentUser != null) {
        _appLogger.i('🔥 - User: ${_currentUser!.customerName}');
      }
    }

    await analytics.logEvent(name: 'screen_duration', parameters: params);
  }

  // Order Tracking
  Future<void> logOrderCreated(String orderId, double amount) async {
    if (kDebugMode) {
      _appLogger.i('🔥 Order Created:');
      _appLogger.i('🔥 - Order ID: $orderId');
      _appLogger.i('🔥 - Amount: $amount INR');
    }

    await analytics.logEvent(
      name: 'order_created',
      parameters: {
        'order_id': orderId,
        'amount': amount,
        'currency': 'INR',
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  // Customer Interaction Tracking
  Future<void> logCustomerInteraction(
    String customerId,
    String interactionType,
  ) async {
    if (kDebugMode) {
      _appLogger.i('🔥 Customer Interaction:');
      _appLogger.i('🔥 - Customer ID: $customerId');
      _appLogger.i('🔥 - Interaction Type: $interactionType');
    }

    await analytics.logEvent(
      name: 'customer_interaction',
      parameters: {
        'customer_id': customerId,
        'interaction_type': interactionType,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  // Error Tracking
  Future<void> logError({
    required String errorType,
    required String errorMessage,
    String? screenName,
    Map<String, Object> additionalParams = const {},
  }) async {
    final params = {
      'error_type': errorType,
      'error_message': errorMessage,
      'screen_name': screenName ?? 'unknown',
      'timestamp': DateTime.now().toIso8601String(),
      ...additionalParams,
    };

    if (kDebugMode) {
      _appLogger.e('🔥 Error: $errorType - $errorMessage');
    }

    await analytics.logEvent(name: 'app_error', parameters: params);
  }

  // Enhanced user engagement tracking
  Future<void> logUserEngagement(int durationMs) async {
    if (kDebugMode) {
      _appLogger.i('🔥 User Engagement:');
      _appLogger.i('🔥 - Duration: ${durationMs}ms');
    }

    final params = {
      'duration_ms': durationMs.toString(),
      'timestamp': DateTime.now().toIso8601String(),
    };

    // Add user info if available
    if (_currentUser != null) {
      params['user_id'] = _currentUser!.customerId;
      params['user_name'] = _currentUser!.customerName;
    }

    await analytics.logEvent(name: 'engagement_user', parameters: params);
  }

  // Track when a user starts a new session
  Future<void> logSessionStart() async {
    final params = {'timestamp': DateTime.now().toIso8601String()};

    // Add user info if available
    if (_currentUser != null) {
      params['user_id'] = _currentUser!.customerId;
      params['user_name'] = _currentUser!.customerName;
    }

    await analytics.logEvent(name: 'start_session', parameters: params);
  }

  // Add standardized user interaction tracking
  Future<void> logUserInteraction({
    required String screenName,
    required String actionName,
    required String elementType,
    String? elementId,
    Map<String, Object> additionalParams = const {},
  }) async {
    final params = {
      'screen_name': screenName,
      'action_name': actionName,
      'element_type': elementType,
      'element_id': elementId ?? 'unknown',
      'timestamp': DateTime.now().toIso8601String(),
      ...additionalParams,
    };

    if (kDebugMode) {
      _appLogger.i('🔥 User Interaction:');
      _appLogger.i('🔥 - Screen: $screenName');
      _appLogger.i('🔥 - Action: $actionName');
      _appLogger.i('🔥 - Element: $elementType (${elementId ?? 'unknown'})');
    }

    await analytics.logEvent(name: 'user_interaction', parameters: params);
  }

  // Add to AnalyticsService class
  Future<void> logUserFlow({
    required String flowName,
    required String stepName,
    required String status,
    Map<String, Object> additionalParams = const {},
  }) async {
    final params = {
      'flow_name': flowName,
      'step_name': stepName,
      'status': status,
      'timestamp': DateTime.now().toIso8601String(),
      ...additionalParams,
    };

    if (kDebugMode) {
      _appLogger.i('🔥 User Flow: $flowName - $stepName ($status)');
      if (additionalParams.isNotEmpty) {
        _appLogger.i('🔥 Additional Params: $additionalParams');
      }
    }

    await analytics.logEvent(name: 'user_flow', parameters: params);
  }

  // Add to AnalyticsService class
  Future<void> updateUserBehaviorProperties() async {
    // Get shared preferences to analyze behavior
    final prefs = await SharedPreferences.getInstance();

    // Find most used feature
    final featureCounts = <String, int>{};
    final featureKeys = prefs.getKeys().where(
      (key) => key.startsWith('feature_count_'),
    );

    for (final key in featureKeys) {
      final featureName = key.replaceFirst('feature_count_', '');
      final count = prefs.getInt(key) ?? 0;
      featureCounts[featureName] = count;
    }

    // Find most used feature
    String? mostUsedFeature;
    int maxCount = 0;

    featureCounts.forEach((feature, count) {
      if (count > maxCount) {
        maxCount = count;
        mostUsedFeature = feature;
      }
    });

    // Update user properties
    if (mostUsedFeature != null) {
      await analytics.setUserProperty(
        name: 'most_used_feature',
        value: mostUsedFeature,
      );

      await analytics.setUserProperty(
        name: 'feature_usage_count',
        value: maxCount.toString(),
      );
    }

    // Calculate app usage frequency
    final sessionDates = <String>[];
    final sessionKeys = prefs.getKeys().where(
      (key) => key.startsWith('session_date_'),
    );

    for (final key in sessionKeys) {
      final dateStr = prefs.getString(key);
      if (dateStr != null) {
        sessionDates.add(dateStr);
      }
    }

    // Calculate days between first and last session
    if (sessionDates.isNotEmpty) {
      sessionDates.sort();
      final firstSession = DateTime.parse(sessionDates.first);
      final lastSession = DateTime.parse(sessionDates.last);
      final daysBetween = lastSession.difference(firstSession).inDays;

      if (daysBetween > 0) {
        final sessionsPerDay = sessionDates.length / daysBetween;
        await analytics.setUserProperty(
          name: 'sessions_per_day',
          value: sessionsPerDay.toStringAsFixed(2),
        );
      }
    }
  }
}
