import '../../domain/entities/app_version.dart';
import '../../domain/entities/update_info.dart';
import '../../domain/entities/update_policy.dart';
import '../../domain/repositories/version_repository.dart';
import '../datasources/device_info_data_source.dart';
import '../datasources/remote/firebase_remote_config_data_source.dart';

class VersionRepositoryImpl implements VersionRepository {
  final RemoteConfigDataSource remoteConfigDataSource;
  final DeviceInfoDataSource deviceInfoDataSource;

  VersionRepositoryImpl({
    required this.remoteConfigDataSource,
    required this.deviceInfoDataSource,
  });

  @override
  Future<AppVersion> getCurrentAppVersion() async {
    final version = await deviceInfoDataSource.getCurrentAppVersion();
    return AppVersion(version);
  }

  @override
  Future<AppVersion> getMinRequiredVersion() async {
    await remoteConfigDataSource.initialize();
    final version = remoteConfigDataSource.getMinRequiredVersion();
    return AppVersion(version);
  }

  @override
  Future<UpdateInfo> getUpdateInfo() async {
    final message = remoteConfigDataSource.getUpdateMessage();
    final storeUrl = await deviceInfoDataSource.getAppStoreUrl();

    return UpdateInfo(message: message, storeUrl: storeUrl);
  }

  @override
  Future<UpdatePolicy> getUpdatePolicy() async {
    await remoteConfigDataSource.initialize();
    final policyString = remoteConfigDataSource.getUpdatePolicy();
    return UpdatePolicy.fromString(policyString);
  }
}
