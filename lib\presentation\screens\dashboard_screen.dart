import 'package:aqua_ui/aqua_ui.dart';
import 'package:aquapartner/core/utils/logger.dart';
import 'package:aquapartner/presentation/widgets/my_farmers_card.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../core/mixins/analytics_mixin.dart';
import '../cubit/dashboard/dashboard_cubit.dart';
import '../cubit/dashboard/dashboard_state.dart';
import '../cubit/home/<USER>';
import '../cubit/product_catalogue/product_catalogue_cubit.dart';
import '../cubit/product_catalogue/product_catalogue_state.dart';
import '../cubit/update_checker/update_checker_cubit.dart';
import '../cubit/update_checker/update_checker_state.dart';
import '../widgets/category_products_list_view_widget.dart';
import '../widgets/loading_widget.dart';
import '../widgets/price_list_widget.dart';
import '../widgets/sales_overview_chart.dart';
import '../widgets/update_dialog.dart';

class DashboardScreen extends StatefulWidget {
  final AppLogger logger = AppLogger();
  DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen>
    with AutomaticKeepAliveClientMixin, AnalyticsMixin {
  @override
  bool get wantKeepAlive => true; // Keep state when navigating between tabs

  @override
  void initState() {
    super.initState();
    // Initialize dashboard data when screen is first loaded
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Check for mandatory updates
      _checkForMandatoryUpdate();

      // Load dashboard data if it's not already loaded
      final dashboardState = context.read<DashboardCubit>().state;
      if (dashboardState is! DashboardLoaded) {
        context.read<DashboardCubit>().loadDashboardData();
      }
    });
  }

  // Check if app needs a mandatory update
  Future<void> _checkForMandatoryUpdate() async {
    try {
      // Use the UpdateCheckerCubit to check for updates
      context.read<UpdateCheckerCubit>().checkForUpdate();
    } catch (e) {
      widget.logger.e('Error checking for updates: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    return Scaffold(
      body: BlocListener<UpdateCheckerCubit, UpdateCheckerState>(
        listener: (context, state) {
          if (state is UpdateRequired) {
            // Show mandatory update dialog
            showDialog(
              context: context,
              barrierDismissible: false,
              builder: (context) => UpdateDialog(updateInfo: state.updateInfo),
            );
          }
        },
        child: BlocConsumer<DashboardCubit, DashboardState>(
          listener: (context, dashboardState) {
            // Handle any state changes that require UI feedback
            if (dashboardState is DashboardError) {
              ScaffoldMessenger.of(
                context,
              ).showSnackBar(SnackBar(content: Text(dashboardState.message)));
            }
          },
          builder: (context, dashboardState) {
            if (dashboardState is DashboardInitial) {
              // Trigger loading if we're still in initial state
              WidgetsBinding.instance.addPostFrameCallback((_) {
                context.read<DashboardCubit>().loadDashboardData();
              });
              return const LoadingWidget(message: "Loading Your Dashboard");
            } else if (dashboardState is DashboardLoading) {
              return const LoadingWidget(message: "Loading Your Dashboard");
            } else if (dashboardState is DashboardLoaded) {
              return _buildDashboardContent(dashboardState);
            } else if (dashboardState is DashboardError) {
              return LoadingWidget(
                message:
                    "An error occurred while loading the dashboard, pull to retry",
              );
            } else {
              return const LoadingWidget(message: "Loading Your Dashboard");
            }
          },
        ),
      ),
    );
  }

  Widget _buildDashboardContent(DashboardLoaded state) {
    return Container(
      color: Color(0xFFF9FAFC),
      child: SingleChildScrollView(
        child: Container(
          color: Color(0xFFF9FAFC),
          margin: EdgeInsets.only(left: 16, right: 16, top: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                margin: EdgeInsets.only(top: 0),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8.0),
                  child: Image.asset(
                    'assets/images/dr_grow_banner.jpg',
                    fit: BoxFit.cover,
                  ),
                ),
              ),

              vSpace8,
              AquaText.headline("Price List", color: acTextSecondaryColor),
              PriceListWidget(),
              vSpace8,
              AquaText.headline("My Farmers", color: acTextSecondaryColor),
              vSpace6,
              MyFarmersCard(
                myFarmers: state.dashboard.myFarmers,
                liquidation: state.dashboard.liquidation,
              ),
              vSpace8,
              AquaText.headline("Overview", color: acTextSecondaryColor),
              vSpace2,
              SalesOverviewChart(
                salesData: state.dashboard.sales,
                paymentsData: state.dashboard.payments,
              ),
              vSpace8,
              Row(
                textBaseline: TextBaseline.alphabetic,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.baseline,
                children: [
                  AquaText.headline("Products", color: acTextSecondaryColor),
                  TextButton(
                    onPressed: () {
                      context.read<HomeCubit>().gotoProductsView();
                    },
                    child: AquaText.body(
                      "See all",
                      color: acPrimaryBlue,
                      weight: AquaFontWeight.bold,
                    ),
                  ),
                ],
              ),
              BlocConsumer<ProductCatalogueCubit, ProductCatalogueState>(
                listener: (context, state) {
                  if (state.isLoading) {}
                },
                builder: (context, state) {
                  return CategoryProductsListViewWidget(
                    categories: state.drGrowProductCatalogue,
                    isScrollable: false,
                    onProductTap:
                        context
                            .read<ProductCatalogueCubit>()
                            .gotoProductDetailsScreen,
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  String get screenName => "DashboardScreen";
}
