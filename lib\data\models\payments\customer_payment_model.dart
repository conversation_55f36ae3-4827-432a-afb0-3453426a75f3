import 'package:objectbox/objectbox.dart';
import '../../../domain/entities/payments/customer_payment.dart';

@Entity()
class CustomerPaymentModel {
  @Id()
  int dbId = 0;

  final String id;
  final String customerId;
  final String customerName;
  final String categoryType;
  final String amount;
  final String paymentsDate;
  final String paymentId;
  final String paymentsMode;
  final String paymentsNumber;

  CustomerPaymentModel({
    required this.id,
    required this.customerId,
    required this.customerName,
    required this.categoryType,
    required this.amount,
    required this.paymentsDate,
    required this.paymentId,
    required this.paymentsMode,
    required this.paymentsNumber,
  });

  factory CustomerPaymentModel.fromJson(Map<String, dynamic> json) {
    return CustomerPaymentModel(
      id: json['id'] ?? json['_id'] ?? '',
      customerId: json['customerId'] ?? '',
      customerName: json['customerName'] ?? '',
      categoryType: json['categoryType'] ?? '',
      amount: json['amount'] ?? '',
      paymentsDate: json['paymentsDate'] ?? '',
      paymentId: json['paymentId'] ?? '',
      paymentsMode: json['paymentsMode'] ?? '',
      paymentsNumber: json['paymentsNumber'] ?? '',
    );
  }

  CustomerPayment toEntity() {
    return CustomerPayment(
      id: id,
      customerId: customerId,
      customerName: customerName,
      categoryType: categoryType,
      amount: amount,
      paymentsDate: DateTime.parse(paymentsDate),
      paymentId: paymentId,
      paymentsMode: paymentsMode,
      paymentsNumber: paymentsNumber,
    );
  }
}
