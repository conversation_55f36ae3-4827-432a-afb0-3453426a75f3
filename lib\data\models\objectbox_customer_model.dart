import 'package:objectbox/objectbox.dart';
import 'customer_model.dart';

@Entity()
class ObjectBoxCustomerModel {
  @Id()
  int id = 0;

  String? mongoId;
  final String customerId;
  final String customerName;
  final String email;
  final String mobileNumber;
  final String companyName;
  final String gstNo;
  final String businessVertical;
  final String customerCode;
  final String billingAddress;
  final bool isSynced;

  ObjectBoxCustomerModel({
    this.id = 0,
    this.mongoId,
    required this.customerId,
    required this.customerName,
    required this.email,
    required this.mobileNumber,
    required this.companyName,
    required this.gstNo,
    required this.businessVertical,
    required this.customerCode,
    required this.billingAddress,
    required this.isSynced,
  });

  factory ObjectBoxCustomerModel.fromCustomerModel(CustomerModel model) {
    return ObjectBoxCustomerModel(
      mongoId: model.id,
      customerId: model.customerId,
      customerName: model.customerName,
      email: model.email,
      mobileNumber: model.mobileNumber,
      companyName: model.companyName,
      gstNo: model.gstNo,
      businessVertical: model.businessVertical,
      customerCode: model.customerCode,
      billingAddress: model.billingAddress,
      isSynced: model.isSynced,
    );
  }

  CustomerModel toCustomerModel() {
    return CustomerModel(
      id: mongoId,
      customerId: customerId,
      customerName: customerName,
      email: email,
      mobileNumber: mobileNumber,
      companyName: companyName,
      gstNo: gstNo,
      businessVertical: businessVertical,
      customerCode: customerCode,
      billingAddress: billingAddress,
      isSynced: isSynced,
    );
  }

  ObjectBoxCustomerModel copyWith({
    int? id,
    String? mongoId,
    String? customerId,
    String? customerName,
    String? email,
    String? mobileNumber,
    String? companyName,
    String? gstNo,
    String? businessVertical,
    String? customerCode,
    String? billingAddress,
    bool? isSynced,
  }) {
    return ObjectBoxCustomerModel(
      id: id ?? this.id,
      mongoId: mongoId ?? this.mongoId,
      customerId: customerId ?? this.customerId,
      customerName: customerName ?? this.customerName,
      email: email ?? this.email,
      mobileNumber: mobileNumber ?? this.mobileNumber,
      companyName: companyName ?? this.companyName,
      gstNo: gstNo ?? this.gstNo,
      businessVertical: businessVertical ?? this.businessVertical,
      customerCode: customerCode ?? this.customerCode,
      billingAddress: billingAddress ?? this.billingAddress,
      isSynced: isSynced ?? this.isSynced,
    );
  }
}
