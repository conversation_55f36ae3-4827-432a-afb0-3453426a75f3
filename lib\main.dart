import 'package:aqua_ui/aqua_ui.dart';
import 'package:aquapartner/core/services/analytics_service.dart';
import 'package:aquapartner/core/services/app_check_service.dart';
import 'package:aquapartner/core/services/performance_monitoring_service.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'core/bloc/simple_bloc_observer.dart';
import 'core/routes/analytics_route_observer.dart';
import 'core/routes/app_router.dart';
import 'core/services/navigation_service.dart';
import 'core/services/session_manager.dart';
import 'firebase_options.dart';
import 'injection_container.dart' as di;
import 'core/utils/logger.dart';
import 'presentation/cubit/auth/auth_cubit.dart';
import 'presentation/cubit/auth/auth_state.dart';
import 'presentation/providers/app_providers.dart';
import 'presentation/screens/home_screen.dart';
import 'presentation/screens/login_screen.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'dart:io';

void main() async {
  // Ensure Flutter is initialized
  WidgetsFlutterBinding.ensureInitialized();

  // Set preferred orientations to portrait only to prevent layout issues
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // Initialize Firebase
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  // Configure Firebase Auth for iOS reCAPTCHA and persistence
  await FirebaseAuth.instance.setLanguageCode("en");

  // Set persistence to NONE on iOS to prevent auth state from persisting after uninstall
  // Note: setPersistence is only supported on web platforms
  // For iOS, we'll handle persistence differently in the AppDelegate

  // Initialize dependencies
  await di.init();

  final logger = di.sl<AppLogger>();
  logger.i("Starting application");

  // Initialize App Check
  final appCheckService = di.sl<AppCheckService>();
  await appCheckService.initialize();

  // Initialize Performance Monitoring
  final performanceService = di.sl<PerformanceMonitoringService>();

  // Start a trace for app startup
  performanceService.startTrace('app_startup');

  // We'll stop this trace after the app is fully initialized

  // Enable analytics collection with debug logging
  await FirebaseAnalytics.instance.setAnalyticsCollectionEnabled(true);
  await FirebaseAnalytics.instance.setSessionTimeoutDuration(
    const Duration(minutes: 30),
  );

  // Enable debug mode for Firebase Analytics in debug builds
  if (kDebugMode) {
    logger.i("Enabling Firebase Analytics debug mode");

    // Set user property to mark this as a debug device
    await FirebaseAnalytics.instance.setUserProperty(
      name: 'debug_mode',
      value: 'true',
    );

    // Log a test event to verify debug setup
    await FirebaseAnalytics.instance.logEvent(
      name: 'debug_mode_enabled',
      parameters: {'timestamp': DateTime.now().toIso8601String()},
    );
  }

  // Enable Bloc observer for debugging
  Bloc.observer = SimpleBlocObserver();

  runApp(AppProviders(child: const MyApp()));
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> with WidgetsBindingObserver {
  final RouteObserver<ModalRoute> routeObserver = RouteObserver<ModalRoute>();
  bool _isInitialized = false;

  // ignore: unused_field
  SessionManager? _sessionManager;

  @override
  void initState() {
    super.initState();
    // Initialize session manager and keep a reference to prevent garbage collection
    // The session manager observes app lifecycle events automatically once initialized
    _sessionManager = di.sl<SessionManager>();

    // Start initialization process
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    final logger = di.sl<AppLogger>();

    try {
      // Use dependency injection directly instead of context
      final prefs = di.sl<SharedPreferences>();
      final isFirstLaunch = !prefs.containsKey('first_launch_completed');
      final authCubit = di.sl<AuthCubit>();

      // For iOS, check if this might be a reinstall by forcing a sign out
      if (Platform.isIOS) {
        final secureStorage = di.sl<FlutterSecureStorage>();
        final hasExistingKeys = await secureStorage.containsKey(
          key: 'auth_token',
        );

        if (hasExistingKeys) {
          await secureStorage.deleteAll();
          await authCubit.signOut();
        }
      }

      if (isFirstLaunch) {
        await prefs.setBool('first_launch_completed', true);

        // Force logout if user is already logged in
        // Log first launch event
        final analyticsService = di.sl<AnalyticsService>();
        analyticsService.logEvent(
          name: 'first_app_launch_forced_logout',
          parameters: {'timestamp': DateTime.now().toIso8601String()},
        );

        // Make sure Firebase Auth is ready before signing out
        await Future.delayed(const Duration(milliseconds: 500));
        await authCubit.signOut();
      }

      // Add a slight delay to show splash screen (optional)
      await Future.delayed(const Duration(seconds: 1));

      // Check authentication status
      // This will trigger the AuthCubit state changes
      await authCubit.checkAuthStatus();

      // Listen for auth state changes to know when auth check is complete
      final authState = authCubit.state;

      // If auth state is already determined, set initialized immediately
      if (authState is AuthSuccess ||
          authState is Unauthenticated ||
          authState is AuthError) {
        setState(() {
          _isInitialized = true;
        });
      } else {
        // Otherwise, wait for auth state to change
        authCubit.stream.listen((state) {
          if (state is AuthSuccess ||
              state is Unauthenticated ||
              state is AuthError) {
            // Only set initialized once if not already set
            if (!_isInitialized) {
              setState(() {
                _isInitialized = true;
              });
            }
          }
        });
      }
    } catch (e) {
      logger.e("Error during app initialization", e);
      // Even on error, we should eventually show the main UI
      setState(() {
        _isInitialized = true;
      });
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    // Handle app lifecycle changes
    final logger = di.sl<AppLogger>();
    logger.d("App lifecycle state changed to: $state");

    if (state == AppLifecycleState.resumed) {
      // Refresh auth status when app is resumed
      context.read<AuthCubit>().checkAuthStatus();
    }
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      navigatorKey: di.sl<NavigationService>().navigatorKey,
      title: 'Aqua Partner',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        useMaterial3: false,
        primarySwatch: Colors.blue,
        visualDensity: VisualDensity.adaptivePlatformDensity,
        appBarTheme: const AppBarTheme(elevation: 0, centerTitle: true),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            foregroundColor: Colors.white,
            backgroundColor: acPrimaryBlue,
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
      ),
      // Use BlocBuilder to determine the initial screen based on auth state
      home:
          !_isInitialized
              ? const SplashScreen()
              : BlocBuilder<AuthCubit, AuthState>(
                builder: (context, state) {
                  if (state is AuthInitial || state is AuthLoading) {
                    return const SplashScreen();
                  }

                  if (state is AuthSuccess) {
                    return const HomeScreen();
                  }

                  if (state is Unauthenticated || state is AuthError) {
                    return const LoginScreen();
                  }

                  // Default to login screen
                  return const LoginScreen();
                },
              ),
      navigatorObservers: [routeObserver, di.sl<AnalyticsRouteObserver>()],
      // Keep onGenerateRoute for named navigation after initial screen
      onGenerateRoute: AppRouter.generateRoute,
    );
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }
}

class SplashScreen extends StatelessWidget {
  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // App logo
            Image.asset(
              'assets/images/logo.png',
              width: 120,
              height: 120,
              errorBuilder: (context, error, stackTrace) {
                return const Icon(
                  Icons.water_drop,
                  size: 80,
                  color: acPrimaryBlue,
                );
              },
            ),
            const SizedBox(height: 24),
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            const Text('Loading...', style: TextStyle(fontSize: 16)),
          ],
        ),
      ),
    );
  }
}
