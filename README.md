## inside the Application build.gradle file, replace with your flutter path

flutter pub get
flutter pub run build_runner build --delete-conflicting-outputs
flutter pub run flutter_native_splash:create flutter_native_splash.yaml

## Configure Your NDK system path

## To Clear the Current Project cache files

flutter clean

## To Clear Over All Flutter Cache

flutter pub cache clean

## Debug the Analytics

adb shell setprop debug.firebase.analytics.app blue.aquaconnect.aquapartner_self_service

## Generate Flutter Options file

dart pub global activate flutterfire_cli
flutterfire configure --project=aquaconnect-partner-prod --out=lib/firebase_options.dart
