import 'package:connectivity_plus/connectivity_plus.dart';

import '../utils/logger.dart';

abstract class NetworkInfo {
  Future<bool> get isConnected;
}

class NetworkInfoImpl implements NetworkInfo {
  final Connectivity connectivity;
  final AppLogger logger;

  NetworkInfoImpl({required this.connectivity, required this.logger});

  @override
  Future<bool> get isConnected async {
    try {
      final connectivityResult = await connectivity.checkConnectivity();
      final hasConnection = connectivityResult != ConnectivityResult.none;
      return hasConnection;
    } catch (e) {
      return false;
    }
  }
}
