import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/farmer_visit/farmer.dart';

/// Abstract interface for the Farmer Repository.
/// This defines the contract that the Data layer must implement.
abstract class FarmerRepository {
  Future<Either<Failure, List<Farmer>>> getAllFarmers(String customerId);
  Future<Either<Failure, void>> storeFarmerData(List<Farmer> farmers);
  Future<Either<Failure, List<Farmer>>> syncFarmers(String customerId);
}
