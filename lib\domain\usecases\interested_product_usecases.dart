import 'package:dartz/dartz.dart';

import '../../core/error/failures.dart';
import '../../core/utils/logger.dart';
import '../entities/interested_product.dart';
import '../repositories/interested_product_repository.dart';

class AddInterestedProductUseCase {
  final InterestedProductRepository repository;
  final AppLogger logger;

  AddInterestedProductUseCase({required this.repository, required this.logger});

  Future<Either<Failure, InterestedProduct>> call(
    InterestedProduct interestedProduct,
  ) async {
    logger.i(
      "AddInterestedProductUseCase: Adding interested product for ${interestedProduct.productName}",
    );
    return await repository.addInterestedProduct(interestedProduct);
  }
}

class SyncInterestedProductsUseCase {
  final InterestedProductRepository repository;
  final AppLogger logger;

  SyncInterestedProductsUseCase({
    required this.repository,
    required this.logger,
  });

  Future<Either<Failure, int>> call() async {
    logger.i("SyncInterestedProductsUseCase: Syncing interested products");
    return await repository.syncInterestedProducts();
  }
}

class GetUnsyncedCountUseCase {
  final InterestedProductRepository repository;
  final AppLogger logger;

  GetUnsyncedCountUseCase({required this.repository, required this.logger});

  Future<Either<Failure, int>> call() async {
    logger.i("GetUnsyncedCountUseCase: Getting unsynced count");
    return await repository.getUnsyncedCount();
  }
}
