import '../../../domain/entities/invoices/invoice_item.dart';

class InvoiceItemModel extends InvoiceItem {
  const InvoiceItemModel({
    required super.id,
    required super.productId,
    required super.itemName,
    required super.quantity,
    required super.invoiceId,
    required super.createdTime,
    required super.discountAmount,
    required super.hsnSac,
    required super.itemPrice,
    required super.lastModifiedTime,
    required super.placeOfSupply,
    required super.productCategory,
    required super.source,
    required super.subTotal,
    required super.total,
  });

  factory InvoiceItemModel.fromJson(Map<String, dynamic> json) {
    return InvoiceItemModel(
      id: json['_id'] ?? '',
      productId: json['productId'] ?? '',
      itemName: json['itemName'] ?? '',
      quantity: (json['quantity'] ?? 0).toDouble(),
      invoiceId: json['invoiceId'] ?? '',
      createdTime:
          json['createdTime'] != null
              ? DateTime.parse(json['createdTime'])
              : DateTime.now(),
      discountAmount: (json['discountAmount'] ?? 0).toDouble(),
      hsnSac: json['hsnSac'] ?? '',
      itemPrice: (json['itemPrice'] ?? 0).toDouble(),
      lastModifiedTime:
          json['lastModifiedTime'] != null
              ? DateTime.parse(json['lastModifiedTime'])
              : DateTime.now(),
      placeOfSupply: json['placeOfSupply'] ?? '',
      productCategory: json['productCategory'] ?? '',
      source: json['source'] ?? '',
      subTotal: (json['subTotal'] ?? 0).toDouble(),
      total: (json['total'] ?? 0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'productId': productId,
      'itemName': itemName,
      'quantity': quantity,
      'invoiceId': invoiceId,
      'createdTime': createdTime.toIso8601String(),
      'discountAmount': discountAmount,
      'hsnSac': hsnSac,
      'itemPrice': itemPrice,
      'lastModifiedTime': lastModifiedTime.toIso8601String(),
      'placeOfSupply': placeOfSupply,
      'productCategory': productCategory,
      'source': source,
      'subTotal': subTotal,
      'total': total,
    };
  }
}
