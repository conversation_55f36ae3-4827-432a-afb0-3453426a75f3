import 'package:objectbox/objectbox.dart';

import '../../../domain/entities/invoices/invoice.dart';
import '../../../domain/entities/invoices/invoice_item.dart';
import 'objectbox_invoice_item_model.dart';

@Entity()
class ObjectBoxInvoiceModel {
  @Id()
  int id = 0;

  @Unique()
  final String invoiceId;
  final String addressId;
  final int ageInDays;
  final String ageTier;
  final double balance;
  final String customerId;
  final String deliveryMode;
  final String deliveryStatus;

  @Property(type: PropertyType.dateNano)
  final DateTime dueDate;

  @Property(type: PropertyType.dateNano)
  final DateTime invoiceDate;

  final String invoiceNumber;
  final String invoiceStatus;
  final double subTotal;
  final double total;

  @Backlink('invoice')
  final items = ToMany<ObjectBoxInvoiceItemModel>();

  ObjectBoxInvoiceModel({
    this.id = 0,
    required this.invoiceId,
    required this.addressId,
    required this.ageInDays,
    required this.ageTier,
    required this.balance,
    required this.customerId,
    required this.deliveryMode,
    required this.deliveryStatus,
    required this.dueDate,
    required this.invoiceDate,
    required this.invoiceNumber,
    required this.invoiceStatus,
    required this.subTotal,
    required this.total,
  });

  Invoice toEntity() {
    List<InvoiceItem> itemEntities =
        items.map((item) => item.toEntity()).toList();

    return Invoice(
      invoiceId: invoiceId,
      addressId: addressId,
      ageInDays: ageInDays,
      ageTier: ageTier,
      balance: balance,
      customerId: customerId,
      deliveryMode: deliveryMode,
      deliveryStatus: deliveryStatus,
      dueDate: dueDate,
      invoiceDate: invoiceDate,
      invoiceNumber: invoiceNumber,
      invoiceStatus: invoiceStatus,
      subTotal: subTotal,
      total: total,
      items: itemEntities,
    );
  }

  static ObjectBoxInvoiceModel fromEntity(Invoice invoice) {
    return ObjectBoxInvoiceModel(
      invoiceId: invoice.invoiceId,
      addressId: invoice.addressId,
      ageInDays: invoice.ageInDays,
      ageTier: invoice.ageTier,
      balance: invoice.balance,
      customerId: invoice.customerId,
      deliveryMode: invoice.deliveryMode,
      deliveryStatus: invoice.deliveryStatus,
      dueDate: invoice.dueDate,
      invoiceDate: invoice.invoiceDate,
      invoiceNumber: invoice.invoiceNumber,
      invoiceStatus: invoice.invoiceStatus,
      subTotal: invoice.subTotal,
      total: invoice.total,
    );
  }
}
