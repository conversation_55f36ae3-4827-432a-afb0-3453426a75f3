import 'package:equatable/equatable.dart';

import '../../../domain/entities/invoices/invoice.dart';

abstract class InvoicesState extends Equatable {
  const InvoicesState();

  @override
  List<Object?> get props => [];
}

class InvoiceInitial extends InvoicesState {}

class InvoicesLoading extends InvoicesState {}

class InvoicesLoaded extends InvoicesState {
  final List<Invoice> invoices;
  final bool isFromCache;
  final bool isCacheStale;
  final bool isBackgroundSyncInProgress;

  const InvoicesLoaded({
    required this.invoices,
    this.isFromCache = false,
    this.isCacheStale = false,
    this.isBackgroundSyncInProgress = false,
  });

  InvoicesLoaded copyWith({
    List<Invoice>? invoices,
    bool? isFromCache,
    bool? isCacheStale,
    bool? isBackgroundSyncInProgress,
  }) {
    return InvoicesLoaded(
      invoices: invoices ?? this.invoices,
      isFromCache: isFromCache ?? this.isFromCache,
      isCacheStale: isCacheStale ?? this.isCacheStale,
      isBackgroundSyncInProgress:
          isBackgroundSyncInProgress ?? this.isBackgroundSyncInProgress,
    );
  }

  @override
  List<Object?> get props => [
    invoices,
    isFromCache,
    isCacheStale,
    isBackgroundSyncInProgress,
  ];
}

class InvoiceDetailLoaded extends InvoicesState {
  final Invoice invoice;

  const InvoiceDetailLoaded({required this.invoice});

  @override
  List<Object?> get props => [invoice];
}

class InvoiceError extends InvoicesState {
  final String message;

  const InvoiceError({required this.message});

  @override
  List<Object?> get props => [message];
}

class InvoiceSyncing extends InvoicesState {}

class InvoiceSyncSuccess extends InvoicesState {
  final DateTime syncTime;

  const InvoiceSyncSuccess({required this.syncTime});

  @override
  List<Object?> get props => [syncTime];
}

class InvoiceSyncError extends InvoicesState {
  final String message;

  const InvoiceSyncError({required this.message});

  @override
  List<Object?> get props => [message];
}
