import 'dart:convert';

import '../../domain/entities/smr_report.dart';

class SMRReportModel extends SMRReport {
  SMRReportModel({
    required super.id,
    required super.so,
    required super.partner,
    required super.partnerId,
    required super.productName,
    required super.startDate,
    required super.lastDate,
    required super.openingBalance,
    required super.invoice,
    required super.srn,
    required super.closingBalance,
    required super.sales,
    required super.status,
  });

  factory SMRReportModel.fromJson(Map<String, dynamic> json) {
    return SMRReportModel(
      id: json['_id'],
      so: json['so'],
      partner: json['partner'],
      partnerId: json['partnerId'],
      productName: json['productName'],
      startDate: DateTime.parse(json['startDate']),
      lastDate: DateTime.parse(json['lastDate']),
      openingBalance: json['openingBalance'],
      invoice: json['invoice'],
      srn: json['srn'],
      closingBalance: json['closingBalance'],
      sales: json['sales'],
      status: json['status'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'so': so,
      'partner': partner,
      'partnerId': partnerId,
      'productName': productName,
      'startDate': startDate.toIso8601String(),
      'lastDate': lastDate.toIso8601String(),
      'openingBalance': openingBalance,
      'invoice': invoice,
      'srn': srn,
      'closingBalance': closingBalance,
      'sales': sales,
      'status': status,
    };
  }

  /// Converts a JSON string to a list of SMRReportModel objects
  static List<SMRReportModel> listFromJson(String jsonString) {
    final Map<String, dynamic> jsonMap = json.decode(jsonString);
    final List<dynamic> resultsJson = jsonMap['results'];
    return resultsJson.map((item) => SMRReportModel.fromJson(item)).toList();
  }

  /// Converts a JSON map to a list of SMRReportModel objects
  static List<SMRReportModel> listFromMap(Map<String, dynamic> jsonMap) {
    final List<dynamic> resultsJson = jsonMap['results'];
    return resultsJson.map((item) => SMRReportModel.fromJson(item)).toList();
  }
}
