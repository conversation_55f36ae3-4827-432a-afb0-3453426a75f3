import 'dart:convert';

import '../../../core/constants/app_constants.dart';
import '../../../core/error/exceptions.dart';
import '../../../core/network/api_client.dart';
import '../../../core/utils/logger.dart';
import '../../models/sales_order/sales_order_model.dart';
import '../../models/sales_order/sales_order_item_model.dart';

abstract class SalesOrderRemoteDataSource {
  /// Calls the https://partner.aquaconnect.blue/api/salesOrders/{customerId} endpoint
  ///
  /// Throws a [ServerException] for all error codes
  Future<List<SalesOrderModel>> getSalesOrders(String customerId);
}

class SalesOrderRemoteDataSourceImpl implements SalesOrderRemoteDataSource {
  final ApiClient apiClient;
  final AppLogger logger;

  SalesOrderRemoteDataSourceImpl({
    required this.apiClient,
    required this.logger,
  });

  @override
  Future<List<SalesOrderModel>> getSalesOrders(String customerId) async {
    try {
      logger.i('Fetching sales orders from API for customer: $customerId');
      final response = await apiClient.get(
        '${AppConstants.baseUrl}/salesOrders/$customerId',
      );
      if (response.statusCode == 200) {
        logger.i('Successfully fetched sales orders from API');
        final jsonData = json.decode(response.data);
        final results = jsonData['results'] as List;

        final List<SalesOrderModel> salesOrders = [];
        
        for (var salesOrderJson in results) {
          final salesOrder = SalesOrderModel.fromJson(salesOrderJson);
          
          // Fetch items for this sales order if available
          if (salesOrderJson['items'] != null && salesOrderJson['items'] is List) {
            final itemsList = salesOrderJson['items'] as List;
            for (var itemJson in itemsList) {
              final item = SalesOrderItemModel.fromJson(itemJson);
              // Set the relationship
              item.salesOrderIdValue = salesOrder.salesOrderId;
              salesOrder.items.add(item);
            }
          }
          
          salesOrders.add(salesOrder);
        }

        logger.i('Fetched ${salesOrders.length} sales orders with their items');
        return salesOrders;
      } else {
        logger.e('Error fetching sales orders: ${response.statusCode}');
        throw ServerException();
      }
    } catch (e) {
      logger.e('Exception while fetching sales orders: $e');
      throw ServerException();
    }
  }
}