import 'package:aquapartner/data/datasources/local/customer_local_data_source.dart';
import 'package:aquapartner/data/datasources/local/dashboard_local_datasource.dart';
import 'package:dartz/dartz.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../core/error/failures.dart';
import '../../domain/repositories/auth_repository.dart';
import '../datasources/local/user_local_data_source.dart';
import '../datasources/remote/auth_remote_data_source.dart';
import '../../core/network/network_info.dart';
import '../../core/utils/logger.dart';

class AuthRepositoryImpl implements AuthRepository {
  final AuthRemoteDataSource remoteDataSource;
  final UserLocalDataSource localDataSource;
  final CustomerLocalDataSource customerLocalDataSource;
  final DashboardLocalDataSource dashboardLocalDataSource;
  final NetworkInfo networkInfo;
  final FirebaseAuth firebaseAuth;
  final AppLogger logger;

  AuthRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
    required this.customerLocalDataSource,
    required this.dashboardLocalDataSource,
    required this.networkInfo,
    required this.firebaseAuth,
    required this.logger,
  });

  @override
  Future<Either<Failure, String>> sendOtp(String phoneNumber) async {
    if (await networkInfo.isConnected) {
      logger.i("Network connected, sending OTP");
      return await remoteDataSource.sendOtp(phoneNumber);
    } else {
      logger.w("No network connection, cannot send OTP");
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, bool>> verifyOtp(
    String verificationId,
    String otp,
  ) async {
    if (await networkInfo.isConnected) {
      logger.i("Network connected, verifying OTP");
      return await remoteDataSource.verifyOtp(verificationId, otp);
    } else {
      logger.w("No network connection, cannot verify OTP");
      return Left(NetworkFailure());
    }
  }

  @override
  Future<void> signOut() async {
    logger.i("Signing out user");
    await remoteDataSource.signOut();
    await localDataSource.deleteUser();
    await customerLocalDataSource.deleteCustomer();
    await dashboardLocalDataSource.deleteDashboard();
  }

  @override
  Future<bool> isUserLoggedIn() async {
    try {
      logger.i("Checking if user is logged in");
      final currentUser = firebaseAuth.currentUser;
      final isLoggedIn = currentUser != null;
      logger.i("User logged in: $isLoggedIn");
      return isLoggedIn;
    } catch (e) {
      logger.e("Error checking if user is logged in", e);
      return false;
    }
  }

  @override
  Future<User?> getCurrentUser() async {
    try {
      logger.i("Getting current Firebase user");
      return firebaseAuth.currentUser;
    } catch (e) {
      logger.e("Error getting current Firebase user", e);
      return null;
    }
  }
}
