abstract class NavigationState {
  final int index;
  final bool performAction;
  
  const NavigationState(this.index, {this.performAction = false});
}

class NavigationInitial extends NavigationState {
  const NavigationInitial() : super(0);
}

class NavigationChanged extends NavigationState {
  const NavigationChanged(super.index);
}

class NavigationActionTriggered extends NavigationState {
  const NavigationActionTriggered(super.index) : super(performAction: true);
}