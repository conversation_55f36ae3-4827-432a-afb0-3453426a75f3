import 'package:aqua_ui/aqua_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../cubit/connectivity/connectivity_cubit.dart';
import '../cubit/connectivity/connectivity_state.dart';

class ConnectivityStatusWidget extends StatelessWidget {
  const ConnectivityStatusWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ConnectivityCubit, ConnectivityState>(
      builder: (context, state) {
        if (state.status == ConnectionStatus.disconnected) {
          return Container(
            width: double.infinity,
            color: Colors.red,
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: AquaText.body(
              'No Internet Connection',
              textAlign: TextAlign.center,
              color: Colors.white,
            ),
          );
        }
        return const SizedBox.shrink();
      },
    );
  }
}
