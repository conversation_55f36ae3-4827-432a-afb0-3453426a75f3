# CI/CD Test Configuration for Comprehensive Testing Strategy
# This file defines the testing pipeline configuration for achieving production readiness

name: Comprehensive Test Pipeline

# Coverage Requirements
coverage:
  minimum_thresholds:
    line_coverage: 70      # Production readiness target
    branch_coverage: 65    # Minimum branch coverage
    function_coverage: 80  # Function coverage target
  
  milestone_targets:
    phase_1: 30           # Data Layer Foundation (Week 1-2)
    phase_2: 50           # Domain Business Logic (Week 3-4)
    phase_3: 70           # Presentation Layer (Week 5-8)
    production: 70        # Production readiness

  critical_components:
    authentication: 90    # Auth flow must have 90%+ coverage
    dashboard: 90         # Dashboard functionality 90%+ coverage
    data_sync: 85         # Data synchronization patterns
    analytics: 80         # Analytics tracking verification

# Test Execution Strategy
test_execution:
  phases:
    - name: "Phase 1 - Data Layer Foundation"
      target_coverage: 30
      duration: "Week 1-2"
      tests:
        - "test/data/repositories/**/*_test.dart"
        - "test/data/models/**/*_test.dart"
        - "test/core/cache/**/*_test.dart"
        - "test/core/database/**/*_test.dart"
        - "test/core/utils/**/*_test.dart"
      
    - name: "Phase 2 - Domain Business Logic"
      target_coverage: 50
      duration: "Week 3-4"
      tests:
        - "test/domain/usecases/**/*_test.dart"
        - "test/domain/services/**/*_test.dart"
        - "test/domain/entities/**/*_test.dart"
      
    - name: "Phase 3 - Presentation Layer"
      target_coverage: 70
      duration: "Week 5-8"
      tests:
        - "test/presentation/cubit/**/*_test.dart"
        - "test/presentation/screens/**/*_test.dart"
        - "test/presentation/widgets/**/*_test.dart"

# Test Categories and Patterns
test_patterns:
  repository_tests:
    pattern: "One-way synchronization (server to local with data clearing)"
    coverage_target: 80
    key_features:
      - "Clear local data before sync"
      - "Replace with remote data"
      - "Error handling for network failures"
      - "Cache management"
      - "Concurrent operation handling"
  
  cubit_tests:
    pattern: "State management with analytics tracking"
    coverage_target: 90
    key_features:
      - "State transitions with bloc_test"
      - "Analytics event verification"
      - "Error scenarios and recovery"
      - "Pull-to-refresh functionality"
      - "Edge cases and performance"
  
  screen_tests:
    pattern: "UI interactions with analytics"
    coverage_target: 90
    key_features:
      - "Widget rendering verification"
      - "User interaction flows"
      - "Analytics tracking (snake_case)"
      - "Pull-to-refresh testing"
      - "Accessibility compliance"
      - "Performance with large datasets"

# Analytics Testing Requirements
analytics_requirements:
  screen_naming: "snake_case"
  hierarchical_tracking: true
  required_events:
    - "screen_view"
    - "user_interaction"
    - "user_flow"
    - "error_tracking"
  
  verification_patterns:
    - "trackUserInteraction() for user actions"
    - "trackError() in catch blocks"
    - "trackUserFlow() for multi-step processes"
    - "parentScreenName for hierarchical tracking"

# Synchronization Testing Patterns
sync_patterns:
  one_way_sync:
    description: "Server to local with data clearing"
    test_requirements:
      - "Clear local data first"
      - "Fetch from remote"
      - "Replace local data"
      - "Verify data integrity"
      - "Handle sync failures"
    
  pull_to_refresh:
    description: "Refresh without loading states"
    test_requirements:
      - "Maintain current state during refresh"
      - "Update data on completion"
      - "Handle refresh failures"
      - "Analytics tracking"

# Quality Gates
quality_gates:
  merge_requirements:
    - "All tests must pass"
    - "Coverage cannot decrease by more than 5%"
    - "No new analyzer warnings"
    - "Code must be properly formatted"
    - "Analytics tracking verified"
  
  production_readiness:
    - "70%+ overall coverage"
    - "90%+ authentication coverage"
    - "90%+ dashboard coverage"
    - "All critical paths tested"
    - "Performance benchmarks met"
    - "Accessibility standards met"

# Test Infrastructure
infrastructure:
  mocking_framework: "mocktail"
  state_testing: "bloc_test"
  widget_testing: "flutter_test"
  coverage_tool: "lcov"
  
  test_helpers:
    - "TestSetupHelper for GetIt configuration"
    - "MockAnalyticsService for analytics verification"
    - "TestAppLogger for logging verification"
    - "Mock data factories for consistent test data"

# Execution Commands
commands:
  run_all_tests: "flutter test test/comprehensive_test_runner.dart --coverage"
  run_phase_1: "flutter test test/data/ test/core/ --coverage"
  run_phase_2: "flutter test test/domain/ --coverage"
  run_phase_3: "flutter test test/presentation/ --coverage"
  
  coverage_report: "genhtml coverage/lcov.info -o coverage/html"
  coverage_check: "lcov --summary coverage/lcov.info"
  
  format_check: "dart format --set-exit-if-changed ."
  analyze_code: "flutter analyze"

# Reporting Configuration
reporting:
  formats:
    - "lcov"
    - "html"
    - "json"
    - "cobertura"
  
  output_paths:
    lcov: "coverage/lcov.info"
    html: "coverage/html/"
    json: "coverage/coverage.json"
    cobertura: "coverage/cobertura.xml"
  
  badges:
    enabled: true
    thresholds:
      excellent: 90  # Green badge
      good: 80       # Yellow badge
      fair: 70       # Orange badge
      poor: 60       # Red badge

# Integration Requirements
integration:
  github_actions: true
  codecov_upload: true
  pr_comments: true
  merge_blocking: true
  
  notifications:
    slack_channel: "#dev-testing"
    email_alerts: true
    coverage_reports: true

# Performance Benchmarks
performance:
  test_execution_time: "< 5 minutes"
  large_dataset_tests: "< 1 second per test"
  memory_usage: "< 2GB during test execution"
  
  benchmarks:
    - "1000+ product list rendering"
    - "100+ invoice list processing"
    - "Concurrent sync operations"
    - "Rapid user interactions"

# Documentation Requirements
documentation:
  test_coverage_report: "Auto-generated with each PR"
  testing_guidelines: "Updated with new patterns"
  troubleshooting_guide: "Common test failures and solutions"
  
  required_sections:
    - "Test execution instructions"
    - "Coverage interpretation"
    - "Adding new tests"
    - "Debugging test failures"
    - "Performance optimization"
