import 'package:mongo_dart/mongo_dart.dart';
import '../../domain/entities/product_catalogue.dart';
import 'product_model.dart';

class ProductCatalogueModel extends ProductCatalogue {
  ProductCatalogueModel({
    String? id,
    required super.name,
    required super.image,
    required super.sortOrder,
    required super.status,
    required List<ProductModel> products,
  }) : super(id: id, products: products);

  factory ProductCatalogueModel.fromJson(Map<String, dynamic> json) {
    // Handle ObjectId conversion to String
    String? idString;
    if (json['_id'] != null) {
      if (json['_id'] is ObjectId) {
        idString = (json['_id'] as ObjectId).oid;
      } else if (json['_id'] is String) {
        idString = json['_id'];
      }
    }

    return ProductCatalogueModel(
      id: idString,
      name: json['name'] ?? '',
      image: json['image'] ?? '',
      sortOrder: json['sortOrder'] ?? '',
      status: json['status'] ?? '',
      products: (json['products'] as List<dynamic>?)
              ?.map((item) => ProductModel.fromJson(item))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    final map = {
      'name': name,
      'image': image,
      'sortOrder': sortOrder,
      'status': status,
      'products': products.map((item) {
        // Safe casting with type check
        if (item is ProductModel) {
          return item.toJson();
        }
        // Fallback for non-model items (shouldn't happen in normal usage)
        throw Exception('Expected ProductModel but got ${item.runtimeType}');
      }).toList(),
    };
    
    // Only include _id if it exists
    if (id != null) {
      map['_id'] = id!;
    }
    
    return map;
  }
  
  /// Converts this model to a domain entity
  ProductCatalogue toDomain() {
    return ProductCatalogue(
      id: id,
      name: name,
      image: image,
      sortOrder: sortOrder,
      status: status,
      products: products.map((product) {
        if (product is ProductModel) {
          return product.toDomain();
        }
        return product;
      }).toList(),
    );
  }
}