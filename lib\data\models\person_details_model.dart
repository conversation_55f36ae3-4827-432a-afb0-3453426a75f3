import 'package:objectbox/objectbox.dart';
import 'package:intl/intl.dart';
import '../../domain/entities/scheme/person_details.dart';
import 'support_person_model.dart';

@Entity()
class PersonDetailsModel {
  @Id()
  int id = 0;

  String rowId;
  String customerName;
  String email;
  String status;
  String mobileNumber;
  String userId;
  
  @Property(type: PropertyType.date)
  DateTime? modifiedTime;
  
  String empId;
  String profile;

  final supportPerson = ToOne<SupportPersonModel>();

  PersonDetailsModel({
    this.id = 0,
    required this.rowId,
    required this.customerName,
    required this.email,
    required this.status,
    required this.mobileNumber,
    required this.userId,
    this.modifiedTime,
    required this.empId,
    required this.profile,
  });

  // Convert from domain entity to model
  factory PersonDetailsModel.fromEntity(PersonDetails entity) {
    return PersonDetailsModel(
      rowId: entity.rowId,
      customerName: entity.customerName,
      email: entity.email,
      status: entity.status,
      mobileNumber: entity.mobileNumber,
      userId: entity.userId,
      modifiedTime: entity.modifiedTime,
      empId: entity.empId,
      profile: entity.profile,
    );
  }

  // Convert from model to domain entity
  PersonDetails toEntity() {
    return PersonDetails(
      rowId: rowId,
      customerName: customerName,
      email: email,
      status: status,
      mobileNumber: mobileNumber,
      userId: userId,
      modifiedTime: modifiedTime,
      empId: empId,
      profile: profile,
    );
  }

  // Convert from JSON to model
  factory PersonDetailsModel.fromJson(Map<String, dynamic> json) {
    DateTime? modifiedTime;
    try {
      if (json['modifiedTime'] != null) {
        modifiedTime = DateFormat(
          'dd-MMM-yyyy HH:mm:ss',
        ).parse(json['modifiedTime']);
      }
    } catch (e) {
      print('Error parsing date: $e');
    }

    return PersonDetailsModel(
      rowId: json['rowId'] ?? '',
      customerName: json['customerName'] ?? '',
      email: json['email'] ?? '',
      status: json['status'] ?? '',
      mobileNumber: json['mobileNumber'] ?? '',
      userId: json['userId'] ?? '',
      modifiedTime: modifiedTime,
      empId: json['empId'] ?? '',
      profile: json['profile'] ?? '',
    );
  }

  // Convert from model to JSON
  Map<String, dynamic> toJson() {
    return {
      'rowId': rowId,
      'customerName': customerName,
      'email': email,
      'status': status,
      'mobileNumber': mobileNumber,
      'userId': userId,
      'modifiedTime':
          modifiedTime != null
              ? DateFormat('dd-MMM-yyyy HH:mm:ss').format(modifiedTime!)
              : null,
      'empId': empId,
      'profile': profile,
    };
  }
}