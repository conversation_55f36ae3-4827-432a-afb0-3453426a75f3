import 'package:aquapartner/core/services/analytics_service.dart';
import 'package:get_it/get_it.dart';
import 'package:mocktail/mocktail.dart';

class MockAnalyticsService extends Mock implements AnalyticsService {}

/// Helper class to setup test environment for screen tests
class TestSetupHelper {
  static MockAnalyticsService? _mockAnalyticsService;

  /// Setup GetIt for screen tests that use AnalyticsMixin
  static void setupGetIt() {
    if (!GetIt.instance.isRegistered<AnalyticsService>()) {
      _mockAnalyticsService = MockAnalyticsService();

      // Setup default mock behaviors for all analytics methods
      when(
        () => _mockAnalyticsService!.logScreenView(
          screenName: any(named: 'screenName'),
          screenClass: any(named: 'screenClass'),
        ),
      ).thenAnswer((_) async {});

      when(
        () => _mockAnalyticsService!.logEvent(
          name: any(named: 'name'),
          parameters: any(named: 'parameters'),
        ),
      ).thenAnswer((_) async {});

      when(
        () => _mockAnalyticsService!.logScreenDuration(
          screenName: any(named: 'screenName'),
          durationMs: any(named: 'durationMs'),
          screenClass: any(named: 'screenClass'),
        ),
      ).thenAnswer((_) async {});

      GetIt.instance.registerSingleton<AnalyticsService>(
        _mockAnalyticsService!,
      );
    }
  }

  /// Clean up GetIt after tests
  static void tearDownGetIt() {
    if (GetIt.instance.isRegistered<AnalyticsService>()) {
      GetIt.instance.unregister<AnalyticsService>();
    }
    _mockAnalyticsService = null;
  }

  /// Get the mock analytics service for verification
  static MockAnalyticsService? get mockAnalyticsService =>
      _mockAnalyticsService;
}
