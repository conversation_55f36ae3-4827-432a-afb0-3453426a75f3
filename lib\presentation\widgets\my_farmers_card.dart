import 'package:aqua_ui/aqua_ui.dart';
import 'package:aquapartner/core/utils/currency_formatter.dart';
import 'package:aquapartner/domain/entities/dashboard/dashboard_entity.dart';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';

class MyFarmersCard extends StatelessWidget {
  final MyFarmersEntity myFarmers;
  final LiquidationEntity liquidation;
  const MyFarmersCard({
    required this.myFarmers,
    required this.liquidation,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    DateTime now = DateTime.now();
    DateTime firstDayOfCurrentMonth = DateTime(now.year, now.month, 1);
    DateTime dateInPreviousMonth = firstDayOfCurrentMonth.subtract(
      const Duration(days: 1),
    );

    String year = dateInPreviousMonth.year.toString();
    String month = dateInPreviousMonth.month.toString();

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: acWhiteColor,
        border: Border.all(
          color: Color(0xFFE2E2E2), // Customize border color
          width: 1.0, // Customize border width
        ),
        borderRadius: BorderRadius.circular(8), // Customize border radius
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildInfoRow('Attached Farmers', '${myFarmers.totalFarmers.length}'),
          const SizedBox(height: 16),
          _buildInfoRow('Potential Farmers', '${myFarmers.potentialFarmers}'),
          const SizedBox(height: 16),
          _buildInfoRow(
            'Last Month Liquidation (MRP)',
            CurrencyFormatter.formatAsINR(
              liquidation.liquidationByYear
                      .firstWhereOrNull((liqudation) => liqudation.year == year)
                      ?.months
                      .entries
                      .firstWhereOrNull((entry) => entry.key == month)
                      ?.value ??
                  0.0,
              decimalPlaces: 0,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AquaText.body(label, color: acTextSecondaryColor),
        const SizedBox(height: 4),
        AquaText.headline(
          value,
          color: acBlackColor,
          weight: AquaFontWeight.bold,
        ),
      ],
    );
  }
}
