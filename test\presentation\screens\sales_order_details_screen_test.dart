import 'package:aquapartner/core/services/analytics_service.dart';
import 'package:aquapartner/domain/entities/customer.dart';
import 'package:aquapartner/domain/entities/sales_order/sales_order.dart';
import 'package:aquapartner/domain/entities/sales_order/sales_order_item.dart';
import 'package:aquapartner/presentation/cubit/customer/customer_cubit.dart';
import 'package:aquapartner/presentation/screens/sales_order_details_screen.dart';
import 'package:bloc_test/bloc_test.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:mocktail/mocktail.dart';

// Mock classes
class MockCustomerCubit extends MockCubit<CustomerState>
    implements CustomerCubit {}

class MockAnalyticsService extends Mock implements AnalyticsService {}

// Helper function to find text in RichText widgets (used by AquaText)
Finder findRichText(String text) {
  return find.byWidgetPredicate((widget) {
    if (widget is RichText) {
      return widget.text.toPlainText().contains(text);
    }
    return false;
  });
}

// Test data
final testCustomer = Customer(
  customerId: 'test_customer_123',
  customerName: 'Test Customer',
  email: '<EMAIL>',
  mobileNumber: '+919999999999',
  companyName: 'Test Company',
  gstNo: 'TEST123456789',
  businessVertical: 'Aquaculture',
  customerCode: 'TC001',
  billingAddress: 'Test Address, Test City, Test State - 123456',
);

final testSalesOrderItems = [
  SalesOrderItem(
    itemId: 'item_001',
    salesOrderId: 'so_001',
    productId: 'prod_001',
    createdTime: DateTime.now(),
    entityDiscountPercent: '10%',
    hsnSac: 'HSN001',
    invoicedQuantityCancelled: '0',
    itemName: 'Test Product 1',
    itemPrice: 1000.0,
    lastModifiedTime: DateTime.now(),
    manuallyFulfilledQuantity: '0',
    nonPackageQuantity: '0',
    placeOfSupply: 'Test State',
    quantityDelivered: '2',
    quantityDropshipped: '0',
    quantityPacked: '2',
    salesVertices: 'vertex1',
    sno: '1',
    total: 2000.0,
  ),
  SalesOrderItem(
    itemId: 'item_002',
    salesOrderId: 'so_001',
    productId: 'prod_002',
    createdTime: DateTime.now(),
    entityDiscountPercent: '5%',
    hsnSac: 'HSN002',
    invoicedQuantityCancelled: '0',
    itemName: 'Test Product 2',
    itemPrice: 500.0,
    lastModifiedTime: DateTime.now(),
    manuallyFulfilledQuantity: '0',
    nonPackageQuantity: '0',
    placeOfSupply: 'Test State',
    quantityDelivered: '1',
    quantityDropshipped: '0',
    quantityPacked: '1',
    salesVertices: 'vertex2',
    sno: '2',
    total: 500.0,
  ),
  SalesOrderItem(
    itemId: 'item_003',
    salesOrderId: 'so_001',
    productId: 'prod_003',
    createdTime: DateTime.now(),
    entityDiscountPercent: '15%',
    hsnSac: 'HSN003',
    invoicedQuantityCancelled: '0',
    itemName: 'Test Product 3',
    itemPrice: 750.0,
    lastModifiedTime: DateTime.now(),
    manuallyFulfilledQuantity: '0',
    nonPackageQuantity: '0',
    placeOfSupply: 'Test State',
    quantityDelivered: '3',
    quantityDropshipped: '0',
    quantityPacked: '3',
    salesVertices: 'vertex3',
    sno: '3',
    total: 2250.0,
  ),
];

final testSalesOrder = SalesOrder(
  salesOrderId: 'so_001',
  addressId: 'addr_001',
  createdTime: DateTime(2024, 1, 15),
  customerId: 'test_customer_123',
  invoicedStatus: 'Pending',
  lastModifiedTime: DateTime(2024, 1, 15),
  orderSource: 'Mobile App',
  paidStatus: 'Unpaid',
  paymentTermsLabel: 'Net 30',
  saleOrderDate: DateTime(2024, 1, 15).toIso8601String(),
  salesChannel: 'Direct',
  salesOrderNumber: 'SO001',
  subTotal: 4275.0,
  total: 4750.0,
  items: testSalesOrderItems,
);

// Helper function to create SalesOrderItem with default values
SalesOrderItem createTestSalesOrderItem({
  String? itemId,
  String? itemName,
  double? itemPrice,
  double? total,
  String? entityDiscountPercent,
  String? quantityDelivered,
  int? index,
}) {
  final idx = index ?? 0;
  return SalesOrderItem(
    itemId: itemId ?? 'item_${idx.toString().padLeft(3, '0')}',
    salesOrderId: 'so_test',
    productId: 'prod_${idx.toString().padLeft(3, '0')}',
    createdTime: DateTime.now(),
    entityDiscountPercent: entityDiscountPercent ?? '${idx % 20}%',
    hsnSac: 'HSN${idx.toString().padLeft(3, '0')}',
    invoicedQuantityCancelled: '0',
    itemName: itemName ?? 'Product $idx',
    itemPrice: itemPrice ?? (100.0 + idx),
    lastModifiedTime: DateTime.now(),
    manuallyFulfilledQuantity: '0',
    nonPackageQuantity: '0',
    placeOfSupply: 'Test State',
    quantityDelivered: quantityDelivered ?? '1',
    quantityDropshipped: '0',
    quantityPacked: quantityDelivered ?? '1',
    salesVertices: 'vertex$idx',
    sno: (idx + 1).toString(),
    total: total ?? (itemPrice ?? (100.0 + idx)),
  );
}

// Helper function to create SalesOrder with default values
SalesOrder createTestSalesOrder({
  String? salesOrderId,
  String? salesOrderNumber,
  String? invoicedStatus,
  double? total,
  double? subTotal,
  List<SalesOrderItem>? items,
  DateTime? orderDate,
}) {
  final date = orderDate ?? DateTime.now();
  final orderItems = items ?? [createTestSalesOrderItem()];
  final calculatedTotal =
      total ?? orderItems.fold<double>(0.0, (sum, item) => sum + item.total);
  return SalesOrder(
    salesOrderId: salesOrderId ?? 'so_test',
    addressId: 'addr_001',
    createdTime: date,
    customerId: 'test_customer_123',
    invoicedStatus: invoicedStatus ?? 'Pending',
    lastModifiedTime: date,
    orderSource: 'Mobile App',
    paidStatus: 'Unpaid',
    paymentTermsLabel: 'Net 30',
    saleOrderDate: date.toIso8601String(),
    salesChannel: 'Direct',
    salesOrderNumber: salesOrderNumber ?? 'SO_TEST',
    subTotal: subTotal ?? (calculatedTotal * 0.9),
    total: calculatedTotal,
    items: orderItems,
  );
}

void main() {
  group('SalesOrderDetailsScreen Tests', () {
    late MockCustomerCubit mockCustomerCubit;
    late MockAnalyticsService mockAnalyticsService;

    setUp(() {
      mockCustomerCubit = MockCustomerCubit();
      mockAnalyticsService = MockAnalyticsService();

      // Register mock services in GetIt
      GetIt.instance.registerSingleton<AnalyticsService>(mockAnalyticsService);

      // Setup default mock behaviors
      when(
        () => mockCustomerCubit.state,
      ).thenReturn(CustomerLoaded(customer: testCustomer));

      // Setup analytics service mock behaviors
      when(
        () => mockAnalyticsService.logEvent(
          name: any(named: 'name'),
          parameters: any(named: 'parameters'),
        ),
      ).thenAnswer((_) async {});
      when(
        () => mockAnalyticsService.logScreenView(
          screenName: any(named: 'screenName'),
          screenClass: any(named: 'screenClass'),
        ),
      ).thenAnswer((_) async {});
      when(
        () => mockAnalyticsService.logUserInteraction(
          screenName: any(named: 'screenName'),
          actionName: any(named: 'actionName'),
          elementType: any(named: 'elementType'),
          elementId: any(named: 'elementId'),
          additionalParams: any(named: 'additionalParams'),
        ),
      ).thenAnswer((_) async {});
      when(
        () => mockAnalyticsService.logError(
          errorType: any(named: 'errorType'),
          errorMessage: any(named: 'errorMessage'),
          screenName: any(named: 'screenName'),
          additionalParams: any(named: 'additionalParams'),
        ),
      ).thenAnswer((_) async {});
      when(
        () => mockAnalyticsService.logUserFlow(
          flowName: any(named: 'flowName'),
          stepName: any(named: 'stepName'),
          status: any(named: 'status'),
          additionalParams: any(named: 'additionalParams'),
        ),
      ).thenAnswer((_) async {});
      when(
        () => mockAnalyticsService.logScreenDuration(
          screenName: any(named: 'screenName'),
          durationMs: any(named: 'durationMs'),
          screenClass: any(named: 'screenClass'),
        ),
      ).thenAnswer((_) async {});
    });

    tearDown(() {
      // Clean up GetIt registrations
      GetIt.instance.reset();
    });

    Widget createTestWidget() {
      return MaterialApp(
        home: BlocProvider<CustomerCubit>.value(
          value: mockCustomerCubit,
          child: SalesOrderDetailsScreen(salesOrder: testSalesOrder),
        ),
      );
    }

    group('Widget Rendering', () {
      testWidgets('debug - should render widget tree', (tester) async {
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Debug: Check for different text widget types
        final textWidgets = find.byType(Text);
        final richTextWidgets = find.byType(RichText);

        print('Found ${textWidgets.evaluate().length} Text widgets');
        print('Found ${richTextWidgets.evaluate().length} RichText widgets');

        // Check for RichText widgets (which AquaText likely uses)
        for (final element in richTextWidgets.evaluate()) {
          final widget = element.widget as RichText;
          final text = widget.text.toPlainText();
          print('RichText widget: "$text"');
        }

        // Just verify the screen renders without crashing
        expect(find.byType(SalesOrderDetailsScreen), findsOneWidget);
      });

      testWidgets('should render sales order details correctly', (
        tester,
      ) async {
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Verify sales order number is displayed (with # prefix in app bar)
        expect(findRichText('#SO001'), findsOneWidget);

        // Verify total amount is displayed (with currency formatting)
        expect(findRichText('4,750'), findsOneWidget);

        // Verify order status is displayed
        expect(findRichText('Pending'), findsOneWidget);
      });

      testWidgets('should render sales order items table', (tester) async {
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Verify table headers
        expect(findRichText('Item'), findsWidgets);
        expect(findRichText('Item Price'), findsOneWidget);
        expect(findRichText('Discount'), findsOneWidget);
        expect(findRichText('Sub Total'), findsOneWidget);

        // Verify item names are displayed
        expect(findRichText('Test Product 1'), findsOneWidget);
        expect(findRichText('Test Product 2'), findsOneWidget);
        expect(findRichText('Test Product 3'), findsOneWidget);

        // Verify item prices are displayed (with currency formatting)
        expect(findRichText('1,000'), findsAtLeastNWidgets(1));
        expect(findRichText('500'), findsAtLeastNWidgets(1));
        expect(findRichText('750'), findsAtLeastNWidgets(1));
      });

      testWidgets('should render customer information', (tester) async {
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Verify customer company name is displayed
        expect(findRichText('Test Company'), findsOneWidget);

        // Verify customer address is displayed
        expect(findRichText('Test Address'), findsOneWidget);
      });

      testWidgets('should render app bar with correct title', (tester) async {
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Verify app bar title (sales order number with # prefix)
        expect(findRichText('#SO001'), findsOneWidget);

        // Verify back button is present
        expect(find.byIcon(Icons.arrow_back), findsOneWidget);
      });

      testWidgets('should display discount percentages correctly', (
        tester,
      ) async {
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Verify discount percentages are displayed (may appear multiple times)
        expect(findRichText('10%'), findsAtLeastNWidgets(1));
        expect(findRichText('5%'), findsAtLeastNWidgets(1));
        expect(findRichText('15%'), findsAtLeastNWidgets(1));
      });
    });

    group('User Interactions', () {
      testWidgets('should handle back button tap', (tester) async {
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Find and tap back button (look for icon instead of BackButton widget)
        final backButton = find.byIcon(Icons.arrow_back);
        expect(backButton, findsOneWidget);

        await tester.tap(backButton);
        await tester.pumpAndSettle();

        // Verify navigation occurred (screen should be popped)
        expect(find.byType(SalesOrderDetailsScreen), findsNothing);
      });

      testWidgets('should handle scrolling through items', (tester) async {
        // Create sales order with many items to test scrolling
        final manyItems = List.generate(
          20,
          (index) => createTestSalesOrderItem(
            itemName: 'Product ${index + 1}',
            itemPrice: 100.0 + index,
            total: 100.0 + index,
            entityDiscountPercent: '${index % 10}%',
            index: index,
          ),
        );

        final orderWithManyItems = createTestSalesOrder(
          salesOrderNumber: 'SO_LARGE',
          invoicedStatus: 'Pending',
          items: manyItems,
        );

        final widget = MaterialApp(
          home: BlocProvider<CustomerCubit>.value(
            value: mockCustomerCubit,
            child: SalesOrderDetailsScreen(salesOrder: orderWithManyItems),
          ),
        );

        await tester.pumpWidget(widget);
        await tester.pumpAndSettle();

        // Find scrollable widget
        final scrollable = find.byType(Scrollable);
        if (scrollable.evaluate().isNotEmpty) {
          // Test scrolling
          await tester.drag(scrollable.first, const Offset(0, -300));
          await tester.pumpAndSettle();

          // Verify scrolling worked
          expect(find.byType(SalesOrderDetailsScreen), findsOneWidget);
        }
      });

      testWidgets('should handle item row taps if implemented', (tester) async {
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Look for tappable item rows
        final itemRow = findRichText('Test Product 1');
        if (itemRow.evaluate().isNotEmpty) {
          await tester.tap(itemRow);
          await tester.pumpAndSettle();

          // Verify interaction handling
        }
      });
    });

    group('Analytics Tracking', () {
      testWidgets('should track sales order details view on init', (
        tester,
      ) async {
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Verify analytics tracking would be called
        // Note: Since analytics is handled in initState, we verify the screen was created
        expect(find.byType(SalesOrderDetailsScreen), findsOneWidget);
      });

      testWidgets('should track user interactions with order items', (
        tester,
      ) async {
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Find and tap on an order item (if tappable)
        final itemWidget = findRichText('Test Product 1');
        if (itemWidget.evaluate().isNotEmpty) {
          await tester.tap(itemWidget);
          await tester.pumpAndSettle();

          // Verify interaction tracking
        }
      });
    });

    group('Data Display', () {
      testWidgets('should format currency correctly', (tester) async {
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Verify currency formatting (assuming INR format)
        expect(findRichText('₹'), findsWidgets);
      });

      testWidgets('should format dates correctly', (tester) async {
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Verify date formatting
        expect(findRichText('15'), findsWidgets); // day
        expect(findRichText('01'), findsWidgets); // month
        expect(findRichText('2024'), findsWidgets); // year
      });

      testWidgets('should display item quantities correctly', (tester) async {
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Verify quantities are displayed (might be in quantity column if present)
        expect(findRichText('2'), findsWidgets); // quantity of first item
        expect(findRichText('1'), findsWidgets); // quantity of second item
        expect(findRichText('3'), findsWidgets); // quantity of third item
      });

      testWidgets('should display subtotals correctly', (tester) async {
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Verify subtotals are displayed (look for currency formatted values)
        expect(
          findRichText('2,000'),
          findsAtLeastNWidgets(1),
        ); // subtotal of first item
        expect(
          findRichText('500'),
          findsAtLeastNWidgets(1),
        ); // subtotal of second item
        expect(
          findRichText('2,250'),
          findsAtLeastNWidgets(1),
        ); // subtotal of third item
      });
    });

    group('Status Updates', () {
      testWidgets('should display different order statuses correctly', (
        tester,
      ) async {
        final completedOrder = createTestSalesOrder(
          salesOrderNumber: 'SO002',
          total: 1000.0,
          invoicedStatus: 'Completed',
          items: [testSalesOrderItems.first],
        );

        final widget = MaterialApp(
          home: BlocProvider<CustomerCubit>.value(
            value: mockCustomerCubit,
            child: SalesOrderDetailsScreen(salesOrder: completedOrder),
          ),
        );

        await tester.pumpWidget(widget);
        await tester.pumpAndSettle();

        // Verify completed status is displayed
        expect(findRichText('Completed'), findsOneWidget);
      });

      testWidgets('should handle cancelled order status', (tester) async {
        final cancelledOrder = createTestSalesOrder(
          salesOrderNumber: 'SO003',
          total: 1000.0,
          invoicedStatus: 'Cancelled',
          items: [testSalesOrderItems.first],
        );

        final widget = MaterialApp(
          home: BlocProvider<CustomerCubit>.value(
            value: mockCustomerCubit,
            child: SalesOrderDetailsScreen(salesOrder: cancelledOrder),
          ),
        );

        await tester.pumpWidget(widget);
        await tester.pumpAndSettle();

        // Verify cancelled status is displayed
        expect(findRichText('Cancelled'), findsOneWidget);
      });
    });

    group('Error Handling', () {
      testWidgets('should handle empty sales order items gracefully', (
        tester,
      ) async {
        final emptyOrder = createTestSalesOrder(
          salesOrderNumber: 'SO_EMPTY',
          total: 0.0,
          subTotal: 0.0,
          invoicedStatus: 'Pending',
          items: [],
        );

        final widget = MaterialApp(
          home: BlocProvider<CustomerCubit>.value(
            value: mockCustomerCubit,
            child: SalesOrderDetailsScreen(salesOrder: emptyOrder),
          ),
        );

        await tester.pumpWidget(widget);
        await tester.pumpAndSettle();

        // Verify screen renders without crashing
        expect(find.byType(SalesOrderDetailsScreen), findsOneWidget);
        expect(findRichText('SO_EMPTY'), findsOneWidget);
      });

      testWidgets('should handle null customer state gracefully', (
        tester,
      ) async {
        when(() => mockCustomerCubit.state).thenReturn(CustomerInitial());

        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Should handle gracefully without crashing and show default values
        expect(find.byType(SalesOrderDetailsScreen), findsOneWidget);
        expect(findRichText('Unknown Company'), findsOneWidget);
        expect(findRichText('Unknown Address'), findsOneWidget);
      });

      testWidgets('should handle very long order numbers', (tester) async {
        final longOrder = createTestSalesOrder(
          salesOrderNumber:
              'VERY_LONG_SALES_ORDER_NUMBER_THAT_MIGHT_OVERFLOW_THE_UI_LAYOUT',
          total: 1000.0,
          invoicedStatus: 'Pending',
          items: testSalesOrderItems,
        );

        final widget = MaterialApp(
          home: BlocProvider<CustomerCubit>.value(
            value: mockCustomerCubit,
            child: SalesOrderDetailsScreen(salesOrder: longOrder),
          ),
        );

        await tester.pumpWidget(widget);
        await tester.pumpAndSettle();

        // Verify screen renders without overflow
        expect(find.byType(SalesOrderDetailsScreen), findsOneWidget);
        expect(tester.takeException(), isNull);
      });

      testWidgets('should handle zero or negative prices', (tester) async {
        final itemsWithZeroPrice = [
          createTestSalesOrderItem(
            itemName: 'Free Product',
            itemPrice: 0.0,
            total: 0.0,
            entityDiscountPercent: '0%',
            index: 0,
          ),
          createTestSalesOrderItem(
            itemName: 'Negative Price Product',
            itemPrice: -100.0,
            total: -100.0,
            entityDiscountPercent: '0%',
            index: 1,
          ),
        ];

        final orderWithZeroPrice = createTestSalesOrder(
          salesOrderNumber: 'SO_ZERO',
          total: -100.0,
          subTotal: -100.0,
          invoicedStatus: 'Pending',
          items: itemsWithZeroPrice,
        );

        final widget = MaterialApp(
          home: BlocProvider<CustomerCubit>.value(
            value: mockCustomerCubit,
            child: SalesOrderDetailsScreen(salesOrder: orderWithZeroPrice),
          ),
        );

        await tester.pumpWidget(widget);
        await tester.pumpAndSettle();

        // Verify screen renders without crashing
        expect(find.byType(SalesOrderDetailsScreen), findsOneWidget);
        expect(findRichText('Free Product'), findsOneWidget);
      });
    });

    group('Accessibility', () {
      testWidgets('should have proper semantic labels', (tester) async {
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Verify semantic labels are present for accessibility
        expect(find.byType(Semantics), findsWidgets);
      });

      testWidgets('should support screen readers', (tester) async {
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Verify screen renders without accessibility issues
        expect(find.byType(SalesOrderDetailsScreen), findsOneWidget);

        // Verify that the screen has accessible content
        expect(findRichText('SO001'), findsOneWidget);
        expect(findRichText('Test Company'), findsOneWidget);
      });
    });

    group('Performance', () {
      testWidgets('should render efficiently with large item lists', (
        tester,
      ) async {
        // Create sales order with many items
        final manyItems = List.generate(
          100,
          (index) => createTestSalesOrderItem(
            itemName: 'Product $index',
            itemPrice: 100.0 + index,
            total: 100.0 + index,
            entityDiscountPercent: '${index % 20}%',
            index: index,
          ),
        );

        final largeOrder = createTestSalesOrder(
          salesOrderNumber: 'SO_LARGE',
          invoicedStatus: 'Pending',
          items: manyItems,
        );

        final widget = MaterialApp(
          home: BlocProvider<CustomerCubit>.value(
            value: mockCustomerCubit,
            child: SalesOrderDetailsScreen(salesOrder: largeOrder),
          ),
        );

        final stopwatch = Stopwatch()..start();
        await tester.pumpWidget(widget);
        await tester.pumpAndSettle();
        stopwatch.stop();

        // Verify reasonable render time (less than 1 second)
        expect(stopwatch.elapsedMilliseconds, lessThan(1000));
        expect(find.byType(SalesOrderDetailsScreen), findsOneWidget);
      });
    });
  });
}
