import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../entities/customer.dart';
import '../repositories/customer_repository.dart';

class CheckIfSyncNeeded {
  final CustomerRepository repository;

  CheckIfSyncNeeded(this.repository);

  Future<Either<Failure, bool>> call() async {
    return await repository.checkIfSyncNeeded();
  }
}

class GetCustomerByMobileNumber {
  final CustomerRepository repository;

  GetCustomerByMobileNumber(this.repository);

  Future<Either<Failure, Customer?>> call(String mobileNumber) async {
    return await repository.getCustomerByMobileNumber(mobileNumber);
  }
}

class GetCustomerByCustomerId {
  final CustomerRepository repository;

  GetCustomerByCustomerId(this.repository);

  Future<Either<Failure, Customer?>> call(String customerId) async {
    return await repository.getCustomerByCustomerId(customerId);
  }
}

class SyncCustomerByCustomerId {
  final CustomerRepository repository;

  SyncCustomerByCustomerId(this.repository);

  Future<Either<Failure, Customer?>> call(String customerId) async {
    return await repository.syncCustomerByCustomerId(customerId);
  }
}
