# Firebase Analytics Dashboard Setup Guide

## Setting Up User Analytics in Firebase

1. **Enable Google Analytics for your Firebase project**

   - Go to Firebase Console > Project Settings
   - Make sure Google Analytics is enabled

2. **Enable BigQuery Export**

   - Go to Firebase Console > Analytics > Dashboard
   - Click "Link to BigQuery" in the sidebar
   - Follow the setup instructions

3. **Create Custom Audience Definitions**

   - Go to Firebase Console > Analytics > Audiences
   - Create the following audiences:
     - **Power Users**: Users with 10+ sessions in the last 30 days
     - **New Users**: First session in the last 7 days
     - **Inactive Users**: No sessions in the last 14 days
     - **Feature Explorers**: Used 5+ different features

4. **Create Custom Dashboards**
   - Go to Firebase Console > Analytics > Dashboard
   - Click "Create custom dashboard"
   - Create a "User Engagement" dashboard with these cards:
     - Daily Active Users
     - Session Duration Distribution
     - Feature Usage Breakdown
     - User Retention

## BigQuery Queries for User Analytics

Once your data is flowing to BigQuery, you can run these queries:

### Top 10 Users by Usage Time

```sql
SELECT
  user_id,
  (SELECT value.string_value FROM UNNEST(user_properties) WHERE key = 'user_name') AS user_name,
  SUM(CAST((SELECT value.string_value FROM UNNEST(event_params) WHERE key = 'duration_ms') AS INT64))/1000/60 AS total_minutes_spent
FROM
  `your-project-id.analytics_XXXXXX.events_*`
WHERE
  event_name = 'engagement_user'
  AND _TABLE_SUFFIX BETWEEN FORMAT_DATE('%Y%m%d', DATE_SUB(CURRENT_DATE(), INTERVAL 30 DAY))
  AND FORMAT_DATE('%Y%m%d', CURRENT_DATE())
GROUP BY
  user_id, user_name
ORDER BY
  total_minutes_spent DESC
LIMIT 10;
```

### Most Used Features

```sql
SELECT
  (SELECT value.string_value FROM UNNEST(event_params) WHERE key = 'feature_name') AS feature_name,
  COUNT(*) AS usage_count
FROM
  `your-project-id.analytics_XXXXXX.events_*`
WHERE
  event_name = 'feature_used'
  AND _TABLE_SUFFIX BETWEEN FORMAT_DATE('%Y%m%d', DATE_SUB(CURRENT_DATE(), INTERVAL 30 DAY))
  AND FORMAT_DATE('%Y%m%d', CURRENT_DATE())
GROUP BY
  feature_name
ORDER BY
  usage_count DESC
LIMIT 20;
```

### Average Session Duration by User Type

```sql
SELECT
  (SELECT value.string_value FROM UNNEST(user_properties) WHERE key = 'user_type') AS user_type,
  AVG(CAST((SELECT value.string_value FROM UNNEST(event_params) WHERE key = 'duration_ms') AS INT64))/1000 AS avg_session_seconds
FROM
  `your-project-id.analytics_XXXXXX.events_*`
WHERE
  event_name = 'engagement_user'
  AND _TABLE_SUFFIX BETWEEN FORMAT_DATE('%Y%m%d', DATE_SUB(CURRENT_DATE(), INTERVAL 30 DAY))
  AND FORMAT_DATE('%Y%m%d', CURRENT_DATE())
GROUP BY
  user_type
ORDER BY
  avg_session_seconds DESC;
```
