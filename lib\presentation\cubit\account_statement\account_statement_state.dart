import 'package:aquapartner/domain/entities/customer.dart';
import 'package:equatable/equatable.dart';
import '../../../domain/entities/account_statement/account_statement.dart';
import '../../../domain/entities/account_statement/account_statement_entity.dart';

abstract class AccountStatementState extends Equatable {
  const AccountStatementState();

  @override
  List<Object?> get props => [];
}

class AccountStatementInitial extends AccountStatementState {}

class AccountStatementLoading extends AccountStatementState {
  final AccountStatement? previousStatement;
  final DateTime? lastSyncTime;
  final bool hasPreviousData;
  final bool isLoadingFromCache;

  const AccountStatementLoading({
    this.previousStatement,
    this.lastSyncTime,
    this.hasPreviousData = false,
    this.isLoadingFromCache = true,
  });

  @override
  List<Object?> get props => [
    previousStatement,
    lastSyncTime,
    hasPreviousData,
    isLoadingFromCache,
  ];
}

class AccountStatementLoaded extends AccountStatementState {
  final Customer customer;
  final AccountStatement statement;
  final bool isSyncing;
  final DateTime? lastSyncTime;
  final bool hasError;
  final String? errorMessage;
  final List<AccountStatementEntity>? filteredEntries;
  final DateTimeRange? dateRange;
  final String selectedFilter;
  final bool hasEmptyStatement;
  final List<String> periods;
  final bool isFromCache;
  final bool isCacheStale;
  final bool isBackgroundSyncInProgress;

  const AccountStatementLoaded({
    required this.customer,
    required this.statement,
    required this.isSyncing,
    this.lastSyncTime,
    this.hasError = false,
    this.errorMessage,
    this.filteredEntries,
    this.dateRange,
    required this.selectedFilter,
    this.hasEmptyStatement = false,
    required this.periods,
    this.isFromCache = true,
    this.isCacheStale = false,
    this.isBackgroundSyncInProgress = false,
  });

  @override
  List<Object?> get props => [
    customer,
    statement,
    isSyncing,
    lastSyncTime,
    hasError,
    errorMessage,
    filteredEntries,
    dateRange,
    selectedFilter,
    hasEmptyStatement,
    periods,
    isFromCache,
    isCacheStale,
    isBackgroundSyncInProgress,
  ];

  AccountStatementLoaded copyWith({
    Customer? customer,
    AccountStatement? statement,
    bool? isSyncing,
    DateTime? lastSyncTime,
    bool? hasError,
    String? errorMessage,
    List<AccountStatementEntity>? filteredEntries,
    DateTimeRange? dateRange,
    String? selectedFilter,
    bool? hasEmptyStatement,
    List<String>? periods,
    bool? isFromCache,
    bool? isCacheStale,
    bool? isBackgroundSyncInProgress,
  }) {
    return AccountStatementLoaded(
      customer: customer ?? this.customer,
      statement: statement ?? this.statement,
      isSyncing: isSyncing ?? this.isSyncing,
      lastSyncTime: lastSyncTime ?? this.lastSyncTime,
      hasError: hasError ?? this.hasError,
      errorMessage: errorMessage ?? this.errorMessage,
      filteredEntries: filteredEntries ?? this.filteredEntries,
      dateRange: dateRange ?? this.dateRange,
      selectedFilter: selectedFilter ?? this.selectedFilter,
      hasEmptyStatement: hasEmptyStatement ?? this.hasEmptyStatement,
      periods: periods ?? this.periods,
      isFromCache: isFromCache ?? this.isFromCache,
      isCacheStale: isCacheStale ?? this.isCacheStale,
      isBackgroundSyncInProgress:
          isBackgroundSyncInProgress ?? this.isBackgroundSyncInProgress,
    );
  }
}

class AccountStatementError extends AccountStatementState {
  final String message;

  const AccountStatementError(this.message);

  @override
  List<Object> get props => [message];
}

class DateTimeRange extends Equatable {
  final DateTime startDate;
  final DateTime endDate;

  const DateTimeRange({required this.startDate, required this.endDate});

  @override
  List<Object?> get props => [startDate, endDate];
}
