import 'package:logger/logger.dart';
import 'package:firebase_auth/firebase_auth.dart';

class AppLogger {
  final Logger _logger;

  AppLogger()
    : _logger = Logger(
        printer: PrettyPrinter(
          methodCount: 0,
          errorMethodCount: 8,
          lineLength: 120,
          colors: true,
          printEmojis: true,
          dateTimeFormat: DateTimeFormat.dateAndTime,
        ),
      );

  void d(String message) {
    _logger.d(message);
  }

  void i(String message) {
    _logger.i(message);
  }

  void w(String message) {
    _logger.w(message);
  }

  void e(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.e(message, error: error, stackTrace: stackTrace);
  }

  void enableFirebaseVerboseLogging() {
    FirebaseAuth.instance.setLanguageCode("en");
    // Enable debug mode for Firebase Auth
    FirebaseAuth.instance.setSettings(
      appVerificationDisabledForTesting: false,
      phoneNumber: null,
      smsCode: null,
      forceRecaptchaFlow: false,
    );
  }
}
