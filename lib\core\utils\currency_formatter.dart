import 'package:intl/intl.dart';

class CurrencyFormatter {
  /// Formats a number as Indian Rupee (INR) with proper comma placement
  ///
  /// Examples:
  /// - 1234 becomes ₹1,234
  /// - 12345 becomes ₹12,345
  /// - 123456 becomes ₹1,23,456 (note the Indian-style comma placement)
  /// - 1234567 becomes ₹12,34,567
  /// - 1234567.89 becomes ₹12,34,567.89
  static String formatAsINR(
    num amount, {
    bool showSymbol = true,
    int decimalPlaces = 2,
  }) {
    // Create a NumberFormat with Indian locale and currency pattern
    final formatter = NumberFormat.currency(
      locale: 'en_IN',
      symbol: showSymbol ? '₹' : '',
      decimalDigits: decimalPlaces,
    );

    return formatter.format(amount);
  }

  /// Formats a number as Indian Rupee (INR) with abbreviated lakhs and crores
  ///
  /// Examples:
  /// - 10000 becomes ₹1L (1 lakh)
  /// - 10000 becomes ₹10L (10 lakhs)
  /// - 10000 becomes ₹1Cr (1 crore)
  static String formatAsINRAbbreviated(num amount, {bool showSymbol = true}) {
    String symbol = showSymbol ? '₹' : '';

    if (amount >= 10000) {
      // Convert to crores (1 crore = 10 million)
      return '$symbol${(amount / 10000).toStringAsFixed(2)}Cr';
    } else if (amount >= 10000) {
      // Convert to lakhs (1 lakh = 100 thousand)
      return '$symbol${(amount / 10000).toStringAsFixed(2)}L';
    } else if (amount >= 1000) {
      // Convert to thousands
      return '$symbol${(amount / 1000).toStringAsFixed(2)}K';
    } else {
      return '$symbol${amount.toStringAsFixed(2)}';
    }
  }

  /// Formats a number as Indian Rupee (INR) with proper comma placement and words
  ///
  /// Examples:
  /// - 10000 becomes ₹1,00,000 (1 Lakh)
  /// - 10000 becomes ₹1,00,00,000 (1 Crore)
  static String formatAsINRWithWords(num amount, {bool showSymbol = true}) {
    String formattedAmount = formatAsINR(amount, showSymbol: showSymbol);
    String words = '';

    if (amount >= 10000) {
      words = ' (${(amount / 10000).toStringAsFixed(2)} Crore)';
    } else if (amount >= 10000) {
      words = ' (${(amount / 10000).toStringAsFixed(2)} Lakh)';
    }

    return '$formattedAmount$words';
  }

  /// Calculates the discounted price based on a subtotal and discount percentage
  ///
  /// Parameters:
  /// - subTotal: The original price (can be num or String)
  /// - discount: The discount percentage (can be num or String)
  ///
  /// Returns:
  /// - The price after applying the discount
  ///
  /// Examples:
  /// - calculateDiscountedPrice(100, 20) returns 80 (20% off 100)
  /// - calculateDiscountedPrice("₹100", "20%") returns 80
  static double calculateDiscountedPrice({
    required dynamic subTotal,
    required dynamic discount,
  }) {
    // Handle string inputs by extracting only numbers
    if (subTotal is String) {
      subTotal =
          double.tryParse(subTotal.replaceAll(RegExp(r'[^0-9.]'), '')) ?? 0;
    }

    if (discount is String) {
      discount =
          double.tryParse(discount.replaceAll(RegExp(r'[^0-9.]'), '')) ?? 0;
    }

    // Convert to double to ensure proper calculation
    double subtotalValue = subTotal is num ? subTotal.toDouble() : 0;
    double discountValue = discount is num ? discount.toDouble() : 0;

    // Calculate the discounted price (original price - discount amount)
    double discountedPrice = subtotalValue * (1 - discountValue / 100);

    // Return 0 if the result is not a valid number
    return discountedPrice.isNaN ? 0 : discountedPrice;
  }

  static calculateOffer({required dynamic subTotal, required dynamic total}) {
    // Handle string inputs by extracting only numbers
    if (subTotal is String) {
      subTotal =
          double.tryParse(subTotal.replaceAll(RegExp(r'[^0-9.]'), '')) ?? 0;
    }

    if (total is String) {
      total = double.tryParse(total.replaceAll(RegExp(r'[^0-9.]'), '')) ?? 0;
    }

    // Convert to double to ensure proper calculation
    double subtotalValue = subTotal is num ? subTotal.toDouble() : 0;
    double totalValue = total is num ? total.toDouble() : 0;

    // Handle edge cases to prevent division by zero and invalid calculations
    if (subtotalValue == 0 || subtotalValue.isNaN || subtotalValue.isInfinite) {
      return 0;
    }

    if (totalValue.isNaN || totalValue.isInfinite) {
      return 0;
    }

    // Calculate the offer percentage
    double offerPercentage = (1 - totalValue / subtotalValue) * 100;

    // Check if the result is valid before rounding
    if (offerPercentage.isNaN || offerPercentage.isInfinite) {
      return 0;
    }

    // Round to nearest integer
    int offer = offerPercentage.round();

    return offer;
  }
}
