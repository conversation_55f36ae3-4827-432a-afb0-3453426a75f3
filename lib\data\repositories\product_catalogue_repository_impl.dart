import 'package:dartz/dartz.dart';
import '../../core/error/exceptions.dart';
import '../../core/error/failures.dart';
import '../../core/network/network_info.dart';
import '../../core/utils/logger.dart';
import '../../domain/entities/product_catalogue.dart';
import '../../domain/repositories/product_catalogue_repository.dart';
import '../datasources/local/product_catalogue_local_datasource.dart';
import '../datasources/remote/product_catalogue_remote_datasource.dart';

class ProductCatalogueRepositoryImpl implements ProductCatalogueRepository {
  final ProductCatalogueRemoteDataSource remoteDataSource;
  final ProductCatalogueLocalDataSource localDataSource;
  final NetworkInfo networkInfo;
  final AppLogger logger;

  ProductCatalogueRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
    required this.networkInfo,
    required this.logger,
  });

  @override
  Future<Either<Failure, List<ProductCatalogue>>> getProductCatalogues() async {
    logger.i("ProductCatalogueRepositoryImpl: Getting product catalogues");

    try {
      // First check if we have data in local storage
      final hasLocalData = await localDataSource.hasProductCatalogues();

      if (hasLocalData) {
        logger.i(
          "Found product catalogues in local storage, returning cached data",
        );
        final localProductCatalogues =
            await localDataSource.getProductCatalogues();
        return Right(
          localProductCatalogues.map((pc) => pc.toDomain()).toList(),
        );
      } else {
        logger.i(
          "No product catalogues in local storage, fetching from remote",
        );
        return await _getRemoteProductCatalogues();
      }
    } catch (e) {
      logger.e("Unexpected error getting product catalogues", e);
      return Left(CacheFailure());
    }
  }

  @override
  Future<Either<Failure, List<ProductCatalogue>>>
  syncProductCatalogues() async {
    logger.i("ProductCatalogueRepositoryImpl: Syncing product catalogues");

    if (await networkInfo.isConnected) {
      return await _getRemoteProductCatalogues();
    } else {
      logger.w("Cannot sync product catalogues: No internet connection");
      return Left(ServerFailure());
    }
  }

  @override
  Future<Either<Failure, ProductCatalogue?>> getProductCatalogueByName(
    String name,
  ) async {
    logger.i(
      "ProductCatalogueRepositoryImpl: Getting product catalogue by name: $name",
    );

    try {
      // First try to get from local cache
      final cachedProductCatalogue = await localDataSource
          .getProductCatalogueByName(name);

      if (cachedProductCatalogue != null) {
        logger.i("Found product catalogue with name: $name in cache");
        return Right(cachedProductCatalogue.toDomain());
      }

      // If not in cache and we have network, try to get from remote
      if (await networkInfo.isConnected) {
        logger.i("Product catalogue not in cache, fetching from remote");
        try {
          final remoteProductCatalogues =
              await remoteDataSource.getProductCatalogues();

          // Find the product catalogue with the matching name
          final matchingProductCatalogues =
              remoteProductCatalogues.where((pc) => pc.name == name).toList();

          if (matchingProductCatalogues.isNotEmpty) {
            final matchingProductCatalogue = matchingProductCatalogues.first;
            // Save to cache
            await localDataSource.saveProductCatalogues([
              matchingProductCatalogue,
            ]);
            return Right(matchingProductCatalogue.toDomain());
          } else {
            logger.w("Product catalogue not found on server");
            return const Right(null);
          }
        } on ServerException catch (_) {
          return Left(ServerFailure());
        }
      } else {
        logger.w("No network and product catalogue not in cache");
        return const Right(null);
      }
    } catch (e) {
      logger.e("Unexpected error getting product catalogue by name", e);
      return Left(ServerFailure());
    }
  }

  Future<Either<Failure, List<ProductCatalogue>>>
  _getRemoteProductCatalogues() async {
    try {
      logger.i("Getting product catalogues from remote");

      final remoteProductCatalogues =
          await remoteDataSource.getProductCatalogues();

      // Save to local storage
      if (remoteProductCatalogues.isNotEmpty) {
        await localDataSource.saveProductCatalogues(remoteProductCatalogues);
      } else {
        logger.w("No product catalogues found on remote server");
      }

      return Right(remoteProductCatalogues.map((pc) => pc.toDomain()).toList());
    } on ServerException catch (_) {
      return Left(ServerFailure());
    } catch (e) {
      return Left(ServerFailure());
    }
  }
}
