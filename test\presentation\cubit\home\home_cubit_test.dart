import 'package:aquapartner/core/error/failures.dart';
import 'package:aquapartner/core/services/analytics_service.dart';
import 'package:aquapartner/core/services/feature_usage_tracker.dart';
import 'package:aquapartner/core/utils/logger.dart';
import 'package:aquapartner/domain/entities/customer.dart';
import 'package:aquapartner/domain/usecases/customer_usercases.dart';
import 'package:aquapartner/domain/usecases/user_usecases.dart';
import 'package:aquapartner/presentation/cubit/home/<USER>';
import 'package:aquapartner/presentation/cubit/home/<USER>';
import 'package:bloc_test/bloc_test.dart';
import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// Mock classes
class MockCheckIfSyncNeeded extends Mock implements CheckIfSyncNeeded {}

class MockGetCustomerByMobileNumber extends Mock
    implements GetCustomerByMobileNumber {}

class MockSyncCustomerByCustomerId extends Mock
    implements SyncCustomerByCustomerId {}

class MockGetUserUseCase extends Mock implements GetUserUseCase {}

class MockAppLogger extends Mock implements AppLogger {}

class MockAnalyticsService extends Mock implements AnalyticsService {}

class MockFeatureUsageTracker extends Mock implements FeatureUsageTracker {}

// Test data
final testCustomer = Customer(
  customerId: 'test_customer_123',
  customerName: 'Test Customer',
  email: '<EMAIL>',
  mobileNumber: '+************',
  companyName: 'Test Company',
  gstNo: 'TEST123456789',
  businessVertical: 'Agriculture',
  customerCode: 'TC123',
  billingAddress: 'Test Address',
);

void main() {
  group('HomeCubit Tests', () {
    late HomeCubit homeCubit;
    late MockCheckIfSyncNeeded mockCheckIfSyncNeeded;
    late MockGetCustomerByMobileNumber mockGetCustomerByMobileNumber;
    late MockSyncCustomerByCustomerId mockSyncCustomerByCustomerId;
    late MockGetUserUseCase mockGetUserUseCase;
    late MockAppLogger mockAppLogger;
    late MockAnalyticsService mockAnalyticsService;
    late MockFeatureUsageTracker mockFeatureUsageTracker;

    setUp(() {
      mockCheckIfSyncNeeded = MockCheckIfSyncNeeded();
      mockGetCustomerByMobileNumber = MockGetCustomerByMobileNumber();
      mockSyncCustomerByCustomerId = MockSyncCustomerByCustomerId();
      mockGetUserUseCase = MockGetUserUseCase();
      mockAppLogger = MockAppLogger();
      mockAnalyticsService = MockAnalyticsService();
      mockFeatureUsageTracker = MockFeatureUsageTracker();

      // Set up default mock behaviors
      when(
        () => mockCheckIfSyncNeeded(),
      ).thenAnswer((_) async => const Right(false));
      when(
        () => mockFeatureUsageTracker.trackFeature(any()),
      ).thenAnswer((_) async {});
      when(
        () => mockAnalyticsService.logEvent(
          name: any(named: 'name'),
          parameters: any(named: 'parameters'),
        ),
      ).thenAnswer((_) async {});
      when(
        () => mockAnalyticsService.logScreenView(
          screenName: any(named: 'screenName'),
          screenClass: any(named: 'screenClass'),
        ),
      ).thenAnswer((_) async {});
      when(
        () => mockAnalyticsService.logFeatureUsage(any()),
      ).thenAnswer((_) async {});
      when(
        () => mockAnalyticsService.logScreenDuration(
          screenName: any(named: 'screenName'),
          durationMs: any(named: 'durationMs'),
          screenClass: any(named: 'screenClass'),
        ),
      ).thenAnswer((_) async {});
      when(() => mockAppLogger.d(any())).thenReturn(null);

      homeCubit = HomeCubit(
        mockCheckIfSyncNeeded,
        mockGetCustomerByMobileNumber,
        mockSyncCustomerByCustomerId,
        mockGetUserUseCase,
        mockAppLogger,
        mockAnalyticsService,
        mockFeatureUsageTracker,
      );
    });

    tearDown(() {
      homeCubit.close();
    });

    group('Initial State', () {
      test('should have initial state with correct default values', () {
        expect(homeCubit.state, isA<HomeState>());
        expect(homeCubit.state.customer, isNull);
        expect(homeCubit.state.phoneNumber, isNull);
        expect(homeCubit.state.viewType, equals(ViewType.dashboard));
        expect(homeCubit.state.isLoading, isFalse);
        expect(homeCubit.state.isSyncing, isFalse);
        expect(homeCubit.state.errorMessage, isNull);
        expect(homeCubit.state.isNetworkError, isFalse);
      });
    });

    group('Load Data', () {
      blocTest<HomeCubit, HomeState>(
        'should emit loading state then success state when loading data',
        build: () => homeCubit,
        act: (cubit) => cubit.loadData(),
        expect:
            () => [
              predicate<HomeState>(
                (state) =>
                    state.isLoading == true && state.errorMessage == null,
              ),
              predicate<HomeState>(
                (state) =>
                    state.isLoading == false &&
                    state.viewType == ViewType.dashboard,
              ),
            ],
      );

      blocTest<HomeCubit, HomeState>(
        'should handle errors during data loading',
        build: () {
          // Override loadData to throw an error for testing
          return homeCubit;
        },
        act: (cubit) async {
          // Simulate an error by calling a method that might fail
          try {
            await cubit.loadData();
          } catch (e) {
            // This is expected for error testing
          }
        },
        expect:
            () => [
              predicate<HomeState>((state) => state.isLoading == true),
              predicate<HomeState>((state) => state.isLoading == false),
            ],
      );
    });

    group('Fetch Customer by Mobile Number', () {
      const testMobileNumber = '+************';

      blocTest<HomeCubit, HomeState>(
        'should emit [loading, success] when fetching customer successfully',
        build: () {
          when(
            () => mockGetCustomerByMobileNumber(testMobileNumber),
          ).thenAnswer((_) async => Right(testCustomer));
          return homeCubit;
        },
        act: (cubit) => cubit.fetchCustomerByMobileNumber(testMobileNumber),
        expect:
            () => [
              predicate<HomeState>(
                (state) =>
                    state.isLoading == true &&
                    state.phoneNumber == testMobileNumber &&
                    state.errorMessage == null,
              ),
              predicate<HomeState>(
                (state) =>
                    state.isLoading == false &&
                    state.customer == testCustomer &&
                    state.phoneNumber == testMobileNumber,
              ),
            ],
        verify: (_) {
          verify(
            () => mockGetCustomerByMobileNumber(testMobileNumber),
          ).called(1);
        },
      );

      blocTest<HomeCubit, HomeState>(
        'should emit [loading, error] when customer not found',
        build: () {
          when(
            () => mockGetCustomerByMobileNumber(testMobileNumber),
          ).thenAnswer((_) async => const Right(null));
          return homeCubit;
        },
        act: (cubit) => cubit.fetchCustomerByMobileNumber(testMobileNumber),
        expect:
            () => [
              predicate<HomeState>(
                (state) =>
                    state.isLoading == true &&
                    state.phoneNumber == testMobileNumber,
              ),
              predicate<HomeState>(
                (state) =>
                    state.isLoading == false &&
                    state.errorMessage == 'Customer not found',
              ),
            ],
      );

      blocTest<HomeCubit, HomeState>(
        'should emit [loading, error] when network failure occurs',
        build: () {
          when(
            () => mockGetCustomerByMobileNumber(testMobileNumber),
          ).thenAnswer((_) async => Left(NetworkFailure()));
          return homeCubit;
        },
        act: (cubit) => cubit.fetchCustomerByMobileNumber(testMobileNumber),
        expect:
            () => [
              predicate<HomeState>(
                (state) =>
                    state.isLoading == true &&
                    state.phoneNumber == testMobileNumber,
              ),
              predicate<HomeState>(
                (state) =>
                    state.isLoading == false &&
                    state.errorMessage ==
                        'Network error occurred. Please check your internet connection' &&
                    state.isNetworkError == true,
              ),
            ],
      );

      blocTest<HomeCubit, HomeState>(
        'should emit [loading, error] when server failure occurs',
        build: () {
          when(
            () => mockGetCustomerByMobileNumber(testMobileNumber),
          ).thenAnswer((_) async => Left(ServerFailure()));
          return homeCubit;
        },
        act: (cubit) => cubit.fetchCustomerByMobileNumber(testMobileNumber),
        expect:
            () => [
              predicate<HomeState>((state) => state.isLoading == true),
              predicate<HomeState>(
                (state) =>
                    state.isLoading == false &&
                    state.errorMessage == 'Check your internet connection',
              ),
            ],
      );

      blocTest<HomeCubit, HomeState>(
        'should emit [loading, error] when cache failure occurs',
        build: () {
          when(
            () => mockGetCustomerByMobileNumber(testMobileNumber),
          ).thenAnswer((_) async => Left(CacheFailure()));
          return homeCubit;
        },
        act: (cubit) => cubit.fetchCustomerByMobileNumber(testMobileNumber),
        expect:
            () => [
              predicate<HomeState>((state) => state.isLoading == true),
              predicate<HomeState>(
                (state) =>
                    state.isLoading == false &&
                    state.errorMessage == 'Try again later',
              ),
            ],
      );
    });

    group('Sync Customer', () {
      blocTest<HomeCubit, HomeState>(
        'should emit [syncing, success] when sync is successful',
        build: () {
          when(
            () => mockSyncCustomerByCustomerId(testCustomer.customerId),
          ).thenAnswer((_) async => Right(testCustomer));
          return homeCubit;
        },
        act: (cubit) => cubit.syncCustomer(testCustomer),
        expect:
            () => [
              predicate<HomeState>(
                (state) =>
                    state.isSyncing == true &&
                    state.customer == testCustomer &&
                    state.errorMessage == null,
              ),
              predicate<HomeState>(
                (state) =>
                    state.isSyncing == false && state.customer == testCustomer,
              ),
            ],
        verify: (_) {
          verify(
            () => mockSyncCustomerByCustomerId(testCustomer.customerId),
          ).called(1);
        },
      );

      blocTest<HomeCubit, HomeState>(
        'should emit [syncing, error] when sync fails with network error',
        build: () {
          when(
            () => mockSyncCustomerByCustomerId(testCustomer.customerId),
          ).thenAnswer((_) async => Left(NetworkFailure()));
          return homeCubit;
        },
        act: (cubit) => cubit.syncCustomer(testCustomer),
        expect:
            () => [
              predicate<HomeState>(
                (state) =>
                    state.isSyncing == true && state.customer == testCustomer,
              ),
              predicate<HomeState>(
                (state) =>
                    state.isSyncing == false &&
                    state.errorMessage ==
                        'Network error occurred. Please check your internet connection' &&
                    state.isNetworkError == true,
              ),
            ],
      );

      blocTest<HomeCubit, HomeState>(
        'should emit error when customer not found on server',
        build: () {
          when(
            () => mockSyncCustomerByCustomerId(testCustomer.customerId),
          ).thenAnswer((_) async => const Right(null));
          return homeCubit;
        },
        act: (cubit) => cubit.syncCustomer(testCustomer),
        expect:
            () => [
              predicate<HomeState>((state) => state.isSyncing == true),
              predicate<HomeState>(
                (state) =>
                    state.isSyncing == false &&
                    state.errorMessage == 'Customer not found on server',
              ),
            ],
      );
    });

    group('Navigation Methods', () {
      blocTest<HomeCubit, HomeState>(
        'should update view type to dashboard',
        build: () => homeCubit,
        act: (cubit) => cubit.gotoDashboardView(),
        expect:
            () => [
              predicate<HomeState>(
                (state) =>
                    state.viewType == ViewType.dashboard &&
                    state.errorMessage == null,
              ),
            ],
      );

      blocTest<HomeCubit, HomeState>(
        'should update view type to account statement',
        build: () => homeCubit,
        act: (cubit) => cubit.gotoAccountStatementView(),
        expect:
            () => [
              predicate<HomeState>(
                (state) =>
                    state.viewType == ViewType.accountStatement &&
                    state.errorMessage == null,
              ),
            ],
      );

      blocTest<HomeCubit, HomeState>(
        'should update view type to billing and payments',
        build: () => homeCubit,
        act: (cubit) => cubit.gotoBillingAndPaymentsView(),
        expect:
            () => [
              predicate<HomeState>(
                (state) =>
                    state.viewType == ViewType.billingAndPayments &&
                    state.errorMessage == null,
              ),
            ],
      );

      blocTest<HomeCubit, HomeState>(
        'should update view type to my farmers',
        build: () => homeCubit,
        act: (cubit) => cubit.gotoMyFarmersView(),
        expect:
            () => [
              predicate<HomeState>(
                (state) =>
                    state.viewType == ViewType.myFarmers &&
                    state.errorMessage == null,
              ),
            ],
      );
    });

    group('State Management', () {
      test('should create new state with copyWith method', () {
        final initialState = HomeState();
        final newState = initialState.copyWith(
          customer: testCustomer,
          phoneNumber: '+************',
          viewType: ViewType.billingAndPayments,
          isLoading: true,
          isSyncing: true,
          errorMessage: 'Test error',
          isNetworkError: true,
        );

        expect(newState.customer, equals(testCustomer));
        expect(newState.phoneNumber, equals('+************'));
        expect(newState.viewType, equals(ViewType.billingAndPayments));
        expect(newState.isLoading, isTrue);
        expect(newState.isSyncing, isTrue);
        expect(newState.errorMessage, equals('Test error'));
        expect(newState.isNetworkError, isTrue);
      });

      test('should clear error message when set to null', () {
        final stateWithError = HomeState(errorMessage: 'Some error');
        final clearedState = stateWithError.copyWith(errorMessage: null);

        expect(clearedState.errorMessage, isNull);
      });
    });

    group('Edge Cases', () {
      test('should handle empty mobile number', () async {
        when(
          () => mockGetCustomerByMobileNumber(''),
        ).thenAnswer((_) async => const Right(null));
        await homeCubit.fetchCustomerByMobileNumber('');
        expect(homeCubit.state.phoneNumber, equals(''));
      });

      test('should handle multiple rapid navigation calls', () {
        homeCubit.gotoDashboardView();
        homeCubit.gotoAccountStatementView();
        homeCubit.gotoBillingAndPaymentsView();
        homeCubit.gotoMyFarmersView();

        expect(homeCubit.state.viewType, equals(ViewType.myFarmers));
      });

      blocTest<HomeCubit, HomeState>(
        'should handle concurrent fetch and sync operations',
        build: () {
          when(
            () => mockGetCustomerByMobileNumber(any()),
          ).thenAnswer((_) async => Right(testCustomer));
          when(
            () => mockSyncCustomerByCustomerId(any()),
          ).thenAnswer((_) async => Right(testCustomer));
          return homeCubit;
        },
        act: (cubit) async {
          cubit.fetchCustomerByMobileNumber('+************');
          await Future.delayed(Duration(milliseconds: 50));
          cubit.syncCustomer(testCustomer);
        },
        expect:
            () => [
              predicate<HomeState>((state) => state.isLoading == true),
              predicate<HomeState>(
                (state) => state.isLoading == false && state.customer != null,
              ),
              predicate<HomeState>((state) => state.isSyncing == true),
              predicate<HomeState>((state) => state.isSyncing == false),
            ],
      );
    });
  });
}
