import 'package:objectbox/objectbox.dart';
import '../../../domain/entities/sales_order/sales_order_item.dart';
import 'sales_order_model.dart';

@Entity()
class SalesOrderItemModel {
  @Id()
  int id = 0;

  final String itemId;
  String? salesOrderIdValue;
  final String productId;

  @Property(type: PropertyType.date)
  final DateTime createdTime;

  final String entityDiscountPercent;
  final String hsnSac;
  final String invoicedQuantityCancelled;
  final String itemName;
  final double itemPrice;

  @Property(type: PropertyType.date)
  final DateTime lastModifiedTime;

  final String manuallyFulfilledQuantity;
  final String nonPackageQuantity;
  final String placeOfSupply;
  final String quantityDelivered;
  final String quantityDropshipped;
  final String quantityPacked;
  final String salesVertices;
  final String sno;
  final double total;

  final salesOrder = ToOne<SalesOrderModel>();

  @Transient()
  String get getSalesOrderId {
    if (salesOrderIdValue != null) return salesOrderIdValue!;
    if (salesOrder.target != null) return salesOrder.target!.salesOrderId;
    return '';
  }

  SalesOrderItemModel({
    required this.itemId,
    String? salesOrderId,
    required this.productId,
    required this.createdTime,
    required this.entityDiscountPercent,
    required this.hsnSac,
    required this.invoicedQuantityCancelled,
    required this.itemName,
    required this.itemPrice,
    required this.lastModifiedTime,
    required this.manuallyFulfilledQuantity,
    required this.nonPackageQuantity,
    required this.placeOfSupply,
    required this.quantityDelivered,
    required this.quantityDropshipped,
    required this.quantityPacked,
    required this.salesVertices,
    required this.sno,
    required this.total,
  }) {
    salesOrderIdValue = salesOrderId;
  }

  // Convert model to entity
  SalesOrderItem toEntity() {
    return SalesOrderItem(
      id: id,
      itemId: itemId,
      salesOrderId: getSalesOrderId,
      productId: productId,
      createdTime: createdTime,
      entityDiscountPercent: entityDiscountPercent,
      hsnSac: hsnSac,
      invoicedQuantityCancelled: invoicedQuantityCancelled,
      itemName: itemName,
      itemPrice: itemPrice,
      lastModifiedTime: lastModifiedTime,
      manuallyFulfilledQuantity: manuallyFulfilledQuantity,
      nonPackageQuantity: nonPackageQuantity,
      placeOfSupply: placeOfSupply,
      quantityDelivered: quantityDelivered,
      quantityDropshipped: quantityDropshipped,
      quantityPacked: quantityPacked,
      salesVertices: salesVertices,
      sno: sno,
      total: total,
    );
  }

  // Create model from entity
  static SalesOrderItemModel fromEntity(SalesOrderItem entity) {
    return SalesOrderItemModel(
      itemId: entity.itemId,
      salesOrderId: entity.salesOrderId,
      productId: entity.productId,
      createdTime: entity.createdTime,
      entityDiscountPercent: entity.entityDiscountPercent,
      hsnSac: entity.hsnSac,
      invoicedQuantityCancelled: entity.invoicedQuantityCancelled,
      itemName: entity.itemName,
      itemPrice: entity.itemPrice,
      lastModifiedTime: entity.lastModifiedTime,
      manuallyFulfilledQuantity: entity.manuallyFulfilledQuantity,
      nonPackageQuantity: entity.nonPackageQuantity,
      placeOfSupply: entity.placeOfSupply,
      quantityDelivered: entity.quantityDelivered,
      quantityDropshipped: entity.quantityDropshipped,
      quantityPacked: entity.quantityPacked,
      salesVertices: entity.salesVertices,
      sno: entity.sno,
      total: entity.total,
    );
  }

  // Convert model to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'itemId': itemId,
      'salesOrderId': getSalesOrderId,
      'productId': productId,
      'createdTime': createdTime.toIso8601String(),
      'entityDiscountPercent': entityDiscountPercent,
      'hsnSac': hsnSac,
      'invoicedQuantityCancelled': invoicedQuantityCancelled,
      'itemName': itemName,
      'itemPrice': itemPrice,
      'lastModifiedTime': lastModifiedTime.toIso8601String(),
      'manuallyFulfilledQuantity': manuallyFulfilledQuantity,
      'nonPackageQuantity': nonPackageQuantity,
      'placeOfSupply': placeOfSupply,
      'quantityDelivered': quantityDelivered,
      'quantityDropshipped': quantityDropshipped,
      'quantityPacked': quantityPacked,
      'salesVertices': salesVertices,
      'sno': sno,
      'total': total,
    };
  }

  // Create model from JSON
  factory SalesOrderItemModel.fromJson(Map<String, dynamic> json) {
    return SalesOrderItemModel(
      itemId: json['_id'] ?? json['itemId'] ?? '',
      salesOrderId: json['salesOrderId'] ?? '',
      productId: json['productId'] ?? '',
      createdTime:
          json['createdTime'] != null
              ? DateTime.parse(json['createdTime'])
              : DateTime.now(),
      entityDiscountPercent: json['entityDiscountPercent']?.toString() ?? '0',
      hsnSac: json['hsnSac']?.toString() ?? '',
      invoicedQuantityCancelled:
          json['invoicedQuantityCancelled']?.toString() ?? '0',
      itemName: json['itemName']?.toString() ?? '',
      itemPrice: (json['itemPrice'] ?? 0).toDouble(),
      lastModifiedTime:
          json['lastModifiedTime'] != null
              ? DateTime.parse(json['lastModifiedTime'])
              : DateTime.now(),
      manuallyFulfilledQuantity:
          json['manuallyFulfilledQuantity']?.toString() ?? '0',
      nonPackageQuantity: json['nonPackageQuantity']?.toString() ?? '0',
      placeOfSupply: json['placeOfSupply']?.toString() ?? '',
      quantityDelivered: json['quantityDelivered']?.toString() ?? '0',
      quantityDropshipped: json['quantityDropshipped']?.toString() ?? '0',
      quantityPacked: json['quantityPacked']?.toString() ?? '0',
      salesVertices: json['salesVertices']?.toString() ?? '',
      sno: json['sno']?.toString() ?? '',
      total: (json['total'] ?? 0).toDouble(),
    );
  }
}
