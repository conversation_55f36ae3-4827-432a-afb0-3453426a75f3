import 'package:aquapartner/core/network/app_check_interceptor.dart';
import 'package:aquapartner/core/services/app_check_service.dart';
import 'package:aquapartner/core/utils/logger.dart';
import 'package:dio/dio.dart';
import 'package:flutter_test/flutter_test.dart';

// Custom logger mock that captures error calls
class TestAppLogger implements AppLogger {
  List<String> errorMessages = [];
  List<Object> errorObjects = [];
  List<String> infoMessages = [];
  List<String> debugMessages = [];
  List<String> warningMessages = [];

  @override
  void e(String message, [dynamic error, StackTrace? stackTrace]) {
    errorMessages.add(message);
    if (error != null) errorObjects.add(error);
  }

  @override
  void i(String message) {
    infoMessages.add(message);
  }

  @override
  void d(String message) {
    debugMessages.add(message);
  }

  @override
  void w(String message) {
    warningMessages.add(message);
  }

  @override
  void enableFirebaseVerboseLogging() {
    // No-op for testing
  }
}

// Custom AppCheckService mock
class TestAppCheckService implements AppCheckService {
  String? tokenToReturn;
  Exception? exceptionToThrow;
  bool initializeCalled = false;

  @override
  Future<void> initialize() async {
    initializeCalled = true;
  }

  @override
  Future<String?> getToken() async {
    if (exceptionToThrow != null) {
      throw exceptionToThrow!;
    }
    return tokenToReturn;
  }
}

// Custom handler for testing
class TestRequestHandler implements RequestInterceptorHandler {
  RequestOptions? passedOptions;
  bool nextCalled = false;

  @override
  void next(RequestOptions options) {
    passedOptions = options;
    nextCalled = true;
  }

  @override
  void reject(DioException error, [bool callFollowingReject = true]) {
    // Not used in this test
  }

  @override
  void resolve(Response response, [bool callFollowingResolve = true]) {
    // Not used in this test
  }

  // These methods are required by the interface but not used in our tests
  @override
  noSuchMethod(Invocation invocation) => super.noSuchMethod(invocation);
}

void main() {
  late TestAppCheckService testAppCheckService;
  late TestAppLogger testLogger;
  late AppCheckInterceptor interceptor;
  late TestRequestHandler testHandler;

  setUp(() {
    testAppCheckService = TestAppCheckService();
    testLogger = TestAppLogger();
    testHandler = TestRequestHandler();
    interceptor = AppCheckInterceptor(testAppCheckService, testLogger);
  });

  group('AppCheckInterceptor', () {
    test(
      'onRequest should add App Check token to headers when available',
      () async {
        // Arrange
        final options = RequestOptions(
          path: '/test',
          method: 'GET',
          baseUrl: 'https://example.com',
          headers: {},
        );

        testAppCheckService.tokenToReturn = 'test-app-check-token';

        // Act
        await interceptor.onRequest(options, testHandler);

        // Assert
        expect(
          options.headers['X-Firebase-AppCheck'],
          equals('test-app-check-token'),
        );
        expect(testHandler.nextCalled, isTrue);
        expect(testHandler.passedOptions, equals(options));
      },
    );

    test('onRequest should log warning when token is not available', () async {
      // Arrange
      final options = RequestOptions(
        path: '/test',
        method: 'GET',
        baseUrl: 'https://example.com',
        headers: {},
      );

      testAppCheckService.tokenToReturn = null;

      // Act
      await interceptor.onRequest(options, testHandler);

      // Assert
      expect(options.headers.containsKey('X-Firebase-AppCheck'), isFalse);
      expect(testLogger.warningMessages, isNotEmpty);
      expect(testHandler.nextCalled, isTrue);
    });

    test('onRequest should handle errors gracefully', () async {
      // Arrange
      final options = RequestOptions(
        path: '/test',
        method: 'GET',
        baseUrl: 'https://example.com',
        headers: {},
      );

      testAppCheckService.exceptionToThrow = Exception('Test error');

      // Act
      await interceptor.onRequest(options, testHandler);

      // Assert
      expect(options.headers.containsKey('X-Firebase-AppCheck'), isFalse);
      expect(testLogger.errorMessages, isNotEmpty);
      expect(testLogger.errorObjects, isNotEmpty);
      expect(testHandler.nextCalled, isTrue);
    });
  });
}
