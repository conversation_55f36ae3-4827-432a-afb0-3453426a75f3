import 'package:equatable/equatable.dart';

/// Represents a Zoho payment session entity
class PaymentSession extends Equatable {
  final String sessionId;
  final String paymentUrl;
  final double amount;
  final String currency;
  final String invoiceNumber;
  final String customerId;
  final String? description;
  final String? customerName;
  final String? customerEmail;
  final PaymentSessionStatus status;
  final DateTime createdAt;
  final DateTime? expiresAt;
  final Map<String, dynamic>? metadata;

  const PaymentSession({
    required this.sessionId,
    required this.paymentUrl,
    required this.amount,
    required this.currency,
    required this.invoiceNumber,
    required this.customerId,
    this.description,
    this.customerName,
    this.customerEmail,
    required this.status,
    required this.createdAt,
    this.expiresAt,
    this.metadata,
  });

  @override
  List<Object?> get props => [
        sessionId,
        paymentUrl,
        amount,
        currency,
        invoiceNumber,
        customerId,
        description,
        customerName,
        customerEmail,
        status,
        createdAt,
        expiresAt,
        metadata,
      ];

  PaymentSession copyWith({
    String? sessionId,
    String? paymentUrl,
    double? amount,
    String? currency,
    String? invoiceNumber,
    String? customerId,
    String? description,
    String? customerName,
    String? customerEmail,
    PaymentSessionStatus? status,
    DateTime? createdAt,
    DateTime? expiresAt,
    Map<String, dynamic>? metadata,
  }) {
    return PaymentSession(
      sessionId: sessionId ?? this.sessionId,
      paymentUrl: paymentUrl ?? this.paymentUrl,
      amount: amount ?? this.amount,
      currency: currency ?? this.currency,
      invoiceNumber: invoiceNumber ?? this.invoiceNumber,
      customerId: customerId ?? this.customerId,
      description: description ?? this.description,
      customerName: customerName ?? this.customerName,
      customerEmail: customerEmail ?? this.customerEmail,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      expiresAt: expiresAt ?? this.expiresAt,
      metadata: metadata ?? this.metadata,
    );
  }

  bool get isExpired {
    if (expiresAt == null) return false;
    return DateTime.now().isAfter(expiresAt!);
  }

  bool get isActive {
    return status == PaymentSessionStatus.created && !isExpired;
  }
}

/// Payment session status enumeration
enum PaymentSessionStatus {
  created,
  pending,
  completed,
  failed,
  cancelled,
  expired,
}

extension PaymentSessionStatusExtension on PaymentSessionStatus {
  String get displayName {
    switch (this) {
      case PaymentSessionStatus.created:
        return 'Created';
      case PaymentSessionStatus.pending:
        return 'Pending';
      case PaymentSessionStatus.completed:
        return 'Completed';
      case PaymentSessionStatus.failed:
        return 'Failed';
      case PaymentSessionStatus.cancelled:
        return 'Cancelled';
      case PaymentSessionStatus.expired:
        return 'Expired';
    }
  }

  bool get isTerminal {
    return this == PaymentSessionStatus.completed ||
        this == PaymentSessionStatus.failed ||
        this == PaymentSessionStatus.cancelled ||
        this == PaymentSessionStatus.expired;
  }
}
