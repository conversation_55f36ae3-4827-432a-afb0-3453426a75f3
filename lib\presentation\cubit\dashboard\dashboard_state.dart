import 'package:equatable/equatable.dart';
import '../../../domain/entities/dashboard/dashboard_entity.dart';

abstract class DashboardState extends Equatable {
  const DashboardState();

  @override
  List<Object?> get props => [];
}

class DashboardInitial extends DashboardState {}

class DashboardLoading extends DashboardState {
  final DashboardEntity? previousDashboard;
  final DateTime? lastSyncTime;

  const DashboardLoading({this.previousDashboard, this.lastSyncTime});

  @override
  List<Object?> get props => [previousDashboard, lastSyncTime];
}

class DashboardLoaded extends DashboardState {
  final DashboardEntity dashboard;
  final bool isSyncing;
  final DateTime? lastSyncTime;
  final bool hasError;
  final String? errorMessage;

  const DashboardLoaded({
    required this.dashboard,
    required this.isSyncing,
    this.lastSyncTime,
    this.hasError = false,
    this.errorMessage,
  });

  @override
  List<Object?> get props => [
    dashboard,
    isSyncing,
    lastSyncTime,
    hasError,
    errorMessage,
  ];

  DashboardLoaded copyWith({
    DashboardEntity? dashboard,
    bool? isSyncing,
    DateTime? lastSyncTime,
    bool? hasError,
    String? errorMessage,
  }) {
    return DashboardLoaded(
      dashboard: dashboard ?? this.dashboard,
      isSyncing: isSyncing ?? this.isSyncing,
      lastSyncTime: lastSyncTime ?? this.lastSyncTime,
      hasError: hasError ?? this.hasError,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}

class DashboardError extends DashboardState {
  final String message;

  const DashboardError(this.message);

  @override
  List<Object> get props => [message];
}
