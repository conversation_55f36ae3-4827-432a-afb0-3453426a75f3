import '../../../core/error/exceptions.dart';
import '../../../domain/entities/user.dart';
import '../../models/user_model.dart';
import '../../../objectbox.g.dart';
import '../../../core/utils/logger.dart';

abstract class UserLocalDataSource {
  Future<void> saveUser(User user);
  Future<User?> getUser();
  Future<void> updateUser(UserModel user);
  Future<void> deleteUser();
  Future<void> markUserSynced(String mongoId);
  Future<bool> hasUser();
  Future<List<UserModel>> getUnsyncedUsers();
}

class UserLocalDataSourceImpl implements UserLocalDataSource {
  final Box<UserModel> userBox;
  final AppLogger logger;

  UserLocalDataSourceImpl({required this.userBox, required this.logger});

  @override
  Future<bool> hasUser() async {
    try {
      logger.i("Checking if user exists in local storage");
      final count = userBox.count();
      logger.i("User count: $count");
      return count > 0;
    } catch (e) {
      logger.e("Error checking if user exists in local storage", e);
      throw CacheException();
    }
  }

  @override
  Future<void> saveUser(User user) async {
    final userModel = UserModel.fromEntity(user);
    userBox.put(userModel);
  }

  @override
  Future<User?> getUser() async {
    final users = userBox.getAll();
    if (users.isEmpty) {
      logger.w("No user found in local storage");
      return null;
    }
    return users.first.toEntity();
  }

  @override
  Future<void> updateUser(UserModel userModel) async {
    userBox.put(userModel);
  }

  @override
  Future<void> deleteUser() async {
    logger.i("Deleting user from local storage");
    userBox.removeAll();
  }

  @override
  Future<void> markUserSynced(String mongoId) async {
    logger.i("Marking user as synced with MongoDB ID: $mongoId");
    final users = userBox.getAll();
    if (users.isNotEmpty) {
      final user = users.first.copyWith(mongoId: mongoId, needsSync: false);
      userBox.put(user);
      logger.i("User marked as synced");
    } else {
      logger.w("No user found to mark as synced");
    }
  }

  @override
  Future<List<UserModel>> getUnsyncedUsers() async {
    logger.i("Getting unsynced users from local storage");
    final query = userBox.query(UserModel_.needsSync.equals(true)).build();
    final userModels = query.find();
    query.close();
    logger.i("Found ${userModels.length} unsynced users");
    return userModels;
  }
}
