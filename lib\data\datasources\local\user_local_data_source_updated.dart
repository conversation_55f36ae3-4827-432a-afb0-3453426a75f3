import '../../../core/error/exceptions.dart';
import '../../../domain/entities/user.dart';
import '../../models/user_model.dart';
import '../../../objectbox.g.dart';
import '../../../core/utils/logger.dart';

abstract class UserLocalDataSource {
  Future<void> saveUser(User user);
  Future<User?> getUser();
  Future<void> updateUser(UserModel user);
  Future<void> deleteUser();
  Future<void> markUserSynced(String mongoId);
  Future<bool> hasUser();
  Future<List<UserModel>> getUnsyncedUsers();
}

class UserLocalDataSourceImpl implements UserLocalDataSource {
  final Box<UserModel> userBox;
  final AppLogger logger;

  UserLocalDataSourceImpl({required this.userBox, required this.logger});

  @override
  Future<bool> hasUser() async {
    try {
      logger.i("Checking if user exists in local storage");
      final count = userBox.count();
      logger.i("User count: $count");
      return count > 0;
    } catch (e) {
      logger.e("Error checking if user exists in local storage", e);
      throw CacheException();
    }
  }

  @override
  Future<void> saveUser(User user) async {
    try {
      logger.i("Saving user to local storage: ${user.phoneNumber}");
      final userModel = UserModel.fromEntity(user);

      // Check if user already exists
      final existingUsers = userBox.getAll();
      if (existingUsers.isNotEmpty) {
        logger.i("User already exists, updating instead of creating new");
        final existingUser = existingUsers.first;
        final updatedUser = existingUser.copyWith(
          phoneNumber: user.phoneNumber,
          isVerified: user.isVerified,
          updatedAt: DateTime.now(),
          needsSync: true,
        );
        userBox.put(updatedUser);
      } else {
        // Create new user
        userBox.put(userModel);
      }
      logger.i("User saved successfully");
    } catch (e) {
      logger.e("Error saving user to local storage", e);
      throw CacheException();
    }
  }

  @override
  Future<User?> getUser() async {
    try {
      final users = userBox.getAll();
      if (users.isEmpty) {
        return null;
      }
      return users.first.toEntity();
    } catch (e) {
      throw CacheException();
    }
  }

  @override
  Future<void> updateUser(UserModel userModel) async {
    try {
      logger.i("Updating user in local storage: ${userModel.phoneNumber}");
      userBox.put(userModel);
      logger.i("User updated successfully");
    } catch (e) {
      logger.e("Error updating user in local storage", e);
      throw CacheException();
    }
  }

  @override
  Future<void> deleteUser() async {
    try {
      logger.i("Deleting user from local storage");
      userBox.removeAll();
      logger.i("User deleted successfully");
    } catch (e) {
      logger.e("Error deleting user from local storage", e);
      throw CacheException();
    }
  }

  @override
  Future<void> markUserSynced(String mongoId) async {
    try {
      logger.i("Marking user as synced with MongoDB ID: $mongoId");
      final users = userBox.getAll();
      if (users.isNotEmpty) {
        final user = users.first.copyWith(mongoId: mongoId, needsSync: false);
        userBox.put(user);
        logger.i("User marked as synced");
      } else {
        logger.w("No user found to mark as synced");
      }
    } catch (e) {
      logger.e("Error marking user as synced", e);
      throw CacheException();
    }
  }

  @override
  Future<List<UserModel>> getUnsyncedUsers() async {
    try {
      logger.i("Getting unsynced users from local storage");
      final query = userBox.query(UserModel_.needsSync.equals(true)).build();
      final userModels = query.find();
      query.close();
      logger.i("Found ${userModels.length} unsynced users");
      return userModels;
    } catch (e) {
      logger.e("Error getting unsynced users", e);
      throw CacheException();
    }
  }
}
