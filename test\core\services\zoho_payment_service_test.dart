import 'dart:convert';
import 'package:flutter_test/flutter_test.dart';
import 'package:http/http.dart' as http;
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import '../../../lib/core/services/zoho_payment_service.dart';
import '../../../lib/domain/entities/payments/payment_request.dart';

import 'zoho_payment_service_test.mocks.dart';

@GenerateMocks([http.Client])
void main() {
  group('ZohoPaymentService Tests', () {
    late MockClient mockClient;
    
    setUp(() {
      mockClient = MockClient();
      // Replace the static client with our mock
      // Note: This requires modifying ZohoPaymentService to accept a client parameter
    });

    group('createPaymentSession', () {
      test('should create payment session successfully', () async {
        // Arrange
        final request = PaymentRequest(
          amount: 100.0,
          currency: 'INR',
          invoiceNumber: 'TEST-001',
          customerId: 'CUST-001',
          description: 'Test Payment',
        );

        final responseBody = {
          'success': true,
          'message': 'Payment session created',
          'data': {
            'payment_session_id': 'session_123',
            'amount': '100.0',
            'currency': 'INR',
            'description': 'Test Payment',
            'invoice_number': 'TEST-001',
            'created_time': 1640995200,
            'transaction_id': 'txn_123',
            'expires_in': '3600',
          }
        };

        when(mockClient.post(
          Uri.parse('https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/api/zoho/payments/create-session/'),
          headers: anyNamed('headers'),
          body: anyNamed('body'),
        )).thenAnswer((_) async => http.Response(
          jsonEncode(responseBody),
          201,
        ));

        // Act
        final result = await ZohoPaymentService.createPaymentSession(request);

        // Assert
        expect(result.success, true);
        expect(result.data.paymentSessionId, 'session_123');
        expect(result.data.amount, '100.0');
        expect(result.data.currency, 'INR');
        
        verify(mockClient.post(
          Uri.parse('https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/api/zoho/payments/create-session/'),
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
          body: jsonEncode(request.toJson()),
        )).called(1);
      });

      test('should throw PaymentException for invalid amount', () async {
        // Arrange
        final request = PaymentRequest(
          amount: -100.0, // Invalid amount
          currency: 'INR',
          invoiceNumber: 'TEST-001',
          customerId: 'CUST-001',
        );

        // Act & Assert
        expect(
          () => ZohoPaymentService.createPaymentSession(request),
          throwsA(isA<PaymentException>().having(
            (e) => e.message,
            'message',
            'Amount must be greater than 0',
          )),
        );
      });

      test('should throw PaymentException for empty invoice number', () async {
        // Arrange
        final request = PaymentRequest(
          amount: 100.0,
          currency: 'INR',
          invoiceNumber: '', // Empty invoice number
          customerId: 'CUST-001',
        );

        // Act & Assert
        expect(
          () => ZohoPaymentService.createPaymentSession(request),
          throwsA(isA<PaymentException>().having(
            (e) => e.message,
            'message',
            'Invoice number cannot be empty',
          )),
        );
      });

      test('should throw PaymentException for invalid email format', () async {
        // Arrange
        final request = PaymentRequest(
          amount: 100.0,
          currency: 'INR',
          invoiceNumber: 'TEST-001',
          customerId: 'CUST-001',
          customerEmail: 'invalid-email', // Invalid email
        );

        // Act & Assert
        expect(
          () => ZohoPaymentService.createPaymentSession(request),
          throwsA(isA<PaymentException>().having(
            (e) => e.message,
            'message',
            'Invalid email format',
          )),
        );
      });

      test('should handle 400 validation error', () async {
        // Arrange
        final request = PaymentRequest(
          amount: 100.0,
          currency: 'INR',
          invoiceNumber: 'TEST-001',
          customerId: 'CUST-001',
        );

        final errorResponse = {
          'message': 'Invalid request data',
          'error': 'VALIDATION_ERROR',
        };

        when(mockClient.post(any, headers: anyNamed('headers'), body: anyNamed('body')))
            .thenAnswer((_) async => http.Response(
              jsonEncode(errorResponse),
              400,
            ));

        // Act & Assert
        expect(
          () => ZohoPaymentService.createPaymentSession(request),
          throwsA(isA<PaymentException>()
              .having((e) => e.statusCode, 'statusCode', 400)
              .having((e) => e.message, 'message', 'Invalid request data')),
        );
      });

      test('should handle network timeout', () async {
        // Arrange
        final request = PaymentRequest(
          amount: 100.0,
          currency: 'INR',
          invoiceNumber: 'TEST-001',
          customerId: 'CUST-001',
        );

        when(mockClient.post(any, headers: anyNamed('headers'), body: anyNamed('body')))
            .thenThrow(Exception('Connection timeout'));

        // Act & Assert
        expect(
          () => ZohoPaymentService.createPaymentSession(request),
          throwsA(isA<PaymentException>().having(
            (e) => e.message,
            'message',
            contains('Network error during payment creation'),
          )),
        );
      });
    });

    group('getPaymentStatus', () {
      test('should get payment status successfully', () async {
        // Arrange
        const sessionId = 'session_123';
        final responseBody = {
          'success': true,
          'message': 'Payment status retrieved',
          'data': {
            'transaction_id': 'txn_123',
            'payment_session_id': 'session_123',
            'status': 'completed',
            'amount': 100.0,
            'currency': 'INR',
            'description': 'Test Payment',
            'invoice_number': 'TEST-001',
            'customer_id': 'CUST-001',
          }
        };

        when(mockClient.get(
          Uri.parse('https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/api/zoho/payments/status/session_123/'),
          headers: anyNamed('headers'),
        )).thenAnswer((_) async => http.Response(
          jsonEncode(responseBody),
          200,
        ));

        // Act
        final result = await ZohoPaymentService.getPaymentStatus(sessionId);

        // Assert
        expect(result.success, true);
        expect(result.data.paymentSessionId, 'session_123');
        expect(result.data.status, 'completed');
        expect(result.data.amount, 100.0);
      });

      test('should throw PaymentException for empty session ID', () async {
        // Act & Assert
        expect(
          () => ZohoPaymentService.getPaymentStatus(''),
          throwsA(isA<PaymentException>().having(
            (e) => e.message,
            'message',
            'Session ID cannot be empty',
          )),
        );
      });

      test('should handle 404 session not found', () async {
        // Arrange
        const sessionId = 'invalid_session';
        final errorResponse = {
          'message': 'Session not found',
          'error': 'SESSION_NOT_FOUND',
        };

        when(mockClient.get(any, headers: anyNamed('headers')))
            .thenAnswer((_) async => http.Response(
              jsonEncode(errorResponse),
              404,
            ));

        // Act & Assert
        expect(
          () => ZohoPaymentService.getPaymentStatus(sessionId),
          throwsA(isA<PaymentException>()
              .having((e) => e.statusCode, 'statusCode', 404)
              .having((e) => e.isNotFoundError, 'isNotFoundError', true)),
        );
      });
    });

    group('checkHealth', () {
      test('should check health successfully', () async {
        // Arrange
        final responseBody = {
          'timestamp': '2024-01-01T00:00:00Z',
          'service': 'zoho-payment-api',
          'version': '1.0.0',
          'status': 'healthy',
          'checks': {
            'database': 'ok',
            'zoho_api': 'ok',
          }
        };

        when(mockClient.get(
          Uri.parse('https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net/api/zoho/health/'),
          headers: anyNamed('headers'),
        )).thenAnswer((_) async => http.Response(
          jsonEncode(responseBody),
          200,
        ));

        // Act
        final result = await ZohoPaymentService.checkHealth();

        // Assert
        expect(result.service, 'zoho-payment-api');
        expect(result.status, 'healthy');
        expect(result.checks['database'], 'ok');
      });

      test('should handle health check failure', () async {
        // Arrange
        when(mockClient.get(any, headers: anyNamed('headers')))
            .thenAnswer((_) async => http.Response('Service Unavailable', 503));

        // Act & Assert
        expect(
          () => ZohoPaymentService.checkHealth(),
          throwsA(isA<PaymentException>()
              .having((e) => e.statusCode, 'statusCode', 503)),
        );
      });
    });

    group('PaymentException', () {
      test('should categorize network errors correctly', () {
        final exception = PaymentException('Network error');
        expect(exception.isNetworkError, true);
        expect(exception.isClientError, false);
      });

      test('should categorize client errors correctly', () {
        final exception = PaymentException('Bad request', statusCode: 400);
        expect(exception.isClientError, true);
        expect(exception.isValidationError, true);
        expect(exception.isNetworkError, false);
      });

      test('should provide user-friendly messages', () {
        final networkError = PaymentException('Connection failed');
        expect(networkError.userFriendlyMessage, contains('Network connection error'));

        final validationError = PaymentException('Invalid data', statusCode: 400);
        expect(validationError.userFriendlyMessage, 'Invalid data');

        final authError = PaymentException('Unauthorized', statusCode: 401);
        expect(authError.userFriendlyMessage, contains('Authentication error'));
      });
    });
  });
}
