import 'package:objectbox/objectbox.dart';
import 'interested_product_model.dart';

@Entity()
class ObjectBoxInterestedProductModel {
  @Id()
  int id = 0;

  String? mongoId;
  final String customerId;
  final String mobile;
  final String productName;
  final String datetimeString; // Store as string for ObjectBox compatibility
  final String source;
  final bool isSynced;

  ObjectBoxInterestedProductModel({
    this.id = 0,
    this.mongoId,
    required this.customerId,
    required this.mobile,
    required this.productName,
    required this.datetimeString,
    required this.source,
    required this.isSynced,
  });

  factory ObjectBoxInterestedProductModel.fromInterestedProductModel(
    InterestedProductModel model,
  ) {
    return ObjectBoxInterestedProductModel(
      mongoId: model.id,
      customerId: model.customerId,
      mobile: model.mobile,
      productName: model.productName,
      datetimeString: model.datetime.toIso8601String(),
      source: model.source,
      isSynced: model.isSynced,
    );
  }

  InterestedProductModel toInterestedProductModel() {
    return InterestedProductModel(
      id: mongoId,
      customerId: customerId,
      mobile: mobile,
      productName: productName,
      datetime: DateTime.parse(datetimeString),
      source: source,
      isSynced: isSynced,
    );
  }

  ObjectBoxInterestedProductModel copyWith({
    int? id,
    String? mongoId,
    String? customerId,
    String? mobile,
    String? productId,
    String? productName,
    String? datetimeString,
    String? source,
    bool? isSynced,
  }) {
    return ObjectBoxInterestedProductModel(
      id: id ?? this.id,
      mongoId: mongoId ?? this.mongoId,
      customerId: customerId ?? this.customerId,
      mobile: mobile ?? this.mobile,
      productName: productName ?? this.productName,
      datetimeString: datetimeString ?? this.datetimeString,
      source: source ?? this.source,
      isSynced: isSynced ?? this.isSynced,
    );
  }
}
