// lib/presentation/bloc/customer_payments/customer_payments_state.dart
import 'package:equatable/equatable.dart';
import '../../../domain/entities/payments/payments_summary.dart';

abstract class CustomerPaymentsState extends Equatable {
  const CustomerPaymentsState();

  @override
  List<Object?> get props => [];
}

class CustomerPaymentsInitial extends CustomerPaymentsState {}

class CustomerPaymentsLoading extends CustomerPaymentsState {}

class CustomerPaymentsSyncing extends CustomerPaymentsState {}

class CustomerPaymentsLoaded extends CustomerPaymentsState {
  final PaymentsSummary paymentsSummary;
  final bool isFromCache;
  final bool isCacheStale;
  final bool isBackgroundSyncInProgress;

  const CustomerPaymentsLoaded(
    this.paymentsSummary, {
    this.isFromCache = false,
    this.isCacheStale = false,
    this.isBackgroundSyncInProgress = false,
  });

  CustomerPaymentsLoaded copyWith({
    PaymentsSummary? paymentsSummary,
    bool? isFromCache,
    bool? isCacheStale,
    bool? isBackgroundSyncInProgress,
  }) {
    return CustomerPaymentsLoaded(
      paymentsSummary ?? this.paymentsSummary,
      isFromCache: isFromCache ?? this.isFromCache,
      isCacheStale: isCacheStale ?? this.isCacheStale,
      isBackgroundSyncInProgress:
          isBackgroundSyncInProgress ?? this.isBackgroundSyncInProgress,
    );
  }

  @override
  List<Object?> get props => [
    paymentsSummary,
    isFromCache,
    isCacheStale,
    isBackgroundSyncInProgress,
  ];
}

class CustomerPaymentsError extends CustomerPaymentsState {
  final String message;

  const CustomerPaymentsError(this.message);

  @override
  List<Object?> get props => [message];
}
