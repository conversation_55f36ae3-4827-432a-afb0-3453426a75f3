import 'package:aquapartner/presentation/cubit/billing_and_payments/billing_and_payments_cubit.dart';
import 'package:aquapartner/presentation/cubit/billing_and_payments/billing_and_payments_state.dart';
import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../mocks/mock_analytics.dart';

void main() {
  group('BillingAndPaymentsPageCubit Tests', () {
    late BillingAndPaymentsPageCubit cubit;
    late MockAnalyticsService mockAnalyticsService;

    setUp(() {
      mockAnalyticsService = MockAnalyticsService();
      cubit = BillingAndPaymentsPageCubit(mockAnalyticsService);
    });

    tearDown(() {
      cubit.close();
    });

    group('Initial State', () {
      test('should have initial state with correct default values', () {
        expect(cubit.state, isA<BillingAndPaymentsPageState>());
        expect(cubit.state.selectedPage, equals('Sales Orders'));
        expect(
          cubit.state.pages,
          equals([
            'Sales Orders',
            'Dues',
            'Invoices',
            'Payments',
            'Credit Notes',
          ]),
        );
      });
    });

    group('Page Selection', () {
      blocTest<BillingAndPaymentsPageCubit, BillingAndPaymentsPageState>(
        'should emit new state when selecting a different page',
        build: () => cubit,
        act: (cubit) => cubit.selectPage('Dues'),
        expect:
            () => [
              BillingAndPaymentsPageState.initial().copyWith(
                selectedPage: 'Dues',
              ),
            ],
      );

      blocTest<BillingAndPaymentsPageCubit, BillingAndPaymentsPageState>(
        'should emit new state when selecting Invoices page',
        build: () => cubit,
        act: (cubit) => cubit.selectPage('Invoices'),
        expect:
            () => [
              BillingAndPaymentsPageState.initial().copyWith(
                selectedPage: 'Invoices',
              ),
            ],
      );

      blocTest<BillingAndPaymentsPageCubit, BillingAndPaymentsPageState>(
        'should emit new state when selecting Payments page',
        build: () => cubit,
        act: (cubit) => cubit.selectPage('Payments'),
        expect:
            () => [
              BillingAndPaymentsPageState.initial().copyWith(
                selectedPage: 'Payments',
              ),
            ],
      );

      blocTest<BillingAndPaymentsPageCubit, BillingAndPaymentsPageState>(
        'should emit new state when selecting Credit Notes page',
        build: () => cubit,
        act: (cubit) => cubit.selectPage('Credit Notes'),
        expect:
            () => [
              BillingAndPaymentsPageState.initial().copyWith(
                selectedPage: 'Credit Notes',
              ),
            ],
      );
    });

    group('Analytics Tracking', () {
      test('should track sub-page duration when switching pages', () async {
        // First select a page
        cubit.selectPage('Dues');

        // Wait a bit to simulate time spent
        await Future.delayed(Duration(milliseconds: 100));

        // Select another page to trigger duration logging
        cubit.selectPage('Invoices');

        // Verify analytics calls would be made
        // Note: The actual analytics calls are private methods,
        // so we test the state changes instead
        expect(cubit.state.selectedPage, equals('Invoices'));
      });

      test('should handle multiple page selections correctly', () async {
        // Test rapid page switching
        cubit.selectPage('Dues');
        cubit.selectPage('Invoices');
        cubit.selectPage('Payments');
        cubit.selectPage('Credit Notes');

        expect(cubit.state.selectedPage, equals('Credit Notes'));
      });
    });

    group('State Persistence', () {
      test('should maintain pages list when changing selected page', () {
        const expectedPages = [
          'Sales Orders',
          'Dues',
          'Invoices',
          'Payments',
          'Credit Notes',
        ];

        cubit.selectPage('Dues');
        expect(cubit.state.pages, equals(expectedPages));

        cubit.selectPage('Invoices');
        expect(cubit.state.pages, equals(expectedPages));
      });
    });

    group('Edge Cases', () {
      test('should handle selecting the same page multiple times', () {
        cubit.selectPage('Sales Orders');
        cubit.selectPage('Sales Orders');
        cubit.selectPage('Sales Orders');

        expect(cubit.state.selectedPage, equals('Sales Orders'));
      });

      test('should handle selecting non-existent page gracefully', () {
        // This should still work as the cubit doesn't validate page names
        cubit.selectPage('Non-existent Page');
        expect(cubit.state.selectedPage, equals('Non-existent Page'));
      });

      test('should handle empty string page selection', () {
        cubit.selectPage('');
        expect(cubit.state.selectedPage, equals(''));
      });
    });

    group('State Equality', () {
      test('should create equal states with same properties', () {
        final state1 = BillingAndPaymentsPageState(
          pages: ['Sales Orders', 'Dues'],
          selectedPage: 'Sales Orders',
        );
        final state2 = BillingAndPaymentsPageState(
          pages: ['Sales Orders', 'Dues'],
          selectedPage: 'Sales Orders',
        );

        expect(state1, equals(state2));
      });

      test('should create different states with different properties', () {
        final state1 = BillingAndPaymentsPageState(
          pages: ['Sales Orders', 'Dues'],
          selectedPage: 'Sales Orders',
        );
        final state2 = BillingAndPaymentsPageState(
          pages: ['Sales Orders', 'Dues'],
          selectedPage: 'Dues',
        );

        expect(state1, isNot(equals(state2)));
      });
    });

    group('CopyWith Functionality', () {
      test('should create new state with updated selectedPage', () {
        final initialState = BillingAndPaymentsPageState.initial();
        final newState = initialState.copyWith(selectedPage: 'Dues');

        expect(newState.selectedPage, equals('Dues'));
        expect(newState.pages, equals(initialState.pages));
      });

      test('should create new state with updated pages', () {
        final initialState = BillingAndPaymentsPageState.initial();
        final newPages = ['Custom Page 1', 'Custom Page 2'];
        final newState = initialState.copyWith(pages: newPages);

        expect(newState.pages, equals(newPages));
        expect(newState.selectedPage, equals(initialState.selectedPage));
      });

      test('should create new state with both properties updated', () {
        final initialState = BillingAndPaymentsPageState.initial();
        final newPages = ['Custom Page 1', 'Custom Page 2'];
        final newState = initialState.copyWith(
          pages: newPages,
          selectedPage: 'Custom Page 1',
        );

        expect(newState.pages, equals(newPages));
        expect(newState.selectedPage, equals('Custom Page 1'));
      });
    });
  });
}
