import 'package:equatable/equatable.dart';

class CreditNote extends Equatable {
  final String id; // Corresponds to _id
  final String customerId;
  final String customerName;
  final DateTime date; // Parsed from date1
  final String creditNoteId;
  final String creditNoteNumber;
  final String? invoiceId; // Optional based on data
  final String? productId; // Optional
  final String itemName;
  final String? brand; // Optional
  final String? itemCategory; // Optional
  final String? categoryType; // Optional
  final int quantity; // Parsed from quantity string
  final double amount; // Parsed from amount string
  final String? tag; // Optional
  final String? category; // Optional

  const CreditNote({
    required this.id,
    required this.customerId,
    required this.customerName,
    required this.date,
    required this.creditNoteId,
    required this.creditNoteNumber,
    this.invoiceId,
    this.productId,
    required this.itemName,
    this.brand,
    this.itemCategory,
    this.categoryType,
    required this.quantity,
    required this.amount,
    this.tag,
    this.category,
  });

  @override
  List<Object?> get props => [
    id,
    customerId,
    customerName,
    date,
    creditNoteId,
    creditNoteNumber,
    invoiceId,
    productId,
    itemName,
    brand,
    itemCategory,
    categoryType,
    quantity,
    amount,
    tag,
    category,
  ];
}
