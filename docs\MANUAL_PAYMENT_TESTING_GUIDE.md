# Manual Payment Testing Guide

## 🎯 Overview

This guide provides step-by-step instructions for manually testing the Zoho payment integration in the AquaPartner Flutter app. Follow these procedures to ensure the payment system works correctly after the recent architecture changes.

## 📋 Pre-Testing Checklist

### Environment Setup
- [ ] App is built with latest code changes
- [ ] Device/emulator has internet connectivity
- [ ] Test user account is available
- [ ] Test invoice data is prepared
- [ ] Payment test cards are ready (if using sandbox)

### Required Test Data
```
Test Customer ID: TEST-CUST-001
Test Invoice Numbers: INV-TEST-001, INV-TEST-002, INV-TEST-003
Test Amounts: ₹100.00, ₹250.50, ₹999.99
Test Email: <EMAIL>
Test Phone: +************
```

## 🧪 Test Scenarios

### Scenario 1: Successful Payment Flow

**Objective:** Verify complete payment flow from initiation to completion

**Steps:**
1. **Launch App**
   - Open AquaPartner app
   - Login with test credentials
   - Navigate to Invoices section

2. **Select Invoice for Payment**
   - Find an overdue invoice (or use test invoice)
   - Verify invoice details are displayed correctly
   - Note the invoice amount and number

3. **Initiate Payment**
   - Tap "Pay Now" button
   - **Expected:** Button shows loading indicator
   - **Expected:** No immediate errors or crashes

4. **Payment Session Creation**
   - Wait for payment session to be created (3-5 seconds)
   - **Expected:** WebView opens with Zoho payment page
   - **Expected:** Payment amount matches invoice amount
   - **Expected:** Invoice number is displayed correctly

5. **WebView Payment Process**
   - Verify payment page loads completely
   - Fill in test payment details:
     ```
     Card Number: 4111 1111 1111 1111 (Visa test card)
     Expiry: 12/25
     CVV: 123
     Name: Test Customer
     ```
   - Submit payment form
   - **Expected:** Payment processing indicator appears

6. **Payment Completion**
   - Wait for payment to process (10-30 seconds)
   - **Expected:** Success page appears in WebView
   - **Expected:** WebView closes automatically
   - **Expected:** Success snackbar appears: "Payment completed successfully!"

7. **Status Verification**
   - **Expected:** Payment button becomes disabled or changes text
   - **Expected:** Invoice status updates to "Paid" (if applicable)
   - **Expected:** Transaction ID is generated and displayed

**Success Criteria:**
- ✅ Payment completes without errors
- ✅ Success message is displayed
- ✅ Invoice status updates correctly
- ✅ Transaction ID is provided

---

### Scenario 2: Payment Failure Handling

**Objective:** Verify proper handling of payment failures

**Steps:**
1. **Setup Failure Scenario**
   - Use test card that triggers failure: `4000 0000 0000 0002`
   - Or use insufficient funds scenario

2. **Follow Payment Flow**
   - Repeat steps 1-4 from Scenario 1
   - In WebView, use failing test card details

3. **Handle Payment Failure**
   - **Expected:** Payment fails in WebView
   - **Expected:** Error message appears in WebView
   - **Expected:** WebView closes or shows retry option

4. **Verify Error Handling**
   - **Expected:** Error snackbar appears: "Payment failed: [reason]"
   - **Expected:** Payment button returns to enabled state
   - **Expected:** Invoice remains unpaid
   - **Expected:** User can retry payment

**Success Criteria:**
- ✅ Failure is handled gracefully
- ✅ Clear error message is shown
- ✅ User can retry payment
- ✅ No app crashes or freezes

---

### Scenario 3: Payment Cancellation

**Objective:** Verify handling of user-cancelled payments

**Steps:**
1. **Initiate Payment**
   - Follow steps 1-4 from Scenario 1

2. **Cancel Payment**
   - In WebView, tap back button or close button
   - Or use browser back navigation

3. **Verify Cancellation Handling**
   - **Expected:** WebView closes
   - **Expected:** No error messages appear
   - **Expected:** Payment button returns to enabled state
   - **Expected:** Invoice remains unpaid

**Success Criteria:**
- ✅ Cancellation is handled smoothly
- ✅ No error messages for cancellation
- ✅ User can retry payment later

---

### Scenario 4: Network Timeout Testing

**Objective:** Verify handling of network issues

**Steps:**
1. **Simulate Poor Network**
   - Enable airplane mode briefly during payment
   - Or use network throttling tools

2. **Test Session Creation Timeout**
   - Tap "Pay Now" button
   - Enable airplane mode immediately
   - Wait for timeout (30 seconds)
   - **Expected:** Timeout error message appears

3. **Test Status Polling Timeout**
   - Complete payment in WebView
   - Enable airplane mode during status polling
   - Wait for polling timeout (5 minutes)
   - **Expected:** Timeout message appears

**Success Criteria:**
- ✅ Timeouts are handled gracefully
- ✅ Clear timeout messages are shown
- ✅ App remains responsive

---

### Scenario 5: Multiple Payment Attempts

**Objective:** Verify handling of multiple payment attempts

**Steps:**
1. **First Attempt - Failure**
   - Initiate payment with failing card
   - Verify failure handling

2. **Second Attempt - Success**
   - Immediately retry with successful card
   - Verify success handling

3. **Verify State Management**
   - **Expected:** Each attempt is independent
   - **Expected:** Previous failure doesn't affect new attempt
   - **Expected:** Success overwrites previous failure

**Success Criteria:**
- ✅ Multiple attempts work correctly
- ✅ State is properly reset between attempts
- ✅ Final state reflects last attempt result

---

### Scenario 6: Edge Cases Testing

**Objective:** Test unusual but valid scenarios

#### 6.1 Large Amount Payment
- Test with amount: ₹99,999.99
- Verify amount formatting in WebView
- Verify successful processing

#### 6.2 Special Characters in Invoice
- Test with invoice: `INV-2024/Q1_001`
- Verify proper encoding in WebView
- Verify successful processing

#### 6.3 Long Customer Names
- Test with name: "Very Long Customer Name That Might Cause Display Issues"
- Verify proper display in WebView

#### 6.4 International Phone Numbers
- Test with phone: "******-123-4567"
- Verify proper formatting

**Success Criteria:**
- ✅ All edge cases handled correctly
- ✅ No display issues or truncation
- ✅ Payments process successfully

## 🔍 Verification Points

### During Testing, Verify:

#### UI/UX Checks
- [ ] Payment button is clearly visible
- [ ] Loading states are intuitive
- [ ] Error messages are user-friendly
- [ ] Success feedback is clear
- [ ] WebView loads without visual glitches

#### Functional Checks
- [ ] Correct payment amount is displayed
- [ ] Invoice details are accurate
- [ ] Transaction IDs are generated
- [ ] Status updates are timely
- [ ] Analytics events are fired (if trackable)

#### Performance Checks
- [ ] Payment session creation < 5 seconds
- [ ] WebView loads < 10 seconds
- [ ] Status polling works correctly
- [ ] App remains responsive throughout

#### Security Checks
- [ ] Payment URL uses HTTPS
- [ ] Sensitive data is not logged
- [ ] WebView restricts navigation to payment domain
- [ ] No payment details stored locally

## 📱 Device-Specific Testing

### Test on Multiple Devices:
- [ ] Android phones (different screen sizes)
- [ ] Android tablets
- [ ] iOS phones (if applicable)
- [ ] iOS tablets (if applicable)

### Test Different Network Conditions:
- [ ] WiFi connection
- [ ] 4G/5G mobile data
- [ ] Slow 3G connection
- [ ] Intermittent connectivity

## 🐛 Issue Reporting Template

When reporting issues, include:

```
**Issue Title:** [Brief description]

**Scenario:** [Which test scenario]

**Steps to Reproduce:**
1. [Step 1]
2. [Step 2]
3. [Step 3]

**Expected Result:** [What should happen]

**Actual Result:** [What actually happened]

**Device Info:**
- Device: [Model]
- OS Version: [Version]
- App Version: [Version]
- Network: [WiFi/Mobile]

**Screenshots/Videos:** [Attach if available]

**Additional Notes:** [Any other relevant information]
```

## ✅ Test Completion Checklist

After completing all scenarios:

- [ ] All success scenarios work correctly
- [ ] All failure scenarios are handled gracefully
- [ ] Edge cases are properly managed
- [ ] Performance is acceptable
- [ ] UI/UX is intuitive
- [ ] Security requirements are met
- [ ] Multiple devices tested
- [ ] Different network conditions tested
- [ ] Issues documented and reported

## 🚀 Next Steps

After manual testing:

1. **Document Results:** Record all test outcomes
2. **Report Issues:** Create tickets for any problems found
3. **Regression Testing:** Re-test after fixes are applied
4. **User Acceptance:** Get stakeholder approval
5. **Production Deployment:** Proceed with release if all tests pass

---

**Note:** This manual testing should be performed in addition to automated tests, not as a replacement. Both manual and automated testing are essential for ensuring payment system reliability.
