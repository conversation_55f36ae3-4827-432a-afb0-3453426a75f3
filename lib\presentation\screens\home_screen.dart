import 'package:aqua_ui/aqua_ui.dart';
import 'package:aquapartner/presentation/screens/billing_and_payments_screen.dart';
import 'package:aquapartner/presentation/screens/dashboard_screen.dart';
import 'package:aquapartner/presentation/screens/help_and_support_screen.dart';
import 'package:aquapartner/presentation/screens/stock_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';

import '../../core/constants/app_constants.dart';
import '../../core/mixins/analytics_mixin.dart';
import '../cubit/connectivity/connectivity_cubit.dart';
import '../cubit/connectivity/connectivity_state.dart';
import '../cubit/home/<USER>';
import '../cubit/home/<USER>';
import '../widgets/drawer_menu.dart';
import 'account_statement_screen.dart';
import 'my_farmers_screen.dart';
import 'price_list_screen.dart';
import 'products_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen>
    with
        AutomaticKeepAliveClientMixin,
        WidgetsBindingObserver,
        AnalyticsMixin<HomeScreen> {
  @override
  bool get wantKeepAlive => true;

  @override
  String get screenName => 'home';

  final Map<ViewType, Widget> _screenCache = {};
  DateTime? _lastBackPressTime;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.paused ||
        state == AppLifecycleState.detached) {
      // Log duration when app goes to background or is closed
      context.read<HomeCubit>().trackCurrentViewDuration();
    } else if (state == AppLifecycleState.resumed) {
      // Optionally restart timing when app is resumed
      final cubit = context.read<HomeCubit>();
      final currentView = cubit.state.viewType;

      // Reset the timer for the current view
      switch (currentView) {
        case ViewType.dashboard:
          cubit.gotoDashboardView();
          break;
        case ViewType.accountStatement:
          cubit.gotoAccountStatementView();
          break;
        case ViewType.billingAndPayments:
          cubit.gotoBillingAndPaymentsView();
          break;
        case ViewType.priceList:
          cubit.gotoPriceListView();
          break;
        case ViewType.productList:
          cubit.gotoProductsView();
          break;
        case ViewType.myFarmers:
          cubit.gotoMyFarmersView();
          break;
        case ViewType.stocks:
          cubit.gotoHelpAndSupportScreen();
          break;
        case ViewType.helpAndSupport:
          cubit.gotoHelpAndSupportScreen();
          break;
      }
    }
  }

  Widget _getScreenForViewType(ViewType viewType) {
    if (!_screenCache.containsKey(viewType)) {
      switch (viewType) {
        case ViewType.dashboard:
          _screenCache[viewType] = DashboardScreen();
          break;
        case ViewType.accountStatement:
          _screenCache[viewType] = AccountStatementScreen();
          break;
        case ViewType.billingAndPayments:
          _screenCache[viewType] = BillingAndPaymentsScreen();
          break;
        case ViewType.priceList:
          _screenCache[viewType] = const PriceListScreen();
          break;
        case ViewType.productList:
          _screenCache[viewType] = const ProductsScreen();
          break;
        case ViewType.myFarmers:
          _screenCache[viewType] = MyFarmersScreen();
          break;
        case ViewType.stocks:
          _screenCache[viewType] = StockScreen();
          break;
        case ViewType.helpAndSupport:
          _screenCache[viewType] = HelpAndSupportScreen();
          break;
      }
    }
    return _screenCache[viewType]!;
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return Scaffold(
      appBar: AppBar(
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.white,
          statusBarIconBrightness: Brightness.dark,
        ),
        title: SvgPicture.asset(
          AppConstants.imgPartnerLogo,
          height: 24,
          // Consider if this colorFilter is always desired, or if the logo has its own colors.
          colorFilter: const ColorFilter.mode(Colors.white, BlendMode.srcIn),
        ),
        iconTheme: IconThemeData(
          color:
              acWhiteColor, // Assuming acWhiteColor is defined (e.g., Colors.white)
        ),
        backgroundColor: acPrimaryBlue, // Assuming acPrimaryBlue is defined
      ),
      drawer: DrawerMenu(),
      body: PopScope<Object?>(
        // Specify the expected result type, Object? is generic
        canPop: false, // Intercept pop attempts
        onPopInvokedWithResult: (bool didPop, Object? result) {
          // didPop will be false if canPop is false and a system back gesture occurred.
          // result will be null for a system back gesture.
          // If this screen was popped with Navigator.pop(context, someResult),
          // 'result' would contain 'someResult'.

          if (didPop) {
            // If didPop is true, it means the pop was not prevented by canPop: false here.
            // This could happen if canPop was true, or if Navigator.pop() with a result
            // was called and PopScope allowed it.
            // You might want to log the result or handle it if necessary.
            // print("PopScope: Pop occurred with result: $result");
            return;
          }

          // Double-tap to exit logic (when didPop is false for a system back gesture)
          final now = DateTime.now();
          if (_lastBackPressTime == null ||
              now.difference(_lastBackPressTime!) >
                  const Duration(seconds: 2)) {
            _lastBackPressTime = now;

            // Track first back button press
            trackUserInteraction(
              'back_button_pressed',
              'system_button',
              additionalParams: {
                'current_view':
                    context.read<HomeCubit>().state.viewType.toString(),
                'press_count': '1',
              },
            );

            if (mounted) {
              // Check if the widget is still in the tree
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Press back again to exit'),
                  duration: Duration(seconds: 2),
                ),
              );
            }
            // Pop is already prevented by canPop: false.
          } else {
            // Second tap within 2 seconds - allow exit.

            // Track app exit
            trackUserInteraction(
              'app_exit',
              'system_button',
              additionalParams: {
                'current_view':
                    context.read<HomeCubit>().state.viewType.toString(),
                'press_count': '2',
                'time_between_presses':
                    now
                        .difference(_lastBackPressTime!)
                        .inMilliseconds
                        .toString(),
              },
            );

            SystemNavigator.pop(); // Exits the application.
          }
        },
        child: Column(
          children: [
            // Connectivity banner
            BlocBuilder<ConnectivityCubit, ConnectivityState>(
              buildWhen:
                  (previous, current) =>
                      previous.isConnected != current.isConnected,
              builder: (context, state) {
                if (!state.isConnected) {
                  return Container(
                    color: Colors.red,
                    padding: const EdgeInsets.all(8.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.wifi_off, color: Colors.white),
                        const SizedBox(width: 8.0),
                        AquaText.body(
                          // Assuming AquaText is available
                          'You are offline. Data will be synced when online.',
                          color: acWhiteColor, // Assuming acWhiteColor
                        ),
                      ],
                    ),
                  );
                }
                return const SizedBox.shrink();
              },
            ),

            // Error message banner
            BlocBuilder<HomeCubit, HomeState>(
              buildWhen:
                  (previous, current) =>
                      previous.errorMessage != current.errorMessage,
              builder: (context, state) {
                if (state.errorMessage != null) {
                  return Container(
                    color: state.isNetworkError ? Colors.orange : Colors.red,
                    padding: const EdgeInsets.all(8.0),
                    child: Row(
                      children: [
                        Icon(
                          state.isNetworkError ? Icons.wifi_off : Icons.error,
                          color: Colors.white,
                        ),
                        const SizedBox(width: 8.0),
                        Expanded(
                          child: AquaText.body(
                            // Assuming AquaText is available
                            state.errorMessage!,
                            color: acWhiteColor, // Assuming acWhiteColor
                          ),
                        ),
                        IconButton(
                          icon: const Icon(Icons.close, color: Colors.white),
                          onPressed:
                              () => context.read<HomeCubit>().clearError(),
                        ),
                      ],
                    ),
                  );
                }
                return const SizedBox.shrink();
              },
            ),

            // Main content
            Expanded(
              child: BlocConsumer<HomeCubit, HomeState>(
                listener: (context, state) {
                  // Track view changes for navigation analytics
                  trackUserInteraction(
                    'view_changed',
                    'navigation',
                    additionalParams: {
                      'new_view': state.viewType.toString(),
                      'is_loading': state.isLoading.toString(),
                      'has_error': (state.errorMessage != null).toString(),
                    },
                  );
                },
                buildWhen:
                    (previous, current) =>
                        previous.viewType != current.viewType ||
                        previous.isLoading != current.isLoading,
                builder: (context, state) {
                  if (state.isLoading) {
                    return const Center(child: CircularProgressIndicator());
                  }
                  return AnimatedSwitcher(
                    duration: const Duration(milliseconds: 300),
                    child: KeyedSubtree(
                      key: ValueKey<ViewType>(state.viewType),
                      child: _getScreenForViewType(state.viewType),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
