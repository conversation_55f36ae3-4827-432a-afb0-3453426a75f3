import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'package:aquapartner/domain/entities/payments/payment_request.dart';
import 'package:aquapartner/domain/entities/payments/payment_session.dart';
import 'package:aquapartner/domain/repositories/payment_repository.dart';
import 'package:aquapartner/domain/usecases/payments/create_payment_session_usecase.dart';
import 'package:aquapartner/core/error/failures.dart';

import 'create_payment_session_usecase_test.mocks.dart';

@GenerateMocks([PaymentRepository])
void main() {
  late CreatePaymentSessionUseCase useCase;
  late MockPaymentRepository mockRepository;

  setUp(() {
    mockRepository = MockPaymentRepository();
    useCase = CreatePaymentSessionUseCase(mockRepository);
  });

  group('CreatePaymentSessionUseCase', () {
    const tPaymentRequest = PaymentRequest(
      amount: 100.0,
      currency: 'INR',
      invoiceNumber: 'INV-001',
      customerId: 'CUST-001',
      description: 'Test payment',
      customerName: 'Test Customer',
      customerEmail: '<EMAIL>',
    );

    final tPaymentSession = PaymentSession(
      sessionId: 'PS_123456789',
      paymentUrl: 'https://payments.zoho.in/checkout/PS_123456789',
      amount: 100.0,
      currency: 'INR',
      invoiceNumber: 'INV-001',
      customerId: 'CUST-001',
      description: 'Test payment',
      customerName: 'Test Customer',
      customerEmail: '<EMAIL>',
      status: PaymentSessionStatus.created,
      createdAt: DateTime(2024, 1, 1),
    );

    test(
      'should create payment session successfully when request is valid',
      () async {
        // arrange
        when(
          mockRepository.createPaymentSession(any),
        ).thenAnswer((_) async => Right(tPaymentSession));

        // act
        final result = await useCase(tPaymentRequest);

        // assert
        expect(result, Right(tPaymentSession));
        verify(mockRepository.createPaymentSession(tPaymentRequest));
        verifyNoMoreInteractions(mockRepository);
      },
    );

    test('should return ValidationFailure when request is invalid', () async {
      // arrange
      const invalidRequest = PaymentRequest(
        amount: -100.0, // Invalid amount
        currency: '', // Invalid currency
        invoiceNumber: '',
        customerId: '',
      );

      // act
      final result = await useCase(invalidRequest);

      // assert
      expect(result, isA<Left<Failure, PaymentSession>>());
      expect(result.fold((l) => l, (r) => null), isA<ValidationFailure>());
      verifyZeroInteractions(mockRepository);
    });

    test(
      'should return ServerFailure when repository throws ServerException',
      () async {
        // arrange
        when(
          mockRepository.createPaymentSession(any),
        ).thenAnswer((_) async => Left(ServerFailure()));

        // act
        final result = await useCase(tPaymentRequest);

        // assert
        expect(result, Left(ServerFailure()));
        verify(mockRepository.createPaymentSession(tPaymentRequest));
        verifyNoMoreInteractions(mockRepository);
      },
    );

    test(
      'should return NetworkFailure when repository throws NetworkException',
      () async {
        // arrange
        when(
          mockRepository.createPaymentSession(any),
        ).thenAnswer((_) async => Left(NetworkFailure()));

        // act
        final result = await useCase(tPaymentRequest);

        // assert
        expect(result, Left(NetworkFailure()));
        verify(mockRepository.createPaymentSession(tPaymentRequest));
        verifyNoMoreInteractions(mockRepository);
      },
    );

    test('should validate required fields', () async {
      // arrange
      const requestWithMissingFields = PaymentRequest(
        amount: 100.0,
        currency: 'INR',
        invoiceNumber: '', // Missing
        customerId: '', // Missing
      );

      // act
      final result = await useCase(requestWithMissingFields);

      // assert
      expect(result, isA<Left<Failure, PaymentSession>>());
      expect(result.fold((l) => l, (r) => null), isA<ValidationFailure>());
      final failure = result.fold((l) => l, (r) => null) as ValidationFailure;
      expect(failure.toString(), contains('Invoice number is required'));
      expect(failure.toString(), contains('Customer ID is required'));
      verifyZeroInteractions(mockRepository);
    });

    test('should validate email format when provided', () async {
      // arrange
      const requestWithInvalidEmail = PaymentRequest(
        amount: 100.0,
        currency: 'INR',
        invoiceNumber: 'INV-001',
        customerId: 'CUST-001',
        customerEmail: 'invalid-email', // Invalid email
      );

      // act
      final result = await useCase(requestWithInvalidEmail);

      // assert
      expect(result, isA<Left<Failure, PaymentSession>>());
      expect(result.fold((l) => l, (r) => null), isA<ValidationFailure>());
      final failure = result.fold((l) => l, (r) => null) as ValidationFailure;
      expect(failure.toString(), contains('Invalid email format'));
      verifyZeroInteractions(mockRepository);
    });

    test('should validate amount is positive', () async {
      // arrange
      const requestWithNegativeAmount = PaymentRequest(
        amount: -50.0, // Negative amount
        currency: 'INR',
        invoiceNumber: 'INV-001',
        customerId: 'CUST-001',
      );

      // act
      final result = await useCase(requestWithNegativeAmount);

      // assert
      expect(result, isA<Left<Failure, PaymentSession>>());
      expect(result.fold((l) => l, (r) => null), isA<ValidationFailure>());
      final failure = result.fold((l) => l, (r) => null) as ValidationFailure;
      expect(failure.toString(), contains('Amount must be greater than 0'));
      verifyZeroInteractions(mockRepository);
    });
  });
}
