import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:dio/dio.dart';
import 'package:get_it/get_it.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:mockito/mockito.dart';

import '../core/constants/payment_test_config.dart';
import 'package:aquapartner/core/network/api_client.dart';
import 'package:aquapartner/core/utils/logger.dart';
import 'package:aquapartner/data/datasources/remote/payment_remote_datasource.dart';
import 'package:aquapartner/domain/entities/payments/payment_request.dart';

/// Mock secure storage for testing
class MockSecureStorage extends Mock implements FlutterSecureStorage {
  final Map<String, String> _storage = {};

  @override
  Future<void> write({
    required String key,
    required String? value,
    IOSOptions? iOptions,
    AndroidOptions? aOptions,
    LinuxOptions? lOptions,
    WebOptions? webOptions,
    MacOsOptions? mOptions,
    WindowsOptions? wOptions,
  }) async {
    if (value != null) {
      _storage[key] = value;
    }
  }

  @override
  Future<String?> read({
    required String key,
    IOSOptions? iOptions,
    AndroidOptions? aOptions,
    LinuxOptions? lOptions,
    WebOptions? webOptions,
    MacOsOptions? mOptions,
    WindowsOptions? wOptions,
  }) async {
    return _storage[key];
  }

  @override
  Future<void> delete({
    required String key,
    IOSOptions? iOptions,
    AndroidOptions? aOptions,
    LinuxOptions? lOptions,
    WebOptions? webOptions,
    MacOsOptions? mOptions,
    WindowsOptions? wOptions,
  }) async {
    _storage.remove(key);
  }

  @override
  Future<void> deleteAll({
    IOSOptions? iOptions,
    AndroidOptions? aOptions,
    LinuxOptions? lOptions,
    WebOptions? webOptions,
    MacOsOptions? mOptions,
    WindowsOptions? wOptions,
  }) async {
    _storage.clear();
  }

  @override
  Future<Map<String, String>> readAll({
    IOSOptions? iOptions,
    AndroidOptions? aOptions,
    LinuxOptions? lOptions,
    WebOptions? webOptions,
    MacOsOptions? mOptions,
    WindowsOptions? wOptions,
  }) async {
    return Map.from(_storage);
  }

  @override
  Future<bool> containsKey({
    required String key,
    IOSOptions? iOptions,
    AndroidOptions? aOptions,
    LinuxOptions? lOptions,
    WebOptions? webOptions,
    MacOsOptions? mOptions,
    WindowsOptions? wOptions,
  }) async {
    return _storage.containsKey(key);
  }
}

/// Mock interceptor for simulating API responses
class MockApiInterceptor extends Interceptor {
  final Map<String, dynamic> _mockResponses = {};
  final Map<String, DioException> _mockErrors = {};

  void addMockResponse(String path, Map<String, dynamic> response) {
    _mockResponses[path] = response;
  }

  void addMockError(String path, DioException error) {
    _mockErrors[path] = error;
  }

  void clearMocks() {
    _mockResponses.clear();
    _mockErrors.clear();
  }

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    final path = options.path;

    // Check if we have a mock error for this path
    if (_mockErrors.containsKey(path)) {
      handler.reject(_mockErrors[path]!);
      return;
    }

    // Check if we have a mock response for this exact path
    if (_mockResponses.containsKey(path)) {
      final response = Response(
        requestOptions: options,
        data: _mockResponses[path],
        statusCode: 200,
        headers: Headers.fromMap({
          'content-type': ['application/json'],
        }),
      );
      handler.resolve(response);
      return;
    }

    // Check for pattern matches (e.g., /zoho/payments/status/{sessionId})
    String? matchedResponse = _findPatternMatch(path);
    if (matchedResponse != null) {
      final response = Response(
        requestOptions: options,
        data: _mockResponses[matchedResponse],
        statusCode: 200,
        headers: Headers.fromMap({
          'content-type': ['application/json'],
        }),
      );
      handler.resolve(response);
      return;
    }

    // If no mock found, reject with server error
    handler.reject(
      DioException(
        requestOptions: options,
        type: DioExceptionType.badResponse,
        response: Response(
          requestOptions: options,
          statusCode: 500,
          statusMessage: 'Internal Server Error',
        ),
      ),
    );
  }

  String? _findPatternMatch(String path) {
    // Check for payment status pattern: /zoho/payments/status/{sessionId}
    if (path.startsWith('/zoho/payments/status/')) {
      return '/zoho/payments/status/*';
    }

    // Check for transaction pattern: /zoho/payments/transaction/{transactionId}
    if (path.startsWith('/zoho/payments/transaction/')) {
      return '/zoho/payments/transaction/*';
    }

    return null;
  }
}

/// Integration tests for Zoho Payment API
///
/// These tests verify the payment API integration without requiring:
/// - External API endpoints
/// - Network connectivity
/// - Real credentials
///
/// The tests use mocked HTTP responses to simulate API behavior.
///
/// Run with: flutter test test/integration/payment_api_integration_test.dart
void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Zoho Payment API Integration Tests', () {
    late PaymentRemoteDataSource dataSource;
    late AppLogger logger;
    late MockApiInterceptor mockInterceptor;

    void setupMockResponses(MockApiInterceptor interceptor) {
      // Mock successful payment session creation
      interceptor.addMockResponse('/zoho/payments/create-session', {
        'success': true,
        'data': {
          'payment_session': {
            'payments_session_id':
                'PS_MOCK_${DateTime.now().millisecondsSinceEpoch}',
            'payment_url':
                'https://payments.zoho.in/checkout/PS_MOCK_123456789',
            'amount': 500.0,
            'currency': 'INR',
            'status': 'created',
            'created_time': DateTime.now().toIso8601String(),
            'expires_at':
                DateTime.now().add(Duration(hours: 1)).toIso8601String(),
          },
          'invoiceNo': 'TEST_INV_123456789',
          'customerId': 'TEST_CUSTOMER_001',
          'description': 'Test payment for integration testing',
          'customerName': 'Test Customer',
          'customerEmail': '<EMAIL>',
        },
      });

      // Mock payment status check (using pattern matching for dynamic session IDs)
      // This will match any path like /zoho/payments/status/{sessionId}
      interceptor.addMockResponse('/zoho/payments/status/*', {
        'success': true,
        'data': {
          'payment_session': {
            'payments_session_id': 'PS_MOCK_123456789',
            'payment_url':
                'https://payments.zoho.in/checkout/PS_MOCK_123456789',
            'amount': 100.0,
            'currency': 'INR',
            'status': 'pending',
            'created_time': DateTime.now().toIso8601String(),
            'expires_at':
                DateTime.now().add(Duration(hours: 1)).toIso8601String(),
          },
        },
      });

      // Mock payment verification (same endpoint as status check)
      // The verifyPayment method uses the same endpoint as checkPaymentStatus

      // Mock webhook processing
      interceptor.addMockResponse('/zoho/webhooks/payment', {
        'success': true,
        'data': {
          'transaction': {
            'transaction_id':
                'TXN_MOCK_${DateTime.now().millisecondsSinceEpoch}',
            'session_id': 'PS_MOCK_123456789',
            'invoice_number': 'TEST_INV_123456789',
            'customer_id': 'TEST_CUSTOMER_001',
            'amount': 100.0,
            'currency': 'INR',
            'status': 'success',
            'payment_method': 'credit_card',
            'transaction_date': DateTime.now().toIso8601String(),
          },
        },
      });
    }

    setUpAll(() async {
      // Initialize test dependencies
      logger = AppLogger();

      // Create mock interceptor
      mockInterceptor = MockApiInterceptor();

      // Create test API client
      final dio = Dio(
        BaseOptions(
          baseUrl: PaymentTestConfig.currentApiBaseUrl,
          connectTimeout: const Duration(seconds: 30),
          receiveTimeout: const Duration(seconds: 30),
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            // Add test authentication headers
            'Authorization': 'Bearer ${PaymentTestConfig.testApiKey}',
          },
        ),
      );

      // Add mock interceptor to simulate API responses
      dio.interceptors.add(mockInterceptor);

      // Create test secure storage (mock for testing)
      final secureStorage = MockSecureStorage();
      final apiClient = ApiClient(dio, secureStorage);

      // Initialize data source and repository
      dataSource = PaymentRemoteDataSourceImpl(
        apiClient: apiClient,
        logger: logger,
      );

      // Note: You'll need to implement the repository
      // repository = PaymentRepositoryImpl(
      //   remoteDataSource: dataSource,
      //   networkInfo: networkInfo,
      //   logger: logger,
      // );

      // Initialize use cases
      // createPaymentSession = CreatePaymentSessionUseCase(repository);
      // verifyPayment = VerifyPaymentUseCase(repository);

      // Setup mock responses for successful scenarios
      setupMockResponses(mockInterceptor);
    });

    group('Payment Session Creation', () {
      testWidgets(
        'should create payment session successfully with valid data',
        (tester) async {
          // Arrange
          final paymentRequest = PaymentRequest(
            amount: PaymentTestConfig.testAmountMedium,
            currency: 'INR',
            invoiceNumber: PaymentTestConfig.generateTestInvoiceNumber(),
            customerId: PaymentTestConfig.testCustomerId,
            description: PaymentTestConfig.testInvoiceDescription,
            customerName: PaymentTestConfig.testCustomerName,
            customerEmail: PaymentTestConfig.testCustomerEmail,
          );

          // Act
          final paymentSession = await dataSource.createPaymentSession(
            paymentRequest,
          );

          // Assert
          expect(paymentSession.sessionId, isNotEmpty);
          expect(paymentSession.paymentUrl, isNotEmpty);
          expect(
            paymentSession.amount,
            equals(PaymentTestConfig.testAmountMedium),
          );
          expect(paymentSession.currency, equals('INR'));
          expect(
            paymentSession.invoiceNumber,
            equals(paymentRequest.invoiceNumber),
          );
          expect(
            paymentSession.customerId,
            equals(PaymentTestConfig.testCustomerId),
          );

          // Verify URL format
          expect(paymentSession.paymentUrl, contains('payments'));
          expect(paymentSession.paymentUrl, contains(paymentSession.sessionId));

          logger.i('✅ Payment session created: ${paymentSession.sessionId}');
        },
      );

      testWidgets('should handle invalid payment request gracefully', (
        tester,
      ) async {
        // Arrange
        final invalidRequest = PaymentRequest(
          amount: -100.0, // Invalid amount
          currency: '', // Invalid currency
          invoiceNumber: '',
          customerId: '',
        );

        // Act & Assert
        expect(
          () => dataSource.createPaymentSession(invalidRequest),
          throwsA(isA<Exception>()),
        );

        logger.i('✅ Invalid request handled correctly');
      });

      testWidgets('should create session with different amounts', (
        tester,
      ) async {
        final testAmounts = [
          PaymentTestConfig.testAmountSmall,
          PaymentTestConfig.testAmountMedium,
          PaymentTestConfig.testAmountLarge,
        ];

        for (final amount in testAmounts) {
          // Arrange
          final paymentRequest = PaymentRequest(
            amount: amount,
            currency: 'INR',
            invoiceNumber: PaymentTestConfig.generateTestInvoiceNumber(),
            customerId: PaymentTestConfig.generateTestCustomerId(),
            description: 'Test payment for amount $amount',
          );

          // Act
          final paymentSession = await dataSource.createPaymentSession(
            paymentRequest,
          );

          // Assert
          expect(paymentSession.amount, equals(amount));
          expect(paymentSession.sessionId, isNotEmpty);

          logger.i(
            '✅ Payment session created for amount $amount: ${paymentSession.sessionId}',
          );
        }
      });
    });

    group('Payment Status Checking', () {
      late String testSessionId;

      setUp(() async {
        // Create a test payment session first
        final paymentRequest = PaymentRequest(
          amount: PaymentTestConfig.testAmountSmall,
          currency: 'INR',
          invoiceNumber: PaymentTestConfig.generateTestInvoiceNumber(),
          customerId: PaymentTestConfig.testCustomerId,
          description: 'Test payment for status checking',
        );

        final paymentSession = await dataSource.createPaymentSession(
          paymentRequest,
        );
        testSessionId = paymentSession.sessionId;
      });

      testWidgets('should check payment status successfully', (tester) async {
        // Act
        final paymentSession = await dataSource.checkPaymentStatus(
          testSessionId,
        );

        // Assert
        expect(paymentSession.sessionId, equals(testSessionId));
        expect(paymentSession.status, isNotNull);

        logger.i('✅ Payment status checked: ${paymentSession.status.name}');
      });

      testWidgets('should handle non-existent session ID', (tester) async {
        // Arrange
        const nonExistentSessionId = 'NON_EXISTENT_SESSION_123';

        // Act & Assert
        expect(
          () => dataSource.checkPaymentStatus(nonExistentSessionId),
          throwsA(isA<Exception>()),
        );

        logger.i('✅ Non-existent session handled correctly');
      });
    });

    group('Payment Verification', () {
      testWidgets('should verify payment when session exists', (tester) async {
        // Arrange - Create a test payment session
        final paymentRequest = PaymentRequest(
          amount: PaymentTestConfig.testAmountSmall,
          currency: 'INR',
          invoiceNumber: PaymentTestConfig.generateTestInvoiceNumber(),
          customerId: PaymentTestConfig.testCustomerId,
          description: 'Test payment for verification',
        );

        final paymentSession = await dataSource.createPaymentSession(
          paymentRequest,
        );

        // Act
        final transaction = await dataSource.verifyPayment(
          paymentSession.sessionId,
        );

        // Assert
        expect(transaction.sessionId, equals(paymentSession.sessionId));
        expect(transaction.invoiceNumber, equals(paymentRequest.invoiceNumber));
        expect(transaction.customerId, equals(paymentRequest.customerId));
        expect(transaction.amount, equals(paymentRequest.amount));

        logger.i('✅ Payment verified: ${transaction.transactionId}');
      });
    });

    group('Error Handling', () {
      testWidgets('should handle network timeouts gracefully', (tester) async {
        // This test would require mocking network conditions
        // or using a test server that can simulate timeouts

        logger.i('✅ Network timeout handling test (implementation needed)');
      });

      testWidgets('should handle authentication errors', (tester) async {
        // Create API client with invalid credentials
        final invalidDio = Dio(
          BaseOptions(
            baseUrl: PaymentTestConfig.currentApiBaseUrl,
            headers: {'Authorization': 'Bearer invalid_token'},
          ),
        );

        final invalidSecureStorage = MockSecureStorage();
        final invalidApiClient = ApiClient(invalidDio, invalidSecureStorage);
        final invalidDataSource = PaymentRemoteDataSourceImpl(
          apiClient: invalidApiClient,
          logger: logger,
        );

        final paymentRequest = PaymentRequest(
          amount: PaymentTestConfig.testAmountSmall,
          currency: 'INR',
          invoiceNumber: PaymentTestConfig.generateTestInvoiceNumber(),
          customerId: PaymentTestConfig.testCustomerId,
        );

        // Act & Assert
        expect(
          () => invalidDataSource.createPaymentSession(paymentRequest),
          throwsA(isA<Exception>()),
        );

        logger.i('✅ Authentication error handled correctly');
      });
    });

    group('Performance Tests', () {
      testWidgets('should handle concurrent payment session creation', (
        tester,
      ) async {
        const concurrentRequests = 5;
        final futures = <Future>[];

        for (int i = 0; i < concurrentRequests; i++) {
          final paymentRequest = PaymentRequest(
            amount: PaymentTestConfig.testAmountSmall,
            currency: 'INR',
            invoiceNumber: PaymentTestConfig.generateTestInvoiceNumber(),
            customerId: '${PaymentTestConfig.testCustomerId}_$i',
            description: 'Concurrent test payment $i',
          );

          futures.add(dataSource.createPaymentSession(paymentRequest));
        }

        // Act
        final results = await Future.wait(futures);

        // Assert
        expect(results.length, equals(concurrentRequests));
        for (final result in results) {
          expect(result.sessionId, isNotEmpty);
        }

        logger.i('✅ Concurrent requests handled successfully');
      });

      testWidgets('should complete payment session creation within timeout', (
        tester,
      ) async {
        final paymentRequest = PaymentRequest(
          amount: PaymentTestConfig.testAmountMedium,
          currency: 'INR',
          invoiceNumber: PaymentTestConfig.generateTestInvoiceNumber(),
          customerId: PaymentTestConfig.testCustomerId,
        );

        // Act
        final stopwatch = Stopwatch()..start();
        final paymentSession = await dataSource.createPaymentSession(
          paymentRequest,
        );
        stopwatch.stop();

        // Assert
        expect(paymentSession.sessionId, isNotEmpty);
        expect(stopwatch.elapsed, lessThan(const Duration(seconds: 10)));

        logger.i(
          '✅ Payment session created in ${stopwatch.elapsed.inMilliseconds}ms',
        );
      });
    });
  });
}

/// Helper class for running manual API tests
class PaymentApiTestRunner {
  static Future<void> runManualTests() async {
    final logger = AppLogger();

    logger.i('🧪 Starting manual Zoho Payment API tests...');

    try {
      await _testCreatePaymentSession(logger);
      await _testCheckPaymentStatus(logger);
      await _testErrorScenarios(logger);

      logger.i('✅ All manual tests completed successfully!');
    } catch (e) {
      logger.e('❌ Manual tests failed: $e');
    }
  }

  static Future<void> _testCreatePaymentSession(AppLogger logger) async {
    logger.i('Testing payment session creation...');

    // Implementation would go here
    // This is a placeholder for manual testing
  }

  static Future<void> _testCheckPaymentStatus(AppLogger logger) async {
    logger.i('Testing payment status checking...');

    // Implementation would go here
  }

  static Future<void> _testErrorScenarios(AppLogger logger) async {
    logger.i('Testing error scenarios...');

    // Implementation would go here
  }
}
