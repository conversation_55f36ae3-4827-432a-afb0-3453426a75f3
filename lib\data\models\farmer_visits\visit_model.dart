import '../../../domain/entities/farmer_visit/visit.dart';

/// Model for each visit entry within a farmer's visits list in the JSON.
class VisitModel {
  final String pondId;
  final String farmerId;
  final String farmerName;
  final String mobileNumber;
  final DateTime createdDateTime;
  final int doc;
  final String productUsed;

  VisitModel({
    required this.createdDateTime,
    required this.doc,
    required this.pondId,
    required this.mobileNumber,
    required this.productUsed,
    required this.farmerId,
    required this.farmerName,
  });

  factory VisitModel.fromJson(
    Map<String, dynamic> json, {
    String? farmerId,
    String? farmerName,
  }) {
    return VisitModel(
      createdDateTime: DateTime.parse(json['createdDateTime']),
      doc:
          json['doc'] is int
              ? json['doc']
              : int.tryParse(json['doc']?.toString() ?? '0') ?? 0,
      pondId: json['pondId'] ?? '',
      mobileNumber: json['mobileNumber'] ?? '',
      productUsed: json['productUsed'] ?? '',
      farmerId: farmerId ?? json['farmerId'] ?? '',
      farmerName: farmerName ?? json['farmerName'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'createdDateTime': createdDateTime.toIso8601String(),
      'doc': doc,
      'pondId': pondId,
      'mobileNumber': mobileNumber,
      'productUsed': productUsed,
      'farmerId': farmerId,
      'farmerName': farmerName,
    };
  }

  // Method to map the VisitModel to a Visit domain entity
  Visit toDomain() {
    return Visit(
      id: null, // API models don't have IDs until stored in the database
      createdDateTime: createdDateTime,
      doc: doc,
      pondId: pondId,
      mobileNumber: mobileNumber,
      productUsed: productUsed,
      farmerId: farmerId,
      farmerName: farmerName,
    );
  }

  // Factory constructor to create a VisitModel from a domain Visit
  factory VisitModel.fromDomain(Visit visit) {
    return VisitModel(
      createdDateTime: visit.createdDateTime,
      doc: visit.doc,
      pondId: visit.pondId,
      mobileNumber: visit.mobileNumber,
      productUsed: visit.productUsed,
      farmerId: visit.farmerId,
      farmerName: visit.farmerName,
    );
  }
}
