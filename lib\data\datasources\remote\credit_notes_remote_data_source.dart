// lib/data/datasources/credit_notes_remote_data_source.dart
import 'dart:convert';

import '../../../core/constants/app_constants.dart';
import '../../../core/error/exceptions.dart';
import '../../../core/network/api_client.dart';
import '../../../core/utils/logger.dart';
import '../../models/credit_notes/credit_note_model.dart';

abstract class CreditNotesRemoteDataSource {
  Future<List<CreditNoteModel>> getCreditNotes(String customerId);
}

class CreditNotesRemoteDataSourceImpl implements CreditNotesRemoteDataSource {
  final ApiClient apiClient;
  final AppLogger logger;

  CreditNotesRemoteDataSourceImpl({
    required this.apiClient,
    required this.logger,
  });

  @override
  Future<List<CreditNoteModel>> getCreditNotes(String customerId) async {
    try {
      final response = await apiClient.get(
        '${AppConstants.baseUrl}/creditNotes/$customerId',
      );

      if (response.statusCode == 200 && response.data != null) {
        final jsonData = jsonDecode(response.data);
        final results = jsonData['results'] as List?;
        if (results != null) {
          return results
              .map(
                (json) =>
                    CreditNoteModel.fromJson(json as Map<String, dynamic>),
              )
              .toList();
        } else {
          return []; // Return empty list if 'results' is null or missing
        }
      } else {
        throw ServerException();
      }
    } catch (e) {
      // Handle other potential errors (e.g., parsing)
      throw ServerException();
    }
  }
}
