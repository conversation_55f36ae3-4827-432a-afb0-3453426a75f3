import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:dartz/dartz.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:aquapartner/data/repositories/auth_repository_impl.dart';
import 'package:aquapartner/data/datasources/local/user_local_data_source.dart';
import 'package:aquapartner/data/datasources/local/customer_local_data_source.dart';
import 'package:aquapartner/data/datasources/local/dashboard_local_datasource.dart';
import 'package:aquapartner/data/datasources/remote/auth_remote_data_source.dart';
import 'package:aquapartner/core/network/network_info.dart';
import 'package:aquapartner/core/utils/logger.dart';
import 'package:aquapartner/core/error/failures.dart';

// Mock classes
class MockAuthRemoteDataSource extends Mock implements AuthRemoteDataSource {}

class MockUserLocalDataSource extends Mock implements UserLocalDataSource {}

class MockCustomerLocalDataSource extends Mock
    implements CustomerLocalDataSource {}

class MockDashboardLocalDataSource extends Mock
    implements DashboardLocalDataSource {}

class MockNetworkInfo extends Mock implements NetworkInfo {}

class MockFirebaseAuth extends Mock implements FirebaseAuth {}

class MockAppLogger extends Mock implements AppLogger {}

class MockUser extends Mock implements User {}

void main() {
  group('AuthRepositoryImpl Tests', () {
    late AuthRepositoryImpl repository;
    late MockAuthRemoteDataSource mockRemoteDataSource;
    late MockUserLocalDataSource mockUserLocalDataSource;
    late MockCustomerLocalDataSource mockCustomerLocalDataSource;
    late MockDashboardLocalDataSource mockDashboardLocalDataSource;
    late MockNetworkInfo mockNetworkInfo;
    late MockFirebaseAuth mockFirebaseAuth;
    late MockAppLogger mockLogger;
    late MockUser mockUser;

    const testPhoneNumber = '+919999999999';
    const testVerificationId = 'test_verification_id_123';
    const testOtp = '123456';

    setUp(() {
      mockRemoteDataSource = MockAuthRemoteDataSource();
      mockUserLocalDataSource = MockUserLocalDataSource();
      mockCustomerLocalDataSource = MockCustomerLocalDataSource();
      mockDashboardLocalDataSource = MockDashboardLocalDataSource();
      mockNetworkInfo = MockNetworkInfo();
      mockFirebaseAuth = MockFirebaseAuth();
      mockLogger = MockAppLogger();
      mockUser = MockUser();

      repository = AuthRepositoryImpl(
        remoteDataSource: mockRemoteDataSource,
        localDataSource: mockUserLocalDataSource,
        customerLocalDataSource: mockCustomerLocalDataSource,
        dashboardLocalDataSource: mockDashboardLocalDataSource,
        networkInfo: mockNetworkInfo,
        firebaseAuth: mockFirebaseAuth,
        logger: mockLogger,
      );
    });

    group('sendOtp Tests', () {
      test('should send OTP successfully when network is connected', () async {
        // Arrange
        when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        when(
          () => mockRemoteDataSource.sendOtp(testPhoneNumber),
        ).thenAnswer((_) async => const Right(testVerificationId));

        // Act
        final result = await repository.sendOtp(testPhoneNumber);

        // Assert
        expect(result, equals(const Right(testVerificationId)));
        verify(() => mockNetworkInfo.isConnected).called(1);
        verify(() => mockRemoteDataSource.sendOtp(testPhoneNumber)).called(1);
        verify(() => mockLogger.i('Network connected, sending OTP')).called(1);
      });

      test(
        'should return NetworkFailure when no internet connection',
        () async {
          // Arrange
          when(
            () => mockNetworkInfo.isConnected,
          ).thenAnswer((_) async => false);

          // Act
          final result = await repository.sendOtp(testPhoneNumber);

          // Assert
          expect(result, equals(Left(NetworkFailure())));
          verify(() => mockNetworkInfo.isConnected).called(1);
          verify(
            () => mockLogger.w('No network connection, cannot send OTP'),
          ).called(1);
          verifyNever(() => mockRemoteDataSource.sendOtp(any()));
        },
      );

      test('should return failure from remote data source', () async {
        // Arrange
        when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        when(
          () => mockRemoteDataSource.sendOtp(testPhoneNumber),
        ).thenAnswer((_) async => Left(ServerFailure()));

        // Act
        final result = await repository.sendOtp(testPhoneNumber);

        // Assert
        expect(result, equals(Left(ServerFailure())));
        verify(() => mockRemoteDataSource.sendOtp(testPhoneNumber)).called(1);
      });

      test('should handle empty phone number', () async {
        // Arrange
        when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        when(() => mockRemoteDataSource.sendOtp('')).thenAnswer(
          (_) async => Left(ValidationFailure('Empty phone number')),
        );

        // Act
        final result = await repository.sendOtp('');

        // Assert
        expect(result, equals(Left(ValidationFailure('Empty phone number'))));
        verify(() => mockRemoteDataSource.sendOtp('')).called(1);
      });

      test('should handle invalid phone number format', () async {
        // Arrange
        const invalidPhoneNumber = 'invalid_phone';
        when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        when(() => mockRemoteDataSource.sendOtp(invalidPhoneNumber)).thenAnswer(
          (_) async => Left(ValidationFailure('Invalid phone number')),
        );

        // Act
        final result = await repository.sendOtp(invalidPhoneNumber);

        // Assert
        expect(result, equals(Left(ValidationFailure('Invalid phone number'))));
        verify(
          () => mockRemoteDataSource.sendOtp(invalidPhoneNumber),
        ).called(1);
      });
    });

    group('verifyOtp Tests', () {
      test(
        'should verify OTP successfully when network is connected',
        () async {
          // Arrange
          when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
          when(
            () => mockRemoteDataSource.verifyOtp(testVerificationId, testOtp),
          ).thenAnswer((_) async => const Right(true));

          // Act
          final result = await repository.verifyOtp(
            testVerificationId,
            testOtp,
          );

          // Assert
          expect(result, equals(const Right(true)));
          verify(() => mockNetworkInfo.isConnected).called(1);
          verify(
            () => mockRemoteDataSource.verifyOtp(testVerificationId, testOtp),
          ).called(1);
          verify(
            () => mockLogger.i('Network connected, verifying OTP'),
          ).called(1);
        },
      );

      test(
        'should return NetworkFailure when no internet connection',
        () async {
          // Arrange
          when(
            () => mockNetworkInfo.isConnected,
          ).thenAnswer((_) async => false);

          // Act
          final result = await repository.verifyOtp(
            testVerificationId,
            testOtp,
          );

          // Assert
          expect(result, equals(Left(NetworkFailure())));
          verify(() => mockNetworkInfo.isConnected).called(1);
          verify(
            () => mockLogger.w('No network connection, cannot verify OTP'),
          ).called(1);
          verifyNever(() => mockRemoteDataSource.verifyOtp(any(), any()));
        },
      );

      test('should return failure from remote data source', () async {
        // Arrange
        when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        when(
          () => mockRemoteDataSource.verifyOtp(testVerificationId, testOtp),
        ).thenAnswer((_) async => Left(AuthFailure()));

        // Act
        final result = await repository.verifyOtp(testVerificationId, testOtp);

        // Assert
        expect(result, equals(Left(AuthFailure())));
        verify(
          () => mockRemoteDataSource.verifyOtp(testVerificationId, testOtp),
        ).called(1);
      });

      test('should handle invalid verification ID', () async {
        // Arrange
        const invalidVerificationId = 'invalid_id';
        when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        when(
          () => mockRemoteDataSource.verifyOtp(invalidVerificationId, testOtp),
        ).thenAnswer((_) async => Left(AuthFailure()));

        // Act
        final result = await repository.verifyOtp(
          invalidVerificationId,
          testOtp,
        );

        // Assert
        expect(result, equals(Left(AuthFailure())));
        verify(
          () => mockRemoteDataSource.verifyOtp(invalidVerificationId, testOtp),
        ).called(1);
      });

      test('should handle invalid OTP', () async {
        // Arrange
        const invalidOtp = '000000';
        when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        when(
          () => mockRemoteDataSource.verifyOtp(testVerificationId, invalidOtp),
        ).thenAnswer((_) async => Left(AuthFailure()));

        // Act
        final result = await repository.verifyOtp(
          testVerificationId,
          invalidOtp,
        );

        // Assert
        expect(result, equals(Left(AuthFailure())));
        verify(
          () => mockRemoteDataSource.verifyOtp(testVerificationId, invalidOtp),
        ).called(1);
      });

      test('should handle empty OTP', () async {
        // Arrange
        when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        when(
          () => mockRemoteDataSource.verifyOtp(testVerificationId, ''),
        ).thenAnswer((_) async => Left(ValidationFailure('Empty OTP')));

        // Act
        final result = await repository.verifyOtp(testVerificationId, '');

        // Assert
        expect(result, equals(Left(ValidationFailure('Empty OTP'))));
        verify(
          () => mockRemoteDataSource.verifyOtp(testVerificationId, ''),
        ).called(1);
      });

      test('should handle OTP with wrong length', () async {
        // Arrange
        const shortOtp = '123';
        when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        when(
          () => mockRemoteDataSource.verifyOtp(testVerificationId, shortOtp),
        ).thenAnswer(
          (_) async => Left(ValidationFailure('Invalid OTP length')),
        );

        // Act
        final result = await repository.verifyOtp(testVerificationId, shortOtp);

        // Assert
        expect(result, equals(Left(ValidationFailure('Invalid OTP length'))));
        verify(
          () => mockRemoteDataSource.verifyOtp(testVerificationId, shortOtp),
        ).called(1);
      });
    });

    group('signOut Tests', () {
      test('should sign out successfully and clear all local data', () async {
        // Arrange
        when(() => mockRemoteDataSource.signOut()).thenAnswer((_) async {});
        when(
          () => mockUserLocalDataSource.deleteUser(),
        ).thenAnswer((_) async {});
        when(
          () => mockCustomerLocalDataSource.deleteCustomer(),
        ).thenAnswer((_) async {});
        when(
          () => mockDashboardLocalDataSource.deleteDashboard(),
        ).thenAnswer((_) async {});

        // Act
        await repository.signOut();

        // Assert
        verify(() => mockRemoteDataSource.signOut()).called(1);
        verify(() => mockUserLocalDataSource.deleteUser()).called(1);
        verify(() => mockCustomerLocalDataSource.deleteCustomer()).called(1);
        verify(() => mockDashboardLocalDataSource.deleteDashboard()).called(1);
        verify(() => mockLogger.i('Signing out user')).called(1);
      });

      test('should throw exception when local data deletion fails', () async {
        // Arrange
        when(() => mockRemoteDataSource.signOut()).thenAnswer((_) async {});
        when(
          () => mockUserLocalDataSource.deleteUser(),
        ).thenThrow(Exception('Delete user failed'));
        when(
          () => mockCustomerLocalDataSource.deleteCustomer(),
        ).thenAnswer((_) async {});
        when(
          () => mockDashboardLocalDataSource.deleteDashboard(),
        ).thenAnswer((_) async {});

        // Act & Assert - Should throw exception
        expect(() => repository.signOut(), throwsA(isA<Exception>()));

        // Note: verify calls are not made here because the exception prevents
        // the test from completing normally. The exception handling is tested
        // by the expect() call above.
      });

      test('should throw exception when remote sign out fails', () async {
        // Arrange
        when(
          () => mockRemoteDataSource.signOut(),
        ).thenThrow(Exception('Remote sign out failed'));
        when(
          () => mockUserLocalDataSource.deleteUser(),
        ).thenAnswer((_) async {});
        when(
          () => mockCustomerLocalDataSource.deleteCustomer(),
        ).thenAnswer((_) async {});
        when(
          () => mockDashboardLocalDataSource.deleteDashboard(),
        ).thenAnswer((_) async {});

        // Act & Assert - Should throw exception
        expect(() => repository.signOut(), throwsA(isA<Exception>()));

        // Note: verify calls are not made here because the exception prevents
        // the test from completing normally. The exception handling is tested
        // by the expect() call above.
      });
    });

    group('isUserLoggedIn Tests', () {
      test('should return true when user is logged in', () async {
        // Arrange
        when(() => mockFirebaseAuth.currentUser).thenReturn(mockUser);

        // Act
        final result = await repository.isUserLoggedIn();

        // Assert
        expect(result, isTrue);
        verify(() => mockFirebaseAuth.currentUser).called(1);
        verify(() => mockLogger.i('Checking if user is logged in')).called(1);
        verify(() => mockLogger.i('User logged in: true')).called(1);
      });

      test('should return false when user is not logged in', () async {
        // Arrange
        when(() => mockFirebaseAuth.currentUser).thenReturn(null);

        // Act
        final result = await repository.isUserLoggedIn();

        // Assert
        expect(result, isFalse);
        verify(() => mockFirebaseAuth.currentUser).called(1);
        verify(() => mockLogger.i('Checking if user is logged in')).called(1);
        verify(() => mockLogger.i('User logged in: false')).called(1);
      });

      test('should return false and log error when exception occurs', () async {
        // Arrange
        when(
          () => mockFirebaseAuth.currentUser,
        ).thenThrow(Exception('Firebase error'));

        // Act
        final result = await repository.isUserLoggedIn();

        // Assert
        expect(result, isFalse);
        verify(() => mockFirebaseAuth.currentUser).called(1);
        verify(
          () => mockLogger.e('Error checking if user is logged in', any()),
        ).called(1);
      });

      test('should handle Firebase Auth state changes', () async {
        // Arrange & Act - Test first state (logged in)
        when(() => mockFirebaseAuth.currentUser).thenReturn(mockUser);
        final result1 = await repository.isUserLoggedIn();

        // Arrange & Act - Test second state (logged out)
        when(() => mockFirebaseAuth.currentUser).thenReturn(null);
        final result2 = await repository.isUserLoggedIn();

        // Assert
        expect(result1, isTrue);
        expect(result2, isFalse);
        verify(() => mockFirebaseAuth.currentUser).called(2);
      });
    });

    group('Edge Cases and Integration Tests', () {
      test('should handle concurrent OTP operations', () async {
        // Arrange
        when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        when(
          () => mockRemoteDataSource.sendOtp(testPhoneNumber),
        ).thenAnswer((_) async => const Right(testVerificationId));
        when(
          () => mockRemoteDataSource.verifyOtp(testVerificationId, testOtp),
        ).thenAnswer((_) async => const Right(true));

        // Act
        final futures = [
          repository.sendOtp(testPhoneNumber),
          repository.verifyOtp(testVerificationId, testOtp),
        ];

        final results = await Future.wait(futures);

        // Assert
        expect(results[0], equals(const Right(testVerificationId)));
        expect(results[1], equals(const Right(true)));
      });

      test(
        'should handle network connectivity changes during operations',
        () async {
          // Arrange & Act - First call with network connected
          when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
          when(
            () => mockRemoteDataSource.sendOtp(testPhoneNumber),
          ).thenAnswer((_) async => const Right(testVerificationId));
          final result1 = await repository.sendOtp(testPhoneNumber);

          // Arrange & Act - Second call with network disconnected
          when(
            () => mockNetworkInfo.isConnected,
          ).thenAnswer((_) async => false);
          final result2 = await repository.sendOtp(testPhoneNumber);

          // Assert
          expect(result1, equals(const Right(testVerificationId)));
          expect(result2, equals(Left(NetworkFailure())));
        },
      );

      test('should handle session management across operations', () async {
        // Arrange
        when(() => mockFirebaseAuth.currentUser).thenReturn(mockUser);
        when(() => mockRemoteDataSource.signOut()).thenAnswer((_) async {});
        when(
          () => mockUserLocalDataSource.deleteUser(),
        ).thenAnswer((_) async {});
        when(
          () => mockCustomerLocalDataSource.deleteCustomer(),
        ).thenAnswer((_) async {});
        when(
          () => mockDashboardLocalDataSource.deleteDashboard(),
        ).thenAnswer((_) async {});

        // Act
        final isLoggedInBefore = await repository.isUserLoggedIn();
        await repository.signOut();

        when(() => mockFirebaseAuth.currentUser).thenReturn(null);

        final isLoggedInAfter = await repository.isUserLoggedIn();

        // Assert
        expect(isLoggedInBefore, isTrue);
        expect(isLoggedInAfter, isFalse);
      });

      test('should handle multiple sign out calls gracefully', () async {
        // Arrange
        when(() => mockRemoteDataSource.signOut()).thenAnswer((_) async {});
        when(
          () => mockUserLocalDataSource.deleteUser(),
        ).thenAnswer((_) async {});
        when(
          () => mockCustomerLocalDataSource.deleteCustomer(),
        ).thenAnswer((_) async {});
        when(
          () => mockDashboardLocalDataSource.deleteDashboard(),
        ).thenAnswer((_) async {});

        // Act
        await repository.signOut();
        await repository.signOut();

        // Assert - Should not throw exception
        verify(() => mockRemoteDataSource.signOut()).called(2);
        verify(() => mockUserLocalDataSource.deleteUser()).called(2);
        verify(() => mockCustomerLocalDataSource.deleteCustomer()).called(2);
        verify(() => mockDashboardLocalDataSource.deleteDashboard()).called(2);
      });

      test('should handle very long phone numbers', () async {
        // Arrange
        const longPhoneNumber = '+919999999999123456789012345';
        when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        when(() => mockRemoteDataSource.sendOtp(longPhoneNumber)).thenAnswer(
          (_) async => Left(ValidationFailure('Phone number too long')),
        );

        // Act
        final result = await repository.sendOtp(longPhoneNumber);

        // Assert
        expect(
          result,
          equals(Left(ValidationFailure('Phone number too long'))),
        );
      });

      test('should handle special characters in verification ID', () async {
        // Arrange
        const specialVerificationId = 'test@#\$%^&*()_+verification_id';
        when(() => mockNetworkInfo.isConnected).thenAnswer((_) async => true);
        when(
          () => mockRemoteDataSource.verifyOtp(specialVerificationId, testOtp),
        ).thenAnswer((_) async => const Right(true));

        // Act
        final result = await repository.verifyOtp(
          specialVerificationId,
          testOtp,
        );

        // Assert
        expect(result, equals(const Right(true)));
      });
    });
  });
}
