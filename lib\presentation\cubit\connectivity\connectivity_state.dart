import 'package:equatable/equatable.dart';

enum ConnectionStatus { connected, disconnected }

class ConnectivityState extends Equatable {
  final ConnectionStatus status;

  const ConnectivityState({required this.status});

  factory ConnectivityState.initial() {
    return const ConnectivityState(status: ConnectionStatus.disconnected);
  }

  ConnectivityState copyWith({ConnectionStatus? status}) {
    return ConnectivityState(status: status ?? this.status);
  }

  bool get isConnected => status == ConnectionStatus.connected;

  @override
  List<Object> get props => [status];
}
