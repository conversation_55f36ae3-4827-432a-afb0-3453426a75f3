import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/error/failures.dart';
import '../../../core/services/analytics_service.dart';
import '../../../core/utils/logger.dart';
import '../../../domain/entities/user.dart';
import '../../../domain/usecases/auth_usecases.dart';
import '../../../domain/usecases/customer_usercases.dart';
import '../../../domain/usecases/sync_usecases.dart';
import '../../../domain/usecases/user_usecases.dart';

import 'auth_state.dart';

class AuthCubit extends Cubit<AuthState> {
  final SendOtpUseCase sendOtpUseCase;
  final VerifyOtpUseCase verifyOtpUseCase;
  final SignOutUseCase signOutUseCase;
  final GetUserUseCase getUserUseCase;
  final SaveUserUseCase saveUserUseCase;
  final UpdateUserUseCase updateUserUseCase;
  final SyncUserUseCase syncUserUseCase;
  final GetSyncStatusUseCase getSyncStatusUseCase;
  final CheckAuthStatusUseCase checkAuthStatusUseCase;
  final GetCustomerByMobileNumber getCustomerByMobileNumber;
  final AppLogger logger;

  String _verificationId = '';
  String _phoneNumber = '';
  StreamSubscription? _syncSubscription;
  StreamSubscription? _syncStatusSubscription;
  bool _isSyncing = false;
  final AnalyticsService _analyticsService;

  AuthCubit({
    required this.sendOtpUseCase,
    required this.verifyOtpUseCase,
    required this.signOutUseCase,
    required this.getUserUseCase,
    required this.saveUserUseCase,
    required this.updateUserUseCase,
    required this.syncUserUseCase,
    required this.getSyncStatusUseCase,
    required this.logger,
    required this.checkAuthStatusUseCase,
    required this.getCustomerByMobileNumber,
    required AnalyticsService analyticsService,
  }) : _analyticsService = analyticsService,
       super(AuthInitial()) {
    // Listen to sync status changes
    _syncSubscription = getSyncStatusUseCase().listen((isSyncing) {
      _isSyncing = isSyncing;
      logger.i("Sync status changed: $isSyncing");
    });
  }

  @override
  Future<void> close() {
    _syncSubscription?.cancel();
    _syncStatusSubscription?.cancel();
    return super.close();
  }

  // Helper method to validate verification ID
  bool _isValidVerificationId(String verificationId) {
    return verificationId.isNotEmpty && verificationId != 'auto-verified';
  }

  Future<void> sendOtp(String phno, {String? initialPhoneNumber}) async {
    // Guard against multiple calls or invalid state transitions
    if (state is AuthLoading) {
      logger.w("Ignoring sendOtp call - already in loading state");
      return;
    }

    try {
      emit(AuthLoading());
      logger.i(
        "Sending OTP to: $phno (current phoneNumber: $_phoneNumber, current verificationId: $_verificationId)",
      );

      _phoneNumber = phno;

      if (phno != initialPhoneNumber) {
        logger.d("Phone number changed or initial login - sending new OTP");

        // Ensure phone number has proper format
        final formattedPhoneNumber = phno.startsWith('+') ? phno : '+91$phno';

        final result = await sendOtpUseCase(formattedPhoneNumber);
        result.fold(
          (failure) {
            logger.e("Send OTP failure: ${_mapFailureToMessage(failure)}");
            // Only emit error if we're still in loading state to prevent race conditions
            if (state is AuthLoading) {
              emit(PhoneNumberError(message: _mapFailureToMessage(failure)));
            } else {
              logger.w("State changed during OTP send - not emitting error");
            }
          },
          (verificationId) {
            // Validate verification ID
            if (!_isValidVerificationId(verificationId)) {
              logger.e("Received invalid verification ID: $verificationId");
              emit(AuthError(message: 'Invalid verification ID received'));
              return;
            }

            logger.d(
              "OTP Sent Successfully with verification ID: $verificationId",
            );
            _verificationId = verificationId;
            final otpSentState = OtpSent(
              verificationId: verificationId,
              phoneNumber: _phoneNumber,
            );
            logger.i("Emitting OtpSent state: $otpSentState");

            emit(otpSentState);
          },
        );
      } else {
        logger.d("Using existing verification ID for same phone number");

        // Validate existing verification ID
        if (!_isValidVerificationId(_verificationId)) {
          logger.e("Invalid existing verification ID: $_verificationId");
          emit(AuthError(message: 'Invalid verification ID'));
          return;
        }

        final otpSentState = OtpSent(
          verificationId: _verificationId,
          phoneNumber: _phoneNumber,
        );
        logger.i(
          "Emitting OtpSent state with existing verification ID: $otpSentState",
        );
        emit(otpSentState);
      }
    } catch (e) {
      logger.e("Unexpected error in sendOtp", e);
      emit(AuthError(message: 'Unexpected error sending OTP'));
    }
  }

  Future<void> verifyOtp(String otp) async {
    // Guard against multiple calls or invalid state transitions
    if (state is AuthLoading) {
      logger.w("Ignoring verifyOtp call - already in loading state");
      return;
    }

    try {
      // Validate verification ID before proceeding
      if (!_isValidVerificationId(_verificationId)) {
        logger.e(
          "Cannot verify OTP - invalid verification ID: $_verificationId",
        );
        emit(
          OtpVerificationError(
            message: 'Invalid verification session. Please request a new OTP.',
          ),
        );
        return;
      }

      // Validate OTP format
      if (otp.length != 6 || int.tryParse(otp) == null) {
        logger.e("Invalid OTP format: $otp");
        emit(
          OtpVerificationError(
            message: 'Invalid OTP format. Please enter a 6-digit code.',
          ),
        );
        return;
      }

      emit(AuthLoading());
      logger.i("Verifying OTP: $otp with verification ID: $_verificationId");

      final result = await verifyOtpUseCase(_verificationId, otp);

      result.fold(
        (failure) {
          final errorMessage = _mapFailureToMessage(failure);
          logger.e("OTP verification failed: $errorMessage");
          // Only emit error if we're still in loading state to prevent race conditions
          if (state is AuthLoading) {
            emit(OtpVerificationError(message: errorMessage));
          } else {
            logger.w(
              "State changed during OTP verification - not emitting error",
            );
          }
        },

        (success) async {
          if (success) {
            logger.i(
              "OTP verification successful, proceeding with user authentication",
            );

            try {
              final customerResult = await getCustomerByMobileNumber(
                _phoneNumber,
              );
              customerResult.fold(
                (failure) async {
                  logger.e(
                    "Customer lookup failed: ${_mapFailureToMessage(failure)}",
                  );
                  emit(
                    AuthError(
                      message:
                          "$_phoneNumber is not registered with us. No worries! Please call us at +************, and we'll help you create an account.",
                    ),
                  );
                },
                (customer) async {
                  logger.i("Customer found for phone number: $_phoneNumber");
                  _analyticsService.logUserLogin(
                    customer!.customerId,
                    customer.customerName,
                  );
                  try {
                    // Handle the customer object as needed
                    final now = DateTime.now();
                    final user = User(
                      id: 0, // Will be set by ObjectBox
                      phoneNumber: _phoneNumber,
                      isVerified: true,
                      createdAt: now,
                      updatedAt: now,
                      needsSync: true,
                    );

                    logger.d("Saving user to local storage");
                    final saveResult = await saveUserUseCase(user);

                    saveResult.fold(
                      (failure) {
                        logger.e(
                          "Save user failure: ${_mapFailureToMessage(failure)}",
                        );
                        emit(AuthError(message: _mapFailureToMessage(failure)));
                      },
                      (_) async {
                        logger.i("User saved successfully");

                        // Try to sync immediately but don't wait for it
                        _syncUserInBackground();

                        try {
                          logger.d("Retrieving saved user from local storage");
                          final userResult = await getUserUseCase();
                          userResult.fold(
                            (failure) {
                              logger.e(
                                "Get user failure: ${_mapFailureToMessage(failure)}",
                              );
                              emit(
                                AuthError(
                                  message: _mapFailureToMessage(failure),
                                ),
                              );
                            },
                            (savedUser) {
                              logger.i("Authentication completed successfully");
                              emit(AuthSuccess(user: savedUser));
                            },
                          );
                        } catch (e) {
                          logger.e("Error retrieving saved user", e);
                          emit(
                            AuthError(message: 'Error retrieving user data'),
                          );
                        }
                      },
                    );
                  } catch (e) {
                    logger.e("Error during user creation/saving", e);
                    emit(AuthError(message: 'Error creating user account'));
                  }
                },
              );
            } catch (e) {
              logger.e("Error during customer lookup", e);
              emit(AuthError(message: 'Error verifying user account'));
            }
          } else {
            logger.w("Verification returned false");
            emit(AuthError(message: 'Verification failed'));
          }
        },
      );
    } catch (e) {
      logger.e("Unexpected error in verifyOtp", e);
      emit(AuthError(message: 'Unexpected error verifying OTP'));
    }
  }

  Future<void> checkAuthStatus() async {
    // Don't emit loading state if we're already in a loading state
    if (state is! AuthLoading) {
      emit(AuthLoading());
    }
    logger.i("Checking authentication status");

    // First check if Firebase Auth has a current user
    final isLoggedIn = await checkAuthStatusUseCase();

    if (isLoggedIn.isRight()) {
      logger.i("Firebase Auth shows user is logged in");

      // If Firebase says we're logged in, get the user from local storage
      final result = await getUserUseCase();

      result.fold(
        (failure) {
          logger.w(
            "Firebase Auth is logged in but no user found in local storage: ",
          );
          // Try to recreate the user in local storage based on Firebase Auth
          _recreateUserFromFirebase();
        },
        (user) {
          if (user.isVerified) {
            logger.i("User found in local storage and is verified");
            emit(AuthSuccess(user: user));

            // Try to sync user data in the background
            _syncUserInBackground();
          } else {
            logger.w("User found but not verified, logging out");
            signOut();
          }
        },
      );
    } else {
      logger.w("Firebase Auth shows user is not logged in");
      emit(Unauthenticated());
    }
  }

  Future<void> _recreateUserFromFirebase() async {
    try {
      // Get current Firebase user
      final firebaseUser = await checkAuthStatusUseCase.getCurrentUser();

      if (firebaseUser != null && firebaseUser.phoneNumber != null) {
        logger.i("Recreating user from Firebase: ${firebaseUser.phoneNumber}");

        // Create a new user object
        final phoneNumber = firebaseUser.phoneNumber!.replaceAll('+91', '');
        final now = DateTime.now();
        final user = User(
          id: 0, // Will be set by ObjectBox
          phoneNumber: phoneNumber,
          isVerified: true,
          createdAt: now,
          updatedAt: now,
          needsSync: true,
        );

        // Save to local storage
        final saveResult = await saveUserUseCase(user);

        saveResult.fold(
          (failure) {
            logger.e("Failed to recreate user");
            emit(Unauthenticated());
          },
          (_) {
            logger.i("User recreated successfully");
            // Try to get the user again
            checkAuthStatus();
          },
        );
      } else {
        logger.w("Firebase user is null or has no phone number");
        emit(Unauthenticated());
      }
    } catch (e) {
      logger.e("Error recreating user from Firebase", e);
      emit(Unauthenticated());
    }
  }

  Future<void> signOut() async {
    if (state is AuthLoading) return; // Prevent multiple calls

    emit(AuthLoading());
    logger.i("Signing out user");
    await signOutUseCase();
    emit(Unauthenticated());
  }

  // Sync user without changing the UI state
  Future<void> _syncUserInBackground() async {
    if (_isSyncing) {
      logger.d("Sync already in progress, skipping");
      return;
    }

    logger.i("Syncing user data with MongoDB in background");

    try {
      final result = await syncUserUseCase();
      result.fold(
        (failure) {
          logger.e("Background sync failure: ${_mapFailureToMessage(failure)}");
        },
        (success) {
          logger.i("Background sync successful");
        },
      );
    } catch (e) {
      logger.e("Error during background sync", e);
    }
  }

  // Sync user with UI state updates
  Future<void> syncUser() async {
    if (_isSyncing) {
      logger.d("Sync already in progress, skipping");
      return;
    }

    final currentState = state;
    emit(SyncingUser());
    logger.i("Syncing user data with MongoDB");

    final result = await syncUserUseCase();

    result.fold(
      (failure) {
        logger.e("Sync failure");
        emit(SyncError(message: _mapFailureToMessage(failure)));

        // Restore previous state after a short delay
        Future.delayed(const Duration(seconds: 2), () {
          if (currentState is AuthSuccess) {
            emit(currentState);
          } else {
            checkAuthStatus();
          }
        });
      },
      (success) async {
        logger.i("Sync successful");
        emit(SyncSuccess());

        // Restore previous state with updated user after a short delay
        Future.delayed(const Duration(seconds: 1), () async {
          final userResult = await getUserUseCase();
          userResult.fold((failure) {
            if (currentState is AuthSuccess) {
              emit(currentState);
            } else {
              emit(Unauthenticated());
            }
          }, (user) => emit(AuthSuccess(user: user)));
        });
      },
    );
  }

  void listenToSyncStatus() {
    _syncStatusSubscription?.cancel();
    _syncStatusSubscription = getSyncStatusUseCase().listen((isSyncing) {
      _isSyncing = isSyncing;
      if (isSyncing && state is AuthSuccess) {
        emit(AuthSyncing());
      } else if (!isSyncing && state is AuthSyncing) {
        // Refresh user data after sync completes
        checkAuthStatus();
      }
    });
  }

  String _mapFailureToMessage(Failure failure) {
    return switch (failure) {
      ServerFailure() => 'Server is unavailable. Please try again later.',
      NetworkFailure() =>
        'No internet connection. Please check your network settings.',
      CacheFailure() => 'Could not access local data. Please restart the app.',
      UserAuthFailure() => 'Authentication failed. Please try again.',
      ValidationFailure(message: var msg) =>
        msg.isNotEmpty ? msg : 'Invalid phone number format.',
      _ => 'An unexpected error occurred. Please try again.',
    };
  }

  void resetState() {
    emit(AuthInitial());
  }
}
