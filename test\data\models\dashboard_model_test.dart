import 'dart:convert';
import 'package:flutter_test/flutter_test.dart';
import 'package:aquapartner/data/models/dashboard/dashboard_model.dart';
import 'package:aquapartner/domain/entities/dashboard/dashboard_entity.dart';

void main() {
  group('DashboardModel Tests', () {
    late DashboardModel testDashboardModel;
    late Map<String, dynamic> testJson;

    setUp(() {
      testDashboardModel = DashboardModel(
        id: 1,
        customerId: 'customer_123',
        salesJson:
            '{"2024": [{"month": "January", "totalSales": 10000.0, "weeks": []}]}',
        paymentsJson:
            '{"2024": [{"month": "January", "totalPayments": 8000.0, "weeks": []}]}',
        duesJson: '[{"ageTier": "0-30 days", "totalAmount": 2000.0}]',
        salesReturnJson: 500.0,
        categoryTypeSalesJson: '[{"categoryType": "Feed", "yearlySales": {}}]',
        liquidationJson: '{"totalLiquidation": 1500.0, "details": []}',
        myFarmersJson: '{"totalFarmers": 25, "activeFarmers": 20}',
        isSynced: true,
        lastSyncedAt: DateTime(2024, 1, 1, 12, 0, 0),
      );

      testJson = {
        'id': 1,
        'customerId': 'customer_123',
        'sales': {
          '2024': [
            {'month': 'January', 'totalSales': 10000.0, 'weeks': []},
          ],
        },
        'payments': {
          '2024': [
            {'month': 'January', 'totalPayments': 8000.0, 'weeks': []},
          ],
        },
        'dues': [
          {'ageTier': '0-30 days', 'totalAmount': 2000.0},
        ],
        'salesReturn': 500.0,
        'categoryTypeSales': [
          {'categoryType': 'Feed', 'yearlySales': {}},
        ],
        'liquidation': {'totalLiquidation': 1500.0, 'details': []},
        'myFarmers': {'totalFarmers': 25, 'activeFarmers': 20},
      };
    });

    group('Constructor Tests', () {
      test('should create DashboardModel with all required fields', () {
        expect(testDashboardModel.id, equals(1));
        expect(testDashboardModel.customerId, equals('customer_123'));
        expect(testDashboardModel.salesJson, contains('January'));
        expect(testDashboardModel.paymentsJson, contains('January'));
        expect(testDashboardModel.duesJson, contains('0-30 days'));
        expect(testDashboardModel.salesReturnJson, equals(500.0));
        expect(testDashboardModel.categoryTypeSalesJson, contains('Feed'));
        expect(
          testDashboardModel.liquidationJson,
          contains('totalLiquidation'),
        );
        expect(testDashboardModel.myFarmersJson, contains('totalFarmers'));
        expect(testDashboardModel.isSynced, isTrue);
        expect(
          testDashboardModel.lastSyncedAt,
          equals(DateTime(2024, 1, 1, 12, 0, 0)),
        );
      });

      test('should create DashboardModel with minimal data', () {
        final minimalModel = DashboardModel(
          id: 0,
          customerId: 'customer_456',
          salesJson: '{}',
          paymentsJson: '{}',
          duesJson: '[]',
          salesReturnJson: 0.0,
          categoryTypeSalesJson: '[]',
          liquidationJson: '{}',
          myFarmersJson: '{}',
          isSynced: false,
          lastSyncedAt: DateTime.now(),
        );

        expect(minimalModel.customerId, equals('customer_456'));
        expect(minimalModel.salesJson, equals('{}'));
        expect(minimalModel.paymentsJson, equals('{}'));
        expect(minimalModel.duesJson, equals('[]'));
        expect(minimalModel.salesReturnJson, equals(0.0));
        expect(minimalModel.isSynced, isFalse);
      });
    });

    group('JSON Parsing Tests', () {
      test('should create DashboardModel from JSON correctly', () {
        final dashboardModel = DashboardModel.fromJson(testJson);

        expect(dashboardModel.customerId, equals('customer_123'));
        expect(dashboardModel.id, equals(1));
        expect(dashboardModel.isSynced, isTrue);
        expect(dashboardModel.lastSyncedAt, isNotNull);

        // Verify JSON fields are properly encoded
        expect(dashboardModel.salesJson, isNotEmpty);
        expect(dashboardModel.paymentsJson, isNotEmpty);
        expect(dashboardModel.duesJson, isNotEmpty);
        expect(dashboardModel.salesReturnJson, equals(500.0));
        expect(dashboardModel.categoryTypeSalesJson, isNotEmpty);
        expect(dashboardModel.liquidationJson, isNotEmpty);
        expect(dashboardModel.myFarmersJson, isNotEmpty);
      });

      test('should handle null values in JSON gracefully', () {
        final jsonWithNulls = {
          'customerId': 'customer_123',
          'sales': null,
          'payments': null,
          'dues': null,
          'salesReturn': null,
          'categoryTypeSales': null,
          'liquidation': null,
          'myFarmers': null,
        };

        final dashboardModel = DashboardModel.fromJson(jsonWithNulls);

        expect(dashboardModel.customerId, equals('customer_123'));
        expect(dashboardModel.salesJson, equals('{}'));
        expect(dashboardModel.paymentsJson, equals('{}'));
        expect(dashboardModel.duesJson, equals('[]'));
        expect(dashboardModel.salesReturnJson, equals(0.0));
        expect(dashboardModel.categoryTypeSalesJson, equals('[]'));
        expect(dashboardModel.liquidationJson, equals('{}'));
        expect(dashboardModel.myFarmersJson, equals('{}'));
      });

      test('should handle missing fields in JSON gracefully', () {
        final incompleteJson = {
          'customerId': 'customer_123',
          'sales': {'2024': []},
        };

        final dashboardModel = DashboardModel.fromJson(incompleteJson);

        expect(dashboardModel.customerId, equals('customer_123'));
        expect(dashboardModel.salesJson, isNotEmpty);
        expect(dashboardModel.paymentsJson, equals('{}'));
        expect(dashboardModel.duesJson, equals('[]'));
        expect(dashboardModel.salesReturnJson, equals(0.0));
      });

      test('should handle different salesReturn data types', () {
        final jsonWithIntSalesReturn = {
          ...testJson,
          'salesReturn': 500, // Integer instead of double
        };

        final dashboardModel = DashboardModel.fromJson(jsonWithIntSalesReturn);
        expect(dashboardModel.salesReturnJson, equals(500.0));

        final jsonWithStringSalesReturn = {
          ...testJson,
          'salesReturn': '750.5', // String instead of number
        };

        final dashboardModel2 = DashboardModel.fromJson(
          jsonWithStringSalesReturn,
        );
        expect(
          dashboardModel2.salesReturnJson,
          equals(0.0),
        ); // Should default to 0.0 for invalid types
      });
    });

    group('JSON Serialization Tests', () {
      test('should convert DashboardModel to JSON correctly', () {
        final json = testDashboardModel.toJson();

        expect(json['customerId'], equals('customer_123'));
        expect(json['sales'], equals(testDashboardModel.salesJson));
        expect(json['payments'], equals(testDashboardModel.paymentsJson));
        expect(json['dues'], equals(testDashboardModel.duesJson));
        expect(json['salesReturn'], equals(500.0));
        expect(
          json['categoryTypeSales'],
          equals(testDashboardModel.categoryTypeSalesJson),
        );
        expect(json['liquidation'], equals(testDashboardModel.liquidationJson));
        expect(json['myFarmers'], equals(testDashboardModel.myFarmersJson));
      });

      test('should maintain data integrity during JSON conversions', () {
        final json = testDashboardModel.toJson();
        final recreatedModel = DashboardModel.fromJson(json);

        expect(
          recreatedModel.customerId,
          equals(testDashboardModel.customerId),
        );
        expect(
          recreatedModel.salesReturnJson,
          equals(testDashboardModel.salesReturnJson),
        );
        expect(recreatedModel.isSynced, isTrue);
      });
    });

    group('Entity Conversion Tests', () {
      test('should convert to DashboardEntity correctly', () {
        final entity = testDashboardModel.toEntity();

        expect(entity, isA<DashboardEntity>());
        expect(entity.customerId, equals(testDashboardModel.customerId));
        expect(entity.salesReturn, equals(testDashboardModel.salesReturnJson));
      });

      test('should handle complex JSON structures in entity conversion', () {
        final complexModel = DashboardModel(
          id: 1,
          customerId: 'customer_complex',
          salesJson: jsonEncode({
            '2024': [
              {
                'month': 'January',
                'totalSales': 15000.0,
                'weeks': [
                  {'week': 'Week 1', 'amount': 3000.0},
                  {'week': 'Week 2', 'amount': 4000.0},
                ],
              },
            ],
          }),
          paymentsJson: jsonEncode({
            '2024': [
              {
                'month': 'January',
                'totalPayments': 12000.0,
                'weeks': [
                  {'week': 'Week 1', 'amount': 2500.0},
                  {'week': 'Week 2', 'amount': 3500.0},
                ],
              },
            ],
          }),
          duesJson: jsonEncode([
            {'ageTier': '0-30 days', 'totalAmount': 1000.0},
            {'ageTier': '31-60 days', 'totalAmount': 500.0},
          ]),
          salesReturnJson: 750.0,
          categoryTypeSalesJson: jsonEncode([
            {
              'categoryType': 'Feed',
              'yearlySales': {
                '2024': [
                  {'month': 'January', 'totalSales': 8000.0, 'weeks': []},
                ],
              },
            },
          ]),
          liquidationJson: jsonEncode({
            'totalLiquidation': 2000.0,
            'details': [
              {'type': 'Product A', 'amount': 1200.0},
              {'type': 'Product B', 'amount': 800.0},
            ],
          }),
          myFarmersJson: jsonEncode({
            'totalFarmers': 50,
            'activeFarmers': 45,
            'newFarmers': 5,
          }),
          isSynced: true,
          lastSyncedAt: DateTime.now(),
        );

        final entity = complexModel.toEntity();

        expect(entity, isA<DashboardEntity>());
        expect(entity.customerId, equals('customer_complex'));
        expect(entity.salesReturn, equals(750.0));
        expect(entity.sales, isA<SalesEntity>());
        expect(entity.payments, isA<PaymentsEntity>());
        expect(entity.dues, isA<List<DueTierEntity>>());
        expect(entity.categoryTypeSales, isA<List<CategorySalesEntity>>());
        expect(entity.liquidation, isA<LiquidationEntity>());
        expect(entity.myFarmers, isA<MyFarmersEntity>());
      });
    });

    group('Edge Cases and Error Handling', () {
      test('should handle empty customer ID', () {
        final modelWithEmptyCustomerId = DashboardModel(
          id: 1,
          customerId: '',
          salesJson: '{}',
          paymentsJson: '{}',
          duesJson: '[]',
          salesReturnJson: 0.0,
          categoryTypeSalesJson: '[]',
          liquidationJson: '{}',
          myFarmersJson: '{}',
          isSynced: false,
          lastSyncedAt: DateTime.now(),
        );

        expect(modelWithEmptyCustomerId.customerId, equals(''));
      });

      test('should handle invalid JSON strings gracefully', () {
        final modelWithInvalidJson = DashboardModel(
          id: 1,
          customerId: 'customer_123',
          salesJson: 'invalid json',
          paymentsJson: '{incomplete json',
          duesJson: '[invalid array',
          salesReturnJson: 0.0,
          categoryTypeSalesJson: 'not json',
          liquidationJson: '{broken: json}',
          myFarmersJson: 'invalid',
          isSynced: true,
          lastSyncedAt: DateTime.now(),
        );

        // Should not throw exception during creation
        expect(modelWithInvalidJson.customerId, equals('customer_123'));
        expect(modelWithInvalidJson.salesJson, equals('invalid json'));
      });

      test('should handle negative salesReturn values', () {
        final modelWithNegativeSalesReturn = DashboardModel(
          id: 1,
          customerId: 'customer_123',
          salesJson: '{}',
          paymentsJson: '{}',
          duesJson: '[]',
          salesReturnJson: -100.0,
          categoryTypeSalesJson: '[]',
          liquidationJson: '{}',
          myFarmersJson: '{}',
          isSynced: true,
          lastSyncedAt: DateTime.now(),
        );

        expect(modelWithNegativeSalesReturn.salesReturnJson, equals(-100.0));
      });

      test('should handle very large salesReturn values', () {
        final modelWithLargeSalesReturn = DashboardModel(
          id: 1,
          customerId: 'customer_123',
          salesJson: '{}',
          paymentsJson: '{}',
          duesJson: '[]',
          salesReturnJson: 999999999.99,
          categoryTypeSalesJson: '[]',
          liquidationJson: '{}',
          myFarmersJson: '{}',
          isSynced: true,
          lastSyncedAt: DateTime.now(),
        );

        expect(modelWithLargeSalesReturn.salesReturnJson, equals(999999999.99));
      });

      test('should handle extreme dates', () {
        final extremeDate = DateTime(1970, 1, 1);
        final modelWithExtremeDate = DashboardModel(
          id: 1,
          customerId: 'customer_123',
          salesJson: '{}',
          paymentsJson: '{}',
          duesJson: '[]',
          salesReturnJson: 0.0,
          categoryTypeSalesJson: '[]',
          liquidationJson: '{}',
          myFarmersJson: '{}',
          isSynced: true,
          lastSyncedAt: extremeDate,
        );

        expect(modelWithExtremeDate.lastSyncedAt, equals(extremeDate));
      });
    });
  });
}
