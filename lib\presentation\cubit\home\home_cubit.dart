import 'package:aquapartner/core/utils/logger.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../core/error/failures.dart';
import '../../../core/services/analytics_service.dart';
import '../../../core/services/feature_usage_tracker.dart';
import '../../../domain/entities/customer.dart';
import '../../../domain/usecases/customer_usercases.dart';
import '../../../domain/usecases/user_usecases.dart';
import 'home_state.dart';

class HomeCubit extends Cubit<HomeState> {
  final CheckIfSyncNeeded checkIfSyncNeeded;
  final GetCustomerByMobileNumber getCustomerByMobileNumber;
  final SyncCustomerByCustomerId syncCustomerByCustomerId;
  final GetUserUseCase getUserUseCase;
  final AppLogger _logger;
  final AnalyticsService _analyticsService;
  final FeatureUsageTracker _featureTracker;

  DateTime? _viewStartTime;
  ViewType? _currentViewType;

  HomeCubit(
    this.checkIfSyncNeeded,
    this.getCustomerByMobileNumber,
    this.syncCustomerByCustomerId,
    this.getUserUseCase,
    this._logger,
    this._analyticsService,
    this._featureTracker,
  ) : super(const HomeState());

  void _logViewDuration() {
    if (_viewStartTime != null && _currentViewType != null) {
      final duration = DateTime.now().difference(_viewStartTime!);
      final screenName = _getScreenNameFromViewType(_currentViewType!);
      _analyticsService.logScreenDuration(
        screenName: screenName,
        durationMs: duration.inMilliseconds,
        screenClass:
            'HomeScreen_${_currentViewType.toString().split('.').last}',
      );
    }
  }

  // Helper to convert ViewType to screen name
  String _getScreenNameFromViewType(ViewType viewType) {
    switch (viewType) {
      case ViewType.dashboard:
        return 'DashboardScreen';
      case ViewType.accountStatement:
        return 'AccountStatementScreen';
      case ViewType.billingAndPayments:
        return 'BillingAndPaymentsScreen';
      case ViewType.priceList:
        return 'PriceListScreen';
      case ViewType.productList:
        return 'ProductsScreen';
      case ViewType.myFarmers:
        return 'MyFarmersScreen';
      case ViewType.stocks:
        return 'StockScreen';
      case ViewType.helpAndSupport:
        return 'HelpAndSupportScreen';
    }
  }

  // Add this to track time when app is closed or paused
  void trackCurrentViewDuration() {
    _logViewDuration();
  }

  Future<void> loadData() async {
    emit(state.copyWith(isLoading: true, errorMessage: null));
    try {
      // Your data loading logic
      await Future.delayed(const Duration(milliseconds: 300));
      emit(state.copyWith(isLoading: false, viewType: ViewType.dashboard));
    } catch (e) {
      emit(state.copyWith(isLoading: false, errorMessage: e.toString()));
    }
  }

  /// Fetches customer data by mobile number from local cache or remote
  Future<void> fetchCustomerByMobileNumber(String mobileNumber) async {
    emit(
      state.copyWith(
        isLoading: true,
        phoneNumber: mobileNumber,
        errorMessage: null,
      ),
    );

    final result = await getCustomerByMobileNumber(mobileNumber);

    result.fold(
      (failure) => emit(
        state.copyWith(
          isLoading: false,
          errorMessage: _mapFailureToMessage(failure),
          isNetworkError: failure is NetworkFailure,
        ),
      ),
      (customer) {
        if (customer != null) {
          _logger.d('Customer fetched: ${customer.companyName}');
          emit(state.copyWith(isLoading: false, customer: customer));

          // Check if we need to sync after loading from cache
          _checkForSyncInBackground(customer);
        } else {
          emit(
            state.copyWith(
              isLoading: false,
              errorMessage: 'Customer not found',
            ),
          );
        }
      },
    );
  }

  Future<void> _checkForSyncInBackground(Customer customer) async {
    final result = await checkIfSyncNeeded();
    result.fold(
      (failure) => null, // Silently ignore background check failures
      (needsSync) {
        if (needsSync) {
          syncCustomer(customer);
        }
      },
    );
  }

  /// Forces a synchronization with the remote server
  Future<void> syncCustomer(Customer customer) async {
    emit(
      state.copyWith(isSyncing: true, customer: customer, errorMessage: null),
    );

    final result = await syncCustomerByCustomerId(customer.customerId);

    result.fold(
      (failure) => emit(
        state.copyWith(
          isSyncing: false,
          errorMessage: _mapFailureToMessage(failure),
          isNetworkError: failure is NetworkFailure,
        ),
      ),
      (customer) {
        if (customer != null) {
          emit(state.copyWith(isSyncing: false, customer: customer));
        } else {
          emit(
            state.copyWith(
              isSyncing: false,
              errorMessage: 'Customer not found on server',
            ),
          );
        }
      },
    );
  }

  // Navigation methods
  void gotoDashboardView() {
    emit(state.copyWith(viewType: ViewType.dashboard, errorMessage: null));
  }

  // Modify your view navigation methods to track time
  void gotoAccountStatementView() {
    // Log time spent on previous view if any
    _logViewDuration();

    // Set new view start time and type
    _viewStartTime = DateTime.now();
    _currentViewType = ViewType.accountStatement;

    // Track feature usage for analytics dashboard
    _featureTracker.trackFeature('AccountStatement');

    _analyticsService.logEvent(
      name: 'screen_view',
      parameters: {
        'screen_name': 'AccountStatementScreen',
        'screen_class': 'HomeScreen_AccountStatement',
        'navigation_source': _currentViewType?.toString() ?? 'unknown',
        'timestamp': DateTime.now().toIso8601String(),
      },
    );

    // Log screen view
    _analyticsService.logScreenView(
      screenName: 'AccountStatementScreen',
      screenClass: 'HomeScreen_AccountStatement',
    );

    emit(
      state.copyWith(viewType: ViewType.accountStatement, errorMessage: null),
    );
  }

  void gotoBillingAndPaymentsView() {
    // Log time spent on previous view if any
    _logViewDuration();

    // Set new view start time and type
    _viewStartTime = DateTime.now();
    _featureTracker.trackFeature('BillingAndPayments');

    // Track feature usage for analytics dashboard
    _analyticsService.logFeatureUsage('BillingAndPayments');

    _analyticsService.logEvent(
      name: 'screen_view',
      parameters: {
        'screen_name': 'BillingAndPaymentsScreen',
        'screen_class': 'HomeScreen_BillingAndPayments',
        'navigation_source': _currentViewType?.toString() ?? 'unknown',
        'timestamp': DateTime.now().toIso8601String(),
      },
    );

    // Log screen view
    _analyticsService.logScreenView(
      screenName: 'BillingAndPaymentsScreen',
      screenClass: 'HomeScreen_BillingAndPayments',
    );
    emit(
      state.copyWith(viewType: ViewType.billingAndPayments, errorMessage: null),
    );
  }

  void gotoPriceListView() {
    // Log time spent on previous view if any
    _logViewDuration();

    // Set new view start time and type
    _viewStartTime = DateTime.now();
    _currentViewType = ViewType.priceList;

    // Track feature usage with enhanced tracker
    _featureTracker.trackFeature('PriceList');

    // REMOVE DUPLICATE: This is already handled by AnalyticsRouteObserver
    // _analyticsService.logScreenView(
    //   screenName: 'PriceListScreen',
    //   screenClass: 'HomeScreen_PriceList',
    // );

    emit(state.copyWith(viewType: ViewType.priceList, errorMessage: null));
  }

  void gotoProductsView() {
    // Log time spent on previous view if any
    _logViewDuration();

    // Set new view start time and type
    _viewStartTime = DateTime.now();
    _currentViewType = ViewType.productList;

    // Track feature usage for analytics dashboard
    _featureTracker.trackFeature('ProductList');

    _analyticsService.logEvent(
      name: 'screen_view',
      parameters: {
        'screen_name': 'ProductsListScreen',
        'screen_class': 'HomeScreen_ProductsList',
        'navigation_source': _currentViewType?.toString() ?? 'unknown',
        'timestamp': DateTime.now().toIso8601String(),
      },
    );

    // Log screen view
    _analyticsService.logScreenView(
      screenName: 'ProductsListScreen',
      screenClass: 'HomeScreen_ProductsList',
    );
    emit(state.copyWith(viewType: ViewType.productList, errorMessage: null));
  }

  void gotoMyFarmersView() {
    // Log time spent on previous view if any
    _logViewDuration();

    // Set new view start time and type
    _viewStartTime = DateTime.now();
    _currentViewType = ViewType.myFarmers;

    // Track feature usage for analytics dashboard
    _featureTracker.trackFeature('MyFarmers');

    _analyticsService.logEvent(
      name: 'screen_view',
      parameters: {
        'screen_name': 'MyFarmersScreen',
        'screen_class': 'HomeScreen_MyFarmers',
        'navigation_source': _currentViewType?.toString() ?? 'unknown',
        'timestamp': DateTime.now().toIso8601String(),
      },
    );

    // Log screen view
    _analyticsService.logScreenView(
      screenName: 'MyFarmersScreen',
      screenClass: 'HomeScreen_MyFarmers',
    );
    emit(state.copyWith(viewType: ViewType.myFarmers, errorMessage: null));
  }

  void gotoStocksView() {
    // Log time spent on previous view if any
    _logViewDuration();

    // Set new view start time and type
    _viewStartTime = DateTime.now();
    _currentViewType = ViewType.stocks;

    // Track feature usage for analytics dashboard
    _featureTracker.trackFeature('Stocks');

    _analyticsService.logEvent(
      name: 'screen_view',
      parameters: {
        'screen_name': 'StocksScreen',
        'screen_class': 'HomeScreen_Stocks',
        'navigation_source': _currentViewType?.toString() ?? 'unknown',
        'timestamp': DateTime.now().toIso8601String(),
      },
    );

    // Log screen view
    _analyticsService.logScreenView(
      screenName: 'StocksScreen',
      screenClass: 'HomeScreen_Stocks',
    );
    emit(state.copyWith(viewType: ViewType.stocks, errorMessage: null));
  }

  void gotoHelpAndSupportScreen() {
    // Log time spent on previous view if any
    _logViewDuration();

    // Set new view start time and type
    _viewStartTime = DateTime.now();
    _currentViewType = ViewType.helpAndSupport;

    // Track feature usage for analytics dashboard
    _analyticsService.logFeatureUsage('HelpAndSupport');

    _analyticsService.logEvent(
      name: 'screen_view',
      parameters: {
        'screen_name': 'HelpAndSupportScreen',
        'screen_class': 'HomeScreen_HelpAndSupport',
        'navigation_source': _currentViewType?.toString() ?? 'unknown',
        'timestamp': DateTime.now().toIso8601String(),
      },
    );

    // Log screen view
    _analyticsService.logScreenView(
      screenName: 'HelpAndSupportScreen',
      screenClass: 'HomeScreen_HelpAndSupport',
    );
    emit(state.copyWith(viewType: ViewType.helpAndSupport, errorMessage: null));
  }

  void clearError() {
    emit(state.copyWith(errorMessage: null));
  }

  void clearCustomerData() {
    emit(state.copyWith(customer: null, phoneNumber: null, errorMessage: null));
  }

  /// Maps failure types to user-friendly error messages
  String _mapFailureToMessage(Failure failure) {
    switch (failure.runtimeType) {
      case ServerFailure:
        return 'Check your internet connection';
      case CacheFailure:
        return 'Try again later';
      case NetworkFailure:
        return 'Network error occurred. Please check your internet connection';
      default:
        return 'Unexpected error occurred';
    }
  }
}
