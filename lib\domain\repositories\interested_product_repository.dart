import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../entities/interested_product.dart';

abstract class InterestedProductRepository {
  /// Gets all interested products from local storage
  Future<Either<Failure, List<InterestedProduct>>> getInterestedProducts();

  /// Adds a new interested product to local storage
  Future<Either<Failure, InterestedProduct>> addInterestedProduct(
    InterestedProduct interestedProduct,
  );

  /// Syncs unsynced interested products to the server
  Future<Either<Failure, int>> syncInterestedProducts();

  /// Gets the count of unsynced interested products
  Future<Either<Failure, int>> getUnsyncedCount();
}
