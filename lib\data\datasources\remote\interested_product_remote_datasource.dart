import 'dart:async';
import 'package:mongo_dart/mongo_dart.dart';
import '../../../core/error/exceptions.dart';
import '../../../core/network/mongodb_connection_manager.dart';
import '../../../core/utils/logger.dart';
import '../../models/interested_product_model.dart';

abstract class InterestedProductRemoteDataSource {
  /// Adds a new interested product to the remote server
  Future<InterestedProductModel> addInterestedProduct(
    InterestedProductModel interestedProduct,
  );

  /// Adds multiple interested products to the remote server
  Future<List<InterestedProductModel>> addInterestedProducts(
    List<InterestedProductModel> interestedProducts,
  );
}

class InterestedProductRemoteDataSourceImpl
    implements InterestedProductRemoteDataSource {
  final MongoDbConnectionManager connectionManager;
  final AppLogger logger;

  // Add a timeout for operations
  final Duration operationTimeout = const Duration(seconds: 15);

  InterestedProductRemoteDataSourceImpl({
    required this.connectionManager,
    required this.logger,
  });

  @override
  Future<InterestedProductModel> addInterestedProduct(
    InterestedProductModel interestedProduct,
  ) async {
    try {
      logger.i(
        "Adding interested product to MongoDB: ${interestedProduct.productName}",
      );

      final mongoService = await connectionManager.getConnection();

      // Get the collection with automatic reconnection
      final collection = await mongoService.interestedProductsCollection;

      // Convert to MongoDB document (without isSynced field)
      final document = interestedProduct.toMongoDocument();

      // Log the document for debugging
      logger.i("Document to insert: $document");

      // Add to MongoDB with timeout
      final result = await collection
          .insertOne(document)
          .timeout(
            operationTimeout,
            onTimeout: () {
              throw TimeoutException("MongoDB operation timed out");
            },
          );

      if (!result.isSuccess) {
        logger.e("MongoDB insert failed: ${result.writeError?.errmsg}");
        throw ServerException();
      }

      // Get the inserted ID
      final insertedId = result.id;
      String idString = insertedId.toString();

      // If it's an ObjectId, get the oid (hexadecimal string)
      if (insertedId is ObjectId) {
        idString = insertedId.oid;
      }

      // Return the model with the MongoDB ID
      final updatedModel = interestedProduct.copyWith(
        id: idString,
        isSynced: true,
      );

      logger.i(
        "Successfully added interested product to MongoDB with ID: ${updatedModel.id}",
      );
      return updatedModel;
    } on TimeoutException catch (e) {
      logger.e("MongoDB operation timed out", e);
      throw ServerException();
    } on ServerException {
      rethrow;
    } catch (e) {
      logger.e("Error adding interested product to MongoDB: $e", e);
      throw ServerException();
    }
  }

  @override
  Future<List<InterestedProductModel>> addInterestedProducts(
    List<InterestedProductModel> interestedProducts,
  ) async {
    try {
      logger.i(
        "Adding ${interestedProducts.length} interested products to MongoDB",
      );

      if (interestedProducts.isEmpty) {
        logger.w("No interested products to add");
        return [];
      }

      final mongoService = await connectionManager.getConnection();

      // Get the collection with automatic reconnection
      final collection = await mongoService.interestedProductsCollection;

      // Convert to MongoDB documents (without isSynced field)
      final documents =
          interestedProducts
              .map((product) => product.toMongoDocument())
              .toList();

      // Add to MongoDB with timeout - use insertMany instead of insertAll
      final result = await collection
          .insertMany(documents)
          .timeout(
            operationTimeout,
            onTimeout: () {
              throw TimeoutException("MongoDB operation timed out");
            },
          );

      // Check if the operation was successful
      if (!result.isSuccess) {
        logger.e("MongoDB insertMany failed");
        throw ServerException();
      }

      // Get the inserted IDs and update the models
      final updatedModels = <InterestedProductModel>[];

      // For bulk operations, we need to manually create IDs since we don't get them back directly
      // We'll use the original models and mark them as synced

      // Since the insert was successful, we can mark all products as synced
      for (var i = 0; i < interestedProducts.length; i++) {
        // Generate a timestamp-based ID if we don't have one
        // This is a temporary solution until we can get the actual MongoDB IDs
        final timestamp = DateTime.now().millisecondsSinceEpoch + i;
        final idString = interestedProducts[i].id ?? "temp_$timestamp";

        final updatedModel = interestedProducts[i].copyWith(
          id: idString,
          isSynced: true,
        );
        updatedModels.add(updatedModel);
      }

      logger.i(
        "Successfully added ${updatedModels.length} interested products to MongoDB",
      );
      return updatedModels;
    } on TimeoutException catch (e) {
      logger.e("MongoDB operation timed out", e);
      throw ServerException();
    } on ServerException {
      rethrow;
    } catch (e) {
      logger.e("Error adding interested products to MongoDB: $e", e);
      throw ServerException();
    }
  }
}
