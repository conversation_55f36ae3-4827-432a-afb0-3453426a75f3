import 'package:dartz/dartz.dart';

import '../../core/error/failures.dart';
import '../entities/dashboard/dashboard_entity.dart';

abstract class DashboardRepository {
  Future<Either<Failure, DashboardEntity>> getDashboard(String customerId);
  Future<Either<Failure, bool>> syncDashboard(String customerId);
  Future<DateTime?> getLastSyncTime(String customerId);
  bool isSyncing(String customerId);
}
