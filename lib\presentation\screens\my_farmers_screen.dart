import 'package:aqua_ui/aqua_ui.dart';
import 'package:aquapartner/core/routes/app_router.dart';
import 'package:aquapartner/domain/entities/farmer_visit/farmer.dart';
import 'package:aquapartner/presentation/widgets/loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../core/mixins/analytics_mixin.dart';
import '../../core/utils/logger.dart';
import '../cubit/my_farmers/farmer_visits_cubit.dart';

class MyFarmersScreen extends StatefulWidget {
  final AppLogger logger = AppLogger();
  MyFarmersScreen({super.key});

  @override
  State<MyFarmersScreen> createState() => _MyFarmersScreenState();
}

class _MyFarmersScreenState extends State<MyFarmersScreen>
    with AutomaticKeepAliveClientMixin, AnalyticsMixin {
  @override
  bool get wantKeepAlive => true;

  // Track screen view time
  DateTime? _screenViewStartTime;
  DateTime? _lastInteractionTime;

  // Track user interactions
  int _farmerTapCount = 0;
  int _refreshCount = 0;
  int _scrollCount = 0;

  // Track scroll position
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _screenViewStartTime = DateTime.now();
    _lastInteractionTime = _screenViewStartTime;

    // Track screen view
    trackEvent('farmers_screen_viewed');

    // Add scroll listener for analytics
    _scrollController.addListener(_onScroll);

    _loadFarmersData();
  }

  @override
  void dispose() {
    // Track total time spent on screen
    _trackScreenDuration();

    // Track final engagement metrics
    _trackEngagementMetrics();

    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _trackScreenDuration() {
    if (_screenViewStartTime != null) {
      final duration = DateTime.now().difference(_screenViewStartTime!);
      trackEvent(
        'farmers_screen_duration',
        params: {
          'duration_ms': duration.inMilliseconds.toString(),
          'duration_seconds': duration.inSeconds.toString(),
        },
      );
    }
  }

  void _trackEngagementMetrics() {
    trackEvent(
      'farmers_screen_engagement',
      params: {
        'farmer_tap_count': _farmerTapCount.toString(),
        'refresh_count': _refreshCount.toString(),
        'scroll_count': _scrollCount.toString(),
        'time_since_last_interaction':
            _lastInteractionTime != null
                ? DateTime.now()
                    .difference(_lastInteractionTime!)
                    .inSeconds
                    .toString()
                : '0',
      },
    );
  }

  void _onScroll() {
    // Don't track every tiny scroll event - only significant ones
    if (!_scrollController.hasClients) return;

    // Track scroll position as percentage
    final scrollPosition = _scrollController.position.pixels;
    final maxScroll = _scrollController.position.maxScrollExtent;

    if (maxScroll <= 0) return;

    final scrollPercentage = (scrollPosition / maxScroll * 100).round();

    // Only track at 25%, 50%, 75%, and 100% scroll positions
    if (scrollPercentage == 25 ||
        scrollPercentage == 50 ||
        scrollPercentage == 75 ||
        scrollPercentage == 100) {
      _scrollCount++;
      _lastInteractionTime = DateTime.now();

      trackEvent(
        'farmers_list_scrolled',
        params: {
          'scroll_percentage': scrollPercentage.toString(),
          'scroll_position': scrollPosition.toString(),
        },
      );
    }
  }

  void _loadFarmersData() {
    // Check current state to avoid unnecessary loading
    final currentState = context.read<FarmerVisitsCubit>().state;
    if (currentState is! FarmerVisitsLoaded &&
        currentState is! FarmerVisitsLoading) {
      widget.logger.i("Loading farmers data");
      context.read<FarmerVisitsCubit>().getFarmerVisits();

      trackEvent('farmers_data_loading_started');
    } else {
      widget.logger.i("Farmers data already loaded or loading in progress");

      if (currentState is FarmerVisitsLoaded) {
        trackEvent(
          'farmers_data_already_loaded',
          params: {'farmer_count': currentState.farmers.length.toString()},
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    return Scaffold(
      body: RefreshIndicator(
        onRefresh: () async {
          widget.logger.i("Pull-to-refresh triggered");
          _refreshCount++;
          _lastInteractionTime = DateTime.now();

          trackEvent(
            'farmers_list_refreshed',
            params: {'refresh_count': _refreshCount.toString()},
          );

          await context.read<FarmerVisitsCubit>().syncFarmerVisits();
        },
        child: BlocConsumer<FarmerVisitsCubit, FarmerVisitsState>(
          listener: (context, state) {
            // Track state changes
            if (state is FarmerVisitsLoading) {
              trackEvent('farmers_loading');
            } else if (state is FarmerVisitsSyncing) {
              trackEvent('farmers_syncing');
            } else if (state is FarmerVisitsLoaded) {
              trackEvent(
                'farmers_loaded',
                params: {
                  'farmer_count': state.farmers.length.toString(),
                  'is_from_cache':
                      'false', // Adjust based on your state implementation
                },
              );
            } else if (state is FarmerVisitsEmpty) {
              trackEvent('farmers_empty');
            } else if (state is FarmerVisitsError) {
              trackEvent(
                'farmers_error',
                params: {'error_message': state.message},
              );
            }
          },
          builder: (context, state) {
            if (state is FarmerVisitsLoading) {
              return const LoadingWidget(message: "Loading Your Farmers");
            } else if (state is FarmerVisitsSyncing) {
              return const LoadingWidget(message: "Syncing Farmers Data...");
            } else if (state is FarmerVisitsLoaded) {
              return _buildFarmersList(state.farmers);
            } else if (state is FarmerVisitsEmpty) {
              // Use a scrollable view for empty state to support pull-to-refresh
              return SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                child: SizedBox(
                  height:
                      MediaQuery.of(context).size.height -
                      100, // Adjust height to ensure scrollability
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        AquaText.body('No farmers found'),
                        const SizedBox(height: 16),
                        AquaText.body('Pull down to sync'),
                      ],
                    ),
                  ),
                ),
              );
            } else if (state is FarmerVisitsError) {
              // Use a scrollable view for error state to support pull-to-refresh
              return SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                child: SizedBox(
                  height:
                      MediaQuery.of(context).size.height -
                      100, // Adjust height to ensure scrollability
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        AquaText.body(state.message),
                        vSpace4,
                        ElevatedButton(
                          onPressed: () {
                            context.read<FarmerVisitsCubit>().getFarmerVisits();
                            trackEvent('farmers_retry_button_clicked');
                          },
                          child: AquaText.body('Try Again'),
                        ),
                        vSpace4,
                        AquaText.body('Or pull down to sync'),
                      ],
                    ),
                  ),
                ),
              );
            }
            // Initial state or any other state
            return SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              child: SizedBox(
                height:
                    MediaQuery.of(context).size.height -
                    100, // Adjust height to ensure scrollability
                child: Center(
                  child: ElevatedButton(
                    onPressed: () {
                      context.read<FarmerVisitsCubit>().getFarmerVisits();
                      trackEvent('farmers_load_button_clicked');
                    },
                    child: AquaText.body('Load Farmers'),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildFarmersList(List<Farmer> farmers) {
    // Use a ListView with physics that works with RefreshIndicator
    return Container(
      color: acWhiteColor,
      child: Padding(
        padding: EdgeInsets.only(left: 16, right: 16, top: 32, bottom: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AquaText.headline(
              "My Farmers",
              weight: AquaFontWeight.semibold,
              color: acTextSecondaryColor,
            ),
            vSpace4,
            Expanded(
              child: ListView.builder(
                controller:
                    _scrollController, // Add controller for scroll tracking
                // Using builder instead of separated for better performance
                // Use physics that work with RefreshIndicator
                physics: const AlwaysScrollableScrollPhysics(),
                itemCount: farmers.length,
                itemBuilder: (context, index) {
                  final farmer = farmers[index];
                  final address = 'No address';

                  // Add divider before each item except the first one
                  if (index > 0) {
                    return Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Divider(height: 1),
                        _buildFarmerListItem(farmer, address, index),
                      ],
                    );
                  }

                  return _buildFarmerListItem(farmer, address, index);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFarmerListItem(Farmer farmer, String address, int index) {
    return ListTile(
      onTap: () {
        widget.logger.i(farmer.name);
        _farmerTapCount++;
        _lastInteractionTime = DateTime.now();

        // Track farmer selection with detailed analytics
        trackUserInteraction(
          'farmer_selected',
          'list_item',
          elementId: farmer.id.toString(),
          additionalParams: {
            'farmer_name': farmer.name,
            'farmer_index': index.toString(),
            'farmer_tap_count': _farmerTapCount.toString(),
            'time_on_screen_before_tap':
                _screenViewStartTime != null
                    ? DateTime.now()
                        .difference(_screenViewStartTime!)
                        .inSeconds
                        .toString()
                    : '0',
          },
        );

        AppRouter.navigateToFarmerDetails(farmer: farmer);
      },
      contentPadding: const EdgeInsets.symmetric(
        vertical: 8.0,
        horizontal: 16.0,
      ),
      leading: _buildFarmerAvatar(farmer),
      title: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            flex: 1,
            child: AquaText.subheadline(
              farmer.name,
              weight: AquaFontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFarmerAvatar(Farmer farmer) {
    // Return simple avatar with text for better performance when no image
    return CircleAvatar(
      backgroundColor: acPrimaryColor.withValues(alpha: 0.2),
      child: AquaText.subheadline(
        farmer.name.isNotEmpty ? farmer.name[0].toUpperCase() : '?',
        color: acPrimaryBlue,
        weight: AquaFontWeight.bold,
      ),
    );
  }

  @override
  String get screenName => 'MyFarmersScreen';
}
