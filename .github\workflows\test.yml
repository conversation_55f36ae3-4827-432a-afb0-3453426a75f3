name: Test and Coverage

on:
  push:
    branches: [ main, dev, develop ]
  pull_request:
    branches: [ main, dev, develop ]

jobs:
  test:
    name: Run Tests
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Java
      uses: actions/setup-java@v4
      with:
        distribution: 'zulu'
        java-version: '17'
        
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.24.0'
        channel: 'stable'
        cache: true
        
    - name: Get dependencies
      run: flutter pub get
      
    - name: Verify the installation
      run: flutter doctor -v
      
    - name: Run code generation
      run: flutter packages pub run build_runner build --delete-conflicting-outputs
      
    - name: Analyze code
      run: flutter analyze
      
    - name: Check formatting
      run: dart format --set-exit-if-changed .
      
    - name: Run unit tests
      run: flutter test --coverage
      
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v4
      with:
        file: coverage/lcov.info
        flags: unittests
        name: codecov-umbrella
        fail_ci_if_error: false
        
    - name: Generate coverage report
      run: |
        sudo apt-get update
        sudo apt-get install -y lcov
        genhtml coverage/lcov.info -o coverage/html
        
    - name: Upload coverage report
      uses: actions/upload-artifact@v4
      with:
        name: coverage-report
        path: coverage/html/
        
  integration-test:
    name: Integration Tests
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Java
      uses: actions/setup-java@v4
      with:
        distribution: 'zulu'
        java-version: '17'
        
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.24.0'
        channel: 'stable'
        cache: true
        
    - name: Get dependencies
      run: flutter pub get
      
    - name: Run code generation
      run: flutter packages pub run build_runner build --delete-conflicting-outputs
      
    - name: Enable KVM group perms
      run: |
        echo 'KERNEL=="kvm", GROUP="kvm", MODE="0666", OPTIONS+="static_node=kvm"' | sudo tee /etc/udev/rules.d/99-kvm4all.rules
        sudo udevadm control --reload-rules
        sudo udevadm trigger --name-match=kvm
        
    - name: Run integration tests
      uses: reactivecircus/android-emulator-runner@v2
      with:
        api-level: 29
        script: flutter test integration_test/
        
  build-test:
    name: Build Test
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Java
      uses: actions/setup-java@v4
      with:
        distribution: 'zulu'
        java-version: '17'
        
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.24.0'
        channel: 'stable'
        cache: true
        
    - name: Get dependencies
      run: flutter pub get
      
    - name: Run code generation
      run: flutter packages pub run build_runner build --delete-conflicting-outputs
      
    - name: Build APK
      run: flutter build apk --debug
      
    - name: Upload APK
      uses: actions/upload-artifact@v4
      with:
        name: debug-apk
        path: build/app/outputs/flutter-apk/app-debug.apk
        
  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.24.0'
        channel: 'stable'
        cache: true
        
    - name: Get dependencies
      run: flutter pub get
      
    - name: Run security audit
      run: flutter pub deps --json | jq '.packages[] | select(.kind == "direct") | .name' | xargs -I {} sh -c 'echo "Checking {}" && flutter pub deps | grep {}'
      
    - name: Check for known vulnerabilities
      run: |
        # This is a placeholder for security scanning
        # You can integrate tools like:
        # - Snyk
        # - OWASP Dependency Check
        # - GitHub Security Advisories
        echo "Security scan completed"
        
  performance-test:
    name: Performance Test
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.24.0'
        channel: 'stable'
        cache: true
        
    - name: Get dependencies
      run: flutter pub get
      
    - name: Run code generation
      run: flutter packages pub run build_runner build --delete-conflicting-outputs
      
    - name: Run performance tests
      run: |
        # Run specific performance tests
        flutter test test/performance/ --reporter=json > performance_results.json || true
        
    - name: Upload performance results
      uses: actions/upload-artifact@v4
      with:
        name: performance-results
        path: performance_results.json
        
  notify:
    name: Notify Results
    runs-on: ubuntu-latest
    needs: [test, integration-test, build-test, security-scan, performance-test]
    if: always()
    
    steps:
    - name: Notify on success
      if: ${{ needs.test.result == 'success' && needs.build-test.result == 'success' }}
      run: |
        echo "✅ All tests passed successfully!"
        echo "Coverage report and build artifacts are available."
        
    - name: Notify on failure
      if: ${{ needs.test.result == 'failure' || needs.build-test.result == 'failure' }}
      run: |
        echo "❌ Tests failed!"
        echo "Please check the test results and fix any issues."
        exit 1
