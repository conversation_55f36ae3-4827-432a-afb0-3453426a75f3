import 'package:dartz/dartz.dart';
import '../../core/cache/cache_manager.dart';
import '../../core/error/exceptions.dart';
import '../../core/error/failures.dart';
import '../../core/network/network_info.dart';
import '../../core/utils/logger.dart';
import '../../domain/entities/account_statement/account_statement.dart';
import '../../domain/repositories/account_statement_repository.dart';
import '../datasources/local/account_statement_local_data_source.dart';
import '../datasources/remote/account_statement_remote_data_source.dart';

class AccountStatementRepositoryImpl implements AccountStatementRepository {
  final AccountStatementRemoteDataSource remoteDataSource;
  final AccountStatementLocalDataSource localDataSource;
  final NetworkInfo networkInfo;
  final AppLogger logger;
  final CacheManager cacheManager;

  // Config for data freshness
  static const Duration _dataFreshnessThreshold = Duration(hours: 2);
  static const String _entityType = 'account_statement';

  AccountStatementRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
    required this.networkInfo,
    required this.logger,
    required this.cacheManager,
  });

  @override
  Future<Either<Failure, AccountStatement?>> getAccountStatement(
    String customerId,
  ) async {
    try {
      logger.i('Attempting to get account statement for customer: $customerId');

      // CACHING-FIRST APPROACH:
      // 1. Always try to get data from local cache first
      final localStatement = await localDataSource.getAccountStatement(
        customerId,
      );

      // 2. If we have cached data, return it immediately
      if (localStatement != null) {
        logger.i(
          'Found account statement in local cache with ${localStatement.entries.length} entries',
        );

        // 3. Check cache status in the background
        final cacheStatus = await cacheManager.getCacheStatus(
          customerId,
          entityType: _entityType,
          freshnessThreshold: _dataFreshnessThreshold,
        );

        // 4. If cache is stale and we're online, trigger a background sync
        //    but don't wait for it to complete - return cached data immediately
        if (cacheStatus == CacheStatus.stale && await networkInfo.isConnected) {
          logger.i('Cache is stale, triggering background sync');
          // Fire and forget - don't await
          syncAccountStatement(customerId).then((result) {
            result.fold(
              (failure) =>
                  logger.w('Background sync failed: ${failure.toString()}'),
              (statement) => logger.i('Background sync completed successfully'),
            );
          });
        }

        // Return cached data immediately, regardless of freshness
        return Right(localStatement.toEntity());
      }

      // 5. If no cached data exists, try to fetch from remote if online
      if (await networkInfo.isConnected) {
        logger.i('No local data, attempting to sync from remote');
        final syncResult = await syncAccountStatement(customerId);
        return syncResult;
      } else {
        // 6. If offline and no cache, return network failure
        logger.w('Device is offline and no local data found');
        return Left(NetworkFailure());
      }
    } on CacheException catch (e) {
      logger.e('Cache exception during getAccountStatement: ${e.toString()}');
      return Left(CacheFailure());
    } catch (e) {
      logger.e('Unexpected error during getAccountStatement: ${e.toString()}');
      return Left(UnexpectedFailure('Unexpected error: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, AccountStatement?>> syncAccountStatement(
    String customerId,
  ) async {
    try {
      // Check network connectivity
      if (!await networkInfo.isConnected) {
        logger.w('Cannot sync account statement: Device is offline');
        return Left(NetworkFailure());
      }

      logger.i('Syncing account statement for customer: $customerId');

      // Fetch data from remote
      final remoteStatement = await remoteDataSource.getAccountStatement(
        customerId,
      );

      // Cache the data locally, including null or empty statements
      if (remoteStatement == null) {
        logger.w('No account statement data received from remote');
        await localDataSource.cacheEmptyStatement(customerId);

        // Update last sync time using cache manager
        await cacheManager.saveLastSyncTime(
          customerId,
          DateTime.now(),
          entityType: _entityType,
        );

        return const Right(null);
      } else {
        // Cache the data even if empty, to record a sync time
        await localDataSource.cacheAccountStatement(remoteStatement);

        // Update last sync time using cache manager
        await cacheManager.saveLastSyncTime(
          customerId,
          DateTime.now(),
          entityType: _entityType,
        );

        if (remoteStatement.entries.isEmpty) {
          logger.w('Empty account statement received from remote');
          return Right(remoteStatement.toEntity());
        } else {
          logger.i(
            'Account statement synced successfully with ${remoteStatement.entries.length} entries',
          );
          return Right(remoteStatement.toEntity());
        }
      }
    } on ServerException catch (e) {
      logger.e('Server exception during syncAccountStatement: ${e.toString()}');
      return Left(ServerFailure());
    } on CacheException catch (e) {
      logger.e('Cache exception during syncAccountStatement: ${e.toString()}');
      return Left(CacheFailure());
    } catch (e) {
      logger.e('Unexpected error during syncAccountStatement: ${e.toString()}');
      return Left(UnexpectedFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> checkIfSyncNeeded(String customerId) async {
    try {
      // Use cache manager to check if sync is needed
      final isSyncNeeded = await cacheManager.isSyncNeeded(
        customerId,
        entityType: _entityType,
        freshnessThreshold: _dataFreshnessThreshold,
      );

      logger.i('Account statement sync needed: $isSyncNeeded');
      return Right(isSyncNeeded);
    } catch (e) {
      logger.e('Error checking if sync needed: ${e.toString()}');
      return Left(UnexpectedFailure(e.toString()));
    }
  }

  /// Force invalidate the cache for a customer's account statement
  /// This will cause the next getAccountStatement call to trigger a sync
  @override
  Future<Either<Failure, bool>> invalidateCache(String customerId) async {
    try {
      final result = await cacheManager.invalidateCache(
        customerId,
        entityType: _entityType,
      );

      logger.i('Account statement cache invalidated: $result');
      return Right(result);
    } catch (e) {
      logger.e('Error invalidating cache: ${e.toString()}');
      return Left(UnexpectedFailure(e.toString()));
    }
  }
}
