import 'package:aquapartner/core/utils/logger.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../domain/services/auth_service.dart';
import '../../../domain/usecases/invoices/check_if_invoices_sync_needed_usecase.dart';
import '../../../domain/usecases/invoices/get_invoice_by_id_usecase.dart';
import '../../../domain/usecases/invoices/get_invoices.dart';
import '../../../domain/usecases/invoices/sync_invoices_usecase.dart';
import 'invoices_state.dart';

class InvoicesCubit extends Cubit<InvoicesState> {
  final GetInvoices _getInvoices;
  final GetInvoiceById _getInvoiceById;
  final SyncInvoices _syncInvoices;
  final CheckIfInvoicesSyncNeeded _checkIfSyncNeeded;
  final AuthService _authService;
  final AppLogger _logger;

  bool _isBackgroundSyncing = false;

  InvoicesCubit({
    required GetInvoices getInvoices,
    required GetInvoiceById getInvoiceById,
    required SyncInvoices syncInvoices,
    required CheckIfInvoicesSyncNeeded checkIfSyncNeeded,
    required AppLogger logger,
    required AuthService authService,
  }) : _getInvoices = getInvoices,
       _getInvoiceById = getInvoiceById,
       _syncInvoices = syncInvoices,
       _checkIfSyncNeeded = checkIfSyncNeeded,
       _authService = authService,
       _logger = logger,
       super(InvoiceInitial());

  /// Load invoices with a caching-first approach
  /// Always returns cached data immediately if available
  /// and triggers a background sync if needed
  Future<void> loadInvoices() async {
    emit(InvoicesLoading());
    final customerResult = await _authService.getCurrentCustomer();

    customerResult.fold(
      (failure) {
        _logger.e('Failed to get current customer: ${failure.toString()}');
        emit(InvoiceError(message: 'Failed to load customer information'));
      },
      (customer) async {
        if (customer == null) {
          emit(InvoiceError(message: 'Customer information not found'));
          return;
        }

        _logger.i('Loading invoices for customer: ${customer.customerId}');

        // CACHING-FIRST APPROACH:
        // 1. Always try to get data from local cache first
        final result = await _getInvoices(customer.customerId);

        result.fold(
          (failure) {
            _logger.e(
              'Failed to load invoices from cache: ${failure.toString()}',
            );
            emit(InvoiceError(message: 'Failed to load invoices, try again'));

            // Try to sync if local fetch fails
            syncInvoices(customer.customerId);
          },
          (invoices) {
            _logger.i(
              'Successfully loaded ${invoices.length} invoices from cache',
            );

            // 2. Emit loaded state with cached data immediately
            emit(
              InvoicesLoaded(
                invoices: invoices,
                isFromCache: true,
                isCacheStale: false, // We don't know yet, will update if needed
                isBackgroundSyncInProgress: false,
              ),
            );

            // 3. Check if sync is needed in the background
            _checkAndSyncIfNeeded(customer.customerId);
          },
        );
      },
    );
  }

  /// Check if sync is needed and perform background sync if necessary
  Future<void> _checkAndSyncIfNeeded(String customerId) async {
    try {
      // Check if sync is needed
      final syncNeededResult = await _checkIfSyncNeeded();

      final syncNeeded = syncNeededResult.fold((failure) {
        _logger.w('Error checking if sync needed: ${failure.toString()}');
        return false; // Don't sync on error
      }, (needed) => needed);

      if (syncNeeded && !_isBackgroundSyncing) {
        _logger.i('Cache is stale, performing background sync');

        // Update state to indicate cache is stale and background sync is in progress
        if (state is InvoicesLoaded) {
          emit(
            (state as InvoicesLoaded).copyWith(
              isCacheStale: true,
              isBackgroundSyncInProgress: true,
            ),
          );
        }

        // Perform background sync
        _isBackgroundSyncing = true;
        await _backgroundSync(customerId);
        _isBackgroundSyncing = false;

        // Update state to indicate background sync is complete
        if (state is InvoicesLoaded) {
          emit(
            (state as InvoicesLoaded).copyWith(
              isBackgroundSyncInProgress: false,
            ),
          );
        }
      }
    } catch (e) {
      _logger.e('Error in _checkAndSyncIfNeeded: $e');
      _isBackgroundSyncing = false;
    }
  }

  /// Perform a background sync without showing loading indicators
  Future<void> _backgroundSync(String customerId) async {
    try {
      _logger.i('Performing background sync for customer: $customerId');

      // Sync invoices
      final syncResult = await _syncInvoices(customerId);

      syncResult.fold(
        (failure) {
          _logger.e('Background sync failed: ${failure.toString()}');
          // Don't emit error state as this is a background operation
        },
        (syncStatus) async {
          _logger.i('Background sync completed successfully');

          // After successful sync, load the updated local data
          final result = await _getInvoices(customerId);

          result.fold(
            (failure) {
              _logger.e(
                'Failed to load invoices after background sync: ${failure.toString()}',
              );
            },
            (invoices) {
              // Update the state with new data
              if (state is InvoicesLoaded) {
                emit(
                  InvoicesLoaded(
                    invoices: invoices,
                    isFromCache: false,
                    isCacheStale: false,
                    isBackgroundSyncInProgress: false,
                  ),
                );
              }
            },
          );
        },
      );
    } catch (e) {
      _logger.e('Error in background sync: $e');
      // Don't emit error state as this is a background operation
    }
  }

  Future<void> getInvoiceById(String invoiceId) async {
    emit(InvoicesLoading());

    final result = await _getInvoiceById(invoiceId);

    result.fold((failure) {
      _logger.e('Failed to load invoice details: ${failure.toString()}');
      emit(InvoiceError(message: 'Failed to load invoice details, try again'));
    }, (invoice) => emit(InvoiceDetailLoaded(invoice: invoice)));
  }

  /// Manual sync that shows loading indicators
  Future<void> syncInvoices(String customerId) async {
    emit(InvoiceSyncing());
    _logger.i('Starting invoice sync for customer: $customerId');

    final syncResult = await _syncInvoices(customerId);

    syncResult.fold(
      (failure) {
        _logger.e('Invoice sync failed: ${failure.toString()}');
        emit(InvoiceSyncError(message: 'Failed to sync invoices'));

        // Even if sync fails, try to load local data
        _loadFromCacheAfterSyncFailure(customerId);
      },
      (syncStatus) {
        _logger.i('Invoice sync completed successfully');

        // After successful sync, load the updated local data
        _loadAfterSuccessfulSync(customerId);
      },
    );
  }

  /// Load from cache after sync failure
  Future<void> _loadFromCacheAfterSyncFailure(String customerId) async {
    try {
      final result = await _getInvoices(customerId);

      result.fold(
        (failure) {
          _logger.e(
            'Error loading from cache after sync failure: ${failure.toString()}',
          );
          // Already in error state, no need to emit again
        },
        (invoices) {
          _logger.i(
            'Loaded ${invoices.length} invoices from cache after sync failure',
          );
          emit(
            InvoicesLoaded(
              invoices: invoices,
              isFromCache: true,
              isCacheStale: true, // Mark as stale since sync failed
              isBackgroundSyncInProgress: false,
            ),
          );
        },
      );
    } catch (e) {
      _logger.e('Exception loading from cache after sync failure: $e');
      // Already in error state, no need to emit again
    }
  }

  /// Load after successful sync
  Future<void> _loadAfterSuccessfulSync(String customerId) async {
    try {
      final result = await _getInvoices(customerId);

      result.fold(
        (failure) {
          _logger.e(
            'Error loading after successful sync: ${failure.toString()}',
          );
          // This is unexpected, emit error
          emit(InvoiceError(message: 'Failed to load invoices after sync'));
        },
        (invoices) {
          _logger.i('Loaded ${invoices.length} invoices after successful sync');
          emit(InvoiceSyncSuccess(syncTime: DateTime.now()));
          emit(
            InvoicesLoaded(
              invoices: invoices,
              isFromCache: false,
              isCacheStale: false,
              isBackgroundSyncInProgress: false,
            ),
          );
        },
      );
    } catch (e) {
      _logger.e('Exception loading after successful sync: $e');
      emit(InvoiceError(message: 'Error loading invoices after sync'));
    }
  }

  /// Force invalidate the cache and trigger a sync
  Future<void> invalidateAndSync(String customerId) async {
    try {
      _logger.i('Invalidating cache and syncing for customer: $customerId');

      // Mark cache as stale if we have loaded data
      if (state is InvoicesLoaded) {
        emit((state as InvoicesLoaded).copyWith(isCacheStale: true));
      }

      // Perform a full sync
      await syncInvoices(customerId);
    } catch (e) {
      _logger.e('Error invalidating cache: $e');
      emit(InvoiceError(message: 'Failed to invalidate cache'));
    }
  }
}
