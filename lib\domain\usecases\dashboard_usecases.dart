import 'package:dartz/dartz.dart';

import '../../../core/utils/logger.dart';
import '../../core/error/failures.dart';
import '../../core/network/network_info.dart';
import '../entities/dashboard/dashboard_entity.dart';
import '../repositories/dashboard_repository.dart';

class GetDashboardSyncStatusUseCase {
  final DashboardRepository repository;
  final AppLogger logger;

  GetDashboardSyncStatusUseCase({
    required this.repository,
    required this.logger,
  });
}

class DashboardSyncStatus {
  final bool needsSync;
  final DateTime? lastSyncTime;
  DashboardSyncStatus({required this.needsSync, required this.lastSyncTime});
}

class GetDashboardUseCase {
  final DashboardRepository repository;
  final AppLogger logger;
  GetDashboardUseCase({required this.repository, required this.logger});
  Future<Either<Failure, DashboardEntity>> call(String customerId) async {
    logger.i('GetDashboardUseCase called for customer: $customerId');
    return await repository.getDashboard(customerId);
  }
}

class SyncDashboardUseCase {
  final DashboardRepository repository;
  final NetworkInfo networkInfo;
  final AppLogger logger;

  SyncDashboardUseCase({
    required this.repository,
    required this.networkInfo,
    required this.logger,
  });

  Future<Either<Failure, bool>> call(String customerId) async {
    logger.i('SyncDashboardUseCase called for customer: $customerId');

    if (!await networkInfo.isConnected) {
      logger.w('Cannot sync dashboard: No network connection');
      return Left(NetworkFailure());
    }

    return await repository.syncDashboard(customerId);
  }
}
