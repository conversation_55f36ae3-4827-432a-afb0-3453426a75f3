import 'package:equatable/equatable.dart';

import '../../../domain/entities/sales_order/sales_order.dart';

abstract class SalesOrderState extends Equatable {
  const SalesOrderState();

  @override
  List<Object?> get props => [];
}

class SalesOrderInitial extends SalesOrderState {
  const SalesOrderInitial();
}

class SalesOrderLoading extends SalesOrderState {
  const SalesOrderLoading();
}

class SalesOrdersLoaded extends SalesOrderState {
  final List<SalesOrder> salesOrders;
  final bool isFromCache;
  final bool isCacheStale;
  final bool isBackgroundSyncInProgress;

  const SalesOrdersLoaded(
    this.salesOrders, {
    this.isFromCache = false,
    this.isCacheStale = false,
    this.isBackgroundSyncInProgress = false,
  });

  SalesOrdersLoaded copyWith({
    List<SalesOrder>? salesOrders,
    bool? isFromCache,
    bool? isCacheStale,
    bool? isBackgroundSyncInProgress,
  }) {
    return SalesOrdersLoaded(
      salesOrders ?? this.salesOrders,
      isFromCache: isFromCache ?? this.isFromCache,
      isCacheStale: isCacheStale ?? this.isCacheStale,
      isBackgroundSyncInProgress:
          isBackgroundSyncInProgress ?? this.isBackgroundSyncInProgress,
    );
  }

  @override
  List<Object?> get props => [
    salesOrders,
    isFromCache,
    isCacheStale,
    isBackgroundSyncInProgress,
  ];
}

class SalesOrderDetailLoaded extends SalesOrderState {
  final SalesOrder salesOrder;
  final bool isFromCache;

  const SalesOrderDetailLoaded(this.salesOrder, {this.isFromCache = false});

  @override
  List<Object?> get props => [salesOrder, isFromCache];
}

class SalesOrderError extends SalesOrderState {
  final String message;

  const SalesOrderError(this.message);

  @override
  List<Object?> get props => [message];
}

class SalesOrderSyncing extends SalesOrderState {
  const SalesOrderSyncing();
}

class SalesOrderSyncSuccess extends SalesOrderState {
  final List<SalesOrder> salesOrders;

  const SalesOrderSyncSuccess(this.salesOrders);

  @override
  List<Object?> get props => [salesOrders];
}

class SalesOrderSyncError extends SalesOrderState {
  final String message;

  const SalesOrderSyncError(this.message);

  @override
  List<Object?> get props => [message];
}
