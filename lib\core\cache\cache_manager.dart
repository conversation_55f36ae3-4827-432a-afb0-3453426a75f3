import 'package:shared_preferences/shared_preferences.dart';
import '../utils/logger.dart';

/// Cache status enum to represent the state of cached data
enum CacheStatus {
  /// Data is fresh and valid
  fresh,

  /// Data exists but is stale (older than freshness threshold)
  stale,

  /// No data exists in cache
  notFound,

  /// Cache error occurred
  error,
}

/// Manager for handling cache operations and policies
class CacheManager {
  final SharedPreferences sharedPreferences;
  final AppLogger logger;

  // Keys for shared preferences
  static const String _lastSyncTimeKeyPrefix = 'last_sync_time_';

  // Default freshness threshold (2 hours)
  static const Duration defaultFreshnessThreshold = Duration(hours: 2);

  CacheManager({required this.sharedPreferences, required this.logger});

  /// Get the last sync time for a specific entity
  Future<DateTime?> getLastSyncTime(
    String entityId, {
    String? entityType,
  }) async {
    try {
      final key = _getLastSyncTimeKey(entityId, entityType);
      final timestamp = sharedPreferences.getInt(key);

      if (timestamp != null) {
        return DateTime.fromMillisecondsSinceEpoch(timestamp);
      }

      return null;
    } catch (e) {
      logger.e('Error getting last sync time: ${e.toString()}');
      return null;
    }
  }

  /// Save the last sync time for a specific entity
  Future<bool> saveLastSyncTime(
    String entityId,
    DateTime time, {
    String? entityType,
  }) async {
    try {
      final key = _getLastSyncTimeKey(entityId, entityType);
      return await sharedPreferences.setInt(key, time.millisecondsSinceEpoch);
    } catch (e) {
      logger.e('Error saving last sync time: ${e.toString()}');
      return false;
    }
  }

  /// Check if data needs to be synced based on freshness threshold
  Future<bool> isSyncNeeded(
    String entityId, {
    String? entityType,
    Duration freshnessThreshold = defaultFreshnessThreshold,
  }) async {
    try {
      final lastSyncTime = await getLastSyncTime(
        entityId,
        entityType: entityType,
      );

      // If never synced, sync is needed
      if (lastSyncTime == null) {
        logger.i('Entity $entityId never synced, sync needed');
        return true;
      }

      // Check if data is stale
      final now = DateTime.now();
      final isStale = now.difference(lastSyncTime) > freshnessThreshold;

      logger.i(
        'Sync needed for $entityId: $isStale (last sync: $lastSyncTime)',
      );
      return isStale;
    } catch (e) {
      logger.e('Error checking if sync needed: ${e.toString()}');
      return true; // Default to sync needed on error
    }
  }

  /// Get the cache status for a specific entity
  Future<CacheStatus> getCacheStatus(
    String entityId, {
    String? entityType,
    Duration freshnessThreshold = defaultFreshnessThreshold,
  }) async {
    try {
      final lastSyncTime = await getLastSyncTime(
        entityId,
        entityType: entityType,
      );

      // If never synced, cache not found
      if (lastSyncTime == null) {
        return CacheStatus.notFound;
      }

      // Check if data is stale
      final now = DateTime.now();
      final isStale = now.difference(lastSyncTime) > freshnessThreshold;

      return isStale ? CacheStatus.stale : CacheStatus.fresh;
    } catch (e) {
      logger.e('Error getting cache status: ${e.toString()}');
      return CacheStatus.error;
    }
  }

  /// Mark cache as invalid to force a sync on next load
  Future<bool> invalidateCache(String entityId, {String? entityType}) async {
    try {
      final key = _getLastSyncTimeKey(entityId, entityType);
      // Setting to 0 effectively makes it very old and stale
      return await sharedPreferences.setInt(key, 0);
    } catch (e) {
      logger.e('Error invalidating cache: ${e.toString()}');
      return false;
    }
  }

  /// Helper method to get the last sync time key
  String _getLastSyncTimeKey(String entityId, String? entityType) {
    if (entityType != null) {
      return '${_lastSyncTimeKeyPrefix}${entityType}_$entityId';
    }
    return _lastSyncTimeKeyPrefix + entityId;
  }
}
