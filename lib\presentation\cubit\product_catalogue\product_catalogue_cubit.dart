import 'package:aquapartner/core/services/analytics_service.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../core/routes/app_router.dart';
import '../../../core/utils/logger.dart';
import '../../../domain/entities/product.dart';
import '../../../domain/usecases/interested_product_usecases.dart';
import '../../../domain/usecases/product_catalogue_usecases.dart';
import '../../../domain/usecases/user_usecases.dart';
import 'product_catalogue_state.dart';

class ProductCatalogueCubit extends Cubit<ProductCatalogueState> {
  final GetProductCataloguesUseCase getProductCataloguesUseCase;
  final SyncProductCataloguesUseCase syncProductCataloguesUseCase;
  final GetProductCatalogueByNameUseCase getProductCatalogueByNameUseCase;
  final AddInterestedProductUseCase addInterestedProductUseCase;
  final GetUserUseCase getUserUseCase;
  final AppLogger logger;
  final AnalyticsService analyticsService;

  ProductCatalogueCubit({
    required this.getProductCataloguesUseCase,
    required this.syncProductCataloguesUseCase,
    required this.getProductCatalogueByNameUseCase,
    required this.addInterestedProductUseCase,
    required this.getUserUseCase,
    required this.logger,
    required this.analyticsService,
  }) : super(ProductCatalogueState());

  bool _isInitialLoadDone = false;

  Future<void> loadProductCatalogues() async {
    if (_isInitialLoadDone && state.productCatalogues.isNotEmpty) {
      // Return if data is already loaded
      return;
    }

    emit(state.copyWith(status: ProductCatalogueStatus.loading));

    final result = await getProductCataloguesUseCase();

    result.fold(
      (failure) {
        logger.e("Failed to load product catalogues", failure);
        emit(
          state.copyWith(
            status: ProductCatalogueStatus.error,
            errorMessage: "Please try again later",
          ),
        );
      },
      (productCatalogues) {
        logger.i(
          "Successfully loaded ${productCatalogues.length} product catalogues",
        );
        _isInitialLoadDone = true;
        emit(
          state.copyWith(
            status: ProductCatalogueStatus.success,
            productCatalogues: productCatalogues,
            errorMessage: null,
          ),
        );
      },
    );
  }

  Future<void> syncProductCatalogues() async {
    emit(state.copyWith(status: ProductCatalogueStatus.syncing));

    final result = await syncProductCataloguesUseCase();

    result.fold(
      (failure) {
        logger.e("Failed to sync product catalogues", failure);
        emit(
          state.copyWith(
            status: ProductCatalogueStatus.error,
            errorMessage: "failed, try again later",
          ),
        );

        // Load from cache after sync failure
        loadProductCatalogues();
      },
      (productCatalogues) {
        logger.i(
          "Successfully synced ${productCatalogues.length} product catalogues",
        );
        emit(
          state.copyWith(
            status: ProductCatalogueStatus.success,
            productCatalogues: productCatalogues,
            errorMessage: null,
          ),
        );
      },
    );
  }

  Future<void> getProductCatalogueByName(String name) async {
    if (_isInitialLoadDone && state.productCatalogues.isNotEmpty) {
      final catalogue =
          state.productCatalogues.where((cat) => cat.name == name).toList();
      if (catalogue.isNotEmpty) {
        emit(
          state.copyWith(
            status: ProductCatalogueStatus.success,
            productCatalogues: catalogue,
            errorMessage: null,
          ),
        );
        return;
      }
    }

    emit(state.copyWith(status: ProductCatalogueStatus.loading));

    final result = await getProductCatalogueByNameUseCase(name);

    result.fold(
      (failure) {
        logger.e("Failed to get product catalogue by name: $name", failure);
        emit(
          state.copyWith(
            status: ProductCatalogueStatus.error,
            errorMessage: "please try again later",
          ),
        );
      },
      (productCatalogue) {
        if (productCatalogue != null) {
          logger.i(
            "Successfully loaded product catalogue: ${productCatalogue.name}",
          );
          emit(
            state.copyWith(
              status: ProductCatalogueStatus.success,
              productCatalogues: [productCatalogue],
              errorMessage: null,
            ),
          );
        } else {
          logger.w("Product catalogue not found: $name");
          emit(
            state.copyWith(
              status: ProductCatalogueStatus.success,
              productCatalogues: const [],
              errorMessage: null,
            ),
          );
        }
      },
    );
  }

  Future<void> addInterestedProduct({
    required String productName,
    required String source,
  }) async {
    try {
      logger.i("product name is $productName");
      // emit(state.copyWith(status: ProductCatalogueStatus.adding));
    } catch (e) {}
  }

  gotoProductDetailsScreen(Product product) {
    AppRouter.navigateToProductDetails(product: product);
  }

  void productTapped(Product product) {
    analyticsService.logProductView(product.productName, product.category);

    // Track additional metrics
    analyticsService.logEvent(
      name: 'product_tapped',
      parameters: {
        'product_name': product.productName,
        'product_category': product.category,
        'product_subcategory': product.subCategory,
        'product_tag': product.productTag,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }
}
