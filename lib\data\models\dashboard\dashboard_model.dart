import 'dart:convert';

import 'package:objectbox/objectbox.dart';
import '../../../domain/entities/dashboard/dashboard_entity.dart';
import 'dashboard_json_parser.dart';

@Entity()
class DashboardModel {
  @Id()
  int id = 0;

  final String customerId;
  final String salesJson;
  final String paymentsJson;
  final String duesJson;
  final double salesReturnJson; // Added salesReturn field
  final String categoryTypeSalesJson;
  final String liquidationJson;
  final String myFarmersJson;
  final bool isSynced;
  final DateTime lastSyncedAt;

  DashboardModel({
    required this.id,
    required this.customerId,
    required this.salesJson,
    required this.paymentsJson,
    required this.duesJson,
    required this.salesReturnJson, // Added salesReturn to constructor
    required this.categoryTypeSalesJson,
    required this.liquidationJson,
    required this.myFarmersJson,
    required this.isSynced,
    required this.lastSyncedAt,
  });

  factory DashboardModel.fromJson(Map<String, dynamic> json) {
    final customerId = json['customerId'] as String? ?? '';

    return DashboardModel(
      id: json['id'] as int? ?? 0,
      customerId: customerId,
      salesJson: json['sales'] != null ? jsonEncode(json['sales']) : '{}',
      paymentsJson:
          json['payments'] != null ? jsonEncode(json['payments']) : '{}',
      duesJson: json['dues'] != null ? jsonEncode(json['dues']) : '[]',
      salesReturnJson:
          (json['salesReturn'] is num)
              ? json['salesReturn'].toDouble()
              : 0.0, // Parse salesReturn
      categoryTypeSalesJson:
          json['categoryTypeSales'] != null
              ? jsonEncode(json['categoryTypeSales'])
              : '[]',
      liquidationJson:
          json['liquidation'] != null ? jsonEncode(json['liquidation']) : '{}',
      myFarmersJson:
          json['myFarmers'] != null ? jsonEncode(json['myFarmers']) : '{}',
      isSynced: true,
      lastSyncedAt: DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'customerId': customerId,
      'sales': salesJson,
      'payments': paymentsJson,
      'dues': duesJson,
      'salesReturn': salesReturnJson, // Added salesReturn to toJson
      'categoryTypeSales': categoryTypeSalesJson,
      'liquidation': liquidationJson,
      'myFarmers': myFarmersJson,
    };
  }

  DashboardEntity toEntity() {
    return DashboardJsonParser.modelToEntity(this);
  }
}
