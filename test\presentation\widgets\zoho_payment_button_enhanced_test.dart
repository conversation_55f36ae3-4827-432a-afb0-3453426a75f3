import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import '../../../lib/domain/entities/payments/payment_request.dart';
import '../../../lib/domain/entities/payments/payment_session.dart';
import '../../../lib/domain/entities/payments/payment_transaction.dart';
import '../../../lib/presentation/cubit/payments/payment_cubit.dart';
import '../../../lib/presentation/cubit/payments/payment_state.dart';
import '../../../lib/presentation/widgets/zoho_payment_button.dart';

import 'zoho_payment_button_enhanced_test.mocks.dart';

@GenerateMocks([PaymentCubit])
void main() {
  group('ZohoPaymentButton Enhanced Tests', () {
    late MockPaymentCubit mockPaymentCubit;
    late GetIt getIt;

    setUp(() {
      mockPaymentCubit = MockPaymentCubit();
      getIt = GetIt.instance;
      
      // Reset GetIt and register mock
      getIt.reset();
      getIt.registerFactory<PaymentCubit>(() => mockPaymentCubit);
      
      // Setup default mock behavior
      when(mockPaymentCubit.state).thenReturn(const PaymentInitial());
      when(mockPaymentCubit.stream).thenAnswer((_) => const Stream.empty());
    });

    tearDown(() {
      getIt.reset();
    });

    Widget createTestWidget({
      double amount = 100.0,
      String invoiceNumber = 'TEST-001',
      String customerId = 'CUST-001',
      String? description,
      String? customerName,
      String? customerEmail,
      String? customerPhone,
      Function(bool, String?)? onPaymentComplete,
      String buttonText = 'Pay Now',
    }) {
      return MaterialApp(
        home: Scaffold(
          body: ZohoPaymentButton(
            amount: amount,
            invoiceNumber: invoiceNumber,
            customerId: customerId,
            description: description,
            customerName: customerName,
            customerEmail: customerEmail,
            customerPhone: customerPhone,
            onPaymentComplete: onPaymentComplete ?? (success, transactionId) {},
            buttonText: buttonText,
          ),
        ),
      );
    }

    group('Widget Rendering', () {
      testWidgets('should display button with correct text', (tester) async {
        await tester.pumpWidget(createTestWidget(buttonText: 'Custom Pay'));
        
        expect(find.text('Custom Pay'), findsOneWidget);
        expect(find.byType(ElevatedButton), findsOneWidget);
      });

      testWidgets('should display loading indicator when payment is loading', (tester) async {
        when(mockPaymentCubit.state).thenReturn(const PaymentLoading());
        
        await tester.pumpWidget(createTestWidget());
        await tester.pump();
        
        expect(find.byType(CircularProgressIndicator), findsOneWidget);
        expect(find.text('Pay Now'), findsNothing);
      });

      testWidgets('should display processing status with message', (tester) async {
        when(mockPaymentCubit.state).thenReturn(const PaymentProcessing(
          sessionId: 'session_123',
          statusMessage: 'Verifying payment...',
        ));
        
        await tester.pumpWidget(createTestWidget());
        await tester.pump();
        
        expect(find.text('Verifying payment...'), findsOneWidget);
        expect(find.byType(CircularProgressIndicator), findsOneWidget);
      });

      testWidgets('should disable button when loading', (tester) async {
        when(mockPaymentCubit.state).thenReturn(const PaymentLoading());
        
        await tester.pumpWidget(createTestWidget());
        await tester.pump();
        
        final button = tester.widget<ElevatedButton>(find.byType(ElevatedButton));
        expect(button.onPressed, isNull);
      });
    });

    group('Payment Flow Interactions', () {
      testWidgets('should call createPaymentSession with correct parameters when tapped', (tester) async {
        await tester.pumpWidget(createTestWidget(
          amount: 150.0,
          invoiceNumber: 'INV-123',
          customerId: 'CUST-456',
          description: 'Test payment',
          customerName: 'John Doe',
          customerEmail: '<EMAIL>',
          customerPhone: '+919876543210',
        ));
        
        await tester.tap(find.byType(ElevatedButton));
        await tester.pump();
        
        verify(mockPaymentCubit.createPaymentSession(argThat(
          predicate<PaymentRequest>((request) =>
            request.amount == 150.0 &&
            request.invoiceNumber == 'INV-123' &&
            request.customerId == 'CUST-456' &&
            request.description == 'Test payment' &&
            request.customerName == 'John Doe' &&
            request.customerEmail == '<EMAIL>' &&
            request.customerPhone == '+919876543210' &&
            request.currency == 'INR' &&
            request.metadata?['payment_type'] == 'invoice' &&
            request.metadata?['business_type'] == 'aquaculture' &&
            request.metadata?['payment_method'] == 'zoho_payments'
          ),
        ))).called(1);
      });

      testWidgets('should not call createPaymentSession when button is disabled', (tester) async {
        when(mockPaymentCubit.state).thenReturn(const PaymentLoading());
        
        await tester.pumpWidget(createTestWidget());
        await tester.pump();
        
        await tester.tap(find.byType(ElevatedButton));
        await tester.pump();
        
        verifyNever(mockPaymentCubit.createPaymentSession(any));
      });

      testWidgets('should handle payment session created state', (tester) async {
        final paymentSession = PaymentSession(
          sessionId: 'session_123',
          paymentUrl: 'https://payment.zoho.com/session_123',
          amount: 100.0,
          currency: 'INR',
          invoiceNumber: 'TEST-001',
          customerId: 'CUST-001',
          description: 'Test payment',
          status: PaymentSessionStatus.created,
          createdAt: DateTime.now(),
        );

        when(mockPaymentCubit.stream).thenAnswer((_) => Stream.fromIterable([
          PaymentSessionCreated(paymentSession: paymentSession),
        ]));
        
        await tester.pumpWidget(createTestWidget());
        await tester.pump();
        
        // Note: WebView navigation would be tested in integration tests
        // Here we just verify the state is handled
      });
    });

    group('Payment Completion Handling', () {
      testWidgets('should show success snackbar and call onPaymentComplete for successful payment', (tester) async {
        bool paymentCompleted = false;
        String? receivedTransactionId;
        
        final transaction = PaymentTransaction(
          transactionId: 'txn_123',
          sessionId: 'session_123',
          invoiceNumber: 'TEST-001',
          customerId: 'CUST-001',
          amount: 100.0,
          currency: 'INR',
          status: PaymentTransactionStatus.success,
          paymentMethod: PaymentMethod.creditCard,
          transactionDate: DateTime.now(),
        );

        when(mockPaymentCubit.stream).thenAnswer((_) => Stream.fromIterable([
          PaymentSuccess(transaction: transaction),
        ]));
        
        await tester.pumpWidget(createTestWidget(
          onPaymentComplete: (success, transactionId) {
            paymentCompleted = success;
            receivedTransactionId = transactionId;
          },
        ));
        await tester.pump();
        await tester.pump(); // Allow snackbar to appear
        
        expect(find.text('Payment completed successfully!'), findsOneWidget);
        expect(paymentCompleted, true);
        expect(receivedTransactionId, 'txn_123');
      });

      testWidgets('should show error snackbar and call onPaymentComplete for failed payment', (tester) async {
        bool paymentCompleted = true;
        String? receivedTransactionId = 'should_be_null';
        
        when(mockPaymentCubit.stream).thenAnswer((_) => Stream.fromIterable([
          const PaymentFailed(sessionId: 'session_123', reason: 'Payment declined'),
        ]));
        
        await tester.pumpWidget(createTestWidget(
          onPaymentComplete: (success, transactionId) {
            paymentCompleted = success;
            receivedTransactionId = transactionId;
          },
        ));
        await tester.pump();
        await tester.pump(); // Allow snackbar to appear
        
        expect(find.text('Payment failed: Payment declined'), findsOneWidget);
        expect(paymentCompleted, false);
        expect(receivedTransactionId, null);
      });

      testWidgets('should handle payment cancellation', (tester) async {
        bool paymentCompleted = true;
        
        when(mockPaymentCubit.stream).thenAnswer((_) => Stream.fromIterable([
          const PaymentCancelled(sessionId: 'session_123'),
        ]));
        
        await tester.pumpWidget(createTestWidget(
          onPaymentComplete: (success, transactionId) {
            paymentCompleted = success;
          },
        ));
        await tester.pump();
        
        expect(paymentCompleted, false);
      });

      testWidgets('should handle payment expiration', (tester) async {
        when(mockPaymentCubit.stream).thenAnswer((_) => Stream.fromIterable([
          const PaymentExpired(sessionId: 'session_123'),
        ]));
        
        await tester.pumpWidget(createTestWidget());
        await tester.pump();
        await tester.pump(); // Allow snackbar to appear
        
        expect(find.text('Payment session expired. Please try again.'), findsOneWidget);
      });

      testWidgets('should handle generic payment error', (tester) async {
        when(mockPaymentCubit.stream).thenAnswer((_) => Stream.fromIterable([
          const PaymentError(message: 'Network error occurred'),
        ]));
        
        await tester.pumpWidget(createTestWidget());
        await tester.pump();
        await tester.pump(); // Allow snackbar to appear
        
        expect(find.text('Network error occurred'), findsOneWidget);
      });
    });

    group('Analytics Integration', () {
      testWidgets('should track payment initiation event', (tester) async {
        await tester.pumpWidget(createTestWidget(
          amount: 250.0,
          invoiceNumber: 'INV-456',
          customerId: 'CUST-789',
        ));
        
        await tester.tap(find.byType(ElevatedButton));
        await tester.pump();
        
        // Note: Analytics tracking would be verified through mock analytics service
        // This test ensures the button tap triggers the payment flow
        verify(mockPaymentCubit.createPaymentSession(any)).called(1);
      });
    });

    group('Edge Cases', () {
      testWidgets('should handle null optional parameters', (tester) async {
        await tester.pumpWidget(createTestWidget(
          description: null,
          customerName: null,
          customerEmail: null,
          customerPhone: null,
        ));
        
        await tester.tap(find.byType(ElevatedButton));
        await tester.pump();
        
        verify(mockPaymentCubit.createPaymentSession(argThat(
          predicate<PaymentRequest>((request) =>
            request.description == 'Payment for invoice TEST-001' &&
            request.customerName == null &&
            request.customerEmail == null &&
            request.customerPhone == null
          ),
        ))).called(1);
      });

      testWidgets('should handle very large amounts', (tester) async {
        await tester.pumpWidget(createTestWidget(amount: 999999.99));
        
        await tester.tap(find.byType(ElevatedButton));
        await tester.pump();
        
        verify(mockPaymentCubit.createPaymentSession(argThat(
          predicate<PaymentRequest>((request) => request.amount == 999999.99),
        ))).called(1);
      });

      testWidgets('should handle special characters in invoice number', (tester) async {
        await tester.pumpWidget(createTestWidget(invoiceNumber: 'INV-123/ABC_456'));
        
        await tester.tap(find.byType(ElevatedButton));
        await tester.pump();
        
        verify(mockPaymentCubit.createPaymentSession(argThat(
          predicate<PaymentRequest>((request) => request.invoiceNumber == 'INV-123/ABC_456'),
        ))).called(1);
      });
    });

    group('Cleanup', () {
      testWidgets('should call resetPayment on dispose', (tester) async {
        await tester.pumpWidget(createTestWidget());
        
        // Navigate away to trigger dispose
        await tester.pumpWidget(const MaterialApp(home: Scaffold(body: Text('New Page'))));
        
        verify(mockPaymentCubit.resetPayment()).called(1);
      });
    });
  });
}
