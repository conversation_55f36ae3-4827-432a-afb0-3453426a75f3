import 'package:objectbox/objectbox.dart';
import '../../../domain/entities/sales_order/sales_order.dart';
import 'sales_order_item_model.dart';

@Entity()
class SalesOrderModel {
  @Id()
  int id = 0;

  final String salesOrderId;
  final String addressId;

  @Property(type: PropertyType.date)
  final DateTime createdTime;

  final String customerId;
  final String invoicedStatus;

  @Property(type: PropertyType.date)
  final DateTime lastModifiedTime;

  final String orderSource;
  final String paidStatus;
  final String paymentTermsLabel;
  final String saleOrderDate;
  final String salesChannel;
  final String salesOrderNumber;
  final double subTotal;
  final double total;
  final bool isSynced;

  @Property(type: PropertyType.date)
  final DateTime? lastSyncedAt;

  @Backlink('salesOrder')
  final items = ToMany<SalesOrderItemModel>();

  SalesOrderModel({
    required this.salesOrderId,
    required this.addressId,
    required this.createdTime,
    required this.customerId,
    required this.invoicedStatus,
    required this.lastModifiedTime,
    required this.orderSource,
    required this.paidStatus,
    required this.paymentTermsLabel,
    required this.saleOrderDate,
    required this.salesChannel,
    required this.salesOrderNumber,
    required this.subTotal,
    required this.total,
    this.isSynced = false,
    this.lastSyncedAt,
  });

  // Convert model to entity
  SalesOrder toEntity() {
    return SalesOrder(
      id: id,
      salesOrderId: salesOrderId,
      addressId: addressId,
      createdTime: createdTime,
      customerId: customerId,
      invoicedStatus: invoicedStatus,
      lastModifiedTime: lastModifiedTime,
      orderSource: orderSource,
      paidStatus: paidStatus,
      paymentTermsLabel: paymentTermsLabel,
      saleOrderDate: saleOrderDate,
      salesChannel: salesChannel,
      salesOrderNumber: salesOrderNumber,
      subTotal: subTotal,
      total: total,
      isSynced: isSynced,
      lastSyncedAt: lastSyncedAt,
      items: items.map((item) => item.toEntity()).toList(),
    );
  }

  // Create model from entity
  static SalesOrderModel fromEntity(SalesOrder entity) {
    return SalesOrderModel(
      salesOrderId: entity.salesOrderId,
      addressId: entity.addressId,
      createdTime: entity.createdTime,
      customerId: entity.customerId,
      invoicedStatus: entity.invoicedStatus,
      lastModifiedTime: entity.lastModifiedTime,
      orderSource: entity.orderSource,
      paidStatus: entity.paidStatus,
      paymentTermsLabel: entity.paymentTermsLabel,
      saleOrderDate: entity.saleOrderDate,
      salesChannel: entity.salesChannel,
      salesOrderNumber: entity.salesOrderNumber,
      subTotal: entity.subTotal,
      total: entity.total,
      isSynced: entity.isSynced,
      lastSyncedAt: entity.lastSyncedAt,
    );
  }

  // Convert model to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'salesOrderId': salesOrderId,
      'addressId': addressId,
      'createdTime': createdTime.toIso8601String(),
      'customerId': customerId,
      'invoicedStatus': invoicedStatus,
      'lastModifiedTime': lastModifiedTime.toIso8601String(),
      'orderSource': orderSource,
      'paidStatus': paidStatus,
      'paymentTermsLabel': paymentTermsLabel,
      'saleOrderDate': saleOrderDate,
      'salesChannel': salesChannel,
      'salesOrderNumber': salesOrderNumber,
      'subTotal': subTotal,
      'total': total,
      'isSynced': isSynced,
      'lastSyncedAt': lastSyncedAt?.toIso8601String(),
      'items': items.map((item) => item.toJson()).toList(),
    };
  }

  // Create model from JSON
  factory SalesOrderModel.fromJson(Map<String, dynamic> json) {
    return SalesOrderModel(
      salesOrderId: json['_id'] ?? json['salesOrderId'] ?? '',
      addressId: json['addressId'] ?? '',
      createdTime:
          json['createdTime'] != null
              ? DateTime.parse(json['createdTime'])
              : DateTime.now(),
      customerId: json['customerId'] ?? '',
      invoicedStatus: json['invoicedStatus'] ?? '',
      lastModifiedTime:
          json['lastModifiedTime'] != null
              ? DateTime.parse(json['lastModifiedTime'])
              : DateTime.now(),
      orderSource: json['orderSource'] ?? '',
      paidStatus: json['paidStatus'] ?? '',
      paymentTermsLabel: json['paymentTermsLabel'] ?? '',
      saleOrderDate: json['saleOrderDate'] ?? '',
      salesChannel: json['salesChannel'] ?? '',
      salesOrderNumber: json['salesOrderNumber'] ?? '',
      subTotal: (json['subTotal'] ?? 0).toDouble(),
      total: (json['total'] ?? 0).toDouble(),
      isSynced: json['isSynced'] ?? false,
      lastSyncedAt:
          json['lastSyncedAt'] != null
              ? DateTime.parse(json['lastSyncedAt'])
              : null,
    );
  }
}
