import 'dart:async';
import 'package:dartz/dartz.dart';

import '../../core/error/exceptions.dart';
import '../../core/error/failures.dart';
import '../../core/network/network_info.dart';
import '../../core/utils/logger.dart';
import '../../domain/entities/dashboard/dashboard_entity.dart';
import '../../domain/entities/sync_status.dart';
import '../../domain/repositories/dashboard_repository.dart';
import '../datasources/local/dashboard_local_datasource.dart';
import '../datasources/remote/dashboard_remote_datasource.dart';
import '../models/dashboard/dashboard_json_parser.dart';
import '../models/dashboard/dashboard_model.dart';

class SyncManager {
  final Map<String, bool> _syncInProgress = {};
  final _syncStatusController = StreamController<SyncStatus>.broadcast();

  Stream<SyncStatus> get syncStatusStream => _syncStatusController.stream;

  bool isSyncing(String customerId) {
    return _syncInProgress[customerId] ?? false;
  }

  void _updateSyncStatus(SyncStatus status) {
    _syncStatusController.add(status);
  }

  Future<T> runWithLock<T>(
    String customerId,
    Future<T> Function() operation,
  ) async {
    if (_syncInProgress[customerId] == true) {
      throw ConcurrencyException(
        'Sync already in progress for customer: $customerId',
      );
    }

    _syncInProgress[customerId] = true;
    _updateSyncStatus(
      SyncStatus(
        customerId: customerId,
        status: SyncStatusType.inProgress,
        message: 'Sync started',
      ),
    );

    try {
      final result = await operation();
      _updateSyncStatus(
        SyncStatus(
          customerId: customerId,
          status: SyncStatusType.completed,
          message: 'Sync completed successfully',
        ),
      );
      return result;
    } catch (e) {
      _updateSyncStatus(
        SyncStatus(
          customerId: customerId,
          status: SyncStatusType.failed,
          message: 'Sync failed: $e',
        ),
      );
      rethrow;
    } finally {
      _syncInProgress[customerId] = false;
    }
  }

  void dispose() {
    _syncStatusController.close();
  }
}

class ConcurrencyException implements Exception {
  final String message;
  ConcurrencyException(this.message);
  @override
  String toString() => 'ConcurrencyException: $message';
}

class DashboardRepositoryImpl implements DashboardRepository {
  final DashboardRemoteDataSource remoteDataSource;
  final DashboardLocalDataSource localDataSource;
  final NetworkInfo networkInfo;
  final AppLogger logger;
  final SyncManager _syncManager = SyncManager();

  final Duration _dataFreshnessThreshold;
  final int _maxRetries;
  final Duration _initialRetryDelay;

  DashboardRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
    required this.networkInfo,
    required this.logger,
    Duration? dataFreshnessThreshold,
    int? maxRetries,
    Duration? initialRetryDelay,
  }) : _dataFreshnessThreshold =
           dataFreshnessThreshold ?? const Duration(seconds: 1),
       _maxRetries = maxRetries ?? 3,
       _initialRetryDelay =
           initialRetryDelay ?? const Duration(milliseconds: 300);

  Stream<SyncStatus> get syncStatusStream => _syncManager.syncStatusStream;

  @override
  Future<Either<Failure, DashboardEntity>> getDashboard(
    String customerId,
  ) async {
    try {
      // Always try local data first for immediate UI response
      final DashboardModel? localData = await _getLocalDashboard(customerId);

      if (localData != null) {
        logger.i(localData.toString());
        logger.i('Serving dashboard from local cache for $customerId');
        final dashboardEntity = DashboardJsonParser.modelToEntity(localData);

        // Trigger background sync check without blocking UI
        _checkAndTriggerBackgroundSyncIfNeeded(customerId);

        return Right(dashboardEntity);
      } else {
        // No local data available
        logger.i('No local data for $customerId');

        // Check network connectivity - only do this if we have no local data
        final bool isConnected = await networkInfo.isConnected;
        if (!isConnected) {
          logger.w('No network connection and no local data for $customerId');
          return Left(CacheFailure());
        }

        // Fetch from remote, cache it, and return
        return _fetchRemoteAndCache(customerId);
      }
    } catch (e, stackTrace) {
      logger.e(
        'Unexpected error in getDashboard for $customerId: $e, stackTrace: $stackTrace',
      );
      return Left(UnexpectedFailure('An unexpected error occurred: $e'));
    }
  }

  // This method runs the connectivity and staleness checks in the background
  void _checkAndTriggerBackgroundSyncIfNeeded(String customerId) {
    // Fire and forget - don't block UI
    Future(() async {
      try {
        final bool isConnected = await networkInfo.isConnected;
        if (isConnected && await _isDataStale(customerId)) {
          _triggerBackgroundSync(customerId);
        }
      } catch (e) {
        logger.w('Error in background sync check: $e');
      }
    });
  }

  Future<DashboardModel?> _getLocalDashboard(String customerId) async {
    try {
      return await localDataSource.getDashboard(customerId);
    } on CacheException {
      return null;
    }
  }

  Future<Either<Failure, DashboardEntity>> _fetchRemoteAndCache(
    String customerId, {
    bool clearLocalFirst = false,
  }) async {
    try {
      final remoteModel = await _fetchRemoteWithRetry(customerId);

      if (remoteModel == null) {
        logger.w('Remote data source returned null for $customerId');
        return Left(ServerFailure());
      }

      logger.i('Retrieved dashboard from remote for $customerId. Caching...');

      // If one-way sync is requested, clear local data first
      if (clearLocalFirst) {
        logger.i(
          'Clearing local dashboard data for $customerId before caching remote data',
        );
        await localDataSource.clearDashboardForCustomer(customerId);
      }

      await localDataSource.cacheDashboard(remoteModel);
      await localDataSource.updateLastSyncTime(customerId, DateTime.now());

      final dashboardEntity = DashboardJsonParser.modelToEntity(remoteModel);
      return Right(dashboardEntity);
    } on ServerException catch (e) {
      logger.e('Server exception during remote fetch for $customerId: $e');
      return Left(ServerFailure());
    } catch (e, stackTrace) {
      logger.e(
        'Error fetching/caching remote data for $customerId: $e, stackTrace: $stackTrace',
      );
      if (!await networkInfo.isConnected) {
        return Left(NetworkFailure());
      }
      return Left(UnexpectedFailure('Failed to fetch or cache data: $e'));
    }
  }

  void _triggerBackgroundSync(String customerId) {
    if (_syncManager.isSyncing(customerId)) {
      logger.i('Background sync skipped: already in progress for $customerId');
      return;
    }

    // Fire and forget - don't await
    // Use the same one-way sync approach for background syncs
    syncDashboard(customerId).then((result) {
      result.fold(
        (failure) => logger.e('Background one-way sync failed: $failure'),
        (_) => logger.i('Background one-way sync completed successfully'),
      );
    });
  }

  Future<DashboardModel?> _fetchRemoteWithRetry(String customerId) async {
    int attempts = 0;
    while (true) {
      try {
        attempts++;
        return await remoteDataSource.getDashboard(customerId);
      } catch (e) {
        logger.w('Remote fetch attempt $attempts failed for $customerId: $e');
        if (attempts >= _maxRetries || e is! ServerException) {
          rethrow;
        }

        final delay = Duration(
          milliseconds:
              _initialRetryDelay.inMilliseconds * (1 << (attempts - 1)),
        );

        logger.i(
          'Retrying fetch for $customerId in ${delay.inMilliseconds}ms...',
        );
        await Future.delayed(delay);
      }
    }
  }

  Future<bool> _isDataStale(String customerId) async {
    try {
      final lastSyncTime = await localDataSource.getLastSyncTime(customerId);
      if (lastSyncTime == null) {
        return true;
      }
      final bool isStale =
          DateTime.now().difference(lastSyncTime) > _dataFreshnessThreshold;
      return isStale;
    } catch (e) {
      logger.w(
        'Error checking sync status for $customerId: $e. Assuming sync is needed.',
      );
      return true;
    }
  }

  @override
  Future<Either<Failure, bool>> syncDashboard(String customerId) async {
    if (!await networkInfo.isConnected) {
      logger.w('Explicit sync failed: No network connection for $customerId');
      return Left(NetworkFailure());
    }

    if (_syncManager.isSyncing(customerId)) {
      logger.i(
        'Explicit sync request ignored: sync already in progress for $customerId',
      );
      return Right(false);
    }

    logger.i('Starting explicit one-way sync for $customerId...');
    try {
      return await _syncManager.runWithLock(customerId, () async {
        // Perform one-way sync: clear local data and replace with remote data
        final Either<Failure, DashboardEntity> result =
            await _fetchRemoteAndCache(customerId, clearLocalFirst: true);
        return result.fold(
          (failure) => Left(failure),
          (_) => const Right(true),
        );
      });
    } on ConcurrencyException {
      logger.w(
        'Explicit sync attempt blocked by concurrency lock for $customerId',
      );
      return Right(false);
    } catch (e, stackTrace) {
      logger.e(
        'Unexpected error during explicit sync for $customerId: $e, stackTrace: $stackTrace',
      );
      return Left(UnexpectedFailure('Sync failed unexpectedly: $e'));
    }
  }

  @override
  Future<DateTime?> getLastSyncTime(String customerId) async {
    try {
      return await localDataSource.getLastSyncTime(customerId);
    } catch (e) {
      logger.w('Failed to get last sync time for $customerId: $e');
      return null;
    }
  }

  @override
  bool isSyncing(String customerId) {
    return _syncManager.isSyncing(customerId);
  }
}
