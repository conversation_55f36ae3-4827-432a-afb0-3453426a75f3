import 'package:equatable/equatable.dart';

class InvoiceItem extends Equatable {
  final String id;
  final String productId;
  final String itemName;
  final double quantity;
  final String invoiceId;
  final DateTime createdTime;
  final double discountAmount;
  final String hsnSac;
  final double itemPrice;
  final DateTime lastModifiedTime;
  final String placeOfSupply;
  final String productCategory;
  final String source;
  final double subTotal;
  final double total;

  const InvoiceItem({
    required this.id,
    required this.productId,
    required this.itemName,
    required this.quantity,
    required this.invoiceId,
    required this.createdTime,
    required this.discountAmount,
    required this.hsnSac,
    required this.itemPrice,
    required this.lastModifiedTime,
    required this.placeOfSupply,
    required this.productCategory,
    required this.source,
    required this.subTotal,
    required this.total,
  });

  @override
  List<Object?> get props => [
    id,
    productId,
    itemName,
    quantity,
    invoiceId,
    createdTime,
    discountAmount,
    hsnSac,
    itemPrice,
    lastModifiedTime,
    placeOfSupply,
    productCategory,
    source,
    subTotal,
    total,
  ];
}
