import 'package:dartz/dartz.dart';

import '../../core/error/failures.dart';
import '../entities/credit_notes/credit_note.dart';

abstract class CreditNotesRepository {
  /// Fetches from network if online, otherwise returns local data.
  /// Also caches the network response locally on success.
  Future<Either<Failure, List<CreditNote>>> getCreditNotes(String customerId);

  /// Forces a fetch from the network and updates the local cache.
  /// Returns failure if offline.
  Future<Either<Failure, void>> syncCreditNotes(String customerId);

  /// Fetches only from the local cache.
  Future<Either<Failure, List<CreditNote>>> getLocalCreditNotes(
    String customerId,
  );
}
