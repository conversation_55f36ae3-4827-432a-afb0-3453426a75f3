import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/farmer_visit/farmer.dart';
import '../repositories/farmer_repository.dart';

class GetAllFarmersParams extends Equatable {
  final String customerId; // Or int, depending on your customer ID type

  const GetAllFarmersParams({required this.customerId});

  @override
  List<Object?> get props => [customerId];
}

/// Use case to get all farmers.
class GetAllFarmersUseCase extends UseCase<List<Farmer>, GetAllFarmersParams> {
  final FarmerRepository repository;

  GetAllFarmersUseCase(this.repository);

  /// Executes the use case to get all farmers from the repository.
  @override
  Future<Either<Failure, List<Farmer>>> call(GetAllFarmersParams params) async {
    return await repository.getAllFarmers(params.customerId);
  }

  /// Performs a one-way sync of farmers data from server to local.
  /// This clears all local data and replaces it with remote data.
  Future<Either<Failure, List<Farmer>>> syncFarmers(String customerId) async {
    return await repository.syncFarmers(customerId);
  }
}
