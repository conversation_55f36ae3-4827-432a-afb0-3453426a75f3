import 'package:aquapartner/core/network/network_info.dart';
import 'package:aquapartner/core/utils/logger.dart';
import 'package:aquapartner/data/datasources/local/customer_local_data_source.dart';
import 'package:aquapartner/data/datasources/local/dashboard_local_datasource.dart';
import 'package:aquapartner/data/datasources/local/user_local_data_source.dart';
import 'package:aquapartner/data/datasources/remote/auth_remote_data_source.dart';
import 'package:aquapartner/data/repositories/auth_repository_impl.dart';
import 'package:aquapartner/domain/repositories/auth_repository.dart';
import 'package:aquapartner/domain/repositories/user_repository.dart';
import 'package:aquapartner/domain/usecases/auth_usecases.dart';
import 'package:aquapartner/domain/usecases/sync_usecases.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:get_it/get_it.dart';

void initAuthDependencies(GetIt sl) {
  // External
  sl.registerLazySingleton(() => FirebaseAuth.instance);

  // Use cases
  sl.registerLazySingleton(() => SendOtpUseCase(sl()));
  sl.registerLazySingleton(() => VerifyOtpUseCase(sl()));
  sl.registerLazySingleton(() => SignOutUseCase(sl()));
  sl.registerLazySingleton(
    () => CheckAuthStatusUseCase(
      repository: sl<UserRepository>(),
      logger: sl<AppLogger>(),
    ),
  );

  // Repository
  sl.registerLazySingleton<AuthRepository>(
    () => AuthRepositoryImpl(
      localDataSource: sl<UserLocalDataSource>(),
      customerLocalDataSource: sl<CustomerLocalDataSource>(),
      dashboardLocalDataSource: sl<DashboardLocalDataSource>(),
      remoteDataSource: sl<AuthRemoteDataSource>(),
      networkInfo: sl<NetworkInfo>(),
      firebaseAuth: sl<FirebaseAuth>(),
      logger: sl<AppLogger>(),
    ),
  );

  // Data sources
  sl.registerLazySingleton<AuthRemoteDataSource>(
    () => AuthRemoteDataSourceImpl(
      auth: sl<FirebaseAuth>(),
      logger: sl<AppLogger>(),
    ),
  );

  sl.registerLazySingleton<UserLocalDataSource>(
    () => UserLocalDataSourceImpl(userBox: sl(), logger: sl<AppLogger>()),
  );
}
