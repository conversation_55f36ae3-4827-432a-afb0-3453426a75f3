class Customer {
  final String? id;
  final String customerId;
  final String customerName;
  final String email;
  final String mobileNumber;
  final String companyName;
  final String gstNo;
  final String businessVertical;
  final String customerCode;
  final String billingAddress;

  Customer({
    this.id,
    required this.customerId,
    required this.customerName,
    required this.email,
    required this.mobileNumber,
    required this.companyName,
    required this.gstNo,
    required this.businessVertical,
    required this.customerCode,
    required this.billingAddress,
  });
}
