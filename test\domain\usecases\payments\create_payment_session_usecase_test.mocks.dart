// Mocks generated by <PERSON>cki<PERSON> 5.4.6 from annotations
// in aquapartner/test/domain/usecases/payments/create_payment_session_usecase_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:aquapartner/core/error/failures.dart' as _i5;
import 'package:aquapartner/domain/entities/payments/payment_request.dart'
    as _i7;
import 'package:aquapartner/domain/entities/payments/payment_session.dart'
    as _i6;
import 'package:aquapartner/domain/entities/payments/payment_transaction.dart'
    as _i8;
import 'package:aquapartner/domain/repositories/payment_repository.dart' as _i3;
import 'package:dartz/dartz.dart' as _i2;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeEither_0<L, R> extends _i1.SmartFake implements _i2.Either<L, R> {
  _FakeEither_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [PaymentRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockPaymentRepository extends _i1.Mock implements _i3.PaymentRepository {
  MockPaymentRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i6.PaymentSession>> createPaymentSession(
    _i7.PaymentRequest? request,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#createPaymentSession, [request]),
            returnValue:
                _i4.Future<_i2.Either<_i5.Failure, _i6.PaymentSession>>.value(
                  _FakeEither_0<_i5.Failure, _i6.PaymentSession>(
                    this,
                    Invocation.method(#createPaymentSession, [request]),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i6.PaymentSession>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i6.PaymentSession>> getPaymentSession(
    String? sessionId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getPaymentSession, [sessionId]),
            returnValue:
                _i4.Future<_i2.Either<_i5.Failure, _i6.PaymentSession>>.value(
                  _FakeEither_0<_i5.Failure, _i6.PaymentSession>(
                    this,
                    Invocation.method(#getPaymentSession, [sessionId]),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i6.PaymentSession>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i8.PaymentTransaction>> verifyPayment(
    String? sessionId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#verifyPayment, [sessionId]),
            returnValue: _i4.Future<
              _i2.Either<_i5.Failure, _i8.PaymentTransaction>
            >.value(
              _FakeEither_0<_i5.Failure, _i8.PaymentTransaction>(
                this,
                Invocation.method(#verifyPayment, [sessionId]),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i8.PaymentTransaction>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i6.PaymentSession>> checkPaymentStatus(
    String? sessionId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#checkPaymentStatus, [sessionId]),
            returnValue:
                _i4.Future<_i2.Either<_i5.Failure, _i6.PaymentSession>>.value(
                  _FakeEither_0<_i5.Failure, _i6.PaymentSession>(
                    this,
                    Invocation.method(#checkPaymentStatus, [sessionId]),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i6.PaymentSession>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, void>> cancelPaymentSession(
    String? sessionId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#cancelPaymentSession, [sessionId]),
            returnValue: _i4.Future<_i2.Either<_i5.Failure, void>>.value(
              _FakeEither_0<_i5.Failure, void>(
                this,
                Invocation.method(#cancelPaymentSession, [sessionId]),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, void>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i8.PaymentTransaction>>
  getPaymentTransaction(String? transactionId) =>
      (super.noSuchMethod(
            Invocation.method(#getPaymentTransaction, [transactionId]),
            returnValue: _i4.Future<
              _i2.Either<_i5.Failure, _i8.PaymentTransaction>
            >.value(
              _FakeEither_0<_i5.Failure, _i8.PaymentTransaction>(
                this,
                Invocation.method(#getPaymentTransaction, [transactionId]),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i8.PaymentTransaction>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, List<_i8.PaymentTransaction>>>
  getCustomerTransactions(String? customerId, {int? limit, int? offset}) =>
      (super.noSuchMethod(
            Invocation.method(
              #getCustomerTransactions,
              [customerId],
              {#limit: limit, #offset: offset},
            ),
            returnValue: _i4.Future<
              _i2.Either<_i5.Failure, List<_i8.PaymentTransaction>>
            >.value(
              _FakeEither_0<_i5.Failure, List<_i8.PaymentTransaction>>(
                this,
                Invocation.method(
                  #getCustomerTransactions,
                  [customerId],
                  {#limit: limit, #offset: offset},
                ),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, List<_i8.PaymentTransaction>>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, List<_i8.PaymentTransaction>>>
  getInvoiceTransactions(String? invoiceNumber) =>
      (super.noSuchMethod(
            Invocation.method(#getInvoiceTransactions, [invoiceNumber]),
            returnValue: _i4.Future<
              _i2.Either<_i5.Failure, List<_i8.PaymentTransaction>>
            >.value(
              _FakeEither_0<_i5.Failure, List<_i8.PaymentTransaction>>(
                this,
                Invocation.method(#getInvoiceTransactions, [invoiceNumber]),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, List<_i8.PaymentTransaction>>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i8.PaymentTransaction>>
  processWebhookNotification(Map<String, dynamic>? webhookData) =>
      (super.noSuchMethod(
            Invocation.method(#processWebhookNotification, [webhookData]),
            returnValue: _i4.Future<
              _i2.Either<_i5.Failure, _i8.PaymentTransaction>
            >.value(
              _FakeEither_0<_i5.Failure, _i8.PaymentTransaction>(
                this,
                Invocation.method(#processWebhookNotification, [webhookData]),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i8.PaymentTransaction>>);
}
