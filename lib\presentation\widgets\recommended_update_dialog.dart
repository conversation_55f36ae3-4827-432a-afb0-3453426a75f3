import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:aqua_ui/aqua_ui.dart';
import '../../domain/entities/update_info.dart';

class RecommendedUpdateDialog extends StatelessWidget {
  final UpdateInfo updateInfo;

  const RecommendedUpdateDialog({super.key, required this.updateInfo});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: AquaText.headline('Update Available'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AquaText.body(updateInfo.message),
          const SizedBox(height: 16),
          AquaText.body(
            'This update is recommended but not required. You can continue using the app without updating.',
            color: acTextSecondaryColor,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.of(context).pop();
          },
          child: AquaText.body('Later'),
        ),
        ElevatedButton(
          onPressed: () async {
            final uri = Uri.parse(updateInfo.storeUrl);
            if (await canLaunchUrl(uri)) {
              await launchUrl(uri);
            }
            Navigator.of(context).pop();
          },
          child: AquaText.body('Update Now'),
        ),
      ],
    );
  }
}
