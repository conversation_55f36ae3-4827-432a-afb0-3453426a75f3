// GENERATED CODE - DO NOT MODIFY BY HAND
// This code was generated by ObjectBox. To update it run the generator again
// with `dart run build_runner build`.
// See also https://docs.objectbox.io/getting-started#generate-objectbox-code

// ignore_for_file: camel_case_types, depend_on_referenced_packages
// coverage:ignore-file

import 'dart:typed_data';

import 'package:flat_buffers/flat_buffers.dart' as fb;
import 'package:objectbox/internal.dart'
    as obx_int; // generated code can access "internal" functionality
import 'package:objectbox/objectbox.dart' as obx;
import 'package:objectbox_flutter_libs/objectbox_flutter_libs.dart';

import 'data/entities/farmer_visits/farmer_entity.dart';
import 'data/entities/farmer_visits/visit_entity.dart';
import 'data/models/account_statement/account_statement_models.dart';
import 'data/models/credit_notes/credit_note_model.dart';
import 'data/models/customer_scheme_model.dart';
import 'data/models/dashboard/dashboard_model.dart';
import 'data/models/dues/dues_model.dart';
import 'data/models/invoices/objectbox_invoice_item_model.dart';
import 'data/models/invoices/objectbox_invoice_model.dart';
import 'data/models/objectbox_customer_model.dart';
import 'data/models/objectbox_interested_product_model.dart';
import 'data/models/objectbox_price_list_model.dart';
import 'data/models/objectbox_product_catalogue_model.dart';
import 'data/models/objectbox_smr_report_model.dart';
import 'data/models/payments/customer_payment_model.dart';
import 'data/models/person_details_model.dart';
import 'data/models/sales_order/sales_order_item_model.dart';
import 'data/models/sales_order/sales_order_model.dart';
import 'data/models/support_person_model.dart';
import 'data/models/user_model.dart';

export 'package:objectbox/objectbox.dart'; // so that callers only have to import this file

final _entities = <obx_int.ModelEntity>[
  obx_int.ModelEntity(
    id: const obx_int.IdUid(1, 8132863513438965635),
    name: 'UserModel',
    lastPropertyId: const obx_int.IdUid(9, 4938733412201330713),
    flags: 0,
    properties: <obx_int.ModelProperty>[
      obx_int.ModelProperty(
        id: const obx_int.IdUid(2, 7813727624144775754),
        name: 'id',
        type: 6,
        flags: 1,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(4, 526574474414702220),
        name: 'phoneNumber',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(5, 7980170251532068486),
        name: 'isVerified',
        type: 1,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(6, 7980500831310509350),
        name: 'createdAt',
        type: 10,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(7, 5208234223804611006),
        name: 'updatedAt',
        type: 10,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(8, 2118680721624706564),
        name: 'mongoId',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(9, 4938733412201330713),
        name: 'needsSync',
        type: 1,
        flags: 0,
      ),
    ],
    relations: <obx_int.ModelRelation>[],
    backlinks: <obx_int.ModelBacklink>[],
  ),
  obx_int.ModelEntity(
    id: const obx_int.IdUid(10, 6497017795649644319),
    name: 'ObjectBoxPriceListModel',
    lastPropertyId: const obx_int.IdUid(5, 8104177192492005546),
    flags: 0,
    properties: <obx_int.ModelProperty>[
      obx_int.ModelProperty(
        id: const obx_int.IdUid(1, 8556425286383023064),
        name: 'id',
        type: 6,
        flags: 1,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(3, 2626206172310092672),
        name: 'state',
        type: 9,
        flags: 2048,
        indexId: const obx_int.IdUid(6, 3742090795844408593),
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(4, 3754461554430980708),
        name: 'date',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(5, 8104177192492005546),
        name: 'pricesJson',
        type: 9,
        flags: 0,
      ),
    ],
    relations: <obx_int.ModelRelation>[],
    backlinks: <obx_int.ModelBacklink>[],
  ),
  obx_int.ModelEntity(
    id: const obx_int.IdUid(11, 4053864638235085459),
    name: 'ObjectBoxProductCatalogueModel',
    lastPropertyId: const obx_int.IdUid(6, 4553388345887935296),
    flags: 0,
    properties: <obx_int.ModelProperty>[
      obx_int.ModelProperty(
        id: const obx_int.IdUid(1, 8737843534052490798),
        name: 'id',
        type: 6,
        flags: 1,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(2, 653327529234038547),
        name: 'name',
        type: 9,
        flags: 2048,
        indexId: const obx_int.IdUid(7, 8040199475711434506),
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(3, 4228581716791851764),
        name: 'image',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(4, 8128377353822469712),
        name: 'sortOrder',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(5, 1809192526727901814),
        name: 'status',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(6, 4553388345887935296),
        name: 'productsJson',
        type: 9,
        flags: 0,
      ),
    ],
    relations: <obx_int.ModelRelation>[],
    backlinks: <obx_int.ModelBacklink>[],
  ),
  obx_int.ModelEntity(
    id: const obx_int.IdUid(12, 7833949612224344513),
    name: 'ObjectBoxInterestedProductModel',
    lastPropertyId: const obx_int.IdUid(10, 3887770387528273979),
    flags: 0,
    properties: <obx_int.ModelProperty>[
      obx_int.ModelProperty(
        id: const obx_int.IdUid(1, 8513985077141153372),
        name: 'id',
        type: 6,
        flags: 1,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(2, 43613475106035517),
        name: 'mongoId',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(3, 1034804957431170406),
        name: 'customerId',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(5, 1971598139448684571),
        name: 'mobile',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(7, 7307437366485070377),
        name: 'productName',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(8, 4856697159961573277),
        name: 'datetimeString',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(9, 3987825882492718805),
        name: 'source',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(10, 3887770387528273979),
        name: 'isSynced',
        type: 1,
        flags: 0,
      ),
    ],
    relations: <obx_int.ModelRelation>[],
    backlinks: <obx_int.ModelBacklink>[],
  ),
  obx_int.ModelEntity(
    id: const obx_int.IdUid(13, 8468007029682329754),
    name: 'DashboardModel',
    lastPropertyId: const obx_int.IdUid(11, 5622652746103328370),
    flags: 0,
    properties: <obx_int.ModelProperty>[
      obx_int.ModelProperty(
        id: const obx_int.IdUid(1, 9022496726046368994),
        name: 'id',
        type: 6,
        flags: 1,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(2, 6929165119548024968),
        name: 'customerId',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(3, 4997330481003813196),
        name: 'salesJson',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(4, 4543177410590619780),
        name: 'paymentsJson',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(5, 5530082251785703314),
        name: 'duesJson',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(6, 582387755001714748),
        name: 'categoryTypeSalesJson',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(7, 9172942400491217766),
        name: 'liquidationJson',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(8, 2418990437687080394),
        name: 'myFarmersJson',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(9, 254608919767970805),
        name: 'isSynced',
        type: 1,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(10, 2272719825210090880),
        name: 'lastSyncedAt',
        type: 10,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(11, 5622652746103328370),
        name: 'salesReturnJson',
        type: 8,
        flags: 0,
      ),
    ],
    relations: <obx_int.ModelRelation>[],
    backlinks: <obx_int.ModelBacklink>[],
  ),
  obx_int.ModelEntity(
    id: const obx_int.IdUid(15, 2622465375205671178),
    name: 'ObjectBoxCustomerModel',
    lastPropertyId: const obx_int.IdUid(12, 3859852864115044945),
    flags: 0,
    properties: <obx_int.ModelProperty>[
      obx_int.ModelProperty(
        id: const obx_int.IdUid(1, 6362589730972117812),
        name: 'id',
        type: 6,
        flags: 1,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(2, 1859011929673787960),
        name: 'customerId',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(3, 7858176125402207777),
        name: 'customerName',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(4, 5460500779320762236),
        name: 'email',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(5, 6454775288910420704),
        name: 'mobileNumber',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(6, 2334873330753522952),
        name: 'companyName',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(7, 3636794851379987538),
        name: 'gstNo',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(8, 6912575503435310675),
        name: 'businessVertical',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(9, 7295265513261415069),
        name: 'customerCode',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(10, 5917572739379987366),
        name: 'billingAddress',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(11, 4629293298796669117),
        name: 'isSynced',
        type: 1,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(12, 3859852864115044945),
        name: 'mongoId',
        type: 9,
        flags: 0,
      ),
    ],
    relations: <obx_int.ModelRelation>[],
    backlinks: <obx_int.ModelBacklink>[],
  ),
  obx_int.ModelEntity(
    id: const obx_int.IdUid(16, 4882109654508943798),
    name: 'CustomerSchemeModel',
    lastPropertyId: const obx_int.IdUid(5, 6103668384829530570),
    flags: 0,
    properties: <obx_int.ModelProperty>[
      obx_int.ModelProperty(
        id: const obx_int.IdUid(1, 7260573194139425711),
        name: 'id',
        type: 6,
        flags: 1,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(2, 6709881415313945185),
        name: 'customerId',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(3, 7583906144425343118),
        name: 'customerName',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(4, 3236732186720400177),
        name: 'sales',
        type: 8,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(5, 6103668384829530570),
        name: 'payment',
        type: 8,
        flags: 0,
      ),
    ],
    relations: <obx_int.ModelRelation>[],
    backlinks: <obx_int.ModelBacklink>[
      obx_int.ModelBacklink(
        name: 'supportPersons',
        srcEntity: 'SupportPersonModel',
        srcField: 'customerScheme',
      ),
    ],
  ),
  obx_int.ModelEntity(
    id: const obx_int.IdUid(17, 3701370663433457316),
    name: 'PersonDetailsModel',
    lastPropertyId: const obx_int.IdUid(11, 4153835492055554100),
    flags: 0,
    properties: <obx_int.ModelProperty>[
      obx_int.ModelProperty(
        id: const obx_int.IdUid(1, 6147390920666881630),
        name: 'id',
        type: 6,
        flags: 1,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(2, 792125977741017443),
        name: 'rowId',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(3, 256517745059490305),
        name: 'customerName',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(4, 3187848432950131906),
        name: 'email',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(5, 3948870499311951649),
        name: 'status',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(6, 865920990051817821),
        name: 'mobileNumber',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(7, 2823896851968162335),
        name: 'userId',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(8, 9188961002572575732),
        name: 'modifiedTime',
        type: 10,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(9, 8684926135621129447),
        name: 'empId',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(10, 3125156980840738563),
        name: 'profile',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(11, 4153835492055554100),
        name: 'supportPersonId',
        type: 11,
        flags: 520,
        indexId: const obx_int.IdUid(8, 4638698375262512895),
        relationTarget: 'SupportPersonModel',
      ),
    ],
    relations: <obx_int.ModelRelation>[],
    backlinks: <obx_int.ModelBacklink>[],
  ),
  obx_int.ModelEntity(
    id: const obx_int.IdUid(18, 1051626658990781257),
    name: 'SupportPersonModel',
    lastPropertyId: const obx_int.IdUid(3, 632205812450862498),
    flags: 0,
    properties: <obx_int.ModelProperty>[
      obx_int.ModelProperty(
        id: const obx_int.IdUid(1, 783141879205471926),
        name: 'id',
        type: 6,
        flags: 1,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(2, 4332631631160806328),
        name: 'profile',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(3, 632205812450862498),
        name: 'customerSchemeId',
        type: 11,
        flags: 520,
        indexId: const obx_int.IdUid(9, 8762833972066506423),
        relationTarget: 'CustomerSchemeModel',
      ),
    ],
    relations: <obx_int.ModelRelation>[
      obx_int.ModelRelation(
        id: const obx_int.IdUid(2, *****************),
        name: 'details',
        targetId: const obx_int.IdUid(17, 3701370663433457316),
      ),
    ],
    backlinks: <obx_int.ModelBacklink>[],
  ),
  obx_int.ModelEntity(
    id: const obx_int.IdUid(19, 2444024015785672861),
    name: 'AccountStatementEntryModel',
    lastPropertyId: const obx_int.IdUid(10, 6098133699297006561),
    flags: 0,
    properties: <obx_int.ModelProperty>[
      obx_int.ModelProperty(
        id: const obx_int.IdUid(1, 2928430498290875247),
        name: 'id',
        type: 6,
        flags: 1,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(2, 950680320747994017),
        name: 'txnDate',
        type: 10,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(3, 341790021140250483),
        name: 'vchType',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(4, 2466611929087199845),
        name: 'invoiceNumber',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(5, 8469104074325282159),
        name: 'particulars',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(6, *******************),
        name: 'debit',
        type: 8,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(7, 588032369962280548),
        name: 'credit',
        type: 8,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(8, 7677653408465930989),
        name: 'balance',
        type: 8,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(9, 3696303761255138272),
        name: 'amount',
        type: 8,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(10, 6098133699297006561),
        name: 'accountStatementId',
        type: 11,
        flags: 520,
        indexId: const obx_int.IdUid(10, 2502110069488155991),
        relationTarget: 'AccountStatementModel',
      ),
    ],
    relations: <obx_int.ModelRelation>[],
    backlinks: <obx_int.ModelBacklink>[],
  ),
  obx_int.ModelEntity(
    id: const obx_int.IdUid(20, 5979677694765782216),
    name: 'AccountStatementModel',
    lastPropertyId: const obx_int.IdUid(3, 1855142523239838595),
    flags: 0,
    properties: <obx_int.ModelProperty>[
      obx_int.ModelProperty(
        id: const obx_int.IdUid(1, 6396700639685618170),
        name: 'id',
        type: 6,
        flags: 1,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(2, 7122518752470478765),
        name: 'customerId',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(3, 1855142523239838595),
        name: 'lastSyncTime',
        type: 10,
        flags: 0,
      ),
    ],
    relations: <obx_int.ModelRelation>[],
    backlinks: <obx_int.ModelBacklink>[
      obx_int.ModelBacklink(
        name: 'entries',
        srcEntity: 'AccountStatementEntryModel',
        srcField: 'accountStatement',
      ),
    ],
  ),
  obx_int.ModelEntity(
    id: const obx_int.IdUid(25, 1687087133803093204),
    name: 'ObjectBoxSMRReportModel',
    lastPropertyId: const obx_int.IdUid(15, 4797458071422253968),
    flags: 0,
    properties: <obx_int.ModelProperty>[
      obx_int.ModelProperty(
        id: const obx_int.IdUid(1, 6397847808783189873),
        name: 'dbId',
        type: 6,
        flags: 1,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(2, 1450841342048403087),
        name: 'id',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(3, 5375964479294417724),
        name: 'so',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(4, 711710645736501298),
        name: 'partner',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(5, 5754368479896380668),
        name: 'partnerId',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(6, 6035029412205846250),
        name: 'productName',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(7, 830511392432985601),
        name: 'startDateMillis',
        type: 6,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(8, 3256267686538221989),
        name: 'lastDateMillis',
        type: 6,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(9, 3334466111894879092),
        name: 'openingBalance',
        type: 6,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(10, 3113039439677848166),
        name: 'invoice',
        type: 6,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(11, 3179800971532456492),
        name: 'srn',
        type: 6,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(12, 4727647823855550433),
        name: 'closingBalance',
        type: 6,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(13, 3968256279929481908),
        name: 'sales',
        type: 6,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(15, 4797458071422253968),
        name: 'status',
        type: 9,
        flags: 0,
      ),
    ],
    relations: <obx_int.ModelRelation>[],
    backlinks: <obx_int.ModelBacklink>[],
  ),
  obx_int.ModelEntity(
    id: const obx_int.IdUid(26, 5416699127255561662),
    name: 'SalesOrderItemModel',
    lastPropertyId: const obx_int.IdUid(21, 6237716295348987742),
    flags: 0,
    properties: <obx_int.ModelProperty>[
      obx_int.ModelProperty(
        id: const obx_int.IdUid(1, 7167440356484885687),
        name: 'id',
        type: 6,
        flags: 1,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(2, 7737593003972711591),
        name: 'itemId',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(3, 3100404337787651179),
        name: 'salesOrderId',
        type: 11,
        flags: 520,
        indexId: const obx_int.IdUid(21, 2009609364210507854),
        relationTarget: 'SalesOrderModel',
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(4, 2456863774633971004),
        name: 'productId',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(5, 3946714783675453608),
        name: 'createdTime',
        type: 10,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(6, 3423228074150619622),
        name: 'entityDiscountPercent',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(7, 784117138974314833),
        name: 'hsnSac',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(8, 3393527901717541499),
        name: 'invoicedQuantityCancelled',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(9, 2609076071390153045),
        name: 'itemName',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(10, 7765421063351668472),
        name: 'itemPrice',
        type: 8,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(11, 2331774167357820782),
        name: 'lastModifiedTime',
        type: 10,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(12, 830206783349988229),
        name: 'manuallyFulfilledQuantity',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(13, 180022786844220169),
        name: 'nonPackageQuantity',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(14, 5696159774009477777),
        name: 'placeOfSupply',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(15, 5099135192701815505),
        name: 'quantityDelivered',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(16, 7158714979499589809),
        name: 'quantityDropshipped',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(17, 9010704902478492513),
        name: 'quantityPacked',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(18, 7780765051678068175),
        name: 'salesVertices',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(19, 709681365198656245),
        name: 'sno',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(20, 941887312270880434),
        name: 'total',
        type: 8,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(21, 6237716295348987742),
        name: 'salesOrderIdValue',
        type: 9,
        flags: 0,
      ),
    ],
    relations: <obx_int.ModelRelation>[],
    backlinks: <obx_int.ModelBacklink>[],
  ),
  obx_int.ModelEntity(
    id: const obx_int.IdUid(27, 814500980066085354),
    name: 'SalesOrderModel',
    lastPropertyId: const obx_int.IdUid(17, 3623601299003227251),
    flags: 0,
    properties: <obx_int.ModelProperty>[
      obx_int.ModelProperty(
        id: const obx_int.IdUid(1, 7405754324650357678),
        name: 'id',
        type: 6,
        flags: 1,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(2, 5343516595216219020),
        name: 'salesOrderId',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(3, 1363887062538266965),
        name: 'addressId',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(4, 7807200534879677880),
        name: 'createdTime',
        type: 10,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(5, 2019887442922116963),
        name: 'customerId',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(6, 3583856748476134431),
        name: 'invoicedStatus',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(7, 3565157346822835000),
        name: 'lastModifiedTime',
        type: 10,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(8, 3921361928928515776),
        name: 'orderSource',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(9, 2875833075594970722),
        name: 'paidStatus',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(10, 4040722605130953239),
        name: 'paymentTermsLabel',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(11, 458825167804020246),
        name: 'saleOrderDate',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(12, 3981989085695837120),
        name: 'salesChannel',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(13, 3897852389454277407),
        name: 'salesOrderNumber',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(14, 819497710774748801),
        name: 'subTotal',
        type: 8,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(15, 7072201275031871278),
        name: 'total',
        type: 8,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(16, 7158113136285340973),
        name: 'isSynced',
        type: 1,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(17, 3623601299003227251),
        name: 'lastSyncedAt',
        type: 10,
        flags: 0,
      ),
    ],
    relations: <obx_int.ModelRelation>[],
    backlinks: <obx_int.ModelBacklink>[
      obx_int.ModelBacklink(
        name: 'items',
        srcEntity: 'SalesOrderItemModel',
        srcField: 'salesOrder',
      ),
    ],
  ),
  obx_int.ModelEntity(
    id: const obx_int.IdUid(28, 7619398349975584887),
    name: 'DuesInvoiceModel',
    lastPropertyId: const obx_int.IdUid(51, 441455897292697180),
    flags: 0,
    properties: <obx_int.ModelProperty>[
      obx_int.ModelProperty(
        id: const obx_int.IdUid(1, 7221392760934666100),
        name: 'dbId',
        type: 6,
        flags: 1,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(2, 6023872549552880213),
        name: 'id',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(3, 3483892206142404005),
        name: 'invoiceId',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(4, 7063254952992476396),
        name: 'invoiceNumber',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(5, 2190826640675120663),
        name: 'invoiceDate',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(6, 5697410686505965898),
        name: 'customerId',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(7, 7456241346203121943),
        name: 'customerCode',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(8, 398327572644078650),
        name: 'customerName',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(9, 131798308043163566),
        name: 'customerDistrict',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(10, 1772126115172089239),
        name: 'customerState',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(11, 8904552996483299871),
        name: 'customerType',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(12, 1840444700691947883),
        name: 'onBoardedTime',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(13, 4260176181610541630),
        name: 'categoryType',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(14, 9081603995030863668),
        name: 'invoiceType',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(15, 7498461819220089835),
        name: 'retailType',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(16, 7958611760564938994),
        name: 'invoiceStatus',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(17, 7167825524676708325),
        name: 'invoiceRaisedBy',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(18, 3498666996772316161),
        name: 'mode',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(19, 7800248198626572704),
        name: 'businessVertical',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(20, 6204560503607037547),
        name: 'feedCreditLimit',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(21, 3389197110740654563),
        name: 'nonFeedCreditLimit',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(22, 7033647679331004774),
        name: 'harvestCreditLimit',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(23, 52570706318842876),
        name: 'totalSalesInclTax',
        type: 8,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(24, 3003028962167604791),
        name: 'totalSalesExclTax',
        type: 8,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(25, 1890601964275028998),
        name: 'tcsAmount',
        type: 8,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(26, *******************),
        name: 'tdsAmount',
        type: 8,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(27, 2501809142453363128),
        name: 'amountAfterTcs',
        type: 8,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(28, 7774996494545512508),
        name: 'shippingCharges',
        type: 8,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(29, 2753145153817392635),
        name: 'feedAmountAfterTcs',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(30, 158733123297542433),
        name: 'nonFeedAmountAfterTcs',
        type: 8,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(31, 2619648730580184171),
        name: 'creditNoteAmountWithTcs',
        type: 8,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(32, 8661217912258206786),
        name: 'payableAmount',
        type: 8,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(33, 593345335258065642),
        name: 'paidAmount',
        type: 8,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(34, *******************),
        name: 'due',
        type: 8,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(35, 5529948356816085672),
        name: 'dueDate',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(36, 8980716763372168926),
        name: 'dueDays',
        type: 6,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(37, 3970908177143511512),
        name: 'aging',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(38, 4038581502368127809),
        name: 'aging1',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(39, 3282417326346831659),
        name: 'paymentCredibility',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(40, 5387381558480726994),
        name: 'lastSyncTimestamp',
        type: 6,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(41, 4526255794759833293),
        name: 'agingGroup',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(42, 6915059801132089276),
        name: 'totalPurchase',
        type: 8,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(43, 8273125161154969619),
        name: 'totalSales',
        type: 8,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(44, 2773420444728003732),
        name: 'salesTier',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(45, 7889574794486911301),
        name: 'healthcareSales',
        type: 8,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(46, 6808074449000085639),
        name: 'feedSales',
        type: 8,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(47, 1683772898227190673),
        name: 'chemicalSales',
        type: 8,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(48, 7580631555469817698),
        name: 'equipmentSales',
        type: 8,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(49, 6895125440864941958),
        name: 'harvestSales',
        type: 8,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(50, 9091561991743632346),
        name: 'grossMargin',
        type: 8,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(51, 441455897292697180),
        name: 'lastPaidDate',
        type: 9,
        flags: 0,
      ),
    ],
    relations: <obx_int.ModelRelation>[],
    backlinks: <obx_int.ModelBacklink>[],
  ),
  obx_int.ModelEntity(
    id: const obx_int.IdUid(29, 127483350007423367),
    name: 'DuesSummaryModel',
    lastPropertyId: const obx_int.IdUid(7, 4512270287764814387),
    flags: 0,
    properties: <obx_int.ModelProperty>[
      obx_int.ModelProperty(
        id: const obx_int.IdUid(1, 345235054106148237),
        name: 'dbId',
        type: 6,
        flags: 1,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(2, 2991516920252848603),
        name: 'customerId',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(6, 1412528597938276577),
        name: 'lastSyncTimestamp',
        type: 6,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(7, 4512270287764814387),
        name: 'totalDue',
        type: 8,
        flags: 0,
      ),
    ],
    relations: <obx_int.ModelRelation>[],
    backlinks: <obx_int.ModelBacklink>[],
  ),
  obx_int.ModelEntity(
    id: const obx_int.IdUid(30, 9091547537228068617),
    name: 'DuesAgingGroupModel',
    lastPropertyId: const obx_int.IdUid(6, 8582727884228263061),
    flags: 0,
    properties: <obx_int.ModelProperty>[
      obx_int.ModelProperty(
        id: const obx_int.IdUid(1, 1715781981689014823),
        name: 'dbId',
        type: 6,
        flags: 1,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(2, 65026892410913163),
        name: 'customerId',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(3, 4824358007860747929),
        name: 'totalPayableAmount',
        type: 8,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(4, 5702601372873634046),
        name: 'aging',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(5, 4646503659370937824),
        name: 'dueDays',
        type: 6,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(6, 8582727884228263061),
        name: 'lastSyncTimestamp',
        type: 6,
        flags: 0,
      ),
    ],
    relations: <obx_int.ModelRelation>[],
    backlinks: <obx_int.ModelBacklink>[],
  ),
  obx_int.ModelEntity(
    id: const obx_int.IdUid(31, 7816786059719311089),
    name: 'ObjectBoxInvoiceItemModel',
    lastPropertyId: const obx_int.IdUid(17, 6747616249130049067),
    flags: 0,
    properties: <obx_int.ModelProperty>[
      obx_int.ModelProperty(
        id: const obx_int.IdUid(1, 3688884873476147441),
        name: 'id',
        type: 6,
        flags: 1,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(2, 3872318638764654221),
        name: 'itemId',
        type: 9,
        flags: 2080,
        indexId: const obx_int.IdUid(22, 3912524812525392017),
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(3, 4714000339497844760),
        name: 'productId',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(4, 942107222364866681),
        name: 'itemName',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(5, 7910087827951546296),
        name: 'quantity',
        type: 8,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(6, 5865147781021639805),
        name: 'invoiceId',
        type: 11,
        flags: 520,
        indexId: const obx_int.IdUid(23, 915386503993134094),
        relationTarget: 'ObjectBoxInvoiceModel',
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(7, 1947070160035015647),
        name: 'createdTime',
        type: 12,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(8, 516513924816206289),
        name: 'discountAmount',
        type: 8,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(9, 3385496905153057694),
        name: 'hsnSac',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(10, 8671827139221004908),
        name: 'itemPrice',
        type: 8,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(11, 1058950144964030219),
        name: 'lastModifiedTime',
        type: 12,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(12, 2934306333960414288),
        name: 'placeOfSupply',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(13, 1855909174778511250),
        name: 'productCategory',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(14, 3145052887252235700),
        name: 'source',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(15, 6098840131587579956),
        name: 'subTotal',
        type: 8,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(16, 8626289698790986047),
        name: 'total',
        type: 8,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(17, 6747616249130049067),
        name: 'invoiceIdValue',
        type: 9,
        flags: 0,
      ),
    ],
    relations: <obx_int.ModelRelation>[],
    backlinks: <obx_int.ModelBacklink>[],
  ),
  obx_int.ModelEntity(
    id: const obx_int.IdUid(32, 2984471775005976020),
    name: 'ObjectBoxInvoiceModel',
    lastPropertyId: const obx_int.IdUid(15, 4672069971120187876),
    flags: 0,
    properties: <obx_int.ModelProperty>[
      obx_int.ModelProperty(
        id: const obx_int.IdUid(1, 2969209294938822207),
        name: 'id',
        type: 6,
        flags: 1,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(2, 742984557286471550),
        name: 'invoiceId',
        type: 9,
        flags: 2080,
        indexId: const obx_int.IdUid(24, 1961136530793205594),
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(3, 3745630360584062594),
        name: 'addressId',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(4, 1519386098629883908),
        name: 'ageInDays',
        type: 6,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(5, 8370055229180809377),
        name: 'ageTier',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(6, 1388802623974722053),
        name: 'balance',
        type: 8,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(7, 2651638587235825050),
        name: 'customerId',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(8, 2376053456584480940),
        name: 'deliveryMode',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(9, 1884854625849539497),
        name: 'deliveryStatus',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(10, 1518941922949514244),
        name: 'dueDate',
        type: 12,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(11, 5754963795496695956),
        name: 'invoiceDate',
        type: 12,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(12, 1767104733507217961),
        name: 'invoiceNumber',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(13, 7731550889137730817),
        name: 'invoiceStatus',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(14, 7438185837840136530),
        name: 'subTotal',
        type: 8,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(15, 4672069971120187876),
        name: 'total',
        type: 8,
        flags: 0,
      ),
    ],
    relations: <obx_int.ModelRelation>[],
    backlinks: <obx_int.ModelBacklink>[
      obx_int.ModelBacklink(
        name: 'items',
        srcEntity: 'ObjectBoxInvoiceItemModel',
        srcField: 'invoice',
      ),
    ],
  ),
  obx_int.ModelEntity(
    id: const obx_int.IdUid(33, 679105967871262757),
    name: 'CustomerPaymentModel',
    lastPropertyId: const obx_int.IdUid(10, 578327840526067982),
    flags: 0,
    properties: <obx_int.ModelProperty>[
      obx_int.ModelProperty(
        id: const obx_int.IdUid(1, 1792888609935635447),
        name: 'dbId',
        type: 6,
        flags: 1,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(2, 97659671459228479),
        name: 'id',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(3, 8601900336231996056),
        name: 'customerId',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(4, 6123934333867825743),
        name: 'customerName',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(5, 9209896867895205658),
        name: 'categoryType',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(6, 5459970059774669810),
        name: 'amount',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(7, 8824680098220050848),
        name: 'paymentsDate',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(8, 5603741182695248744),
        name: 'paymentId',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(9, 5889486873869265141),
        name: 'paymentsMode',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(10, 578327840526067982),
        name: 'paymentsNumber',
        type: 9,
        flags: 0,
      ),
    ],
    relations: <obx_int.ModelRelation>[],
    backlinks: <obx_int.ModelBacklink>[],
  ),
  obx_int.ModelEntity(
    id: const obx_int.IdUid(34, 6820031882220582708),
    name: 'CreditNoteModel',
    lastPropertyId: const obx_int.IdUid(17, 8360644782941912249),
    flags: 0,
    properties: <obx_int.ModelProperty>[
      obx_int.ModelProperty(
        id: const obx_int.IdUid(1, 8344894409991460058),
        name: 'dbId',
        type: 6,
        flags: 1,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(2, 8540249337993205732),
        name: 'customerId',
        type: 9,
        flags: 2048,
        indexId: const obx_int.IdUid(25, 952934008462249335),
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(3, 5397776548367442609),
        name: 'id',
        type: 9,
        flags: 34848,
        indexId: const obx_int.IdUid(26, 2407415480963475852),
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(4, 2535383220083015425),
        name: 'customerName',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(5, 3737282665629663668),
        name: 'date',
        type: 10,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(6, *******************),
        name: 'creditNoteId',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(7, 2663345182548744627),
        name: 'creditNoteNumber',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(8, 1516585349460284434),
        name: 'invoiceId',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(9, 665538327453405788),
        name: 'productId',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(10, 1836079966870208808),
        name: 'itemName',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(11, 6813492393889107428),
        name: 'brand',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(12, 1820580770661053171),
        name: 'itemCategory',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(13, *******************),
        name: 'categoryType',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(14, 1903214856020461235),
        name: 'quantity',
        type: 6,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(15, 7078132522428659070),
        name: 'amount',
        type: 8,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(16, 434722390188129769),
        name: 'tag',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(17, 8360644782941912249),
        name: 'category',
        type: 9,
        flags: 0,
      ),
    ],
    relations: <obx_int.ModelRelation>[],
    backlinks: <obx_int.ModelBacklink>[],
  ),
  obx_int.ModelEntity(
    id: const obx_int.IdUid(35, 8133874219143661632),
    name: 'FarmerEntity',
    lastPropertyId: const obx_int.IdUid(3, 1658834792794860199),
    flags: 0,
    properties: <obx_int.ModelProperty>[
      obx_int.ModelProperty(
        id: const obx_int.IdUid(1, 5883270228869891054),
        name: 'id',
        type: 6,
        flags: 1,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(2, 4403526973364719730),
        name: 'name',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(3, 1658834792794860199),
        name: 'mobileNumber',
        type: 9,
        flags: 0,
      ),
    ],
    relations: <obx_int.ModelRelation>[],
    backlinks: <obx_int.ModelBacklink>[
      obx_int.ModelBacklink(
        name: 'visits',
        srcEntity: 'VisitEntity',
        srcField: '',
      ),
    ],
  ),
  obx_int.ModelEntity(
    id: const obx_int.IdUid(36, 5990479454950517684),
    name: 'VisitEntity',
    lastPropertyId: const obx_int.IdUid(13, 5923613917358837074),
    flags: 0,
    properties: <obx_int.ModelProperty>[
      obx_int.ModelProperty(
        id: const obx_int.IdUid(1, 7747438256384921919),
        name: 'id',
        type: 6,
        flags: 1,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(2, 3672420115274906416),
        name: 'createdDateTimeMillis',
        type: 6,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(3, 3859352571271079345),
        name: 'doc',
        type: 6,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(7, 4150908857375190288),
        name: 'mobileNumber',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(9, 4592440719662545700),
        name: 'productUsed',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(10, 2095085204175045813),
        name: 'farmerId',
        type: 11,
        flags: 520,
        indexId: const obx_int.IdUid(29, 4251531164893688960),
        relationTarget: 'FarmerEntity',
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(12, 12617839105876289),
        name: 'pondId',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(13, 5923613917358837074),
        name: 'farmerName',
        type: 9,
        flags: 0,
      ),
    ],
    relations: <obx_int.ModelRelation>[],
    backlinks: <obx_int.ModelBacklink>[],
  ),
];

/// Shortcut for [obx.Store.new] that passes [getObjectBoxModel] and for Flutter
/// apps by default a [directory] using `defaultStoreDirectory()` from the
/// ObjectBox Flutter library.
///
/// Note: for desktop apps it is recommended to specify a unique [directory].
///
/// See [obx.Store.new] for an explanation of all parameters.
///
/// For Flutter apps, also calls `loadObjectBoxLibraryAndroidCompat()` from
/// the ObjectBox Flutter library to fix loading the native ObjectBox library
/// on Android 6 and older.
Future<obx.Store> openStore({
  String? directory,
  int? maxDBSizeInKB,
  int? maxDataSizeInKB,
  int? fileMode,
  int? maxReaders,
  bool queriesCaseSensitiveDefault = true,
  String? macosApplicationGroup,
}) async {
  await loadObjectBoxLibraryAndroidCompat();
  return obx.Store(
    getObjectBoxModel(),
    directory: directory ?? (await defaultStoreDirectory()).path,
    maxDBSizeInKB: maxDBSizeInKB,
    maxDataSizeInKB: maxDataSizeInKB,
    fileMode: fileMode,
    maxReaders: maxReaders,
    queriesCaseSensitiveDefault: queriesCaseSensitiveDefault,
    macosApplicationGroup: macosApplicationGroup,
  );
}

/// Returns the ObjectBox model definition for this project for use with
/// [obx.Store.new].
obx_int.ModelDefinition getObjectBoxModel() {
  final model = obx_int.ModelInfo(
    entities: _entities,
    lastEntityId: const obx_int.IdUid(36, 5990479454950517684),
    lastIndexId: const obx_int.IdUid(29, 4251531164893688960),
    lastRelationId: const obx_int.IdUid(3, 6048782637445196261),
    lastSequenceId: const obx_int.IdUid(0, 0),
    retiredEntityUids: const [
      2866476964716789107,
      6445089904477806503,
      8533437899419862511,
      6346573762641548921,
      3601745023699878972,
      7020672411963003329,
      1752825334234401646,
      1790285891352590687,
      5676693064730952399,
      1650133050956712868,
      4509021158960870247,
      4146949866952824582,
      6404753190517256118,
    ],
    retiredIndexUids: const [
      7255140425930971515,
      2119861996282406426,
      3267626651500785862,
      5011226780311576653,
      4462066830847960845,
      6873172300178442970,
      2194643639767647429,
      3607978129057609020,
      8936514519946942187,
      7310727773008741557,
      296498085273555962,
      1015949556075453124,
    ],
    retiredPropertyUids: const [
      8388459128206106591,
      5636055644662326932,
      1169265267166184952,
      2388711096337726496,
      8861020485375795895,
      270837165743595999,
      793938725259152277,
      1563202682503174701,
      8355976867704545661,
      5099237017975173754,
      7075728947111179954,
      4322425104449788961,
      3993589733776758073,
      4768657512218037365,
      1575436969333688208,
      2181172578181573078,
      2476321902614475038,
      4556729902342696833,
      7651943591486451077,
      1182891608037039387,
      2073137537291178056,
      3874737163156346881,
      4991812519640035851,
      5612035577542603083,
      8561785599753987957,
      250153355248888333,
      7062226278682579236,
      4054982596461724859,
      2524102093304326149,
      5241893158921378181,
      7657628598326822192,
      6594852177405164434,
      2501801424973031461,
      789569295604937614,
      2287258747934666075,
      6453373360768749285,
      ************3378749,
      1077112486451167144,
      1094907220582399423,
      6505817429074173834,
      6188791987711909113,
      6705324769637601471,
      5890853705337606327,
      8632366525323256232,
      4335408070175991523,
      5997134815169542041,
      4731012527649833497,
      2964778942020128477,
      8273983021459210857,
      3651051516081632186,
      3242525344324181140,
      1435072699679368339,
      7112377478872975540,
      9087922757283571929,
      1678345538501387635,
      7454758496131018270,
      5984597822987910239,
      8311814153899672034,
      5728157447144185807,
      8104532247944011169,
      7235274985749960035,
      5845163556572532293,
      4766619360327114985,
      384004621616346184,
      2229114844192788705,
      8841285723146974482,
      140482400439287038,
      2498132937658317465,
      985463713764876279,
      2710702048246889539,
      6381342920409254804,
      4872375087398157762,
      5378132087831818010,
      3069364533194312324,
      2279962904945262555,
      2725771844612150584,
      7273623594572657705,
      2626673310542235113,
      1279787718924848880,
      484305213093869750,
      3356703007213921275,
      4270002305945466487,
      3136594338322364707,
      5755093868979904646,
      8060799698929120816,
      8571654957485439677,
      5158255751037730820,
      5312007745704503561,
      3812801803937502657,
      2643142419514223232,
      1876344152710584444,
      3864524601008105124,
      7782115253920410161,
      2462010731464983063,
      6677433326524801815,
      1778985345252331494,
      4988338041594029627,
      6399023576511470846,
      8209878501355854604,
      3342733780568396212,
      6213357013771936579,
      2569898773272001570,
      3266407006511653655,
      500451559102827360,
      2504184041899239332,
      3078158018787913943,
      6772024230737910181,
      2704467930772856433,
      302985683283538112,
    ],
    retiredRelationUids: const [2937710677954111317, 6048782637445196261],
    modelVersion: 5,
    modelVersionParserMinimum: 5,
    version: 1,
  );

  final bindings = <Type, obx_int.EntityDefinition>{
    UserModel: obx_int.EntityDefinition<UserModel>(
      model: _entities[0],
      toOneRelations: (UserModel object) => [],
      toManyRelations: (UserModel object) => {},
      getId: (UserModel object) => object.id,
      setId: (UserModel object, int id) {
        object.id = id;
      },
      objectToFB: (UserModel object, fb.Builder fbb) {
        final phoneNumberOffset = fbb.writeString(object.phoneNumber);
        final mongoIdOffset = object.mongoId == null
            ? null
            : fbb.writeString(object.mongoId!);
        fbb.startTable(10);
        fbb.addInt64(1, object.id);
        fbb.addOffset(3, phoneNumberOffset);
        fbb.addBool(4, object.isVerified);
        fbb.addInt64(5, object.createdAt.millisecondsSinceEpoch);
        fbb.addInt64(6, object.updatedAt.millisecondsSinceEpoch);
        fbb.addOffset(7, mongoIdOffset);
        fbb.addBool(8, object.needsSync);
        fbb.finish(fbb.endTable());
        return object.id;
      },
      objectFromFB: (obx.Store store, ByteData fbData) {
        final buffer = fb.BufferContext(fbData);
        final rootOffset = buffer.derefObject(0);
        final idParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          6,
          0,
        );
        final phoneNumberParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 10, '');
        final isVerifiedParam = const fb.BoolReader().vTableGet(
          buffer,
          rootOffset,
          12,
          false,
        );
        final mongoIdParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGetNullable(buffer, rootOffset, 18);
        final needsSyncParam = const fb.BoolReader().vTableGet(
          buffer,
          rootOffset,
          20,
          false,
        );
        final createdAtParam = DateTime.fromMillisecondsSinceEpoch(
          const fb.Int64Reader().vTableGet(buffer, rootOffset, 14, 0),
        );
        final updatedAtParam = DateTime.fromMillisecondsSinceEpoch(
          const fb.Int64Reader().vTableGet(buffer, rootOffset, 16, 0),
        );
        final object = UserModel(
          id: idParam,
          phoneNumber: phoneNumberParam,
          isVerified: isVerifiedParam,
          mongoId: mongoIdParam,
          needsSync: needsSyncParam,
          createdAt: createdAtParam,
          updatedAt: updatedAtParam,
        );

        return object;
      },
    ),
    ObjectBoxPriceListModel: obx_int.EntityDefinition<ObjectBoxPriceListModel>(
      model: _entities[1],
      toOneRelations: (ObjectBoxPriceListModel object) => [],
      toManyRelations: (ObjectBoxPriceListModel object) => {},
      getId: (ObjectBoxPriceListModel object) => object.id,
      setId: (ObjectBoxPriceListModel object, int id) {
        object.id = id;
      },
      objectToFB: (ObjectBoxPriceListModel object, fb.Builder fbb) {
        final stateOffset = fbb.writeString(object.state);
        final dateOffset = fbb.writeString(object.date);
        final pricesJsonOffset = fbb.writeString(object.pricesJson);
        fbb.startTable(6);
        fbb.addInt64(0, object.id);
        fbb.addOffset(2, stateOffset);
        fbb.addOffset(3, dateOffset);
        fbb.addOffset(4, pricesJsonOffset);
        fbb.finish(fbb.endTable());
        return object.id;
      },
      objectFromFB: (obx.Store store, ByteData fbData) {
        final buffer = fb.BufferContext(fbData);
        final rootOffset = buffer.derefObject(0);
        final idParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          4,
          0,
        );
        final stateParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 8, '');
        final dateParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 10, '');
        final pricesJsonParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 12, '');
        final object = ObjectBoxPriceListModel(
          id: idParam,
          state: stateParam,
          date: dateParam,
          pricesJson: pricesJsonParam,
        );

        return object;
      },
    ),
    ObjectBoxProductCatalogueModel:
        obx_int.EntityDefinition<ObjectBoxProductCatalogueModel>(
          model: _entities[2],
          toOneRelations: (ObjectBoxProductCatalogueModel object) => [],
          toManyRelations: (ObjectBoxProductCatalogueModel object) => {},
          getId: (ObjectBoxProductCatalogueModel object) => object.id,
          setId: (ObjectBoxProductCatalogueModel object, int id) {
            object.id = id;
          },
          objectToFB: (ObjectBoxProductCatalogueModel object, fb.Builder fbb) {
            final nameOffset = fbb.writeString(object.name);
            final imageOffset = fbb.writeString(object.image);
            final sortOrderOffset = fbb.writeString(object.sortOrder);
            final statusOffset = fbb.writeString(object.status);
            final productsJsonOffset = fbb.writeString(object.productsJson);
            fbb.startTable(7);
            fbb.addInt64(0, object.id);
            fbb.addOffset(1, nameOffset);
            fbb.addOffset(2, imageOffset);
            fbb.addOffset(3, sortOrderOffset);
            fbb.addOffset(4, statusOffset);
            fbb.addOffset(5, productsJsonOffset);
            fbb.finish(fbb.endTable());
            return object.id;
          },
          objectFromFB: (obx.Store store, ByteData fbData) {
            final buffer = fb.BufferContext(fbData);
            final rootOffset = buffer.derefObject(0);
            final idParam = const fb.Int64Reader().vTableGet(
              buffer,
              rootOffset,
              4,
              0,
            );
            final nameParam = const fb.StringReader(
              asciiOptimization: true,
            ).vTableGet(buffer, rootOffset, 6, '');
            final imageParam = const fb.StringReader(
              asciiOptimization: true,
            ).vTableGet(buffer, rootOffset, 8, '');
            final sortOrderParam = const fb.StringReader(
              asciiOptimization: true,
            ).vTableGet(buffer, rootOffset, 10, '');
            final statusParam = const fb.StringReader(
              asciiOptimization: true,
            ).vTableGet(buffer, rootOffset, 12, '');
            final productsJsonParam = const fb.StringReader(
              asciiOptimization: true,
            ).vTableGet(buffer, rootOffset, 14, '');
            final object = ObjectBoxProductCatalogueModel(
              id: idParam,
              name: nameParam,
              image: imageParam,
              sortOrder: sortOrderParam,
              status: statusParam,
              productsJson: productsJsonParam,
            );

            return object;
          },
        ),
    ObjectBoxInterestedProductModel:
        obx_int.EntityDefinition<ObjectBoxInterestedProductModel>(
          model: _entities[3],
          toOneRelations: (ObjectBoxInterestedProductModel object) => [],
          toManyRelations: (ObjectBoxInterestedProductModel object) => {},
          getId: (ObjectBoxInterestedProductModel object) => object.id,
          setId: (ObjectBoxInterestedProductModel object, int id) {
            object.id = id;
          },
          objectToFB: (ObjectBoxInterestedProductModel object, fb.Builder fbb) {
            final mongoIdOffset = object.mongoId == null
                ? null
                : fbb.writeString(object.mongoId!);
            final customerIdOffset = fbb.writeString(object.customerId);
            final mobileOffset = fbb.writeString(object.mobile);
            final productNameOffset = fbb.writeString(object.productName);
            final datetimeStringOffset = fbb.writeString(object.datetimeString);
            final sourceOffset = fbb.writeString(object.source);
            fbb.startTable(11);
            fbb.addInt64(0, object.id);
            fbb.addOffset(1, mongoIdOffset);
            fbb.addOffset(2, customerIdOffset);
            fbb.addOffset(4, mobileOffset);
            fbb.addOffset(6, productNameOffset);
            fbb.addOffset(7, datetimeStringOffset);
            fbb.addOffset(8, sourceOffset);
            fbb.addBool(9, object.isSynced);
            fbb.finish(fbb.endTable());
            return object.id;
          },
          objectFromFB: (obx.Store store, ByteData fbData) {
            final buffer = fb.BufferContext(fbData);
            final rootOffset = buffer.derefObject(0);
            final idParam = const fb.Int64Reader().vTableGet(
              buffer,
              rootOffset,
              4,
              0,
            );
            final mongoIdParam = const fb.StringReader(
              asciiOptimization: true,
            ).vTableGetNullable(buffer, rootOffset, 6);
            final customerIdParam = const fb.StringReader(
              asciiOptimization: true,
            ).vTableGet(buffer, rootOffset, 8, '');
            final mobileParam = const fb.StringReader(
              asciiOptimization: true,
            ).vTableGet(buffer, rootOffset, 12, '');
            final productNameParam = const fb.StringReader(
              asciiOptimization: true,
            ).vTableGet(buffer, rootOffset, 16, '');
            final datetimeStringParam = const fb.StringReader(
              asciiOptimization: true,
            ).vTableGet(buffer, rootOffset, 18, '');
            final sourceParam = const fb.StringReader(
              asciiOptimization: true,
            ).vTableGet(buffer, rootOffset, 20, '');
            final isSyncedParam = const fb.BoolReader().vTableGet(
              buffer,
              rootOffset,
              22,
              false,
            );
            final object = ObjectBoxInterestedProductModel(
              id: idParam,
              mongoId: mongoIdParam,
              customerId: customerIdParam,
              mobile: mobileParam,
              productName: productNameParam,
              datetimeString: datetimeStringParam,
              source: sourceParam,
              isSynced: isSyncedParam,
            );

            return object;
          },
        ),
    DashboardModel: obx_int.EntityDefinition<DashboardModel>(
      model: _entities[4],
      toOneRelations: (DashboardModel object) => [],
      toManyRelations: (DashboardModel object) => {},
      getId: (DashboardModel object) => object.id,
      setId: (DashboardModel object, int id) {
        object.id = id;
      },
      objectToFB: (DashboardModel object, fb.Builder fbb) {
        final customerIdOffset = fbb.writeString(object.customerId);
        final salesJsonOffset = fbb.writeString(object.salesJson);
        final paymentsJsonOffset = fbb.writeString(object.paymentsJson);
        final duesJsonOffset = fbb.writeString(object.duesJson);
        final categoryTypeSalesJsonOffset = fbb.writeString(
          object.categoryTypeSalesJson,
        );
        final liquidationJsonOffset = fbb.writeString(object.liquidationJson);
        final myFarmersJsonOffset = fbb.writeString(object.myFarmersJson);
        fbb.startTable(12);
        fbb.addInt64(0, object.id);
        fbb.addOffset(1, customerIdOffset);
        fbb.addOffset(2, salesJsonOffset);
        fbb.addOffset(3, paymentsJsonOffset);
        fbb.addOffset(4, duesJsonOffset);
        fbb.addOffset(5, categoryTypeSalesJsonOffset);
        fbb.addOffset(6, liquidationJsonOffset);
        fbb.addOffset(7, myFarmersJsonOffset);
        fbb.addBool(8, object.isSynced);
        fbb.addInt64(9, object.lastSyncedAt.millisecondsSinceEpoch);
        fbb.addFloat64(10, object.salesReturnJson);
        fbb.finish(fbb.endTable());
        return object.id;
      },
      objectFromFB: (obx.Store store, ByteData fbData) {
        final buffer = fb.BufferContext(fbData);
        final rootOffset = buffer.derefObject(0);
        final idParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          4,
          0,
        );
        final customerIdParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 6, '');
        final salesJsonParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 8, '');
        final paymentsJsonParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 10, '');
        final duesJsonParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 12, '');
        final salesReturnJsonParam = const fb.Float64Reader().vTableGet(
          buffer,
          rootOffset,
          24,
          0,
        );
        final categoryTypeSalesJsonParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 14, '');
        final liquidationJsonParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 16, '');
        final myFarmersJsonParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 18, '');
        final isSyncedParam = const fb.BoolReader().vTableGet(
          buffer,
          rootOffset,
          20,
          false,
        );
        final lastSyncedAtParam = DateTime.fromMillisecondsSinceEpoch(
          const fb.Int64Reader().vTableGet(buffer, rootOffset, 22, 0),
        );
        final object = DashboardModel(
          id: idParam,
          customerId: customerIdParam,
          salesJson: salesJsonParam,
          paymentsJson: paymentsJsonParam,
          duesJson: duesJsonParam,
          salesReturnJson: salesReturnJsonParam,
          categoryTypeSalesJson: categoryTypeSalesJsonParam,
          liquidationJson: liquidationJsonParam,
          myFarmersJson: myFarmersJsonParam,
          isSynced: isSyncedParam,
          lastSyncedAt: lastSyncedAtParam,
        );

        return object;
      },
    ),
    ObjectBoxCustomerModel: obx_int.EntityDefinition<ObjectBoxCustomerModel>(
      model: _entities[5],
      toOneRelations: (ObjectBoxCustomerModel object) => [],
      toManyRelations: (ObjectBoxCustomerModel object) => {},
      getId: (ObjectBoxCustomerModel object) => object.id,
      setId: (ObjectBoxCustomerModel object, int id) {
        object.id = id;
      },
      objectToFB: (ObjectBoxCustomerModel object, fb.Builder fbb) {
        final customerIdOffset = fbb.writeString(object.customerId);
        final customerNameOffset = fbb.writeString(object.customerName);
        final emailOffset = fbb.writeString(object.email);
        final mobileNumberOffset = fbb.writeString(object.mobileNumber);
        final companyNameOffset = fbb.writeString(object.companyName);
        final gstNoOffset = fbb.writeString(object.gstNo);
        final businessVerticalOffset = fbb.writeString(object.businessVertical);
        final customerCodeOffset = fbb.writeString(object.customerCode);
        final billingAddressOffset = fbb.writeString(object.billingAddress);
        final mongoIdOffset = object.mongoId == null
            ? null
            : fbb.writeString(object.mongoId!);
        fbb.startTable(13);
        fbb.addInt64(0, object.id);
        fbb.addOffset(1, customerIdOffset);
        fbb.addOffset(2, customerNameOffset);
        fbb.addOffset(3, emailOffset);
        fbb.addOffset(4, mobileNumberOffset);
        fbb.addOffset(5, companyNameOffset);
        fbb.addOffset(6, gstNoOffset);
        fbb.addOffset(7, businessVerticalOffset);
        fbb.addOffset(8, customerCodeOffset);
        fbb.addOffset(9, billingAddressOffset);
        fbb.addBool(10, object.isSynced);
        fbb.addOffset(11, mongoIdOffset);
        fbb.finish(fbb.endTable());
        return object.id;
      },
      objectFromFB: (obx.Store store, ByteData fbData) {
        final buffer = fb.BufferContext(fbData);
        final rootOffset = buffer.derefObject(0);
        final idParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          4,
          0,
        );
        final mongoIdParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGetNullable(buffer, rootOffset, 26);
        final customerIdParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 6, '');
        final customerNameParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 8, '');
        final emailParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 10, '');
        final mobileNumberParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 12, '');
        final companyNameParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 14, '');
        final gstNoParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 16, '');
        final businessVerticalParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 18, '');
        final customerCodeParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 20, '');
        final billingAddressParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 22, '');
        final isSyncedParam = const fb.BoolReader().vTableGet(
          buffer,
          rootOffset,
          24,
          false,
        );
        final object = ObjectBoxCustomerModel(
          id: idParam,
          mongoId: mongoIdParam,
          customerId: customerIdParam,
          customerName: customerNameParam,
          email: emailParam,
          mobileNumber: mobileNumberParam,
          companyName: companyNameParam,
          gstNo: gstNoParam,
          businessVertical: businessVerticalParam,
          customerCode: customerCodeParam,
          billingAddress: billingAddressParam,
          isSynced: isSyncedParam,
        );

        return object;
      },
    ),
    CustomerSchemeModel: obx_int.EntityDefinition<CustomerSchemeModel>(
      model: _entities[6],
      toOneRelations: (CustomerSchemeModel object) => [],
      toManyRelations: (CustomerSchemeModel object) => {
        obx_int.RelInfo<SupportPersonModel>.toOneBacklink(
          3,
          object.id,
          (SupportPersonModel srcObject) => srcObject.customerScheme,
        ): object.supportPersons,
      },
      getId: (CustomerSchemeModel object) => object.id,
      setId: (CustomerSchemeModel object, int id) {
        object.id = id;
      },
      objectToFB: (CustomerSchemeModel object, fb.Builder fbb) {
        final customerIdOffset = fbb.writeString(object.customerId);
        final customerNameOffset = fbb.writeString(object.customerName);
        fbb.startTable(6);
        fbb.addInt64(0, object.id);
        fbb.addOffset(1, customerIdOffset);
        fbb.addOffset(2, customerNameOffset);
        fbb.addFloat64(3, object.sales);
        fbb.addFloat64(4, object.payment);
        fbb.finish(fbb.endTable());
        return object.id;
      },
      objectFromFB: (obx.Store store, ByteData fbData) {
        final buffer = fb.BufferContext(fbData);
        final rootOffset = buffer.derefObject(0);
        final idParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          4,
          0,
        );
        final customerIdParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 6, '');
        final customerNameParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 8, '');
        final salesParam = const fb.Float64Reader().vTableGet(
          buffer,
          rootOffset,
          10,
          0,
        );
        final paymentParam = const fb.Float64Reader().vTableGet(
          buffer,
          rootOffset,
          12,
          0,
        );
        final object = CustomerSchemeModel(
          id: idParam,
          customerId: customerIdParam,
          customerName: customerNameParam,
          sales: salesParam,
          payment: paymentParam,
        );
        obx_int.InternalToManyAccess.setRelInfo<CustomerSchemeModel>(
          object.supportPersons,
          store,
          obx_int.RelInfo<SupportPersonModel>.toOneBacklink(
            3,
            object.id,
            (SupportPersonModel srcObject) => srcObject.customerScheme,
          ),
        );
        return object;
      },
    ),
    PersonDetailsModel: obx_int.EntityDefinition<PersonDetailsModel>(
      model: _entities[7],
      toOneRelations: (PersonDetailsModel object) => [object.supportPerson],
      toManyRelations: (PersonDetailsModel object) => {},
      getId: (PersonDetailsModel object) => object.id,
      setId: (PersonDetailsModel object, int id) {
        object.id = id;
      },
      objectToFB: (PersonDetailsModel object, fb.Builder fbb) {
        final rowIdOffset = fbb.writeString(object.rowId);
        final customerNameOffset = fbb.writeString(object.customerName);
        final emailOffset = fbb.writeString(object.email);
        final statusOffset = fbb.writeString(object.status);
        final mobileNumberOffset = fbb.writeString(object.mobileNumber);
        final userIdOffset = fbb.writeString(object.userId);
        final empIdOffset = fbb.writeString(object.empId);
        final profileOffset = fbb.writeString(object.profile);
        fbb.startTable(12);
        fbb.addInt64(0, object.id);
        fbb.addOffset(1, rowIdOffset);
        fbb.addOffset(2, customerNameOffset);
        fbb.addOffset(3, emailOffset);
        fbb.addOffset(4, statusOffset);
        fbb.addOffset(5, mobileNumberOffset);
        fbb.addOffset(6, userIdOffset);
        fbb.addInt64(7, object.modifiedTime?.millisecondsSinceEpoch);
        fbb.addOffset(8, empIdOffset);
        fbb.addOffset(9, profileOffset);
        fbb.addInt64(10, object.supportPerson.targetId);
        fbb.finish(fbb.endTable());
        return object.id;
      },
      objectFromFB: (obx.Store store, ByteData fbData) {
        final buffer = fb.BufferContext(fbData);
        final rootOffset = buffer.derefObject(0);
        final modifiedTimeValue = const fb.Int64Reader().vTableGetNullable(
          buffer,
          rootOffset,
          18,
        );
        final idParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          4,
          0,
        );
        final rowIdParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 6, '');
        final customerNameParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 8, '');
        final emailParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 10, '');
        final statusParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 12, '');
        final mobileNumberParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 14, '');
        final userIdParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 16, '');
        final modifiedTimeParam = modifiedTimeValue == null
            ? null
            : DateTime.fromMillisecondsSinceEpoch(modifiedTimeValue);
        final empIdParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 20, '');
        final profileParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 22, '');
        final object = PersonDetailsModel(
          id: idParam,
          rowId: rowIdParam,
          customerName: customerNameParam,
          email: emailParam,
          status: statusParam,
          mobileNumber: mobileNumberParam,
          userId: userIdParam,
          modifiedTime: modifiedTimeParam,
          empId: empIdParam,
          profile: profileParam,
        );
        object.supportPerson.targetId = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          24,
          0,
        );
        object.supportPerson.attach(store);
        return object;
      },
    ),
    SupportPersonModel: obx_int.EntityDefinition<SupportPersonModel>(
      model: _entities[8],
      toOneRelations: (SupportPersonModel object) => [object.customerScheme],
      toManyRelations: (SupportPersonModel object) => {
        obx_int.RelInfo<SupportPersonModel>.toMany(2, object.id):
            object.details,
      },
      getId: (SupportPersonModel object) => object.id,
      setId: (SupportPersonModel object, int id) {
        object.id = id;
      },
      objectToFB: (SupportPersonModel object, fb.Builder fbb) {
        final profileOffset = fbb.writeString(object.profile);
        fbb.startTable(4);
        fbb.addInt64(0, object.id);
        fbb.addOffset(1, profileOffset);
        fbb.addInt64(2, object.customerScheme.targetId);
        fbb.finish(fbb.endTable());
        return object.id;
      },
      objectFromFB: (obx.Store store, ByteData fbData) {
        final buffer = fb.BufferContext(fbData);
        final rootOffset = buffer.derefObject(0);
        final idParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          4,
          0,
        );
        final profileParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 6, '');
        final object = SupportPersonModel(id: idParam, profile: profileParam);
        object.customerScheme.targetId = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          8,
          0,
        );
        object.customerScheme.attach(store);
        obx_int.InternalToManyAccess.setRelInfo<SupportPersonModel>(
          object.details,
          store,
          obx_int.RelInfo<SupportPersonModel>.toMany(2, object.id),
        );
        return object;
      },
    ),
    AccountStatementEntryModel:
        obx_int.EntityDefinition<AccountStatementEntryModel>(
          model: _entities[9],
          toOneRelations: (AccountStatementEntryModel object) => [
            object.accountStatement,
          ],
          toManyRelations: (AccountStatementEntryModel object) => {},
          getId: (AccountStatementEntryModel object) => object.id,
          setId: (AccountStatementEntryModel object, int id) {
            object.id = id;
          },
          objectToFB: (AccountStatementEntryModel object, fb.Builder fbb) {
            final vchTypeOffset = fbb.writeString(object.vchType);
            final invoiceNumberOffset = fbb.writeString(object.invoiceNumber);
            final particularsOffset = fbb.writeString(object.particulars);
            fbb.startTable(11);
            fbb.addInt64(0, object.id);
            fbb.addInt64(1, object.txnDate.millisecondsSinceEpoch);
            fbb.addOffset(2, vchTypeOffset);
            fbb.addOffset(3, invoiceNumberOffset);
            fbb.addOffset(4, particularsOffset);
            fbb.addFloat64(5, object.debit);
            fbb.addFloat64(6, object.credit);
            fbb.addFloat64(7, object.balance);
            fbb.addFloat64(8, object.amount);
            fbb.addInt64(9, object.accountStatement.targetId);
            fbb.finish(fbb.endTable());
            return object.id;
          },
          objectFromFB: (obx.Store store, ByteData fbData) {
            final buffer = fb.BufferContext(fbData);
            final rootOffset = buffer.derefObject(0);
            final idParam = const fb.Int64Reader().vTableGet(
              buffer,
              rootOffset,
              4,
              0,
            );
            final txnDateParam = DateTime.fromMillisecondsSinceEpoch(
              const fb.Int64Reader().vTableGet(buffer, rootOffset, 6, 0),
            );
            final vchTypeParam = const fb.StringReader(
              asciiOptimization: true,
            ).vTableGet(buffer, rootOffset, 8, '');
            final invoiceNumberParam = const fb.StringReader(
              asciiOptimization: true,
            ).vTableGet(buffer, rootOffset, 10, '');
            final particularsParam = const fb.StringReader(
              asciiOptimization: true,
            ).vTableGet(buffer, rootOffset, 12, '');
            final debitParam = const fb.Float64Reader().vTableGet(
              buffer,
              rootOffset,
              14,
              0,
            );
            final creditParam = const fb.Float64Reader().vTableGet(
              buffer,
              rootOffset,
              16,
              0,
            );
            final balanceParam = const fb.Float64Reader().vTableGet(
              buffer,
              rootOffset,
              18,
              0,
            );
            final amountParam = const fb.Float64Reader().vTableGet(
              buffer,
              rootOffset,
              20,
              0,
            );
            final object = AccountStatementEntryModel(
              id: idParam,
              txnDate: txnDateParam,
              vchType: vchTypeParam,
              invoiceNumber: invoiceNumberParam,
              particulars: particularsParam,
              debit: debitParam,
              credit: creditParam,
              balance: balanceParam,
              amount: amountParam,
            );
            object.accountStatement.targetId = const fb.Int64Reader().vTableGet(
              buffer,
              rootOffset,
              22,
              0,
            );
            object.accountStatement.attach(store);
            return object;
          },
        ),
    AccountStatementModel: obx_int.EntityDefinition<AccountStatementModel>(
      model: _entities[10],
      toOneRelations: (AccountStatementModel object) => [],
      toManyRelations: (AccountStatementModel object) => {
        obx_int.RelInfo<AccountStatementEntryModel>.toOneBacklink(
          10,
          object.id,
          (AccountStatementEntryModel srcObject) => srcObject.accountStatement,
        ): object.entries,
      },
      getId: (AccountStatementModel object) => object.id,
      setId: (AccountStatementModel object, int id) {
        object.id = id;
      },
      objectToFB: (AccountStatementModel object, fb.Builder fbb) {
        final customerIdOffset = fbb.writeString(object.customerId);
        fbb.startTable(4);
        fbb.addInt64(0, object.id);
        fbb.addOffset(1, customerIdOffset);
        fbb.addInt64(2, object.lastSyncTime.millisecondsSinceEpoch);
        fbb.finish(fbb.endTable());
        return object.id;
      },
      objectFromFB: (obx.Store store, ByteData fbData) {
        final buffer = fb.BufferContext(fbData);
        final rootOffset = buffer.derefObject(0);
        final idParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          4,
          0,
        );
        final customerIdParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 6, '');
        final lastSyncTimeParam = DateTime.fromMillisecondsSinceEpoch(
          const fb.Int64Reader().vTableGet(buffer, rootOffset, 8, 0),
        );
        final object = AccountStatementModel(
          id: idParam,
          customerId: customerIdParam,
          lastSyncTime: lastSyncTimeParam,
        );
        obx_int.InternalToManyAccess.setRelInfo<AccountStatementModel>(
          object.entries,
          store,
          obx_int.RelInfo<AccountStatementEntryModel>.toOneBacklink(
            10,
            object.id,
            (AccountStatementEntryModel srcObject) =>
                srcObject.accountStatement,
          ),
        );
        return object;
      },
    ),
    ObjectBoxSMRReportModel: obx_int.EntityDefinition<ObjectBoxSMRReportModel>(
      model: _entities[11],
      toOneRelations: (ObjectBoxSMRReportModel object) => [],
      toManyRelations: (ObjectBoxSMRReportModel object) => {},
      getId: (ObjectBoxSMRReportModel object) => object.dbId,
      setId: (ObjectBoxSMRReportModel object, int id) {
        object.dbId = id;
      },
      objectToFB: (ObjectBoxSMRReportModel object, fb.Builder fbb) {
        final idOffset = fbb.writeString(object.id);
        final soOffset = fbb.writeString(object.so);
        final partnerOffset = fbb.writeString(object.partner);
        final partnerIdOffset = fbb.writeString(object.partnerId);
        final productNameOffset = fbb.writeString(object.productName);
        final statusOffset = fbb.writeString(object.status);
        fbb.startTable(16);
        fbb.addInt64(0, object.dbId);
        fbb.addOffset(1, idOffset);
        fbb.addOffset(2, soOffset);
        fbb.addOffset(3, partnerOffset);
        fbb.addOffset(4, partnerIdOffset);
        fbb.addOffset(5, productNameOffset);
        fbb.addInt64(6, object.startDateMillis);
        fbb.addInt64(7, object.lastDateMillis);
        fbb.addInt64(8, object.openingBalance);
        fbb.addInt64(9, object.invoice);
        fbb.addInt64(10, object.srn);
        fbb.addInt64(11, object.closingBalance);
        fbb.addInt64(12, object.sales);
        fbb.addOffset(14, statusOffset);
        fbb.finish(fbb.endTable());
        return object.dbId;
      },
      objectFromFB: (obx.Store store, ByteData fbData) {
        final buffer = fb.BufferContext(fbData);
        final rootOffset = buffer.derefObject(0);
        final dbIdParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          4,
          0,
        );
        final idParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 6, '');
        final soParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 8, '');
        final partnerParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 10, '');
        final partnerIdParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 12, '');
        final productNameParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 14, '');
        final startDateMillisParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          16,
          0,
        );
        final lastDateMillisParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          18,
          0,
        );
        final openingBalanceParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          20,
          0,
        );
        final invoiceParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          22,
          0,
        );
        final srnParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          24,
          0,
        );
        final closingBalanceParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          26,
          0,
        );
        final salesParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          28,
          0,
        );
        final statusParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 32, '');
        final object = ObjectBoxSMRReportModel(
          dbId: dbIdParam,
          id: idParam,
          so: soParam,
          partner: partnerParam,
          partnerId: partnerIdParam,
          productName: productNameParam,
          startDateMillis: startDateMillisParam,
          lastDateMillis: lastDateMillisParam,
          openingBalance: openingBalanceParam,
          invoice: invoiceParam,
          srn: srnParam,
          closingBalance: closingBalanceParam,
          sales: salesParam,
          status: statusParam,
        );

        return object;
      },
    ),
    SalesOrderItemModel: obx_int.EntityDefinition<SalesOrderItemModel>(
      model: _entities[12],
      toOneRelations: (SalesOrderItemModel object) => [object.salesOrder],
      toManyRelations: (SalesOrderItemModel object) => {},
      getId: (SalesOrderItemModel object) => object.id,
      setId: (SalesOrderItemModel object, int id) {
        object.id = id;
      },
      objectToFB: (SalesOrderItemModel object, fb.Builder fbb) {
        final itemIdOffset = fbb.writeString(object.itemId);
        final productIdOffset = fbb.writeString(object.productId);
        final entityDiscountPercentOffset = fbb.writeString(
          object.entityDiscountPercent,
        );
        final hsnSacOffset = fbb.writeString(object.hsnSac);
        final invoicedQuantityCancelledOffset = fbb.writeString(
          object.invoicedQuantityCancelled,
        );
        final itemNameOffset = fbb.writeString(object.itemName);
        final manuallyFulfilledQuantityOffset = fbb.writeString(
          object.manuallyFulfilledQuantity,
        );
        final nonPackageQuantityOffset = fbb.writeString(
          object.nonPackageQuantity,
        );
        final placeOfSupplyOffset = fbb.writeString(object.placeOfSupply);
        final quantityDeliveredOffset = fbb.writeString(
          object.quantityDelivered,
        );
        final quantityDropshippedOffset = fbb.writeString(
          object.quantityDropshipped,
        );
        final quantityPackedOffset = fbb.writeString(object.quantityPacked);
        final salesVerticesOffset = fbb.writeString(object.salesVertices);
        final snoOffset = fbb.writeString(object.sno);
        final salesOrderIdValueOffset = object.salesOrderIdValue == null
            ? null
            : fbb.writeString(object.salesOrderIdValue!);
        fbb.startTable(22);
        fbb.addInt64(0, object.id);
        fbb.addOffset(1, itemIdOffset);
        fbb.addInt64(2, object.salesOrder.targetId);
        fbb.addOffset(3, productIdOffset);
        fbb.addInt64(4, object.createdTime.millisecondsSinceEpoch);
        fbb.addOffset(5, entityDiscountPercentOffset);
        fbb.addOffset(6, hsnSacOffset);
        fbb.addOffset(7, invoicedQuantityCancelledOffset);
        fbb.addOffset(8, itemNameOffset);
        fbb.addFloat64(9, object.itemPrice);
        fbb.addInt64(10, object.lastModifiedTime.millisecondsSinceEpoch);
        fbb.addOffset(11, manuallyFulfilledQuantityOffset);
        fbb.addOffset(12, nonPackageQuantityOffset);
        fbb.addOffset(13, placeOfSupplyOffset);
        fbb.addOffset(14, quantityDeliveredOffset);
        fbb.addOffset(15, quantityDropshippedOffset);
        fbb.addOffset(16, quantityPackedOffset);
        fbb.addOffset(17, salesVerticesOffset);
        fbb.addOffset(18, snoOffset);
        fbb.addFloat64(19, object.total);
        fbb.addOffset(20, salesOrderIdValueOffset);
        fbb.finish(fbb.endTable());
        return object.id;
      },
      objectFromFB: (obx.Store store, ByteData fbData) {
        final buffer = fb.BufferContext(fbData);
        final rootOffset = buffer.derefObject(0);
        final itemIdParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 6, '');
        final productIdParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 10, '');
        final createdTimeParam = DateTime.fromMillisecondsSinceEpoch(
          const fb.Int64Reader().vTableGet(buffer, rootOffset, 12, 0),
        );
        final entityDiscountPercentParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 14, '');
        final hsnSacParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 16, '');
        final invoicedQuantityCancelledParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 18, '');
        final itemNameParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 20, '');
        final itemPriceParam = const fb.Float64Reader().vTableGet(
          buffer,
          rootOffset,
          22,
          0,
        );
        final lastModifiedTimeParam = DateTime.fromMillisecondsSinceEpoch(
          const fb.Int64Reader().vTableGet(buffer, rootOffset, 24, 0),
        );
        final manuallyFulfilledQuantityParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 26, '');
        final nonPackageQuantityParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 28, '');
        final placeOfSupplyParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 30, '');
        final quantityDeliveredParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 32, '');
        final quantityDropshippedParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 34, '');
        final quantityPackedParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 36, '');
        final salesVerticesParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 38, '');
        final snoParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 40, '');
        final totalParam = const fb.Float64Reader().vTableGet(
          buffer,
          rootOffset,
          42,
          0,
        );
        final object =
            SalesOrderItemModel(
                itemId: itemIdParam,
                productId: productIdParam,
                createdTime: createdTimeParam,
                entityDiscountPercent: entityDiscountPercentParam,
                hsnSac: hsnSacParam,
                invoicedQuantityCancelled: invoicedQuantityCancelledParam,
                itemName: itemNameParam,
                itemPrice: itemPriceParam,
                lastModifiedTime: lastModifiedTimeParam,
                manuallyFulfilledQuantity: manuallyFulfilledQuantityParam,
                nonPackageQuantity: nonPackageQuantityParam,
                placeOfSupply: placeOfSupplyParam,
                quantityDelivered: quantityDeliveredParam,
                quantityDropshipped: quantityDropshippedParam,
                quantityPacked: quantityPackedParam,
                salesVertices: salesVerticesParam,
                sno: snoParam,
                total: totalParam,
              )
              ..id = const fb.Int64Reader().vTableGet(buffer, rootOffset, 4, 0)
              ..salesOrderIdValue = const fb.StringReader(
                asciiOptimization: true,
              ).vTableGetNullable(buffer, rootOffset, 44);
        object.salesOrder.targetId = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          8,
          0,
        );
        object.salesOrder.attach(store);
        return object;
      },
    ),
    SalesOrderModel: obx_int.EntityDefinition<SalesOrderModel>(
      model: _entities[13],
      toOneRelations: (SalesOrderModel object) => [],
      toManyRelations: (SalesOrderModel object) => {
        obx_int.RelInfo<SalesOrderItemModel>.toOneBacklink(
          3,
          object.id,
          (SalesOrderItemModel srcObject) => srcObject.salesOrder,
        ): object.items,
      },
      getId: (SalesOrderModel object) => object.id,
      setId: (SalesOrderModel object, int id) {
        object.id = id;
      },
      objectToFB: (SalesOrderModel object, fb.Builder fbb) {
        final salesOrderIdOffset = fbb.writeString(object.salesOrderId);
        final addressIdOffset = fbb.writeString(object.addressId);
        final customerIdOffset = fbb.writeString(object.customerId);
        final invoicedStatusOffset = fbb.writeString(object.invoicedStatus);
        final orderSourceOffset = fbb.writeString(object.orderSource);
        final paidStatusOffset = fbb.writeString(object.paidStatus);
        final paymentTermsLabelOffset = fbb.writeString(
          object.paymentTermsLabel,
        );
        final saleOrderDateOffset = fbb.writeString(object.saleOrderDate);
        final salesChannelOffset = fbb.writeString(object.salesChannel);
        final salesOrderNumberOffset = fbb.writeString(object.salesOrderNumber);
        fbb.startTable(18);
        fbb.addInt64(0, object.id);
        fbb.addOffset(1, salesOrderIdOffset);
        fbb.addOffset(2, addressIdOffset);
        fbb.addInt64(3, object.createdTime.millisecondsSinceEpoch);
        fbb.addOffset(4, customerIdOffset);
        fbb.addOffset(5, invoicedStatusOffset);
        fbb.addInt64(6, object.lastModifiedTime.millisecondsSinceEpoch);
        fbb.addOffset(7, orderSourceOffset);
        fbb.addOffset(8, paidStatusOffset);
        fbb.addOffset(9, paymentTermsLabelOffset);
        fbb.addOffset(10, saleOrderDateOffset);
        fbb.addOffset(11, salesChannelOffset);
        fbb.addOffset(12, salesOrderNumberOffset);
        fbb.addFloat64(13, object.subTotal);
        fbb.addFloat64(14, object.total);
        fbb.addBool(15, object.isSynced);
        fbb.addInt64(16, object.lastSyncedAt?.millisecondsSinceEpoch);
        fbb.finish(fbb.endTable());
        return object.id;
      },
      objectFromFB: (obx.Store store, ByteData fbData) {
        final buffer = fb.BufferContext(fbData);
        final rootOffset = buffer.derefObject(0);
        final lastSyncedAtValue = const fb.Int64Reader().vTableGetNullable(
          buffer,
          rootOffset,
          36,
        );
        final salesOrderIdParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 6, '');
        final addressIdParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 8, '');
        final createdTimeParam = DateTime.fromMillisecondsSinceEpoch(
          const fb.Int64Reader().vTableGet(buffer, rootOffset, 10, 0),
        );
        final customerIdParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 12, '');
        final invoicedStatusParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 14, '');
        final lastModifiedTimeParam = DateTime.fromMillisecondsSinceEpoch(
          const fb.Int64Reader().vTableGet(buffer, rootOffset, 16, 0),
        );
        final orderSourceParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 18, '');
        final paidStatusParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 20, '');
        final paymentTermsLabelParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 22, '');
        final saleOrderDateParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 24, '');
        final salesChannelParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 26, '');
        final salesOrderNumberParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 28, '');
        final subTotalParam = const fb.Float64Reader().vTableGet(
          buffer,
          rootOffset,
          30,
          0,
        );
        final totalParam = const fb.Float64Reader().vTableGet(
          buffer,
          rootOffset,
          32,
          0,
        );
        final isSyncedParam = const fb.BoolReader().vTableGet(
          buffer,
          rootOffset,
          34,
          false,
        );
        final lastSyncedAtParam = lastSyncedAtValue == null
            ? null
            : DateTime.fromMillisecondsSinceEpoch(lastSyncedAtValue);
        final object = SalesOrderModel(
          salesOrderId: salesOrderIdParam,
          addressId: addressIdParam,
          createdTime: createdTimeParam,
          customerId: customerIdParam,
          invoicedStatus: invoicedStatusParam,
          lastModifiedTime: lastModifiedTimeParam,
          orderSource: orderSourceParam,
          paidStatus: paidStatusParam,
          paymentTermsLabel: paymentTermsLabelParam,
          saleOrderDate: saleOrderDateParam,
          salesChannel: salesChannelParam,
          salesOrderNumber: salesOrderNumberParam,
          subTotal: subTotalParam,
          total: totalParam,
          isSynced: isSyncedParam,
          lastSyncedAt: lastSyncedAtParam,
        )..id = const fb.Int64Reader().vTableGet(buffer, rootOffset, 4, 0);
        obx_int.InternalToManyAccess.setRelInfo<SalesOrderModel>(
          object.items,
          store,
          obx_int.RelInfo<SalesOrderItemModel>.toOneBacklink(
            3,
            object.id,
            (SalesOrderItemModel srcObject) => srcObject.salesOrder,
          ),
        );
        return object;
      },
    ),
    DuesInvoiceModel: obx_int.EntityDefinition<DuesInvoiceModel>(
      model: _entities[14],
      toOneRelations: (DuesInvoiceModel object) => [],
      toManyRelations: (DuesInvoiceModel object) => {},
      getId: (DuesInvoiceModel object) => object.dbId,
      setId: (DuesInvoiceModel object, int id) {
        object.dbId = id;
      },
      objectToFB: (DuesInvoiceModel object, fb.Builder fbb) {
        final idOffset = fbb.writeString(object.id);
        final invoiceIdOffset = fbb.writeString(object.invoiceId);
        final invoiceNumberOffset = fbb.writeString(object.invoiceNumber);
        final invoiceDateOffset = fbb.writeString(object.invoiceDate);
        final customerIdOffset = fbb.writeString(object.customerId);
        final customerCodeOffset = fbb.writeString(object.customerCode);
        final customerNameOffset = fbb.writeString(object.customerName);
        final customerDistrictOffset = fbb.writeString(object.customerDistrict);
        final customerStateOffset = fbb.writeString(object.customerState);
        final customerTypeOffset = fbb.writeString(object.customerType);
        final onBoardedTimeOffset = fbb.writeString(object.onBoardedTime);
        final categoryTypeOffset = fbb.writeString(object.categoryType);
        final invoiceTypeOffset = fbb.writeString(object.invoiceType);
        final retailTypeOffset = fbb.writeString(object.retailType);
        final invoiceStatusOffset = fbb.writeString(object.invoiceStatus);
        final invoiceRaisedByOffset = fbb.writeString(object.invoiceRaisedBy);
        final modeOffset = fbb.writeString(object.mode);
        final businessVerticalOffset = fbb.writeString(object.businessVertical);
        final feedCreditLimitOffset = fbb.writeString(object.feedCreditLimit);
        final nonFeedCreditLimitOffset = fbb.writeString(
          object.nonFeedCreditLimit,
        );
        final harvestCreditLimitOffset = fbb.writeString(
          object.harvestCreditLimit,
        );
        final feedAmountAfterTcsOffset = fbb.writeString(
          object.feedAmountAfterTcs,
        );
        final dueDateOffset = fbb.writeString(object.dueDate);
        final agingOffset = fbb.writeString(object.aging);
        final aging1Offset = fbb.writeString(object.aging1);
        final paymentCredibilityOffset = fbb.writeString(
          object.paymentCredibility,
        );
        final agingGroupOffset = fbb.writeString(object.agingGroup);
        final salesTierOffset = object.salesTier == null
            ? null
            : fbb.writeString(object.salesTier!);
        final lastPaidDateOffset = object.lastPaidDate == null
            ? null
            : fbb.writeString(object.lastPaidDate!);
        fbb.startTable(52);
        fbb.addInt64(0, object.dbId);
        fbb.addOffset(1, idOffset);
        fbb.addOffset(2, invoiceIdOffset);
        fbb.addOffset(3, invoiceNumberOffset);
        fbb.addOffset(4, invoiceDateOffset);
        fbb.addOffset(5, customerIdOffset);
        fbb.addOffset(6, customerCodeOffset);
        fbb.addOffset(7, customerNameOffset);
        fbb.addOffset(8, customerDistrictOffset);
        fbb.addOffset(9, customerStateOffset);
        fbb.addOffset(10, customerTypeOffset);
        fbb.addOffset(11, onBoardedTimeOffset);
        fbb.addOffset(12, categoryTypeOffset);
        fbb.addOffset(13, invoiceTypeOffset);
        fbb.addOffset(14, retailTypeOffset);
        fbb.addOffset(15, invoiceStatusOffset);
        fbb.addOffset(16, invoiceRaisedByOffset);
        fbb.addOffset(17, modeOffset);
        fbb.addOffset(18, businessVerticalOffset);
        fbb.addOffset(19, feedCreditLimitOffset);
        fbb.addOffset(20, nonFeedCreditLimitOffset);
        fbb.addOffset(21, harvestCreditLimitOffset);
        fbb.addFloat64(22, object.totalSalesInclTax);
        fbb.addFloat64(23, object.totalSalesExclTax);
        fbb.addFloat64(24, object.tcsAmount);
        fbb.addFloat64(25, object.tdsAmount);
        fbb.addFloat64(26, object.amountAfterTcs);
        fbb.addFloat64(27, object.shippingCharges);
        fbb.addOffset(28, feedAmountAfterTcsOffset);
        fbb.addFloat64(29, object.nonFeedAmountAfterTcs);
        fbb.addFloat64(30, object.creditNoteAmountWithTcs);
        fbb.addFloat64(31, object.payableAmount);
        fbb.addFloat64(32, object.paidAmount);
        fbb.addFloat64(33, object.due);
        fbb.addOffset(34, dueDateOffset);
        fbb.addInt64(35, object.dueDays);
        fbb.addOffset(36, agingOffset);
        fbb.addOffset(37, aging1Offset);
        fbb.addOffset(38, paymentCredibilityOffset);
        fbb.addInt64(39, object.lastSyncTimestamp);
        fbb.addOffset(40, agingGroupOffset);
        fbb.addFloat64(41, object.totalPurchase);
        fbb.addFloat64(42, object.totalSales);
        fbb.addOffset(43, salesTierOffset);
        fbb.addFloat64(44, object.healthcareSales);
        fbb.addFloat64(45, object.feedSales);
        fbb.addFloat64(46, object.chemicalSales);
        fbb.addFloat64(47, object.equipmentSales);
        fbb.addFloat64(48, object.harvestSales);
        fbb.addFloat64(49, object.grossMargin);
        fbb.addOffset(50, lastPaidDateOffset);
        fbb.finish(fbb.endTable());
        return object.dbId;
      },
      objectFromFB: (obx.Store store, ByteData fbData) {
        final buffer = fb.BufferContext(fbData);
        final rootOffset = buffer.derefObject(0);
        final dbIdParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          4,
          0,
        );
        final agingGroupParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 84, '');
        final customerIdParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 14, '');
        final idParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 6, '');
        final invoiceIdParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 8, '');
        final invoiceNumberParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 10, '');
        final invoiceDateParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 12, '');
        final customerCodeParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 16, '');
        final customerNameParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 18, '');
        final customerDistrictParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 20, '');
        final customerStateParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 22, '');
        final customerTypeParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 24, '');
        final onBoardedTimeParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 26, '');
        final categoryTypeParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 28, '');
        final invoiceTypeParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 30, '');
        final retailTypeParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 32, '');
        final invoiceStatusParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 34, '');
        final invoiceRaisedByParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 36, '');
        final modeParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 38, '');
        final businessVerticalParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 40, '');
        final feedCreditLimitParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 42, '');
        final nonFeedCreditLimitParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 44, '');
        final harvestCreditLimitParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 46, '');
        final totalSalesInclTaxParam = const fb.Float64Reader().vTableGet(
          buffer,
          rootOffset,
          48,
          0,
        );
        final totalSalesExclTaxParam = const fb.Float64Reader().vTableGet(
          buffer,
          rootOffset,
          50,
          0,
        );
        final tcsAmountParam = const fb.Float64Reader().vTableGet(
          buffer,
          rootOffset,
          52,
          0,
        );
        final tdsAmountParam = const fb.Float64Reader().vTableGet(
          buffer,
          rootOffset,
          54,
          0,
        );
        final amountAfterTcsParam = const fb.Float64Reader().vTableGet(
          buffer,
          rootOffset,
          56,
          0,
        );
        final shippingChargesParam = const fb.Float64Reader().vTableGet(
          buffer,
          rootOffset,
          58,
          0,
        );
        final feedAmountAfterTcsParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 60, '');
        final nonFeedAmountAfterTcsParam = const fb.Float64Reader().vTableGet(
          buffer,
          rootOffset,
          62,
          0,
        );
        final creditNoteAmountWithTcsParam = const fb.Float64Reader().vTableGet(
          buffer,
          rootOffset,
          64,
          0,
        );
        final payableAmountParam = const fb.Float64Reader().vTableGet(
          buffer,
          rootOffset,
          66,
          0,
        );
        final paidAmountParam = const fb.Float64Reader().vTableGet(
          buffer,
          rootOffset,
          68,
          0,
        );
        final dueParam = const fb.Float64Reader().vTableGet(
          buffer,
          rootOffset,
          70,
          0,
        );
        final dueDateParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 72, '');
        final dueDaysParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          74,
          0,
        );
        final agingParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 76, '');
        final aging1Param = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 78, '');
        final paymentCredibilityParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 80, '');
        final totalPurchaseParam = const fb.Float64Reader().vTableGetNullable(
          buffer,
          rootOffset,
          86,
        );
        final totalSalesParam = const fb.Float64Reader().vTableGetNullable(
          buffer,
          rootOffset,
          88,
        );
        final salesTierParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGetNullable(buffer, rootOffset, 90);
        final healthcareSalesParam = const fb.Float64Reader().vTableGetNullable(
          buffer,
          rootOffset,
          92,
        );
        final feedSalesParam = const fb.Float64Reader().vTableGetNullable(
          buffer,
          rootOffset,
          94,
        );
        final chemicalSalesParam = const fb.Float64Reader().vTableGetNullable(
          buffer,
          rootOffset,
          96,
        );
        final equipmentSalesParam = const fb.Float64Reader().vTableGetNullable(
          buffer,
          rootOffset,
          98,
        );
        final harvestSalesParam = const fb.Float64Reader().vTableGetNullable(
          buffer,
          rootOffset,
          100,
        );
        final grossMarginParam = const fb.Float64Reader().vTableGetNullable(
          buffer,
          rootOffset,
          102,
        );
        final lastPaidDateParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGetNullable(buffer, rootOffset, 104);
        final lastSyncTimestampParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          82,
          0,
        );
        final object = DuesInvoiceModel(
          dbId: dbIdParam,
          agingGroup: agingGroupParam,
          customerId: customerIdParam,
          id: idParam,
          invoiceId: invoiceIdParam,
          invoiceNumber: invoiceNumberParam,
          invoiceDate: invoiceDateParam,
          customerCode: customerCodeParam,
          customerName: customerNameParam,
          customerDistrict: customerDistrictParam,
          customerState: customerStateParam,
          customerType: customerTypeParam,
          onBoardedTime: onBoardedTimeParam,
          categoryType: categoryTypeParam,
          invoiceType: invoiceTypeParam,
          retailType: retailTypeParam,
          invoiceStatus: invoiceStatusParam,
          invoiceRaisedBy: invoiceRaisedByParam,
          mode: modeParam,
          businessVertical: businessVerticalParam,
          feedCreditLimit: feedCreditLimitParam,
          nonFeedCreditLimit: nonFeedCreditLimitParam,
          harvestCreditLimit: harvestCreditLimitParam,
          totalSalesInclTax: totalSalesInclTaxParam,
          totalSalesExclTax: totalSalesExclTaxParam,
          tcsAmount: tcsAmountParam,
          tdsAmount: tdsAmountParam,
          amountAfterTcs: amountAfterTcsParam,
          shippingCharges: shippingChargesParam,
          feedAmountAfterTcs: feedAmountAfterTcsParam,
          nonFeedAmountAfterTcs: nonFeedAmountAfterTcsParam,
          creditNoteAmountWithTcs: creditNoteAmountWithTcsParam,
          payableAmount: payableAmountParam,
          paidAmount: paidAmountParam,
          due: dueParam,
          dueDate: dueDateParam,
          dueDays: dueDaysParam,
          aging: agingParam,
          aging1: aging1Param,
          paymentCredibility: paymentCredibilityParam,
          totalPurchase: totalPurchaseParam,
          totalSales: totalSalesParam,
          salesTier: salesTierParam,
          healthcareSales: healthcareSalesParam,
          feedSales: feedSalesParam,
          chemicalSales: chemicalSalesParam,
          equipmentSales: equipmentSalesParam,
          harvestSales: harvestSalesParam,
          grossMargin: grossMarginParam,
          lastPaidDate: lastPaidDateParam,
          lastSyncTimestamp: lastSyncTimestampParam,
        );

        return object;
      },
    ),
    DuesSummaryModel: obx_int.EntityDefinition<DuesSummaryModel>(
      model: _entities[15],
      toOneRelations: (DuesSummaryModel object) => [],
      toManyRelations: (DuesSummaryModel object) => {},
      getId: (DuesSummaryModel object) => object.dbId,
      setId: (DuesSummaryModel object, int id) {
        object.dbId = id;
      },
      objectToFB: (DuesSummaryModel object, fb.Builder fbb) {
        final customerIdOffset = fbb.writeString(object.customerId);
        fbb.startTable(8);
        fbb.addInt64(0, object.dbId);
        fbb.addOffset(1, customerIdOffset);
        fbb.addInt64(5, object.lastSyncTimestamp);
        fbb.addFloat64(6, object.totalDue);
        fbb.finish(fbb.endTable());
        return object.dbId;
      },
      objectFromFB: (obx.Store store, ByteData fbData) {
        final buffer = fb.BufferContext(fbData);
        final rootOffset = buffer.derefObject(0);
        final dbIdParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          4,
          0,
        );
        final customerIdParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 6, '');
        final totalDueParam = const fb.Float64Reader().vTableGet(
          buffer,
          rootOffset,
          16,
          0,
        );
        final lastSyncTimestampParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          14,
          0,
        );
        final object = DuesSummaryModel(
          dbId: dbIdParam,
          customerId: customerIdParam,
          totalDue: totalDueParam,
          lastSyncTimestamp: lastSyncTimestampParam,
        );

        return object;
      },
    ),
    DuesAgingGroupModel: obx_int.EntityDefinition<DuesAgingGroupModel>(
      model: _entities[16],
      toOneRelations: (DuesAgingGroupModel object) => [],
      toManyRelations: (DuesAgingGroupModel object) => {},
      getId: (DuesAgingGroupModel object) => object.dbId,
      setId: (DuesAgingGroupModel object, int id) {
        object.dbId = id;
      },
      objectToFB: (DuesAgingGroupModel object, fb.Builder fbb) {
        final customerIdOffset = fbb.writeString(object.customerId);
        final agingOffset = fbb.writeString(object.aging);
        fbb.startTable(7);
        fbb.addInt64(0, object.dbId);
        fbb.addOffset(1, customerIdOffset);
        fbb.addFloat64(2, object.totalPayableAmount);
        fbb.addOffset(3, agingOffset);
        fbb.addInt64(4, object.dueDays);
        fbb.addInt64(5, object.lastSyncTimestamp);
        fbb.finish(fbb.endTable());
        return object.dbId;
      },
      objectFromFB: (obx.Store store, ByteData fbData) {
        final buffer = fb.BufferContext(fbData);
        final rootOffset = buffer.derefObject(0);
        final dbIdParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          4,
          0,
        );
        final customerIdParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 6, '');
        final totalPayableAmountParam = const fb.Float64Reader().vTableGet(
          buffer,
          rootOffset,
          8,
          0,
        );
        final agingParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 10, '');
        final dueDaysParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          12,
          0,
        );
        final lastSyncTimestampParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          14,
          0,
        );
        final object = DuesAgingGroupModel(
          dbId: dbIdParam,
          customerId: customerIdParam,
          totalPayableAmount: totalPayableAmountParam,
          aging: agingParam,
          dueDays: dueDaysParam,
          lastSyncTimestamp: lastSyncTimestampParam,
        );

        return object;
      },
    ),
    ObjectBoxInvoiceItemModel:
        obx_int.EntityDefinition<ObjectBoxInvoiceItemModel>(
          model: _entities[17],
          toOneRelations: (ObjectBoxInvoiceItemModel object) => [
            object.invoice,
          ],
          toManyRelations: (ObjectBoxInvoiceItemModel object) => {},
          getId: (ObjectBoxInvoiceItemModel object) => object.id,
          setId: (ObjectBoxInvoiceItemModel object, int id) {
            object.id = id;
          },
          objectToFB: (ObjectBoxInvoiceItemModel object, fb.Builder fbb) {
            final itemIdOffset = fbb.writeString(object.itemId);
            final productIdOffset = fbb.writeString(object.productId);
            final itemNameOffset = fbb.writeString(object.itemName);
            final hsnSacOffset = fbb.writeString(object.hsnSac);
            final placeOfSupplyOffset = fbb.writeString(object.placeOfSupply);
            final productCategoryOffset = fbb.writeString(
              object.productCategory,
            );
            final sourceOffset = fbb.writeString(object.source);
            final invoiceIdValueOffset = fbb.writeString(object.invoiceIdValue);
            fbb.startTable(18);
            fbb.addInt64(0, object.id);
            fbb.addOffset(1, itemIdOffset);
            fbb.addOffset(2, productIdOffset);
            fbb.addOffset(3, itemNameOffset);
            fbb.addFloat64(4, object.quantity);
            fbb.addInt64(5, object.invoice.targetId);
            fbb.addInt64(6, object.createdTime.microsecondsSinceEpoch * 1000);
            fbb.addFloat64(7, object.discountAmount);
            fbb.addOffset(8, hsnSacOffset);
            fbb.addFloat64(9, object.itemPrice);
            fbb.addInt64(
              10,
              object.lastModifiedTime.microsecondsSinceEpoch * 1000,
            );
            fbb.addOffset(11, placeOfSupplyOffset);
            fbb.addOffset(12, productCategoryOffset);
            fbb.addOffset(13, sourceOffset);
            fbb.addFloat64(14, object.subTotal);
            fbb.addFloat64(15, object.total);
            fbb.addOffset(16, invoiceIdValueOffset);
            fbb.finish(fbb.endTable());
            return object.id;
          },
          objectFromFB: (obx.Store store, ByteData fbData) {
            final buffer = fb.BufferContext(fbData);
            final rootOffset = buffer.derefObject(0);
            final idParam = const fb.Int64Reader().vTableGet(
              buffer,
              rootOffset,
              4,
              0,
            );
            final itemIdParam = const fb.StringReader(
              asciiOptimization: true,
            ).vTableGet(buffer, rootOffset, 6, '');
            final productIdParam = const fb.StringReader(
              asciiOptimization: true,
            ).vTableGet(buffer, rootOffset, 8, '');
            final itemNameParam = const fb.StringReader(
              asciiOptimization: true,
            ).vTableGet(buffer, rootOffset, 10, '');
            final quantityParam = const fb.Float64Reader().vTableGet(
              buffer,
              rootOffset,
              12,
              0,
            );
            final invoiceIdValueParam = const fb.StringReader(
              asciiOptimization: true,
            ).vTableGet(buffer, rootOffset, 36, '');
            final createdTimeParam = DateTime.fromMicrosecondsSinceEpoch(
              (const fb.Int64Reader().vTableGet(buffer, rootOffset, 16, 0) /
                      1000)
                  .round(),
            );
            final discountAmountParam = const fb.Float64Reader().vTableGet(
              buffer,
              rootOffset,
              18,
              0,
            );
            final hsnSacParam = const fb.StringReader(
              asciiOptimization: true,
            ).vTableGet(buffer, rootOffset, 20, '');
            final itemPriceParam = const fb.Float64Reader().vTableGet(
              buffer,
              rootOffset,
              22,
              0,
            );
            final lastModifiedTimeParam = DateTime.fromMicrosecondsSinceEpoch(
              (const fb.Int64Reader().vTableGet(buffer, rootOffset, 24, 0) /
                      1000)
                  .round(),
            );
            final placeOfSupplyParam = const fb.StringReader(
              asciiOptimization: true,
            ).vTableGet(buffer, rootOffset, 26, '');
            final productCategoryParam = const fb.StringReader(
              asciiOptimization: true,
            ).vTableGet(buffer, rootOffset, 28, '');
            final sourceParam = const fb.StringReader(
              asciiOptimization: true,
            ).vTableGet(buffer, rootOffset, 30, '');
            final subTotalParam = const fb.Float64Reader().vTableGet(
              buffer,
              rootOffset,
              32,
              0,
            );
            final totalParam = const fb.Float64Reader().vTableGet(
              buffer,
              rootOffset,
              34,
              0,
            );
            final object = ObjectBoxInvoiceItemModel(
              id: idParam,
              itemId: itemIdParam,
              productId: productIdParam,
              itemName: itemNameParam,
              quantity: quantityParam,
              invoiceIdValue: invoiceIdValueParam,
              createdTime: createdTimeParam,
              discountAmount: discountAmountParam,
              hsnSac: hsnSacParam,
              itemPrice: itemPriceParam,
              lastModifiedTime: lastModifiedTimeParam,
              placeOfSupply: placeOfSupplyParam,
              productCategory: productCategoryParam,
              source: sourceParam,
              subTotal: subTotalParam,
              total: totalParam,
            );
            object.invoice.targetId = const fb.Int64Reader().vTableGet(
              buffer,
              rootOffset,
              14,
              0,
            );
            object.invoice.attach(store);
            return object;
          },
        ),
    ObjectBoxInvoiceModel: obx_int.EntityDefinition<ObjectBoxInvoiceModel>(
      model: _entities[18],
      toOneRelations: (ObjectBoxInvoiceModel object) => [],
      toManyRelations: (ObjectBoxInvoiceModel object) => {
        obx_int.RelInfo<ObjectBoxInvoiceItemModel>.toOneBacklink(
          6,
          object.id,
          (ObjectBoxInvoiceItemModel srcObject) => srcObject.invoice,
        ): object.items,
      },
      getId: (ObjectBoxInvoiceModel object) => object.id,
      setId: (ObjectBoxInvoiceModel object, int id) {
        object.id = id;
      },
      objectToFB: (ObjectBoxInvoiceModel object, fb.Builder fbb) {
        final invoiceIdOffset = fbb.writeString(object.invoiceId);
        final addressIdOffset = fbb.writeString(object.addressId);
        final ageTierOffset = fbb.writeString(object.ageTier);
        final customerIdOffset = fbb.writeString(object.customerId);
        final deliveryModeOffset = fbb.writeString(object.deliveryMode);
        final deliveryStatusOffset = fbb.writeString(object.deliveryStatus);
        final invoiceNumberOffset = fbb.writeString(object.invoiceNumber);
        final invoiceStatusOffset = fbb.writeString(object.invoiceStatus);
        fbb.startTable(16);
        fbb.addInt64(0, object.id);
        fbb.addOffset(1, invoiceIdOffset);
        fbb.addOffset(2, addressIdOffset);
        fbb.addInt64(3, object.ageInDays);
        fbb.addOffset(4, ageTierOffset);
        fbb.addFloat64(5, object.balance);
        fbb.addOffset(6, customerIdOffset);
        fbb.addOffset(7, deliveryModeOffset);
        fbb.addOffset(8, deliveryStatusOffset);
        fbb.addInt64(9, object.dueDate.microsecondsSinceEpoch * 1000);
        fbb.addInt64(10, object.invoiceDate.microsecondsSinceEpoch * 1000);
        fbb.addOffset(11, invoiceNumberOffset);
        fbb.addOffset(12, invoiceStatusOffset);
        fbb.addFloat64(13, object.subTotal);
        fbb.addFloat64(14, object.total);
        fbb.finish(fbb.endTable());
        return object.id;
      },
      objectFromFB: (obx.Store store, ByteData fbData) {
        final buffer = fb.BufferContext(fbData);
        final rootOffset = buffer.derefObject(0);
        final idParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          4,
          0,
        );
        final invoiceIdParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 6, '');
        final addressIdParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 8, '');
        final ageInDaysParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          10,
          0,
        );
        final ageTierParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 12, '');
        final balanceParam = const fb.Float64Reader().vTableGet(
          buffer,
          rootOffset,
          14,
          0,
        );
        final customerIdParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 16, '');
        final deliveryModeParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 18, '');
        final deliveryStatusParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 20, '');
        final dueDateParam = DateTime.fromMicrosecondsSinceEpoch(
          (const fb.Int64Reader().vTableGet(buffer, rootOffset, 22, 0) / 1000)
              .round(),
        );
        final invoiceDateParam = DateTime.fromMicrosecondsSinceEpoch(
          (const fb.Int64Reader().vTableGet(buffer, rootOffset, 24, 0) / 1000)
              .round(),
        );
        final invoiceNumberParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 26, '');
        final invoiceStatusParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 28, '');
        final subTotalParam = const fb.Float64Reader().vTableGet(
          buffer,
          rootOffset,
          30,
          0,
        );
        final totalParam = const fb.Float64Reader().vTableGet(
          buffer,
          rootOffset,
          32,
          0,
        );
        final object = ObjectBoxInvoiceModel(
          id: idParam,
          invoiceId: invoiceIdParam,
          addressId: addressIdParam,
          ageInDays: ageInDaysParam,
          ageTier: ageTierParam,
          balance: balanceParam,
          customerId: customerIdParam,
          deliveryMode: deliveryModeParam,
          deliveryStatus: deliveryStatusParam,
          dueDate: dueDateParam,
          invoiceDate: invoiceDateParam,
          invoiceNumber: invoiceNumberParam,
          invoiceStatus: invoiceStatusParam,
          subTotal: subTotalParam,
          total: totalParam,
        );
        obx_int.InternalToManyAccess.setRelInfo<ObjectBoxInvoiceModel>(
          object.items,
          store,
          obx_int.RelInfo<ObjectBoxInvoiceItemModel>.toOneBacklink(
            6,
            object.id,
            (ObjectBoxInvoiceItemModel srcObject) => srcObject.invoice,
          ),
        );
        return object;
      },
    ),
    CustomerPaymentModel: obx_int.EntityDefinition<CustomerPaymentModel>(
      model: _entities[19],
      toOneRelations: (CustomerPaymentModel object) => [],
      toManyRelations: (CustomerPaymentModel object) => {},
      getId: (CustomerPaymentModel object) => object.dbId,
      setId: (CustomerPaymentModel object, int id) {
        object.dbId = id;
      },
      objectToFB: (CustomerPaymentModel object, fb.Builder fbb) {
        final idOffset = fbb.writeString(object.id);
        final customerIdOffset = fbb.writeString(object.customerId);
        final customerNameOffset = fbb.writeString(object.customerName);
        final categoryTypeOffset = fbb.writeString(object.categoryType);
        final amountOffset = fbb.writeString(object.amount);
        final paymentsDateOffset = fbb.writeString(object.paymentsDate);
        final paymentIdOffset = fbb.writeString(object.paymentId);
        final paymentsModeOffset = fbb.writeString(object.paymentsMode);
        final paymentsNumberOffset = fbb.writeString(object.paymentsNumber);
        fbb.startTable(11);
        fbb.addInt64(0, object.dbId);
        fbb.addOffset(1, idOffset);
        fbb.addOffset(2, customerIdOffset);
        fbb.addOffset(3, customerNameOffset);
        fbb.addOffset(4, categoryTypeOffset);
        fbb.addOffset(5, amountOffset);
        fbb.addOffset(6, paymentsDateOffset);
        fbb.addOffset(7, paymentIdOffset);
        fbb.addOffset(8, paymentsModeOffset);
        fbb.addOffset(9, paymentsNumberOffset);
        fbb.finish(fbb.endTable());
        return object.dbId;
      },
      objectFromFB: (obx.Store store, ByteData fbData) {
        final buffer = fb.BufferContext(fbData);
        final rootOffset = buffer.derefObject(0);
        final idParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 6, '');
        final customerIdParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 8, '');
        final customerNameParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 10, '');
        final categoryTypeParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 12, '');
        final amountParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 14, '');
        final paymentsDateParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 16, '');
        final paymentIdParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 18, '');
        final paymentsModeParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 20, '');
        final paymentsNumberParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 22, '');
        final object = CustomerPaymentModel(
          id: idParam,
          customerId: customerIdParam,
          customerName: customerNameParam,
          categoryType: categoryTypeParam,
          amount: amountParam,
          paymentsDate: paymentsDateParam,
          paymentId: paymentIdParam,
          paymentsMode: paymentsModeParam,
          paymentsNumber: paymentsNumberParam,
        )..dbId = const fb.Int64Reader().vTableGet(buffer, rootOffset, 4, 0);

        return object;
      },
    ),
    CreditNoteModel: obx_int.EntityDefinition<CreditNoteModel>(
      model: _entities[20],
      toOneRelations: (CreditNoteModel object) => [],
      toManyRelations: (CreditNoteModel object) => {},
      getId: (CreditNoteModel object) => object.dbId,
      setId: (CreditNoteModel object, int id) {
        object.dbId = id;
      },
      objectToFB: (CreditNoteModel object, fb.Builder fbb) {
        final customerIdOffset = fbb.writeString(object.customerId);
        final idOffset = fbb.writeString(object.id);
        final customerNameOffset = fbb.writeString(object.customerName);
        final creditNoteIdOffset = fbb.writeString(object.creditNoteId);
        final creditNoteNumberOffset = fbb.writeString(object.creditNoteNumber);
        final invoiceIdOffset = object.invoiceId == null
            ? null
            : fbb.writeString(object.invoiceId!);
        final productIdOffset = object.productId == null
            ? null
            : fbb.writeString(object.productId!);
        final itemNameOffset = fbb.writeString(object.itemName);
        final brandOffset = object.brand == null
            ? null
            : fbb.writeString(object.brand!);
        final itemCategoryOffset = object.itemCategory == null
            ? null
            : fbb.writeString(object.itemCategory!);
        final categoryTypeOffset = object.categoryType == null
            ? null
            : fbb.writeString(object.categoryType!);
        final tagOffset = object.tag == null
            ? null
            : fbb.writeString(object.tag!);
        final categoryOffset = object.category == null
            ? null
            : fbb.writeString(object.category!);
        fbb.startTable(18);
        fbb.addInt64(0, object.dbId);
        fbb.addOffset(1, customerIdOffset);
        fbb.addOffset(2, idOffset);
        fbb.addOffset(3, customerNameOffset);
        fbb.addInt64(4, object.date.millisecondsSinceEpoch);
        fbb.addOffset(5, creditNoteIdOffset);
        fbb.addOffset(6, creditNoteNumberOffset);
        fbb.addOffset(7, invoiceIdOffset);
        fbb.addOffset(8, productIdOffset);
        fbb.addOffset(9, itemNameOffset);
        fbb.addOffset(10, brandOffset);
        fbb.addOffset(11, itemCategoryOffset);
        fbb.addOffset(12, categoryTypeOffset);
        fbb.addInt64(13, object.quantity);
        fbb.addFloat64(14, object.amount);
        fbb.addOffset(15, tagOffset);
        fbb.addOffset(16, categoryOffset);
        fbb.finish(fbb.endTable());
        return object.dbId;
      },
      objectFromFB: (obx.Store store, ByteData fbData) {
        final buffer = fb.BufferContext(fbData);
        final rootOffset = buffer.derefObject(0);
        final idParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 8, '');
        final customerIdParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 6, '');
        final customerNameParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 10, '');
        final dateParam = DateTime.fromMillisecondsSinceEpoch(
          const fb.Int64Reader().vTableGet(buffer, rootOffset, 12, 0),
        );
        final creditNoteIdParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 14, '');
        final creditNoteNumberParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 16, '');
        final invoiceIdParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGetNullable(buffer, rootOffset, 18);
        final productIdParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGetNullable(buffer, rootOffset, 20);
        final itemNameParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 22, '');
        final brandParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGetNullable(buffer, rootOffset, 24);
        final itemCategoryParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGetNullable(buffer, rootOffset, 26);
        final categoryTypeParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGetNullable(buffer, rootOffset, 28);
        final quantityParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          30,
          0,
        );
        final amountParam = const fb.Float64Reader().vTableGet(
          buffer,
          rootOffset,
          32,
          0,
        );
        final tagParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGetNullable(buffer, rootOffset, 34);
        final categoryParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGetNullable(buffer, rootOffset, 36);
        final object = CreditNoteModel(
          id: idParam,
          customerId: customerIdParam,
          customerName: customerNameParam,
          date: dateParam,
          creditNoteId: creditNoteIdParam,
          creditNoteNumber: creditNoteNumberParam,
          invoiceId: invoiceIdParam,
          productId: productIdParam,
          itemName: itemNameParam,
          brand: brandParam,
          itemCategory: itemCategoryParam,
          categoryType: categoryTypeParam,
          quantity: quantityParam,
          amount: amountParam,
          tag: tagParam,
          category: categoryParam,
        )..dbId = const fb.Int64Reader().vTableGet(buffer, rootOffset, 4, 0);

        return object;
      },
    ),
    FarmerEntity: obx_int.EntityDefinition<FarmerEntity>(
      model: _entities[21],
      toOneRelations: (FarmerEntity object) => [],
      toManyRelations: (FarmerEntity object) => {
        obx_int.RelInfo<VisitEntity>.toOneBacklink(
          10,
          object.id,
          (VisitEntity srcObject) => srcObject.farmer,
        ): object.visits,
      },
      getId: (FarmerEntity object) => object.id,
      setId: (FarmerEntity object, int id) {
        object.id = id;
      },
      objectToFB: (FarmerEntity object, fb.Builder fbb) {
        final nameOffset = fbb.writeString(object.name);
        final mobileNumberOffset = fbb.writeString(object.mobileNumber);
        fbb.startTable(4);
        fbb.addInt64(0, object.id);
        fbb.addOffset(1, nameOffset);
        fbb.addOffset(2, mobileNumberOffset);
        fbb.finish(fbb.endTable());
        return object.id;
      },
      objectFromFB: (obx.Store store, ByteData fbData) {
        final buffer = fb.BufferContext(fbData);
        final rootOffset = buffer.derefObject(0);
        final nameParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 6, '');
        final mobileNumberParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 8, '');
        final object = FarmerEntity(
          name: nameParam,
          mobileNumber: mobileNumberParam,
        )..id = const fb.Int64Reader().vTableGet(buffer, rootOffset, 4, 0);
        obx_int.InternalToManyAccess.setRelInfo<FarmerEntity>(
          object.visits,
          store,
          obx_int.RelInfo<VisitEntity>.toOneBacklink(
            10,
            object.id,
            (VisitEntity srcObject) => srcObject.farmer,
          ),
        );
        return object;
      },
    ),
    VisitEntity: obx_int.EntityDefinition<VisitEntity>(
      model: _entities[22],
      toOneRelations: (VisitEntity object) => [object.farmer],
      toManyRelations: (VisitEntity object) => {},
      getId: (VisitEntity object) => object.id,
      setId: (VisitEntity object, int id) {
        object.id = id;
      },
      objectToFB: (VisitEntity object, fb.Builder fbb) {
        final mobileNumberOffset = fbb.writeString(object.mobileNumber);
        final productUsedOffset = fbb.writeString(object.productUsed);
        final pondIdOffset = fbb.writeString(object.pondId);
        final farmerNameOffset = fbb.writeString(object.farmerName);
        fbb.startTable(14);
        fbb.addInt64(0, object.id);
        fbb.addInt64(1, object.createdDateTimeMillis);
        fbb.addInt64(2, object.doc);
        fbb.addOffset(6, mobileNumberOffset);
        fbb.addOffset(8, productUsedOffset);
        fbb.addInt64(9, object.farmer.targetId);
        fbb.addOffset(11, pondIdOffset);
        fbb.addOffset(12, farmerNameOffset);
        fbb.finish(fbb.endTable());
        return object.id;
      },
      objectFromFB: (obx.Store store, ByteData fbData) {
        final buffer = fb.BufferContext(fbData);
        final rootOffset = buffer.derefObject(0);
        final createdDateTimeMillisParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          6,
          0,
        );
        final docParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          8,
          0,
        );
        final pondIdParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 26, '');
        final mobileNumberParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 16, '');
        final productUsedParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 20, '');
        final farmerNameParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 28, '');
        final object = VisitEntity(
          createdDateTimeMillis: createdDateTimeMillisParam,
          doc: docParam,
          pondId: pondIdParam,
          mobileNumber: mobileNumberParam,
          productUsed: productUsedParam,
          farmerName: farmerNameParam,
        )..id = const fb.Int64Reader().vTableGet(buffer, rootOffset, 4, 0);
        object.farmer.targetId = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          22,
          0,
        );
        object.farmer.attach(store);
        return object;
      },
    ),
  };

  return obx_int.ModelDefinition(model, bindings);
}

/// [UserModel] entity fields to define ObjectBox queries.
class UserModel_ {
  /// See [UserModel.id].
  static final id = obx.QueryIntegerProperty<UserModel>(
    _entities[0].properties[0],
  );

  /// See [UserModel.phoneNumber].
  static final phoneNumber = obx.QueryStringProperty<UserModel>(
    _entities[0].properties[1],
  );

  /// See [UserModel.isVerified].
  static final isVerified = obx.QueryBooleanProperty<UserModel>(
    _entities[0].properties[2],
  );

  /// See [UserModel.createdAt].
  static final createdAt = obx.QueryDateProperty<UserModel>(
    _entities[0].properties[3],
  );

  /// See [UserModel.updatedAt].
  static final updatedAt = obx.QueryDateProperty<UserModel>(
    _entities[0].properties[4],
  );

  /// See [UserModel.mongoId].
  static final mongoId = obx.QueryStringProperty<UserModel>(
    _entities[0].properties[5],
  );

  /// See [UserModel.needsSync].
  static final needsSync = obx.QueryBooleanProperty<UserModel>(
    _entities[0].properties[6],
  );
}

/// [ObjectBoxPriceListModel] entity fields to define ObjectBox queries.
class ObjectBoxPriceListModel_ {
  /// See [ObjectBoxPriceListModel.id].
  static final id = obx.QueryIntegerProperty<ObjectBoxPriceListModel>(
    _entities[1].properties[0],
  );

  /// See [ObjectBoxPriceListModel.state].
  static final state = obx.QueryStringProperty<ObjectBoxPriceListModel>(
    _entities[1].properties[1],
  );

  /// See [ObjectBoxPriceListModel.date].
  static final date = obx.QueryStringProperty<ObjectBoxPriceListModel>(
    _entities[1].properties[2],
  );

  /// See [ObjectBoxPriceListModel.pricesJson].
  static final pricesJson = obx.QueryStringProperty<ObjectBoxPriceListModel>(
    _entities[1].properties[3],
  );
}

/// [ObjectBoxProductCatalogueModel] entity fields to define ObjectBox queries.
class ObjectBoxProductCatalogueModel_ {
  /// See [ObjectBoxProductCatalogueModel.id].
  static final id = obx.QueryIntegerProperty<ObjectBoxProductCatalogueModel>(
    _entities[2].properties[0],
  );

  /// See [ObjectBoxProductCatalogueModel.name].
  static final name = obx.QueryStringProperty<ObjectBoxProductCatalogueModel>(
    _entities[2].properties[1],
  );

  /// See [ObjectBoxProductCatalogueModel.image].
  static final image = obx.QueryStringProperty<ObjectBoxProductCatalogueModel>(
    _entities[2].properties[2],
  );

  /// See [ObjectBoxProductCatalogueModel.sortOrder].
  static final sortOrder =
      obx.QueryStringProperty<ObjectBoxProductCatalogueModel>(
        _entities[2].properties[3],
      );

  /// See [ObjectBoxProductCatalogueModel.status].
  static final status = obx.QueryStringProperty<ObjectBoxProductCatalogueModel>(
    _entities[2].properties[4],
  );

  /// See [ObjectBoxProductCatalogueModel.productsJson].
  static final productsJson =
      obx.QueryStringProperty<ObjectBoxProductCatalogueModel>(
        _entities[2].properties[5],
      );
}

/// [ObjectBoxInterestedProductModel] entity fields to define ObjectBox queries.
class ObjectBoxInterestedProductModel_ {
  /// See [ObjectBoxInterestedProductModel.id].
  static final id = obx.QueryIntegerProperty<ObjectBoxInterestedProductModel>(
    _entities[3].properties[0],
  );

  /// See [ObjectBoxInterestedProductModel.mongoId].
  static final mongoId =
      obx.QueryStringProperty<ObjectBoxInterestedProductModel>(
        _entities[3].properties[1],
      );

  /// See [ObjectBoxInterestedProductModel.customerId].
  static final customerId =
      obx.QueryStringProperty<ObjectBoxInterestedProductModel>(
        _entities[3].properties[2],
      );

  /// See [ObjectBoxInterestedProductModel.mobile].
  static final mobile =
      obx.QueryStringProperty<ObjectBoxInterestedProductModel>(
        _entities[3].properties[3],
      );

  /// See [ObjectBoxInterestedProductModel.productName].
  static final productName =
      obx.QueryStringProperty<ObjectBoxInterestedProductModel>(
        _entities[3].properties[4],
      );

  /// See [ObjectBoxInterestedProductModel.datetimeString].
  static final datetimeString =
      obx.QueryStringProperty<ObjectBoxInterestedProductModel>(
        _entities[3].properties[5],
      );

  /// See [ObjectBoxInterestedProductModel.source].
  static final source =
      obx.QueryStringProperty<ObjectBoxInterestedProductModel>(
        _entities[3].properties[6],
      );

  /// See [ObjectBoxInterestedProductModel.isSynced].
  static final isSynced =
      obx.QueryBooleanProperty<ObjectBoxInterestedProductModel>(
        _entities[3].properties[7],
      );
}

/// [DashboardModel] entity fields to define ObjectBox queries.
class DashboardModel_ {
  /// See [DashboardModel.id].
  static final id = obx.QueryIntegerProperty<DashboardModel>(
    _entities[4].properties[0],
  );

  /// See [DashboardModel.customerId].
  static final customerId = obx.QueryStringProperty<DashboardModel>(
    _entities[4].properties[1],
  );

  /// See [DashboardModel.salesJson].
  static final salesJson = obx.QueryStringProperty<DashboardModel>(
    _entities[4].properties[2],
  );

  /// See [DashboardModel.paymentsJson].
  static final paymentsJson = obx.QueryStringProperty<DashboardModel>(
    _entities[4].properties[3],
  );

  /// See [DashboardModel.duesJson].
  static final duesJson = obx.QueryStringProperty<DashboardModel>(
    _entities[4].properties[4],
  );

  /// See [DashboardModel.categoryTypeSalesJson].
  static final categoryTypeSalesJson = obx.QueryStringProperty<DashboardModel>(
    _entities[4].properties[5],
  );

  /// See [DashboardModel.liquidationJson].
  static final liquidationJson = obx.QueryStringProperty<DashboardModel>(
    _entities[4].properties[6],
  );

  /// See [DashboardModel.myFarmersJson].
  static final myFarmersJson = obx.QueryStringProperty<DashboardModel>(
    _entities[4].properties[7],
  );

  /// See [DashboardModel.isSynced].
  static final isSynced = obx.QueryBooleanProperty<DashboardModel>(
    _entities[4].properties[8],
  );

  /// See [DashboardModel.lastSyncedAt].
  static final lastSyncedAt = obx.QueryDateProperty<DashboardModel>(
    _entities[4].properties[9],
  );

  /// See [DashboardModel.salesReturnJson].
  static final salesReturnJson = obx.QueryDoubleProperty<DashboardModel>(
    _entities[4].properties[10],
  );
}

/// [ObjectBoxCustomerModel] entity fields to define ObjectBox queries.
class ObjectBoxCustomerModel_ {
  /// See [ObjectBoxCustomerModel.id].
  static final id = obx.QueryIntegerProperty<ObjectBoxCustomerModel>(
    _entities[5].properties[0],
  );

  /// See [ObjectBoxCustomerModel.customerId].
  static final customerId = obx.QueryStringProperty<ObjectBoxCustomerModel>(
    _entities[5].properties[1],
  );

  /// See [ObjectBoxCustomerModel.customerName].
  static final customerName = obx.QueryStringProperty<ObjectBoxCustomerModel>(
    _entities[5].properties[2],
  );

  /// See [ObjectBoxCustomerModel.email].
  static final email = obx.QueryStringProperty<ObjectBoxCustomerModel>(
    _entities[5].properties[3],
  );

  /// See [ObjectBoxCustomerModel.mobileNumber].
  static final mobileNumber = obx.QueryStringProperty<ObjectBoxCustomerModel>(
    _entities[5].properties[4],
  );

  /// See [ObjectBoxCustomerModel.companyName].
  static final companyName = obx.QueryStringProperty<ObjectBoxCustomerModel>(
    _entities[5].properties[5],
  );

  /// See [ObjectBoxCustomerModel.gstNo].
  static final gstNo = obx.QueryStringProperty<ObjectBoxCustomerModel>(
    _entities[5].properties[6],
  );

  /// See [ObjectBoxCustomerModel.businessVertical].
  static final businessVertical =
      obx.QueryStringProperty<ObjectBoxCustomerModel>(
        _entities[5].properties[7],
      );

  /// See [ObjectBoxCustomerModel.customerCode].
  static final customerCode = obx.QueryStringProperty<ObjectBoxCustomerModel>(
    _entities[5].properties[8],
  );

  /// See [ObjectBoxCustomerModel.billingAddress].
  static final billingAddress = obx.QueryStringProperty<ObjectBoxCustomerModel>(
    _entities[5].properties[9],
  );

  /// See [ObjectBoxCustomerModel.isSynced].
  static final isSynced = obx.QueryBooleanProperty<ObjectBoxCustomerModel>(
    _entities[5].properties[10],
  );

  /// See [ObjectBoxCustomerModel.mongoId].
  static final mongoId = obx.QueryStringProperty<ObjectBoxCustomerModel>(
    _entities[5].properties[11],
  );
}

/// [CustomerSchemeModel] entity fields to define ObjectBox queries.
class CustomerSchemeModel_ {
  /// See [CustomerSchemeModel.id].
  static final id = obx.QueryIntegerProperty<CustomerSchemeModel>(
    _entities[6].properties[0],
  );

  /// See [CustomerSchemeModel.customerId].
  static final customerId = obx.QueryStringProperty<CustomerSchemeModel>(
    _entities[6].properties[1],
  );

  /// See [CustomerSchemeModel.customerName].
  static final customerName = obx.QueryStringProperty<CustomerSchemeModel>(
    _entities[6].properties[2],
  );

  /// See [CustomerSchemeModel.sales].
  static final sales = obx.QueryDoubleProperty<CustomerSchemeModel>(
    _entities[6].properties[3],
  );

  /// See [CustomerSchemeModel.payment].
  static final payment = obx.QueryDoubleProperty<CustomerSchemeModel>(
    _entities[6].properties[4],
  );

  /// see [CustomerSchemeModel.supportPersons]
  static final supportPersons =
      obx.QueryBacklinkToMany<SupportPersonModel, CustomerSchemeModel>(
        SupportPersonModel_.customerScheme,
      );
}

/// [PersonDetailsModel] entity fields to define ObjectBox queries.
class PersonDetailsModel_ {
  /// See [PersonDetailsModel.id].
  static final id = obx.QueryIntegerProperty<PersonDetailsModel>(
    _entities[7].properties[0],
  );

  /// See [PersonDetailsModel.rowId].
  static final rowId = obx.QueryStringProperty<PersonDetailsModel>(
    _entities[7].properties[1],
  );

  /// See [PersonDetailsModel.customerName].
  static final customerName = obx.QueryStringProperty<PersonDetailsModel>(
    _entities[7].properties[2],
  );

  /// See [PersonDetailsModel.email].
  static final email = obx.QueryStringProperty<PersonDetailsModel>(
    _entities[7].properties[3],
  );

  /// See [PersonDetailsModel.status].
  static final status = obx.QueryStringProperty<PersonDetailsModel>(
    _entities[7].properties[4],
  );

  /// See [PersonDetailsModel.mobileNumber].
  static final mobileNumber = obx.QueryStringProperty<PersonDetailsModel>(
    _entities[7].properties[5],
  );

  /// See [PersonDetailsModel.userId].
  static final userId = obx.QueryStringProperty<PersonDetailsModel>(
    _entities[7].properties[6],
  );

  /// See [PersonDetailsModel.modifiedTime].
  static final modifiedTime = obx.QueryDateProperty<PersonDetailsModel>(
    _entities[7].properties[7],
  );

  /// See [PersonDetailsModel.empId].
  static final empId = obx.QueryStringProperty<PersonDetailsModel>(
    _entities[7].properties[8],
  );

  /// See [PersonDetailsModel.profile].
  static final profile = obx.QueryStringProperty<PersonDetailsModel>(
    _entities[7].properties[9],
  );

  /// See [PersonDetailsModel.supportPerson].
  static final supportPerson =
      obx.QueryRelationToOne<PersonDetailsModel, SupportPersonModel>(
        _entities[7].properties[10],
      );
}

/// [SupportPersonModel] entity fields to define ObjectBox queries.
class SupportPersonModel_ {
  /// See [SupportPersonModel.id].
  static final id = obx.QueryIntegerProperty<SupportPersonModel>(
    _entities[8].properties[0],
  );

  /// See [SupportPersonModel.profile].
  static final profile = obx.QueryStringProperty<SupportPersonModel>(
    _entities[8].properties[1],
  );

  /// See [SupportPersonModel.customerScheme].
  static final customerScheme =
      obx.QueryRelationToOne<SupportPersonModel, CustomerSchemeModel>(
        _entities[8].properties[2],
      );

  /// see [SupportPersonModel.details]
  static final details =
      obx.QueryRelationToMany<SupportPersonModel, PersonDetailsModel>(
        _entities[8].relations[0],
      );
}

/// [AccountStatementEntryModel] entity fields to define ObjectBox queries.
class AccountStatementEntryModel_ {
  /// See [AccountStatementEntryModel.id].
  static final id = obx.QueryIntegerProperty<AccountStatementEntryModel>(
    _entities[9].properties[0],
  );

  /// See [AccountStatementEntryModel.txnDate].
  static final txnDate = obx.QueryDateProperty<AccountStatementEntryModel>(
    _entities[9].properties[1],
  );

  /// See [AccountStatementEntryModel.vchType].
  static final vchType = obx.QueryStringProperty<AccountStatementEntryModel>(
    _entities[9].properties[2],
  );

  /// See [AccountStatementEntryModel.invoiceNumber].
  static final invoiceNumber =
      obx.QueryStringProperty<AccountStatementEntryModel>(
        _entities[9].properties[3],
      );

  /// See [AccountStatementEntryModel.particulars].
  static final particulars =
      obx.QueryStringProperty<AccountStatementEntryModel>(
        _entities[9].properties[4],
      );

  /// See [AccountStatementEntryModel.debit].
  static final debit = obx.QueryDoubleProperty<AccountStatementEntryModel>(
    _entities[9].properties[5],
  );

  /// See [AccountStatementEntryModel.credit].
  static final credit = obx.QueryDoubleProperty<AccountStatementEntryModel>(
    _entities[9].properties[6],
  );

  /// See [AccountStatementEntryModel.balance].
  static final balance = obx.QueryDoubleProperty<AccountStatementEntryModel>(
    _entities[9].properties[7],
  );

  /// See [AccountStatementEntryModel.amount].
  static final amount = obx.QueryDoubleProperty<AccountStatementEntryModel>(
    _entities[9].properties[8],
  );

  /// See [AccountStatementEntryModel.accountStatement].
  static final accountStatement =
      obx.QueryRelationToOne<AccountStatementEntryModel, AccountStatementModel>(
        _entities[9].properties[9],
      );
}

/// [AccountStatementModel] entity fields to define ObjectBox queries.
class AccountStatementModel_ {
  /// See [AccountStatementModel.id].
  static final id = obx.QueryIntegerProperty<AccountStatementModel>(
    _entities[10].properties[0],
  );

  /// See [AccountStatementModel.customerId].
  static final customerId = obx.QueryStringProperty<AccountStatementModel>(
    _entities[10].properties[1],
  );

  /// See [AccountStatementModel.lastSyncTime].
  static final lastSyncTime = obx.QueryDateProperty<AccountStatementModel>(
    _entities[10].properties[2],
  );

  /// see [AccountStatementModel.entries]
  static final entries =
      obx.QueryBacklinkToMany<
        AccountStatementEntryModel,
        AccountStatementModel
      >(AccountStatementEntryModel_.accountStatement);
}

/// [ObjectBoxSMRReportModel] entity fields to define ObjectBox queries.
class ObjectBoxSMRReportModel_ {
  /// See [ObjectBoxSMRReportModel.dbId].
  static final dbId = obx.QueryIntegerProperty<ObjectBoxSMRReportModel>(
    _entities[11].properties[0],
  );

  /// See [ObjectBoxSMRReportModel.id].
  static final id = obx.QueryStringProperty<ObjectBoxSMRReportModel>(
    _entities[11].properties[1],
  );

  /// See [ObjectBoxSMRReportModel.so].
  static final so = obx.QueryStringProperty<ObjectBoxSMRReportModel>(
    _entities[11].properties[2],
  );

  /// See [ObjectBoxSMRReportModel.partner].
  static final partner = obx.QueryStringProperty<ObjectBoxSMRReportModel>(
    _entities[11].properties[3],
  );

  /// See [ObjectBoxSMRReportModel.partnerId].
  static final partnerId = obx.QueryStringProperty<ObjectBoxSMRReportModel>(
    _entities[11].properties[4],
  );

  /// See [ObjectBoxSMRReportModel.productName].
  static final productName = obx.QueryStringProperty<ObjectBoxSMRReportModel>(
    _entities[11].properties[5],
  );

  /// See [ObjectBoxSMRReportModel.startDateMillis].
  static final startDateMillis =
      obx.QueryIntegerProperty<ObjectBoxSMRReportModel>(
        _entities[11].properties[6],
      );

  /// See [ObjectBoxSMRReportModel.lastDateMillis].
  static final lastDateMillis =
      obx.QueryIntegerProperty<ObjectBoxSMRReportModel>(
        _entities[11].properties[7],
      );

  /// See [ObjectBoxSMRReportModel.openingBalance].
  static final openingBalance =
      obx.QueryIntegerProperty<ObjectBoxSMRReportModel>(
        _entities[11].properties[8],
      );

  /// See [ObjectBoxSMRReportModel.invoice].
  static final invoice = obx.QueryIntegerProperty<ObjectBoxSMRReportModel>(
    _entities[11].properties[9],
  );

  /// See [ObjectBoxSMRReportModel.srn].
  static final srn = obx.QueryIntegerProperty<ObjectBoxSMRReportModel>(
    _entities[11].properties[10],
  );

  /// See [ObjectBoxSMRReportModel.closingBalance].
  static final closingBalance =
      obx.QueryIntegerProperty<ObjectBoxSMRReportModel>(
        _entities[11].properties[11],
      );

  /// See [ObjectBoxSMRReportModel.sales].
  static final sales = obx.QueryIntegerProperty<ObjectBoxSMRReportModel>(
    _entities[11].properties[12],
  );

  /// See [ObjectBoxSMRReportModel.status].
  static final status = obx.QueryStringProperty<ObjectBoxSMRReportModel>(
    _entities[11].properties[13],
  );
}

/// [SalesOrderItemModel] entity fields to define ObjectBox queries.
class SalesOrderItemModel_ {
  /// See [SalesOrderItemModel.id].
  static final id = obx.QueryIntegerProperty<SalesOrderItemModel>(
    _entities[12].properties[0],
  );

  /// See [SalesOrderItemModel.itemId].
  static final itemId = obx.QueryStringProperty<SalesOrderItemModel>(
    _entities[12].properties[1],
  );

  /// See [SalesOrderItemModel.salesOrder].
  static final salesOrder =
      obx.QueryRelationToOne<SalesOrderItemModel, SalesOrderModel>(
        _entities[12].properties[2],
      );

  /// See [SalesOrderItemModel.productId].
  static final productId = obx.QueryStringProperty<SalesOrderItemModel>(
    _entities[12].properties[3],
  );

  /// See [SalesOrderItemModel.createdTime].
  static final createdTime = obx.QueryDateProperty<SalesOrderItemModel>(
    _entities[12].properties[4],
  );

  /// See [SalesOrderItemModel.entityDiscountPercent].
  static final entityDiscountPercent =
      obx.QueryStringProperty<SalesOrderItemModel>(_entities[12].properties[5]);

  /// See [SalesOrderItemModel.hsnSac].
  static final hsnSac = obx.QueryStringProperty<SalesOrderItemModel>(
    _entities[12].properties[6],
  );

  /// See [SalesOrderItemModel.invoicedQuantityCancelled].
  static final invoicedQuantityCancelled =
      obx.QueryStringProperty<SalesOrderItemModel>(_entities[12].properties[7]);

  /// See [SalesOrderItemModel.itemName].
  static final itemName = obx.QueryStringProperty<SalesOrderItemModel>(
    _entities[12].properties[8],
  );

  /// See [SalesOrderItemModel.itemPrice].
  static final itemPrice = obx.QueryDoubleProperty<SalesOrderItemModel>(
    _entities[12].properties[9],
  );

  /// See [SalesOrderItemModel.lastModifiedTime].
  static final lastModifiedTime = obx.QueryDateProperty<SalesOrderItemModel>(
    _entities[12].properties[10],
  );

  /// See [SalesOrderItemModel.manuallyFulfilledQuantity].
  static final manuallyFulfilledQuantity =
      obx.QueryStringProperty<SalesOrderItemModel>(
        _entities[12].properties[11],
      );

  /// See [SalesOrderItemModel.nonPackageQuantity].
  static final nonPackageQuantity =
      obx.QueryStringProperty<SalesOrderItemModel>(
        _entities[12].properties[12],
      );

  /// See [SalesOrderItemModel.placeOfSupply].
  static final placeOfSupply = obx.QueryStringProperty<SalesOrderItemModel>(
    _entities[12].properties[13],
  );

  /// See [SalesOrderItemModel.quantityDelivered].
  static final quantityDelivered = obx.QueryStringProperty<SalesOrderItemModel>(
    _entities[12].properties[14],
  );

  /// See [SalesOrderItemModel.quantityDropshipped].
  static final quantityDropshipped =
      obx.QueryStringProperty<SalesOrderItemModel>(
        _entities[12].properties[15],
      );

  /// See [SalesOrderItemModel.quantityPacked].
  static final quantityPacked = obx.QueryStringProperty<SalesOrderItemModel>(
    _entities[12].properties[16],
  );

  /// See [SalesOrderItemModel.salesVertices].
  static final salesVertices = obx.QueryStringProperty<SalesOrderItemModel>(
    _entities[12].properties[17],
  );

  /// See [SalesOrderItemModel.sno].
  static final sno = obx.QueryStringProperty<SalesOrderItemModel>(
    _entities[12].properties[18],
  );

  /// See [SalesOrderItemModel.total].
  static final total = obx.QueryDoubleProperty<SalesOrderItemModel>(
    _entities[12].properties[19],
  );

  /// See [SalesOrderItemModel.salesOrderIdValue].
  static final salesOrderIdValue = obx.QueryStringProperty<SalesOrderItemModel>(
    _entities[12].properties[20],
  );
}

/// [SalesOrderModel] entity fields to define ObjectBox queries.
class SalesOrderModel_ {
  /// See [SalesOrderModel.id].
  static final id = obx.QueryIntegerProperty<SalesOrderModel>(
    _entities[13].properties[0],
  );

  /// See [SalesOrderModel.salesOrderId].
  static final salesOrderId = obx.QueryStringProperty<SalesOrderModel>(
    _entities[13].properties[1],
  );

  /// See [SalesOrderModel.addressId].
  static final addressId = obx.QueryStringProperty<SalesOrderModel>(
    _entities[13].properties[2],
  );

  /// See [SalesOrderModel.createdTime].
  static final createdTime = obx.QueryDateProperty<SalesOrderModel>(
    _entities[13].properties[3],
  );

  /// See [SalesOrderModel.customerId].
  static final customerId = obx.QueryStringProperty<SalesOrderModel>(
    _entities[13].properties[4],
  );

  /// See [SalesOrderModel.invoicedStatus].
  static final invoicedStatus = obx.QueryStringProperty<SalesOrderModel>(
    _entities[13].properties[5],
  );

  /// See [SalesOrderModel.lastModifiedTime].
  static final lastModifiedTime = obx.QueryDateProperty<SalesOrderModel>(
    _entities[13].properties[6],
  );

  /// See [SalesOrderModel.orderSource].
  static final orderSource = obx.QueryStringProperty<SalesOrderModel>(
    _entities[13].properties[7],
  );

  /// See [SalesOrderModel.paidStatus].
  static final paidStatus = obx.QueryStringProperty<SalesOrderModel>(
    _entities[13].properties[8],
  );

  /// See [SalesOrderModel.paymentTermsLabel].
  static final paymentTermsLabel = obx.QueryStringProperty<SalesOrderModel>(
    _entities[13].properties[9],
  );

  /// See [SalesOrderModel.saleOrderDate].
  static final saleOrderDate = obx.QueryStringProperty<SalesOrderModel>(
    _entities[13].properties[10],
  );

  /// See [SalesOrderModel.salesChannel].
  static final salesChannel = obx.QueryStringProperty<SalesOrderModel>(
    _entities[13].properties[11],
  );

  /// See [SalesOrderModel.salesOrderNumber].
  static final salesOrderNumber = obx.QueryStringProperty<SalesOrderModel>(
    _entities[13].properties[12],
  );

  /// See [SalesOrderModel.subTotal].
  static final subTotal = obx.QueryDoubleProperty<SalesOrderModel>(
    _entities[13].properties[13],
  );

  /// See [SalesOrderModel.total].
  static final total = obx.QueryDoubleProperty<SalesOrderModel>(
    _entities[13].properties[14],
  );

  /// See [SalesOrderModel.isSynced].
  static final isSynced = obx.QueryBooleanProperty<SalesOrderModel>(
    _entities[13].properties[15],
  );

  /// See [SalesOrderModel.lastSyncedAt].
  static final lastSyncedAt = obx.QueryDateProperty<SalesOrderModel>(
    _entities[13].properties[16],
  );

  /// see [SalesOrderModel.items]
  static final items =
      obx.QueryBacklinkToMany<SalesOrderItemModel, SalesOrderModel>(
        SalesOrderItemModel_.salesOrder,
      );
}

/// [DuesInvoiceModel] entity fields to define ObjectBox queries.
class DuesInvoiceModel_ {
  /// See [DuesInvoiceModel.dbId].
  static final dbId = obx.QueryIntegerProperty<DuesInvoiceModel>(
    _entities[14].properties[0],
  );

  /// See [DuesInvoiceModel.id].
  static final id = obx.QueryStringProperty<DuesInvoiceModel>(
    _entities[14].properties[1],
  );

  /// See [DuesInvoiceModel.invoiceId].
  static final invoiceId = obx.QueryStringProperty<DuesInvoiceModel>(
    _entities[14].properties[2],
  );

  /// See [DuesInvoiceModel.invoiceNumber].
  static final invoiceNumber = obx.QueryStringProperty<DuesInvoiceModel>(
    _entities[14].properties[3],
  );

  /// See [DuesInvoiceModel.invoiceDate].
  static final invoiceDate = obx.QueryStringProperty<DuesInvoiceModel>(
    _entities[14].properties[4],
  );

  /// See [DuesInvoiceModel.customerId].
  static final customerId = obx.QueryStringProperty<DuesInvoiceModel>(
    _entities[14].properties[5],
  );

  /// See [DuesInvoiceModel.customerCode].
  static final customerCode = obx.QueryStringProperty<DuesInvoiceModel>(
    _entities[14].properties[6],
  );

  /// See [DuesInvoiceModel.customerName].
  static final customerName = obx.QueryStringProperty<DuesInvoiceModel>(
    _entities[14].properties[7],
  );

  /// See [DuesInvoiceModel.customerDistrict].
  static final customerDistrict = obx.QueryStringProperty<DuesInvoiceModel>(
    _entities[14].properties[8],
  );

  /// See [DuesInvoiceModel.customerState].
  static final customerState = obx.QueryStringProperty<DuesInvoiceModel>(
    _entities[14].properties[9],
  );

  /// See [DuesInvoiceModel.customerType].
  static final customerType = obx.QueryStringProperty<DuesInvoiceModel>(
    _entities[14].properties[10],
  );

  /// See [DuesInvoiceModel.onBoardedTime].
  static final onBoardedTime = obx.QueryStringProperty<DuesInvoiceModel>(
    _entities[14].properties[11],
  );

  /// See [DuesInvoiceModel.categoryType].
  static final categoryType = obx.QueryStringProperty<DuesInvoiceModel>(
    _entities[14].properties[12],
  );

  /// See [DuesInvoiceModel.invoiceType].
  static final invoiceType = obx.QueryStringProperty<DuesInvoiceModel>(
    _entities[14].properties[13],
  );

  /// See [DuesInvoiceModel.retailType].
  static final retailType = obx.QueryStringProperty<DuesInvoiceModel>(
    _entities[14].properties[14],
  );

  /// See [DuesInvoiceModel.invoiceStatus].
  static final invoiceStatus = obx.QueryStringProperty<DuesInvoiceModel>(
    _entities[14].properties[15],
  );

  /// See [DuesInvoiceModel.invoiceRaisedBy].
  static final invoiceRaisedBy = obx.QueryStringProperty<DuesInvoiceModel>(
    _entities[14].properties[16],
  );

  /// See [DuesInvoiceModel.mode].
  static final mode = obx.QueryStringProperty<DuesInvoiceModel>(
    _entities[14].properties[17],
  );

  /// See [DuesInvoiceModel.businessVertical].
  static final businessVertical = obx.QueryStringProperty<DuesInvoiceModel>(
    _entities[14].properties[18],
  );

  /// See [DuesInvoiceModel.feedCreditLimit].
  static final feedCreditLimit = obx.QueryStringProperty<DuesInvoiceModel>(
    _entities[14].properties[19],
  );

  /// See [DuesInvoiceModel.nonFeedCreditLimit].
  static final nonFeedCreditLimit = obx.QueryStringProperty<DuesInvoiceModel>(
    _entities[14].properties[20],
  );

  /// See [DuesInvoiceModel.harvestCreditLimit].
  static final harvestCreditLimit = obx.QueryStringProperty<DuesInvoiceModel>(
    _entities[14].properties[21],
  );

  /// See [DuesInvoiceModel.totalSalesInclTax].
  static final totalSalesInclTax = obx.QueryDoubleProperty<DuesInvoiceModel>(
    _entities[14].properties[22],
  );

  /// See [DuesInvoiceModel.totalSalesExclTax].
  static final totalSalesExclTax = obx.QueryDoubleProperty<DuesInvoiceModel>(
    _entities[14].properties[23],
  );

  /// See [DuesInvoiceModel.tcsAmount].
  static final tcsAmount = obx.QueryDoubleProperty<DuesInvoiceModel>(
    _entities[14].properties[24],
  );

  /// See [DuesInvoiceModel.tdsAmount].
  static final tdsAmount = obx.QueryDoubleProperty<DuesInvoiceModel>(
    _entities[14].properties[25],
  );

  /// See [DuesInvoiceModel.amountAfterTcs].
  static final amountAfterTcs = obx.QueryDoubleProperty<DuesInvoiceModel>(
    _entities[14].properties[26],
  );

  /// See [DuesInvoiceModel.shippingCharges].
  static final shippingCharges = obx.QueryDoubleProperty<DuesInvoiceModel>(
    _entities[14].properties[27],
  );

  /// See [DuesInvoiceModel.feedAmountAfterTcs].
  static final feedAmountAfterTcs = obx.QueryStringProperty<DuesInvoiceModel>(
    _entities[14].properties[28],
  );

  /// See [DuesInvoiceModel.nonFeedAmountAfterTcs].
  static final nonFeedAmountAfterTcs =
      obx.QueryDoubleProperty<DuesInvoiceModel>(_entities[14].properties[29]);

  /// See [DuesInvoiceModel.creditNoteAmountWithTcs].
  static final creditNoteAmountWithTcs =
      obx.QueryDoubleProperty<DuesInvoiceModel>(_entities[14].properties[30]);

  /// See [DuesInvoiceModel.payableAmount].
  static final payableAmount = obx.QueryDoubleProperty<DuesInvoiceModel>(
    _entities[14].properties[31],
  );

  /// See [DuesInvoiceModel.paidAmount].
  static final paidAmount = obx.QueryDoubleProperty<DuesInvoiceModel>(
    _entities[14].properties[32],
  );

  /// See [DuesInvoiceModel.due].
  static final due = obx.QueryDoubleProperty<DuesInvoiceModel>(
    _entities[14].properties[33],
  );

  /// See [DuesInvoiceModel.dueDate].
  static final dueDate = obx.QueryStringProperty<DuesInvoiceModel>(
    _entities[14].properties[34],
  );

  /// See [DuesInvoiceModel.dueDays].
  static final dueDays = obx.QueryIntegerProperty<DuesInvoiceModel>(
    _entities[14].properties[35],
  );

  /// See [DuesInvoiceModel.aging].
  static final aging = obx.QueryStringProperty<DuesInvoiceModel>(
    _entities[14].properties[36],
  );

  /// See [DuesInvoiceModel.aging1].
  static final aging1 = obx.QueryStringProperty<DuesInvoiceModel>(
    _entities[14].properties[37],
  );

  /// See [DuesInvoiceModel.paymentCredibility].
  static final paymentCredibility = obx.QueryStringProperty<DuesInvoiceModel>(
    _entities[14].properties[38],
  );

  /// See [DuesInvoiceModel.lastSyncTimestamp].
  static final lastSyncTimestamp = obx.QueryIntegerProperty<DuesInvoiceModel>(
    _entities[14].properties[39],
  );

  /// See [DuesInvoiceModel.agingGroup].
  static final agingGroup = obx.QueryStringProperty<DuesInvoiceModel>(
    _entities[14].properties[40],
  );

  /// See [DuesInvoiceModel.totalPurchase].
  static final totalPurchase = obx.QueryDoubleProperty<DuesInvoiceModel>(
    _entities[14].properties[41],
  );

  /// See [DuesInvoiceModel.totalSales].
  static final totalSales = obx.QueryDoubleProperty<DuesInvoiceModel>(
    _entities[14].properties[42],
  );

  /// See [DuesInvoiceModel.salesTier].
  static final salesTier = obx.QueryStringProperty<DuesInvoiceModel>(
    _entities[14].properties[43],
  );

  /// See [DuesInvoiceModel.healthcareSales].
  static final healthcareSales = obx.QueryDoubleProperty<DuesInvoiceModel>(
    _entities[14].properties[44],
  );

  /// See [DuesInvoiceModel.feedSales].
  static final feedSales = obx.QueryDoubleProperty<DuesInvoiceModel>(
    _entities[14].properties[45],
  );

  /// See [DuesInvoiceModel.chemicalSales].
  static final chemicalSales = obx.QueryDoubleProperty<DuesInvoiceModel>(
    _entities[14].properties[46],
  );

  /// See [DuesInvoiceModel.equipmentSales].
  static final equipmentSales = obx.QueryDoubleProperty<DuesInvoiceModel>(
    _entities[14].properties[47],
  );

  /// See [DuesInvoiceModel.harvestSales].
  static final harvestSales = obx.QueryDoubleProperty<DuesInvoiceModel>(
    _entities[14].properties[48],
  );

  /// See [DuesInvoiceModel.grossMargin].
  static final grossMargin = obx.QueryDoubleProperty<DuesInvoiceModel>(
    _entities[14].properties[49],
  );

  /// See [DuesInvoiceModel.lastPaidDate].
  static final lastPaidDate = obx.QueryStringProperty<DuesInvoiceModel>(
    _entities[14].properties[50],
  );
}

/// [DuesSummaryModel] entity fields to define ObjectBox queries.
class DuesSummaryModel_ {
  /// See [DuesSummaryModel.dbId].
  static final dbId = obx.QueryIntegerProperty<DuesSummaryModel>(
    _entities[15].properties[0],
  );

  /// See [DuesSummaryModel.customerId].
  static final customerId = obx.QueryStringProperty<DuesSummaryModel>(
    _entities[15].properties[1],
  );

  /// See [DuesSummaryModel.lastSyncTimestamp].
  static final lastSyncTimestamp = obx.QueryIntegerProperty<DuesSummaryModel>(
    _entities[15].properties[2],
  );

  /// See [DuesSummaryModel.totalDue].
  static final totalDue = obx.QueryDoubleProperty<DuesSummaryModel>(
    _entities[15].properties[3],
  );
}

/// [DuesAgingGroupModel] entity fields to define ObjectBox queries.
class DuesAgingGroupModel_ {
  /// See [DuesAgingGroupModel.dbId].
  static final dbId = obx.QueryIntegerProperty<DuesAgingGroupModel>(
    _entities[16].properties[0],
  );

  /// See [DuesAgingGroupModel.customerId].
  static final customerId = obx.QueryStringProperty<DuesAgingGroupModel>(
    _entities[16].properties[1],
  );

  /// See [DuesAgingGroupModel.totalPayableAmount].
  static final totalPayableAmount =
      obx.QueryDoubleProperty<DuesAgingGroupModel>(_entities[16].properties[2]);

  /// See [DuesAgingGroupModel.aging].
  static final aging = obx.QueryStringProperty<DuesAgingGroupModel>(
    _entities[16].properties[3],
  );

  /// See [DuesAgingGroupModel.dueDays].
  static final dueDays = obx.QueryIntegerProperty<DuesAgingGroupModel>(
    _entities[16].properties[4],
  );

  /// See [DuesAgingGroupModel.lastSyncTimestamp].
  static final lastSyncTimestamp =
      obx.QueryIntegerProperty<DuesAgingGroupModel>(
        _entities[16].properties[5],
      );
}

/// [ObjectBoxInvoiceItemModel] entity fields to define ObjectBox queries.
class ObjectBoxInvoiceItemModel_ {
  /// See [ObjectBoxInvoiceItemModel.id].
  static final id = obx.QueryIntegerProperty<ObjectBoxInvoiceItemModel>(
    _entities[17].properties[0],
  );

  /// See [ObjectBoxInvoiceItemModel.itemId].
  static final itemId = obx.QueryStringProperty<ObjectBoxInvoiceItemModel>(
    _entities[17].properties[1],
  );

  /// See [ObjectBoxInvoiceItemModel.productId].
  static final productId = obx.QueryStringProperty<ObjectBoxInvoiceItemModel>(
    _entities[17].properties[2],
  );

  /// See [ObjectBoxInvoiceItemModel.itemName].
  static final itemName = obx.QueryStringProperty<ObjectBoxInvoiceItemModel>(
    _entities[17].properties[3],
  );

  /// See [ObjectBoxInvoiceItemModel.quantity].
  static final quantity = obx.QueryDoubleProperty<ObjectBoxInvoiceItemModel>(
    _entities[17].properties[4],
  );

  /// See [ObjectBoxInvoiceItemModel.invoice].
  static final invoice =
      obx.QueryRelationToOne<ObjectBoxInvoiceItemModel, ObjectBoxInvoiceModel>(
        _entities[17].properties[5],
      );

  /// See [ObjectBoxInvoiceItemModel.createdTime].
  static final createdTime =
      obx.QueryDateNanoProperty<ObjectBoxInvoiceItemModel>(
        _entities[17].properties[6],
      );

  /// See [ObjectBoxInvoiceItemModel.discountAmount].
  static final discountAmount =
      obx.QueryDoubleProperty<ObjectBoxInvoiceItemModel>(
        _entities[17].properties[7],
      );

  /// See [ObjectBoxInvoiceItemModel.hsnSac].
  static final hsnSac = obx.QueryStringProperty<ObjectBoxInvoiceItemModel>(
    _entities[17].properties[8],
  );

  /// See [ObjectBoxInvoiceItemModel.itemPrice].
  static final itemPrice = obx.QueryDoubleProperty<ObjectBoxInvoiceItemModel>(
    _entities[17].properties[9],
  );

  /// See [ObjectBoxInvoiceItemModel.lastModifiedTime].
  static final lastModifiedTime =
      obx.QueryDateNanoProperty<ObjectBoxInvoiceItemModel>(
        _entities[17].properties[10],
      );

  /// See [ObjectBoxInvoiceItemModel.placeOfSupply].
  static final placeOfSupply =
      obx.QueryStringProperty<ObjectBoxInvoiceItemModel>(
        _entities[17].properties[11],
      );

  /// See [ObjectBoxInvoiceItemModel.productCategory].
  static final productCategory =
      obx.QueryStringProperty<ObjectBoxInvoiceItemModel>(
        _entities[17].properties[12],
      );

  /// See [ObjectBoxInvoiceItemModel.source].
  static final source = obx.QueryStringProperty<ObjectBoxInvoiceItemModel>(
    _entities[17].properties[13],
  );

  /// See [ObjectBoxInvoiceItemModel.subTotal].
  static final subTotal = obx.QueryDoubleProperty<ObjectBoxInvoiceItemModel>(
    _entities[17].properties[14],
  );

  /// See [ObjectBoxInvoiceItemModel.total].
  static final total = obx.QueryDoubleProperty<ObjectBoxInvoiceItemModel>(
    _entities[17].properties[15],
  );

  /// See [ObjectBoxInvoiceItemModel.invoiceIdValue].
  static final invoiceIdValue =
      obx.QueryStringProperty<ObjectBoxInvoiceItemModel>(
        _entities[17].properties[16],
      );
}

/// [ObjectBoxInvoiceModel] entity fields to define ObjectBox queries.
class ObjectBoxInvoiceModel_ {
  /// See [ObjectBoxInvoiceModel.id].
  static final id = obx.QueryIntegerProperty<ObjectBoxInvoiceModel>(
    _entities[18].properties[0],
  );

  /// See [ObjectBoxInvoiceModel.invoiceId].
  static final invoiceId = obx.QueryStringProperty<ObjectBoxInvoiceModel>(
    _entities[18].properties[1],
  );

  /// See [ObjectBoxInvoiceModel.addressId].
  static final addressId = obx.QueryStringProperty<ObjectBoxInvoiceModel>(
    _entities[18].properties[2],
  );

  /// See [ObjectBoxInvoiceModel.ageInDays].
  static final ageInDays = obx.QueryIntegerProperty<ObjectBoxInvoiceModel>(
    _entities[18].properties[3],
  );

  /// See [ObjectBoxInvoiceModel.ageTier].
  static final ageTier = obx.QueryStringProperty<ObjectBoxInvoiceModel>(
    _entities[18].properties[4],
  );

  /// See [ObjectBoxInvoiceModel.balance].
  static final balance = obx.QueryDoubleProperty<ObjectBoxInvoiceModel>(
    _entities[18].properties[5],
  );

  /// See [ObjectBoxInvoiceModel.customerId].
  static final customerId = obx.QueryStringProperty<ObjectBoxInvoiceModel>(
    _entities[18].properties[6],
  );

  /// See [ObjectBoxInvoiceModel.deliveryMode].
  static final deliveryMode = obx.QueryStringProperty<ObjectBoxInvoiceModel>(
    _entities[18].properties[7],
  );

  /// See [ObjectBoxInvoiceModel.deliveryStatus].
  static final deliveryStatus = obx.QueryStringProperty<ObjectBoxInvoiceModel>(
    _entities[18].properties[8],
  );

  /// See [ObjectBoxInvoiceModel.dueDate].
  static final dueDate = obx.QueryDateNanoProperty<ObjectBoxInvoiceModel>(
    _entities[18].properties[9],
  );

  /// See [ObjectBoxInvoiceModel.invoiceDate].
  static final invoiceDate = obx.QueryDateNanoProperty<ObjectBoxInvoiceModel>(
    _entities[18].properties[10],
  );

  /// See [ObjectBoxInvoiceModel.invoiceNumber].
  static final invoiceNumber = obx.QueryStringProperty<ObjectBoxInvoiceModel>(
    _entities[18].properties[11],
  );

  /// See [ObjectBoxInvoiceModel.invoiceStatus].
  static final invoiceStatus = obx.QueryStringProperty<ObjectBoxInvoiceModel>(
    _entities[18].properties[12],
  );

  /// See [ObjectBoxInvoiceModel.subTotal].
  static final subTotal = obx.QueryDoubleProperty<ObjectBoxInvoiceModel>(
    _entities[18].properties[13],
  );

  /// See [ObjectBoxInvoiceModel.total].
  static final total = obx.QueryDoubleProperty<ObjectBoxInvoiceModel>(
    _entities[18].properties[14],
  );

  /// see [ObjectBoxInvoiceModel.items]
  static final items =
      obx.QueryBacklinkToMany<ObjectBoxInvoiceItemModel, ObjectBoxInvoiceModel>(
        ObjectBoxInvoiceItemModel_.invoice,
      );
}

/// [CustomerPaymentModel] entity fields to define ObjectBox queries.
class CustomerPaymentModel_ {
  /// See [CustomerPaymentModel.dbId].
  static final dbId = obx.QueryIntegerProperty<CustomerPaymentModel>(
    _entities[19].properties[0],
  );

  /// See [CustomerPaymentModel.id].
  static final id = obx.QueryStringProperty<CustomerPaymentModel>(
    _entities[19].properties[1],
  );

  /// See [CustomerPaymentModel.customerId].
  static final customerId = obx.QueryStringProperty<CustomerPaymentModel>(
    _entities[19].properties[2],
  );

  /// See [CustomerPaymentModel.customerName].
  static final customerName = obx.QueryStringProperty<CustomerPaymentModel>(
    _entities[19].properties[3],
  );

  /// See [CustomerPaymentModel.categoryType].
  static final categoryType = obx.QueryStringProperty<CustomerPaymentModel>(
    _entities[19].properties[4],
  );

  /// See [CustomerPaymentModel.amount].
  static final amount = obx.QueryStringProperty<CustomerPaymentModel>(
    _entities[19].properties[5],
  );

  /// See [CustomerPaymentModel.paymentsDate].
  static final paymentsDate = obx.QueryStringProperty<CustomerPaymentModel>(
    _entities[19].properties[6],
  );

  /// See [CustomerPaymentModel.paymentId].
  static final paymentId = obx.QueryStringProperty<CustomerPaymentModel>(
    _entities[19].properties[7],
  );

  /// See [CustomerPaymentModel.paymentsMode].
  static final paymentsMode = obx.QueryStringProperty<CustomerPaymentModel>(
    _entities[19].properties[8],
  );

  /// See [CustomerPaymentModel.paymentsNumber].
  static final paymentsNumber = obx.QueryStringProperty<CustomerPaymentModel>(
    _entities[19].properties[9],
  );
}

/// [CreditNoteModel] entity fields to define ObjectBox queries.
class CreditNoteModel_ {
  /// See [CreditNoteModel.dbId].
  static final dbId = obx.QueryIntegerProperty<CreditNoteModel>(
    _entities[20].properties[0],
  );

  /// See [CreditNoteModel.customerId].
  static final customerId = obx.QueryStringProperty<CreditNoteModel>(
    _entities[20].properties[1],
  );

  /// See [CreditNoteModel.id].
  static final id = obx.QueryStringProperty<CreditNoteModel>(
    _entities[20].properties[2],
  );

  /// See [CreditNoteModel.customerName].
  static final customerName = obx.QueryStringProperty<CreditNoteModel>(
    _entities[20].properties[3],
  );

  /// See [CreditNoteModel.date].
  static final date = obx.QueryDateProperty<CreditNoteModel>(
    _entities[20].properties[4],
  );

  /// See [CreditNoteModel.creditNoteId].
  static final creditNoteId = obx.QueryStringProperty<CreditNoteModel>(
    _entities[20].properties[5],
  );

  /// See [CreditNoteModel.creditNoteNumber].
  static final creditNoteNumber = obx.QueryStringProperty<CreditNoteModel>(
    _entities[20].properties[6],
  );

  /// See [CreditNoteModel.invoiceId].
  static final invoiceId = obx.QueryStringProperty<CreditNoteModel>(
    _entities[20].properties[7],
  );

  /// See [CreditNoteModel.productId].
  static final productId = obx.QueryStringProperty<CreditNoteModel>(
    _entities[20].properties[8],
  );

  /// See [CreditNoteModel.itemName].
  static final itemName = obx.QueryStringProperty<CreditNoteModel>(
    _entities[20].properties[9],
  );

  /// See [CreditNoteModel.brand].
  static final brand = obx.QueryStringProperty<CreditNoteModel>(
    _entities[20].properties[10],
  );

  /// See [CreditNoteModel.itemCategory].
  static final itemCategory = obx.QueryStringProperty<CreditNoteModel>(
    _entities[20].properties[11],
  );

  /// See [CreditNoteModel.categoryType].
  static final categoryType = obx.QueryStringProperty<CreditNoteModel>(
    _entities[20].properties[12],
  );

  /// See [CreditNoteModel.quantity].
  static final quantity = obx.QueryIntegerProperty<CreditNoteModel>(
    _entities[20].properties[13],
  );

  /// See [CreditNoteModel.amount].
  static final amount = obx.QueryDoubleProperty<CreditNoteModel>(
    _entities[20].properties[14],
  );

  /// See [CreditNoteModel.tag].
  static final tag = obx.QueryStringProperty<CreditNoteModel>(
    _entities[20].properties[15],
  );

  /// See [CreditNoteModel.category].
  static final category = obx.QueryStringProperty<CreditNoteModel>(
    _entities[20].properties[16],
  );
}

/// [FarmerEntity] entity fields to define ObjectBox queries.
class FarmerEntity_ {
  /// See [FarmerEntity.id].
  static final id = obx.QueryIntegerProperty<FarmerEntity>(
    _entities[21].properties[0],
  );

  /// See [FarmerEntity.name].
  static final name = obx.QueryStringProperty<FarmerEntity>(
    _entities[21].properties[1],
  );

  /// See [FarmerEntity.mobileNumber].
  static final mobileNumber = obx.QueryStringProperty<FarmerEntity>(
    _entities[21].properties[2],
  );

  /// see [FarmerEntity.visits]
  static final visits = obx.QueryBacklinkToMany<VisitEntity, FarmerEntity>(
    VisitEntity_.farmer,
  );
}

/// [VisitEntity] entity fields to define ObjectBox queries.
class VisitEntity_ {
  /// See [VisitEntity.id].
  static final id = obx.QueryIntegerProperty<VisitEntity>(
    _entities[22].properties[0],
  );

  /// See [VisitEntity.createdDateTimeMillis].
  static final createdDateTimeMillis = obx.QueryIntegerProperty<VisitEntity>(
    _entities[22].properties[1],
  );

  /// See [VisitEntity.doc].
  static final doc = obx.QueryIntegerProperty<VisitEntity>(
    _entities[22].properties[2],
  );

  /// See [VisitEntity.mobileNumber].
  static final mobileNumber = obx.QueryStringProperty<VisitEntity>(
    _entities[22].properties[3],
  );

  /// See [VisitEntity.productUsed].
  static final productUsed = obx.QueryStringProperty<VisitEntity>(
    _entities[22].properties[4],
  );

  /// See [VisitEntity.farmer].
  static final farmer = obx.QueryRelationToOne<VisitEntity, FarmerEntity>(
    _entities[22].properties[5],
  );

  /// See [VisitEntity.pondId].
  static final pondId = obx.QueryStringProperty<VisitEntity>(
    _entities[22].properties[6],
  );

  /// See [VisitEntity.farmerName].
  static final farmerName = obx.QueryStringProperty<VisitEntity>(
    _entities[22].properties[7],
  );
}
