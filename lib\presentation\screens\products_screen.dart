import 'package:aqua_ui/aqua_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../core/mixins/analytics_mixin.dart';
import '../../core/routes/app_router.dart';
import '../../domain/entities/product.dart';
import '../../domain/entities/product_catalogue.dart';
import '../cubit/product_catalogue/product_catalogue_cubit.dart';
import '../cubit/product_catalogue/product_catalogue_state.dart';
import '../widgets/category_products_list_view_widget.dart';

class ProductsScreen extends StatefulWidget {
  const ProductsScreen({super.key});

  @override
  State<ProductsScreen> createState() => _ProductCatalogueScreenState();
}

class _ProductCatalogueScreenState extends State<ProductsScreen>
    with SingleTickerProviderStateMixin, AnalyticsMixin {
  late TabController _tabController;

  // Track screen view time
  DateTime? _screenViewStartTime;
  DateTime? _lastInteractionTime;

  // Track user interactions
  int _tabSwitchCount = 0;
  int _categoryFilterCount = 0;

  @override
  String get screenName => 'products';

  @override
  String get parentScreenName => 'home';

  @override
  void initState() {
    super.initState();
    _screenViewStartTime = DateTime.now();
    _lastInteractionTime = _screenViewStartTime;

    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(_onTabChanged);

    // Track screen view
    trackEvent('products_screen_viewed');
  }

  void _onTabChanged() {
    if (_tabController.indexIsChanging) {
      return;
    }

    _tabSwitchCount++;
    _lastInteractionTime = DateTime.now();

    final tabNames = ['DrGrow', 'Others'];
    final selectedTab = tabNames[_tabController.index];

    trackUserInteraction(
      'tab_changed',
      'tab_bar',
      elementId: selectedTab,
      additionalParams: {
        'tab_index': _tabController.index.toString(),
        'tab_name': selectedTab,
        'tab_switch_count': _tabSwitchCount.toString(),
        'time_on_screen_before_switch':
            _screenViewStartTime != null
                ? DateTime.now()
                    .difference(_screenViewStartTime!)
                    .inSeconds
                    .toString()
                : '0',
      },
    );
  }

  @override
  void dispose() {
    // Track total time spent on screen
    _trackScreenDuration();

    // Track final engagement metrics
    _trackEngagementMetrics();

    _tabController.removeListener(_onTabChanged);
    _tabController.dispose();
    super.dispose();
  }

  void _trackScreenDuration() {
    if (_screenViewStartTime != null) {
      final duration = DateTime.now().difference(_screenViewStartTime!);
      trackEvent(
        'products_screen_duration',
        params: {
          'duration_ms': duration.inMilliseconds.toString(),
          'duration_seconds': duration.inSeconds.toString(),
        },
      );
    }
  }

  void _trackEngagementMetrics() {
    trackEvent(
      'products_screen_engagement',
      params: {
        'tab_switch_count': _tabSwitchCount.toString(),
        'category_filter_count': _categoryFilterCount.toString(),
        'time_since_last_interaction':
            _lastInteractionTime != null
                ? DateTime.now()
                    .difference(_lastInteractionTime!)
                    .inSeconds
                    .toString()
                : '0',
        'final_tab_index': _tabController.index.toString(),
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFFF9FAFC),
      appBar: AppBar(
        actionsPadding: EdgeInsets.all(0),
        backgroundColor: Color(0xFFF9FAFC),
        title: Padding(
          padding: EdgeInsets.only(top: 24),
          child: AquaText.headline(
            "Product Catalogue",
            weight: AquaFontWeight.semibold,
            color: acTextSecondaryColor,
          ),
        ),
        centerTitle: false,
        bottom: TabBar(
          controller: _tabController,
          labelColor: acPrimaryBlue,
          unselectedLabelColor: Colors.grey,
          indicatorColor: acPrimaryBlue,
          indicatorWeight: 2,
          indicatorSize: TabBarIndicatorSize.tab,
          labelPadding: EdgeInsets.symmetric(horizontal: 16),
          labelStyle: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          unselectedLabelStyle: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
          tabs: [
            Tab(
              child: SizedBox(
                width: double.infinity,
                child: Center(child: Text('Dr.Grow')),
              ),
            ),
            Tab(
              child: SizedBox(
                width: double.infinity,
                child: Center(child: Text('Others')),
              ),
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          DrGrowProductsTab(
            onCategoryFilterChanged: (category) {
              _categoryFilterCount++;
              _lastInteractionTime = DateTime.now();

              trackUserInteraction(
                'category_filter_changed',
                'filter_chip',
                elementId: category,
                additionalParams: {
                  'tab': 'DrGrow',
                  'selected_category': category,
                  'filter_change_count': _categoryFilterCount.toString(),
                },
              );
            },
          ),
          OtherProductsTab(
            onCategoryFilterChanged: (category) {
              _categoryFilterCount++;
              _lastInteractionTime = DateTime.now();

              trackUserInteraction(
                'category_filter_changed',
                'filter_chip',
                elementId: category,
                additionalParams: {
                  'tab': 'Others',
                  'selected_category': category,
                  'filter_change_count': _categoryFilterCount.toString(),
                },
              );
            },
          ),
        ],
      ),
    );
  }
}

class DrGrowProductsTab extends StatefulWidget {
  final Function(String) onCategoryFilterChanged;

  const DrGrowProductsTab({super.key, required this.onCategoryFilterChanged});

  @override
  State<DrGrowProductsTab> createState() => _DrGrowProductsTabState();
}

class _DrGrowProductsTabState extends State<DrGrowProductsTab>
    with AutomaticKeepAliveClientMixin, AnalyticsMixin {
  String selectedFilter = "All";

  // Track screen view time
  DateTime? _viewStartTime;
  DateTime? _lastInteractionTime;

  // Track user interactions
  int _productTapCount = 0;
  int _scrollCount = 0;

  // Track scroll position
  final ScrollController _scrollController = ScrollController();

  @override
  String get screenName => 'drgrow_products_tab';

  @override
  String get parentScreenName => 'products';

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _viewStartTime = DateTime.now();
    _lastInteractionTime = _viewStartTime;

    // Add scroll listener for analytics
    _scrollController.addListener(_onScroll);

    // Track tab view
    trackEvent('drgrow_products_tab_viewed');
  }

  @override
  void dispose() {
    // Track total time spent on tab
    _trackViewDuration();

    // Track final engagement metrics
    _trackEngagementMetrics();

    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _trackViewDuration() {
    if (_viewStartTime != null) {
      final duration = DateTime.now().difference(_viewStartTime!);
      trackEvent(
        'drgrow_tab_duration',
        params: {
          'duration_ms': duration.inMilliseconds.toString(),
          'duration_seconds': duration.inSeconds.toString(),
        },
      );
    }
  }

  void _trackEngagementMetrics() {
    trackEvent(
      'drgrow_tab_engagement',
      params: {
        'product_tap_count': _productTapCount.toString(),
        'scroll_count': _scrollCount.toString(),
        'selected_filter': selectedFilter,
        'time_since_last_interaction':
            _lastInteractionTime != null
                ? DateTime.now()
                    .difference(_lastInteractionTime!)
                    .inSeconds
                    .toString()
                : '0',
      },
    );
  }

  void _onScroll() {
    // Don't track every tiny scroll event - only significant ones
    if (!_scrollController.hasClients) return;

    // Track scroll position as percentage
    final scrollPosition = _scrollController.position.pixels;
    final maxScroll = _scrollController.position.maxScrollExtent;

    if (maxScroll <= 0) return;

    final scrollPercentage = (scrollPosition / maxScroll * 100).round();

    // Only track at 25%, 50%, 75%, and 100% scroll positions
    if (scrollPercentage == 25 ||
        scrollPercentage == 50 ||
        scrollPercentage == 75 ||
        scrollPercentage == 100) {
      _scrollCount++;
      _lastInteractionTime = DateTime.now();

      trackEvent(
        'drgrow_products_scrolled',
        params: {
          'scroll_percentage': scrollPercentage.toString(),
          'scroll_position': scrollPosition.toString(),
          'selected_filter': selectedFilter,
        },
      );
    }
  }

  void _trackProductTap(Product product) {
    _productTapCount++;
    _lastInteractionTime = DateTime.now();

    trackUserInteraction(
      'product_selected',
      'product_card',
      elementId: product.productName,
      additionalParams: {
        'product_name': product.productName,
        'product_category': product.category,
        'product_tap_count': _productTapCount.toString(),
        'selected_filter': selectedFilter,
        'time_on_tab_before_tap':
            _viewStartTime != null
                ? DateTime.now()
                    .difference(_viewStartTime!)
                    .inSeconds
                    .toString()
                : '0',
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return BlocConsumer<ProductCatalogueCubit, ProductCatalogueState>(
      builder: (context, state) {
        if (state.isLoading) {
          return const Center(child: CircularProgressIndicator());
        } else if (state.drGrowProductCatalogue.isNotEmpty) {
          final categories = state.drGrowProductCatalogue;

          // Filter categories based on selection
          final filteredCatalogue =
              selectedFilter == "All"
                  ? categories
                  : categories.where((c) => c.name == selectedFilter).toList();

          return Column(
            children: [
              verticalSpaceLarge,
              // Product list
              Expanded(
                child:
                    filteredCatalogue.isEmpty
                        ? Center(
                          child: AquaText.body(
                            'No products found for "$selectedFilter" category',
                          ),
                        )
                        : CategoryProductsListViewWidget(
                          paddingLeft: 16,
                          paddingRight: 16,
                          categories: filteredCatalogue,
                          scrollController: _scrollController,
                          onProductTap: (Product product) {
                            _trackProductTap(product);
                            context.read<ProductCatalogueCubit>().productTapped(
                              product,
                            );
                            AppRouter.navigateToProductDetails(
                              product: product,
                            );
                          },
                        ),
              ),
            ],
          );
        } else if (state.isError) {
          return _buildErrorState(
            context,
            state.errorMessage ?? 'Unknown error',
          );
        } else if (state.isSyncing) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                AquaText.body('Syncing products...'),
              ],
            ),
          );
        }
        return const Center(child: Text('No products available'));
      },
      listener: (context, state) {
        // Track state changes for analytics
        if (state.isLoading) {
          trackEvent('drgrow_products_loading');
        } else if (state.isSyncing) {
          trackEvent('drgrow_products_syncing');
        } else if (state.drGrowProductCatalogue.isNotEmpty) {
          _lastInteractionTime = DateTime.now();

          int totalProducts = 0;
          for (var category in state.drGrowProductCatalogue) {
            totalProducts += category.products.length;
          }

          trackEvent(
            'drgrow_products_loaded',
            params: {
              'category_count': state.drGrowProductCatalogue.length.toString(),
              'product_count': totalProducts.toString(),
              'time_to_load':
                  _viewStartTime != null
                      ? DateTime.now()
                          .difference(_viewStartTime!)
                          .inMilliseconds
                          .toString()
                      : '0',
            },
          );
        } else if (state.isError) {
          trackEvent(
            'drgrow_products_error',
            params: {'error_message': state.errorMessage ?? 'Unknown error'},
          );
        }
      },
    );
  }

  Widget _buildErrorState(BuildContext context, String errorMessage) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 48, color: Colors.red),
          SizedBox(height: 16),
          AquaText.body(errorMessage),
          SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              context.read<ProductCatalogueCubit>().loadProductCatalogues();
              trackEvent('drgrow_products_retry_clicked');
            },
            child: AquaText.body('Try Again'),
          ),
        ],
      ),
    );
  }
}

class OtherProductsTab extends StatefulWidget {
  final Function(String) onCategoryFilterChanged;

  const OtherProductsTab({Key? key, required this.onCategoryFilterChanged})
    : super(key: key);

  @override
  State<OtherProductsTab> createState() => _OtherProductsTabState();
}

class _OtherProductsTabState extends State<OtherProductsTab>
    with AutomaticKeepAliveClientMixin, AnalyticsMixin {
  String selectedFilter = "All";

  // Track screen view time
  DateTime? _viewStartTime;
  DateTime? _lastInteractionTime;

  // Track user interactions
  int _productTapCount = 0;
  int _scrollCount = 0;

  // Track scroll position
  final ScrollController _scrollController = ScrollController();

  @override
  String get screenName => 'other_products_tab';

  @override
  String get parentScreenName => 'products';

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _viewStartTime = DateTime.now();
    _lastInteractionTime = _viewStartTime;

    // Add scroll listener for analytics
    _scrollController.addListener(_onScroll);

    // Track tab view
    trackEvent('other_products_tab_viewed');
  }

  @override
  void dispose() {
    // Track total time spent on tab
    _trackViewDuration();

    // Track final engagement metrics
    _trackEngagementMetrics();

    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _trackViewDuration() {
    if (_viewStartTime != null) {
      final duration = DateTime.now().difference(_viewStartTime!);
      trackEvent(
        'other_products_tab_duration',
        params: {
          'duration_ms': duration.inMilliseconds.toString(),
          'duration_seconds': duration.inSeconds.toString(),
        },
      );
    }
  }

  void _trackEngagementMetrics() {
    trackEvent(
      'other_products_tab_engagement',
      params: {
        'product_tap_count': _productTapCount.toString(),
        'scroll_count': _scrollCount.toString(),
        'selected_filter': selectedFilter,
        'time_since_last_interaction':
            _lastInteractionTime != null
                ? DateTime.now()
                    .difference(_lastInteractionTime!)
                    .inSeconds
                    .toString()
                : '0',
      },
    );
  }

  void _onScroll() {
    // Don't track every tiny scroll event - only significant ones
    if (!_scrollController.hasClients) return;

    // Track scroll position as percentage
    final scrollPosition = _scrollController.position.pixels;
    final maxScroll = _scrollController.position.maxScrollExtent;

    if (maxScroll <= 0) return;

    final scrollPercentage = (scrollPosition / maxScroll * 100).round();

    // Only track at 25%, 50%, 75%, and 100% scroll positions
    if (scrollPercentage == 25 ||
        scrollPercentage == 50 ||
        scrollPercentage == 75 ||
        scrollPercentage == 100) {
      _scrollCount++;
      _lastInteractionTime = DateTime.now();

      trackEvent(
        'other_products_scrolled',
        params: {
          'scroll_percentage': scrollPercentage.toString(),
          'scroll_position': scrollPosition.toString(),
          'selected_filter': selectedFilter,
        },
      );
    }
  }

  void _trackProductTap(Product product) {
    _productTapCount++;
    _lastInteractionTime = DateTime.now();

    trackUserInteraction(
      'product_selected',
      'product_card',
      elementId: product.productName,
      additionalParams: {
        'product_name': product.productName,
        'product_category': product.category,
        'product_tap_count': _productTapCount.toString(),
        'selected_filter': selectedFilter,
        'time_on_tab_before_tap':
            _viewStartTime != null
                ? DateTime.now()
                    .difference(_viewStartTime!)
                    .inSeconds
                    .toString()
                : '0',
      },
    );
  }

  Widget _buildCategoryFilters(List<ProductCatalogue> categories) {
    final allCategories = ["All"];
    allCategories.addAll(categories.map((c) => c.name));

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Wrap(
          spacing: 8,
          children:
              allCategories.map((category) {
                final isSelected = selectedFilter == category;
                return FilterChip(
                  label: Text(category),
                  selected: isSelected,
                  onSelected: (selected) {
                    setState(() {
                      selectedFilter = category;
                    });

                    widget.onCategoryFilterChanged(category);
                  },
                  backgroundColor: Colors.grey[200],
                  selectedColor: acPrimaryBlue.withValues(alpha: 0.2),
                  checkmarkColor: acPrimaryBlue,
                  labelStyle: TextStyle(
                    color: isSelected ? acPrimaryBlue : Colors.black,
                    fontWeight:
                        isSelected ? FontWeight.bold : FontWeight.normal,
                  ),
                );
              }).toList(),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return BlocConsumer<ProductCatalogueCubit, ProductCatalogueState>(
      builder: (context, state) {
        if (state.isLoading) {
          return const Center(child: CircularProgressIndicator());
        } else if (state.otherProductsCatelogue.isNotEmpty) {
          final categories = state.otherProductsCatelogue;

          // Filter categories based on selection
          final filteredCatalogue =
              selectedFilter == "All"
                  ? categories
                  : categories.where((c) => c.name == selectedFilter).toList();

          return Column(
            children: [
              // Category filter chips
              _buildCategoryFilters(categories),
              // Product list
              Expanded(
                child:
                    filteredCatalogue.isEmpty
                        ? Center(
                          child: AquaText.body(
                            'No products found for "$selectedFilter" category',
                          ),
                        )
                        : CategoryProductsListViewWidget(
                          paddingLeft: 16,
                          paddingRight: 16,
                          categories: filteredCatalogue,
                          scrollController: _scrollController,
                          onProductTap: (Product product) {
                            _trackProductTap(product);
                            context.read<ProductCatalogueCubit>().productTapped(
                              product,
                            );
                            AppRouter.navigateToProductDetails(
                              product: product,
                            );
                          },
                        ),
              ),
            ],
          );
        } else if (state.isError) {
          return _buildErrorState(
            context,
            state.errorMessage ?? 'Unknown error',
          );
        } else if (state.isSyncing) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                AquaText.body('Syncing products...'),
              ],
            ),
          );
        }
        return const Center(child: Text('No products available'));
      },
      listener: (context, state) {
        // Track state changes for analytics
        if (state.isLoading) {
          trackEvent('other_products_loading');
        } else if (state.isSyncing) {
          trackEvent('other_products_syncing');
        } else if (state.otherProductsCatelogue.isNotEmpty) {
          _lastInteractionTime = DateTime.now();

          int totalProducts = 0;
          for (var category in state.otherProductsCatelogue) {
            totalProducts += category.products.length;
          }

          trackEvent(
            'other_products_loaded',
            params: {
              'category_count': state.otherProductsCatelogue.length.toString(),
              'product_count': totalProducts.toString(),
              'time_to_load':
                  _viewStartTime != null
                      ? DateTime.now()
                          .difference(_viewStartTime!)
                          .inMilliseconds
                          .toString()
                      : '0',
            },
          );
        } else if (state.isError) {
          trackEvent(
            'other_products_error',
            params: {'error_message': state.errorMessage ?? 'Unknown error'},
          );
        }
      },
    );
  }

  Widget _buildErrorState(BuildContext context, String errorMessage) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 48, color: Colors.red),
          SizedBox(height: 16),
          AquaText.body(errorMessage),
          SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              context.read<ProductCatalogueCubit>().loadProductCatalogues();
              trackEvent('other_products_retry_clicked');
            },
            child: AquaText.body('Try Again'),
          ),
        ],
      ),
    );
  }
}
