import '../../domain/entities/customer.dart';

class CustomerModel {
  final String? id;
  final String customerId;
  final String customerName;
  final String email;
  final String mobileNumber;
  final String companyName;
  final String gstNo;
  final String businessVertical;
  final String customerCode;
  final String billingAddress;
  final bool isSynced;

  CustomerModel({
    this.id,
    required this.customerId,
    required this.customerName,
    required this.email,
    required this.mobileNumber,
    required this.companyName,
    required this.gstNo,
    required this.businessVertical,
    required this.customerCode,
    required this.billingAddress,
    this.isSynced = false,
  });

  factory CustomerModel.fromJson(Map<String, dynamic> json) {
    return CustomerModel(
      id: json['_id'],
      customerId: json['customerId'] ?? '',
      customerName: json['customerName'] ?? '',
      email: json['email'] ?? '',
      mobileNumber: json['mobileNumber'] ?? '',
      companyName: json['companyName'] ?? '',
      gstNo: json['gstNo'] ?? '',
      businessVertical: json['businessVertical'] ?? '',
      customerCode: json['customerCode'] ?? '',
      billingAddress: json['billingAddress'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) '_id': id,
      'customerId': customerId,
      'customerName': customerName,
      'email': email,
      'mobileNumber': mobileNumber,
      'companyName': companyName,
      'gstNo': gstNo,
      'businessVertical': businessVertical,
      'customerCode': customerCode,
      'billingAddress': billingAddress,
    };
  }

  // Convert CustomerModel to Customer entity
  Customer toEntity() {
    return Customer(
      id: id,
      customerId: customerId,
      customerName: customerName,
      email: email,
      mobileNumber: mobileNumber,
      companyName: companyName,
      gstNo: gstNo,
      businessVertical: businessVertical,
      customerCode: customerCode,
      billingAddress: billingAddress,
    );
  }

  // Create CustomerModel from Customer entity
  factory CustomerModel.fromEntity(Customer entity, {bool isSynced = false}) {
    return CustomerModel(
      id: entity.id,
      customerId: entity.customerId,
      customerName: entity.customerName,
      email: entity.email,
      mobileNumber: entity.mobileNumber,
      companyName: entity.companyName,
      gstNo: entity.gstNo,
      businessVertical: entity.businessVertical,
      customerCode: entity.customerCode,
      billingAddress: entity.billingAddress,
      isSynced: isSynced,
    );
  }

  CustomerModel copyWith({
    String? id,
    String? customerId,
    String? customerName,
    String? email,
    String? mobileNumber,
    String? companyName,
    String? gstNo,
    String? businessVertical,
    String? customerCode,
    String? billingAddress,
    bool? isSynced,
  }) {
    return CustomerModel(
      id: id ?? this.id,
      customerId: customerId ?? this.customerId,
      customerName: customerName ?? this.customerName,
      email: email ?? this.email,
      mobileNumber: mobileNumber ?? this.mobileNumber,
      companyName: companyName ?? this.companyName,
      gstNo: gstNo ?? this.gstNo,
      businessVertical: businessVertical ?? this.businessVertical,
      customerCode: customerCode ?? this.customerCode,
      billingAddress: billingAddress ?? this.billingAddress,
      isSynced: isSynced ?? this.isSynced,
    );
  }
}
