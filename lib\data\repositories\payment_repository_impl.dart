import 'package:dartz/dartz.dart';
import '../../core/error/exceptions.dart';
import '../../core/error/failures.dart';
import '../../core/network/network_info.dart';
import '../../core/utils/logger.dart';
import '../../domain/entities/payments/payment_request.dart';
import '../../domain/entities/payments/payment_session.dart';
import '../../domain/entities/payments/payment_transaction.dart';
import '../../domain/repositories/payment_repository.dart';
import '../datasources/remote/payment_remote_datasource.dart';

/// Implementation of PaymentRepository
class PaymentRepositoryImpl implements PaymentRepository {
  final PaymentRemoteDataSource remoteDataSource;
  final NetworkInfo networkInfo;
  final AppLogger logger;

  PaymentRepositoryImpl({
    required this.remoteDataSource,
    required this.networkInfo,
    required this.logger,
  });

  @override
  Future<Either<Failure, PaymentSession>> createPaymentSession(
    PaymentRequest request,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        logger.i('Creating payment session for invoice: ${request.invoiceNumber}');
        final paymentSession = await remoteDataSource.createPaymentSession(request);
        logger.i('Payment session created successfully: ${paymentSession.sessionId}');
        return Right(paymentSession);
      } on ServerException catch (e) {
        logger.e('Server exception while creating payment session: $e');
        return Left(ServerFailure());
      } on NetworkException catch (e) {
        logger.e('Network exception while creating payment session: $e');
        return Left(NetworkFailure());
      } on AuthException catch (e) {
        logger.e('Auth exception while creating payment session: $e');
        return Left(AuthFailure());
      } catch (e) {
        logger.e('Unexpected error while creating payment session: $e');
        return Left(ServerFailure());
      }
    } else {
      logger.w('No network connection available for creating payment session');
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, PaymentSession>> getPaymentSession(
    String sessionId,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        logger.i('Getting payment session: $sessionId');
        final paymentSession = await remoteDataSource.getPaymentSession(sessionId);
        logger.i('Payment session retrieved successfully');
        return Right(paymentSession);
      } on ServerException catch (e) {
        logger.e('Server exception while getting payment session: $e');
        return Left(ServerFailure());
      } on NetworkException catch (e) {
        logger.e('Network exception while getting payment session: $e');
        return Left(NetworkFailure());
      } on AuthException catch (e) {
        logger.e('Auth exception while getting payment session: $e');
        return Left(AuthFailure());
      } catch (e) {
        logger.e('Unexpected error while getting payment session: $e');
        return Left(ServerFailure());
      }
    } else {
      logger.w('No network connection available for getting payment session');
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, PaymentTransaction>> verifyPayment(
    String sessionId,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        logger.i('Verifying payment for session: $sessionId');
        final transaction = await remoteDataSource.verifyPayment(sessionId);
        logger.i('Payment verification completed successfully');
        return Right(transaction);
      } on ServerException catch (e) {
        logger.e('Server exception while verifying payment: $e');
        return Left(ServerFailure());
      } on NetworkException catch (e) {
        logger.e('Network exception while verifying payment: $e');
        return Left(NetworkFailure());
      } on AuthException catch (e) {
        logger.e('Auth exception while verifying payment: $e');
        return Left(AuthFailure());
      } catch (e) {
        logger.e('Unexpected error while verifying payment: $e');
        return Left(ServerFailure());
      }
    } else {
      logger.w('No network connection available for verifying payment');
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, PaymentSession>> checkPaymentStatus(
    String sessionId,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        logger.i('Checking payment status for session: $sessionId');
        final paymentSession = await remoteDataSource.checkPaymentStatus(sessionId);
        logger.i('Payment status checked successfully');
        return Right(paymentSession);
      } on ServerException catch (e) {
        logger.e('Server exception while checking payment status: $e');
        return Left(ServerFailure());
      } on NetworkException catch (e) {
        logger.e('Network exception while checking payment status: $e');
        return Left(NetworkFailure());
      } on AuthException catch (e) {
        logger.e('Auth exception while checking payment status: $e');
        return Left(AuthFailure());
      } catch (e) {
        logger.e('Unexpected error while checking payment status: $e');
        return Left(ServerFailure());
      }
    } else {
      logger.w('No network connection available for checking payment status');
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, void>> cancelPaymentSession(String sessionId) async {
    if (await networkInfo.isConnected) {
      try {
        logger.i('Cancelling payment session: $sessionId');
        await remoteDataSource.cancelPaymentSession(sessionId);
        logger.i('Payment session cancelled successfully');
        return const Right(null);
      } on ServerException catch (e) {
        logger.e('Server exception while cancelling payment session: $e');
        return Left(ServerFailure());
      } on NetworkException catch (e) {
        logger.e('Network exception while cancelling payment session: $e');
        return Left(NetworkFailure());
      } on AuthException catch (e) {
        logger.e('Auth exception while cancelling payment session: $e');
        return Left(AuthFailure());
      } catch (e) {
        logger.e('Unexpected error while cancelling payment session: $e');
        return Left(ServerFailure());
      }
    } else {
      logger.w('No network connection available for cancelling payment session');
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, PaymentTransaction>> getPaymentTransaction(
    String transactionId,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        logger.i('Getting payment transaction: $transactionId');
        final transaction = await remoteDataSource.getPaymentTransaction(transactionId);
        logger.i('Payment transaction retrieved successfully');
        return Right(transaction);
      } on ServerException catch (e) {
        logger.e('Server exception while getting payment transaction: $e');
        return Left(ServerFailure());
      } on NetworkException catch (e) {
        logger.e('Network exception while getting payment transaction: $e');
        return Left(NetworkFailure());
      } on AuthException catch (e) {
        logger.e('Auth exception while getting payment transaction: $e');
        return Left(AuthFailure());
      } catch (e) {
        logger.e('Unexpected error while getting payment transaction: $e');
        return Left(ServerFailure());
      }
    } else {
      logger.w('No network connection available for getting payment transaction');
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, List<PaymentTransaction>>> getCustomerTransactions(
    String customerId, {
    int? limit,
    int? offset,
  }) async {
    if (await networkInfo.isConnected) {
      try {
        logger.i('Getting customer transactions for: $customerId');
        final transactions = await remoteDataSource.getCustomerTransactions(
          customerId,
          limit: limit,
          offset: offset,
        );
        logger.i('Customer transactions retrieved successfully: ${transactions.length}');
        return Right(transactions);
      } on ServerException catch (e) {
        logger.e('Server exception while getting customer transactions: $e');
        return Left(ServerFailure());
      } on NetworkException catch (e) {
        logger.e('Network exception while getting customer transactions: $e');
        return Left(NetworkFailure());
      } on AuthException catch (e) {
        logger.e('Auth exception while getting customer transactions: $e');
        return Left(AuthFailure());
      } catch (e) {
        logger.e('Unexpected error while getting customer transactions: $e');
        return Left(ServerFailure());
      }
    } else {
      logger.w('No network connection available for getting customer transactions');
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, List<PaymentTransaction>>> getInvoiceTransactions(
    String invoiceNumber,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        logger.i('Getting invoice transactions for: $invoiceNumber');
        final transactions = await remoteDataSource.getInvoiceTransactions(invoiceNumber);
        logger.i('Invoice transactions retrieved successfully: ${transactions.length}');
        return Right(transactions);
      } on ServerException catch (e) {
        logger.e('Server exception while getting invoice transactions: $e');
        return Left(ServerFailure());
      } on NetworkException catch (e) {
        logger.e('Network exception while getting invoice transactions: $e');
        return Left(NetworkFailure());
      } on AuthException catch (e) {
        logger.e('Auth exception while getting invoice transactions: $e');
        return Left(AuthFailure());
      } catch (e) {
        logger.e('Unexpected error while getting invoice transactions: $e');
        return Left(ServerFailure());
      }
    } else {
      logger.w('No network connection available for getting invoice transactions');
      return Left(NetworkFailure());
    }
  }

  @override
  Future<Either<Failure, PaymentTransaction>> processWebhookNotification(
    Map<String, dynamic> webhookData,
  ) async {
    if (await networkInfo.isConnected) {
      try {
        logger.i('Processing webhook notification');
        final transaction = await remoteDataSource.processWebhookNotification(webhookData);
        logger.i('Webhook notification processed successfully');
        return Right(transaction);
      } on ServerException catch (e) {
        logger.e('Server exception while processing webhook notification: $e');
        return Left(ServerFailure());
      } on NetworkException catch (e) {
        logger.e('Network exception while processing webhook notification: $e');
        return Left(NetworkFailure());
      } on AuthException catch (e) {
        logger.e('Auth exception while processing webhook notification: $e');
        return Left(AuthFailure());
      } catch (e) {
        logger.e('Unexpected error while processing webhook notification: $e');
        return Left(ServerFailure());
      }
    } else {
      logger.w('No network connection available for processing webhook notification');
      return Left(NetworkFailure());
    }
  }
}
