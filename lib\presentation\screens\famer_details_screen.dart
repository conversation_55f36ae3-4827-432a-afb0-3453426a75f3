import 'package:aqua_ui/aqua_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import '../../core/mixins/analytics_mixin.dart';
import '../../domain/entities/farmer_visit/farmer.dart';
import '../../domain/entities/farmer_visit/visit.dart';
import '../widgets/styled_generic_table.dart';

class FarmerDetailsScreen extends StatefulWidget {
  final Farmer farmer;
  const FarmerDetailsScreen({super.key, required this.farmer});

  @override
  State<FarmerDetailsScreen> createState() => _FarmerDetailsScreenState();
}

class _FarmerDetailsScreenState extends State<FarmerDetailsScreen>
    with AnalyticsMixin<FarmerDetailsScreen> {
  @override
  String get screenName => 'farmer_details';

  @override
  String get parentScreenName => 'my_farmers';

  @override
  void initState() {
    super.initState();

    // Track farmer details view
    trackEvent(
      'farmer_details_viewed',
      params: {
        'farmer_name': widget.farmer.name,
        'farmer_id': widget.farmer.id.toString(),
        'visit_count': widget.farmer.visits.length.toString(),
        'has_visits': widget.farmer.visits.isNotEmpty.toString(),
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.white,
          statusBarIconBrightness: Brightness.dark,
        ),
        backgroundColor: acWhiteColor,
        titleSpacing: 16,
        leadingWidth: 32.0,
        leading: IconButton(
          padding: const EdgeInsets.only(left: 16.0),
          icon: const Icon(Icons.arrow_back),
          color: acTextSecondaryColor,
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: AquaText.headline(
          widget.farmer.name,
          weight: AquaFontWeight.semibold,
          color: acTextSecondaryColor,
        ),
        centerTitle: false,
      ),
      body: Container(
        color: acWhiteColor,
        width: double.infinity,
        height: double.infinity,
        padding: EdgeInsets.symmetric(horizontal: 16),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: 16),

              // Only show table if there are visits
              if (widget.farmer.visits.isNotEmpty)
                StyledGenericTable<Visit>(
                  items: widget.farmer.visits,
                  showDividers: true,
                  columns: [
                    ColumnConfig<Visit>(
                      title: 'Visited Date',
                      width: 120,
                      cellBuilder:
                          (visit) => AquaText.body(
                            DateFormat(
                              "dd-MM-yyyy",
                            ).format(visit.createdDateTime),
                          ),
                    ),
                    ColumnConfig<Visit>(
                      title: 'Pond ID',
                      width: 100,
                      cellBuilder: (visit) => AquaText.body(visit.pondId),
                    ),
                    ColumnConfig<Visit>(
                      title: 'DOC',
                      width: 60,
                      cellBuilder:
                          (visit) => AquaText.body(visit.doc.toString()),
                    ),
                    ColumnConfig<Visit>(
                      title: 'Products',
                      width: 200,
                      cellBuilder: (visit) => AquaText.body(visit.productUsed),
                    ),
                  ],
                )
              else
                Center(
                  child: Padding(
                    padding: EdgeInsets.symmetric(vertical: 24),
                    child: AquaText.body("No visit data available"),
                  ),
                ),

              SizedBox(height: 24),
            ],
          ),
        ),
      ),
    );
  }
}
