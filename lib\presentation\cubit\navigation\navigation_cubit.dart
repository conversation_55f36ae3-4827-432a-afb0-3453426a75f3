import 'package:flutter_bloc/flutter_bloc.dart';
import 'navigation_state.dart';

class NavigationCubit extends Cubit<NavigationState> {
  NavigationCubit() : super(const NavigationInitial());

  void getNavBarItem(int index) {
    final currentState = state;
    
    // If tapping the same tab, trigger an action
    if (currentState.index == index) {
      emit(NavigationActionTriggered(index));
      // Reset state after action
      Future.delayed(const Duration(milliseconds: 100), () {
        emit(NavigationChanged(index));
      });
    } else {
      emit(NavigationChanged(index));
    }
  }
}