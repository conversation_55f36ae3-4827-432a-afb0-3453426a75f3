import 'package:flutter/material.dart';

class LoadingIndicator extends StatelessWidget {
  final String? message;
  final bool isOverlay;
  final Color? backgroundColor;

  const LoadingIndicator({
    super.key,
    this.message,
    this.isOverlay = false,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    final loadingWidget = Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(),
          if (message != null) ...[
            const SizedBox(height: 16),
            Text(
              message!,
              style: const TextStyle(fontSize: 16),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );

    if (isOverlay) {
      return Stack(
        children: [
          // Semi-transparent background
          Positioned.fill(
            child: Container(
              color: backgroundColor ?? Colors.black.withValues(alpha: 0.3),
            ),
          ),
          // Loading indicator
          Positioned.fill(
            child: Material(
              type: MaterialType.transparency,
              child: loadingWidget,
            ),
          ),
        ],
      );
    }

    return loadingWidget;
  }
}

// Extension to easily show loading overlay
extension LoadingOverlayExtension on BuildContext {
  void showLoadingOverlay({String? message}) {
    showDialog(
      context: this,
      barrierDismissible: false,
      builder: (context) => LoadingIndicator(message: message, isOverlay: true),
    );
  }

  void hideLoadingOverlay() {
    if (Navigator.of(this).canPop()) {
      Navigator.of(this).pop();
    }
  }
}
