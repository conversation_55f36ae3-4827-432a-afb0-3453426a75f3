import 'package:aqua_ui/aqua_ui.dart';
import 'package:aquapartner/core/mixins/analytics_mixin.dart';
import 'package:aquapartner/presentation/cubit/billing_and_payments/billing_and_payments_cubit.dart';
import 'package:aquapartner/presentation/cubit/billing_and_payments/billing_and_payments_state.dart';
import 'package:aquapartner/presentation/widgets/dues_page.dart';
import 'package:aquapartner/presentation/widgets/sales_orders_page.dart';
import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../core/services/analytics_service.dart';
import '../../injection_container.dart' as di;
import '../widgets/credit_notes_page.dart';
import '../widgets/invoices_page.dart';
import '../widgets/payments_page.dart';

class BillingAndPaymentsScreen extends StatefulWidget {
  const BillingAndPaymentsScreen({super.key});

  @override
  State<BillingAndPaymentsScreen> createState() =>
      _BillingAndPaymentsScreenState();
}

class _BillingAndPaymentsScreenState extends State<BillingAndPaymentsScreen>
    with AnalyticsMixin<BillingAndPaymentsScreen> {
  @override
  Widget build(BuildContext context) {
    return BlocConsumer<
      BillingAndPaymentsPageCubit,
      BillingAndPaymentsPageState
    >(
      builder: (context, state) {
        return Scaffold(
          backgroundColor: Colors.white,

          body: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.only(left: 16, top: 32, bottom: 16),
                child: AquaText.headline(
                  "Billing & Payments",
                  weight: AquaFontWeight.semibold,
                  color: acTextSecondaryColor,
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Container(
                  decoration: BoxDecoration(
                    border: Border.all(color: acGrey200),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: DropdownButtonHideUnderline(
                    child: DropdownButton2<String>(
                      value: state.selectedPage,
                      isExpanded: true,
                      buttonStyleData: ButtonStyleData(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        padding: EdgeInsets.only(right: 16),
                      ),
                      dropdownStyleData: DropdownStyleData(
                        padding: EdgeInsets.only(right: 0),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          color: Colors.white,
                          border: Border.all(
                            // Add border here
                            color:
                                acGrey200, // Change to your desired color (e.g., acGrey200)
                            width: 1.0, // Adjust border thickness
                          ),
                        ),
                        elevation: 0,
                        offset: const Offset(0, -5),
                        direction: DropdownDirection.left,
                        maxHeight: 300,
                      ),
                      items:
                          state.pages.map((String page) {
                            return DropdownMenuItem<String>(
                              value: page,
                              child: Container(
                                padding: const EdgeInsets.only(
                                  right: 0,
                                  left: 0,
                                ),
                                child: AquaText.body(page),
                              ),
                            );
                          }).toList(),
                      onChanged: (String? newValue) {
                        if (newValue != null) {
                          // Track page selection
                          final analyticsService = di.sl<AnalyticsService>();
                          analyticsService.logEvent(
                            name: 'billing_page_selected',
                            parameters: {
                              'screen_name': 'BillingAndPaymentsScreen',
                              'selected_page': newValue,
                              'previous_page':
                                  context
                                      .read<BillingAndPaymentsPageCubit>()
                                      .state
                                      .selectedPage,
                              'timestamp': DateTime.now().toIso8601String(),
                            },
                          );

                          context
                              .read<BillingAndPaymentsPageCubit>()
                              .selectPage(newValue);
                        }
                      },
                    ),
                  ),
                ),
              ),
              SizedBox(height: 8),
              Expanded(child: _buildPageContent(context, state.selectedPage)),
            ],
          ),
        );
      },
      listener: (context, state) {},
    );
  }

  Widget _buildPageContent(BuildContext context, String selectedPage) {
    // Track sub-page view
    final analyticsService = di.sl<AnalyticsService>();
    analyticsService.logEvent(
      name: 'billing_subpage_view',
      parameters: {
        'screen_name': 'BillingAndPaymentsScreen',
        'sub_page': selectedPage,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );

    // Return different content based on selected page
    switch (selectedPage) {
      case 'Sales Orders':
        return SalesOrdersPage();
      case 'Dues':
        return DuesPage();
      case 'Invoices':
        return InvoicesPage();
      case 'Payments':
        return PaymentsPage();
      case 'Credit Notes':
        return CreditNotesPage();
      default:
        return const Center(child: Text('No content available'));
    }
  }

  @override
  String get screenName => 'BillingAndPaymentsScreen';
}
