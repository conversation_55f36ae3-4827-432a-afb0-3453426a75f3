import 'package:flutter_test/flutter_test.dart';
import 'package:aquapartner/data/models/credit_notes/credit_note_model.dart';
import 'package:aquapartner/domain/entities/credit_notes/credit_note.dart';

void main() {
  group('CreditNoteModel Tests', () {
    late CreditNoteModel testCreditNoteModel;
    late Map<String, dynamic> testJson;
    late DateTime testDateTime;

    setUp(() {
      testDateTime = DateTime(2023, 9, 1, 0, 0, 0);

      testJson = {
        '_id': 'credit_note_123',
        'customerId': 'customer_123',
        'customerName': 'Test Customer',
        'date1':
            'Fri Sep 01 2023 00:00:00 GMT+0000 (Coordinated Universal Time)',
        'creditNoteId': 'cn_123',
        'creditNoteNumber': 'CN-2023-001',
        'invoiceId': 'invoice_123',
        'productId': 'product_123',
        'itemName': 'Test Product',
        'brand': 'Test Brand',
        'itemCategory': 'Feed',
        'categoryType': 'Aquaculture',
        'quantity': '5',
        'amount': '2500.50',
        'tag': 'refund',
        'category': 'product_return',
      };

      testCreditNoteModel = CreditNoteModel.fromJson(testJson);
    });

    group('Constructor Tests', () {
      test('should create CreditNoteModel with all required fields', () {
        final creditNoteModel = CreditNoteModel(
          id: 'credit_note_123',
          customerId: 'customer_123',
          customerName: 'Test Customer',
          date: testDateTime,
          creditNoteId: 'cn_123',
          creditNoteNumber: 'CN-2023-001',
          invoiceId: 'invoice_123',
          productId: 'product_123',
          itemName: 'Test Product',
          brand: 'Test Brand',
          itemCategory: 'Feed',
          categoryType: 'Aquaculture',
          quantity: 5,
          amount: 2500.50,
          tag: 'refund',
          category: 'product_return',
        );

        expect(creditNoteModel.id, equals('credit_note_123'));
        expect(creditNoteModel.customerId, equals('customer_123'));
        expect(creditNoteModel.customerName, equals('Test Customer'));
        expect(creditNoteModel.date, equals(testDateTime));
        expect(creditNoteModel.creditNoteId, equals('cn_123'));
        expect(creditNoteModel.creditNoteNumber, equals('CN-2023-001'));
        expect(creditNoteModel.invoiceId, equals('invoice_123'));
        expect(creditNoteModel.productId, equals('product_123'));
        expect(creditNoteModel.itemName, equals('Test Product'));
        expect(creditNoteModel.brand, equals('Test Brand'));
        expect(creditNoteModel.itemCategory, equals('Feed'));
        expect(creditNoteModel.categoryType, equals('Aquaculture'));
        expect(creditNoteModel.quantity, equals(5));
        expect(creditNoteModel.amount, equals(2500.50));
        expect(creditNoteModel.tag, equals('refund'));
        expect(creditNoteModel.category, equals('product_return'));
        expect(creditNoteModel.dbId, equals(0));
      });

      test('should create CreditNoteModel with optional fields as null', () {
        final creditNoteModel = CreditNoteModel(
          id: 'credit_note_123',
          customerId: 'customer_123',
          customerName: 'Test Customer',
          date: testDateTime,
          creditNoteId: 'cn_123',
          creditNoteNumber: 'CN-2023-001',
          itemName: 'Test Product',
          quantity: 5,
          amount: 2500.50,
        );

        expect(creditNoteModel.invoiceId, isNull);
        expect(creditNoteModel.productId, isNull);
        expect(creditNoteModel.brand, isNull);
        expect(creditNoteModel.itemCategory, isNull);
        expect(creditNoteModel.categoryType, isNull);
        expect(creditNoteModel.tag, isNull);
        expect(creditNoteModel.category, isNull);
      });
    });

    group('JSON Serialization Tests', () {
      test('should create CreditNoteModel from JSON correctly', () {
        expect(testCreditNoteModel.id, equals('credit_note_123'));
        expect(testCreditNoteModel.customerId, equals('customer_123'));
        expect(testCreditNoteModel.customerName, equals('Test Customer'));
        expect(testCreditNoteModel.date, isA<DateTime>());
        expect(testCreditNoteModel.creditNoteId, equals('cn_123'));
        expect(testCreditNoteModel.creditNoteNumber, equals('CN-2023-001'));
        expect(testCreditNoteModel.invoiceId, equals('invoice_123'));
        expect(testCreditNoteModel.productId, equals('product_123'));
        expect(testCreditNoteModel.itemName, equals('Test Product'));
        expect(testCreditNoteModel.brand, equals('Test Brand'));
        expect(testCreditNoteModel.itemCategory, equals('Feed'));
        expect(testCreditNoteModel.categoryType, equals('Aquaculture'));
        expect(testCreditNoteModel.quantity, equals(5));
        expect(testCreditNoteModel.amount, equals(2500.50));
        expect(testCreditNoteModel.tag, equals('refund'));
        expect(testCreditNoteModel.category, equals('product_return'));
      });

      test('should handle missing JSON fields with defaults', () {
        final incompleteJson = <String, dynamic>{
          '_id': 'credit_note_123',
          'customerId': 'customer_123',
          'customerName': 'Test Customer',
          'creditNoteId': 'cn_123',
          'creditNoteNumber': 'CN-2023-001',
          'itemName': 'Test Product',
          'date1':
              'Fri Sep 01 2023 00:00:00 GMT+0000 (Coordinated Universal Time)',
        };

        final creditNoteModel = CreditNoteModel.fromJson(incompleteJson);

        expect(creditNoteModel.id, equals('credit_note_123'));
        expect(creditNoteModel.customerId, equals('customer_123'));
        expect(creditNoteModel.customerName, equals('Test Customer'));
        expect(creditNoteModel.creditNoteId, equals('cn_123'));
        expect(creditNoteModel.creditNoteNumber, equals('CN-2023-001'));
        expect(creditNoteModel.itemName, equals('Test Product'));
        expect(creditNoteModel.invoiceId, isNull);
        expect(creditNoteModel.productId, isNull);
        expect(creditNoteModel.brand, isNull);
        expect(creditNoteModel.itemCategory, isNull);
        expect(creditNoteModel.categoryType, isNull);
        expect(creditNoteModel.quantity, equals(0));
        expect(creditNoteModel.amount, equals(0.0));
        expect(creditNoteModel.tag, isNull);
        expect(creditNoteModel.category, isNull);
        expect(creditNoteModel.date, isA<DateTime>());
      });

      test('should handle null JSON values with defaults', () {
        final nullJson = <String, dynamic>{
          '_id': null,
          'customerId': null,
          'customerName': null,
          'creditNoteId': null,
          'creditNoteNumber': null,
          'itemName': null,
          'quantity': null,
          'amount': null,
          'date1':
              'Fri Sep 01 2023 00:00:00 GMT+0000 (Coordinated Universal Time)',
        };

        final creditNoteModel = CreditNoteModel.fromJson(nullJson);

        expect(creditNoteModel.id, isEmpty);
        expect(creditNoteModel.customerId, isEmpty);
        expect(creditNoteModel.customerName, isEmpty);
        expect(creditNoteModel.creditNoteId, isEmpty);
        expect(creditNoteModel.creditNoteNumber, isEmpty);
        expect(creditNoteModel.itemName, isEmpty);
        expect(creditNoteModel.quantity, equals(0));
        expect(creditNoteModel.amount, equals(0.0));
        expect(creditNoteModel.date, isA<DateTime>());
      });

      test('should handle different date formats', () {
        final differentDateJson = Map<String, dynamic>.from(testJson);
        differentDateJson['date1'] =
            'Mon Jan 15 2024 12:30:45 GMT+0000 (Coordinated Universal Time)';

        final creditNoteModel = CreditNoteModel.fromJson(differentDateJson);

        expect(creditNoteModel.date, isA<DateTime>());
        expect(creditNoteModel.date.year, equals(2024));
        expect(creditNoteModel.date.month, equals(1));
        expect(creditNoteModel.date.day, equals(15));
      });

      test('should handle invalid date format gracefully', () {
        final invalidDateJson = Map<String, dynamic>.from(testJson);
        invalidDateJson['date1'] = 'invalid date string';

        final creditNoteModel = CreditNoteModel.fromJson(invalidDateJson);

        expect(creditNoteModel.date, isA<DateTime>());
        // Should fallback to current date when parsing fails
      });

      test('should handle empty date string', () {
        final emptyDateJson = Map<String, dynamic>.from(testJson);
        emptyDateJson['date1'] = '';

        final creditNoteModel = CreditNoteModel.fromJson(emptyDateJson);

        expect(creditNoteModel.date, isA<DateTime>());
      });

      test('should handle missing date field', () {
        final noDateJson = Map<String, dynamic>.from(testJson);
        noDateJson.remove('date1');

        final creditNoteModel = CreditNoteModel.fromJson(noDateJson);

        expect(creditNoteModel.date, isA<DateTime>());
      });
    });

    group('Entity Conversion Tests', () {
      test('should convert CreditNoteModel to CreditNote entity correctly', () {
        final creditNoteEntity = testCreditNoteModel.toEntity();

        expect(creditNoteEntity, isA<CreditNote>());
        expect(creditNoteEntity.id, equals(testCreditNoteModel.id));
        expect(
          creditNoteEntity.customerId,
          equals(testCreditNoteModel.customerId),
        );
        expect(
          creditNoteEntity.customerName,
          equals(testCreditNoteModel.customerName),
        );
        expect(creditNoteEntity.date, equals(testCreditNoteModel.date));
        expect(
          creditNoteEntity.creditNoteId,
          equals(testCreditNoteModel.creditNoteId),
        );
        expect(
          creditNoteEntity.creditNoteNumber,
          equals(testCreditNoteModel.creditNoteNumber),
        );
        expect(
          creditNoteEntity.invoiceId,
          equals(testCreditNoteModel.invoiceId),
        );
        expect(
          creditNoteEntity.productId,
          equals(testCreditNoteModel.productId),
        );
        expect(creditNoteEntity.itemName, equals(testCreditNoteModel.itemName));
        expect(creditNoteEntity.brand, equals(testCreditNoteModel.brand));
        expect(
          creditNoteEntity.itemCategory,
          equals(testCreditNoteModel.itemCategory),
        );
        expect(
          creditNoteEntity.categoryType,
          equals(testCreditNoteModel.categoryType),
        );
        expect(creditNoteEntity.quantity, equals(testCreditNoteModel.quantity));
        expect(creditNoteEntity.amount, equals(testCreditNoteModel.amount));
        expect(creditNoteEntity.tag, equals(testCreditNoteModel.tag));
        expect(creditNoteEntity.category, equals(testCreditNoteModel.category));
      });

      test('should maintain data integrity during entity conversion', () {
        final convertedEntity = testCreditNoteModel.toEntity();

        expect(convertedEntity.id, equals(testJson['_id']));
        expect(convertedEntity.customerId, equals(testJson['customerId']));
        expect(convertedEntity.customerName, equals(testJson['customerName']));
        expect(convertedEntity.creditNoteId, equals(testJson['creditNoteId']));
        expect(
          convertedEntity.creditNoteNumber,
          equals(testJson['creditNoteNumber']),
        );
        expect(convertedEntity.invoiceId, equals(testJson['invoiceId']));
        expect(convertedEntity.productId, equals(testJson['productId']));
        expect(convertedEntity.itemName, equals(testJson['itemName']));
        expect(convertedEntity.brand, equals(testJson['brand']));
        expect(convertedEntity.itemCategory, equals(testJson['itemCategory']));
        expect(convertedEntity.categoryType, equals(testJson['categoryType']));
        expect(
          convertedEntity.quantity,
          equals(int.parse(testJson['quantity'])),
        );
        expect(
          convertedEntity.amount,
          equals(double.parse(testJson['amount'])),
        );
        expect(convertedEntity.tag, equals(testJson['tag']));
        expect(convertedEntity.category, equals(testJson['category']));
      });

      test(
        'should convert model with null optional fields to entity correctly',
        () {
          final modelWithNulls = CreditNoteModel(
            id: 'credit_note_123',
            customerId: 'customer_123',
            customerName: 'Test Customer',
            date: testDateTime,
            creditNoteId: 'cn_123',
            creditNoteNumber: 'CN-2023-001',
            itemName: 'Test Product',
            quantity: 5,
            amount: 2500.50,
          );

          final entity = modelWithNulls.toEntity();

          expect(entity.invoiceId, isNull);
          expect(entity.productId, isNull);
          expect(entity.brand, isNull);
          expect(entity.itemCategory, isNull);
          expect(entity.categoryType, isNull);
          expect(entity.tag, isNull);
          expect(entity.category, isNull);
        },
      );
    });

    group('Edge Cases and Validation Tests', () {
      test('should handle special characters in string fields', () {
        final specialCharJson = {
          '_id': 'credit_note_123 & special',
          'customerId': 'customer_123-special',
          'customerName': 'Test Customer & Co.',
          'date1':
              'Fri Sep 01 2023 00:00:00 GMT+0000 (Coordinated Universal Time)',
          'creditNoteId': 'cn_123 (revised)',
          'creditNoteNumber': 'CN-2023-001 & updated',
          'invoiceId': 'invoice_123-special',
          'productId': 'product_123 & variant',
          'itemName': 'Test Product & Accessories',
          'brand': 'Test Brand (Premium)',
          'itemCategory': 'Feed & Supplements',
          'categoryType': 'Aquaculture & Marine',
          'quantity': '5',
          'amount': '2500.50',
          'tag': 'refund & adjustment',
          'category': 'product_return & exchange',
        };

        final creditNoteModel = CreditNoteModel.fromJson(specialCharJson);

        expect(creditNoteModel.id, equals('credit_note_123 & special'));
        expect(creditNoteModel.customerId, equals('customer_123-special'));
        expect(creditNoteModel.customerName, equals('Test Customer & Co.'));
        expect(creditNoteModel.creditNoteId, equals('cn_123 (revised)'));
        expect(
          creditNoteModel.creditNoteNumber,
          equals('CN-2023-001 & updated'),
        );
        expect(creditNoteModel.invoiceId, equals('invoice_123-special'));
        expect(creditNoteModel.productId, equals('product_123 & variant'));
        expect(creditNoteModel.itemName, equals('Test Product & Accessories'));
        expect(creditNoteModel.brand, equals('Test Brand (Premium)'));
        expect(creditNoteModel.itemCategory, equals('Feed & Supplements'));
        expect(creditNoteModel.categoryType, equals('Aquaculture & Marine'));
        expect(creditNoteModel.tag, equals('refund & adjustment'));
        expect(creditNoteModel.category, equals('product_return & exchange'));
      });

      test('should handle extreme numeric values', () {
        final extremeJson = {
          '_id': 'credit_note_123',
          'customerId': 'customer_123',
          'customerName': 'Test Customer',
          'creditNoteId': 'cn_123',
          'creditNoteNumber': 'CN-2023-001',
          'itemName': 'Test Product',
          'quantity': '999999',
          'amount': '999999999.99',
          'date1':
              'Fri Sep 01 2023 00:00:00 GMT+0000 (Coordinated Universal Time)',
        };

        final creditNoteModel = CreditNoteModel.fromJson(extremeJson);

        expect(creditNoteModel.quantity, equals(999999));
        expect(creditNoteModel.amount, equals(999999999.99));
      });

      test('should handle negative numeric values', () {
        final negativeJson = {
          '_id': 'credit_note_123',
          'customerId': 'customer_123',
          'customerName': 'Test Customer',
          'creditNoteId': 'cn_123',
          'creditNoteNumber': 'CN-2023-001',
          'itemName': 'Test Product',
          'quantity': '-5',
          'amount': '-2500.50',
          'date1':
              'Fri Sep 01 2023 00:00:00 GMT+0000 (Coordinated Universal Time)',
        };

        final creditNoteModel = CreditNoteModel.fromJson(negativeJson);

        expect(creditNoteModel.quantity, equals(-5));
        expect(creditNoteModel.amount, equals(-2500.50));
      });

      test('should handle invalid numeric strings', () {
        final invalidNumericJson = {
          '_id': 'credit_note_123',
          'customerId': 'customer_123',
          'customerName': 'Test Customer',
          'creditNoteId': 'cn_123',
          'creditNoteNumber': 'CN-2023-001',
          'itemName': 'Test Product',
          'quantity': 'invalid_number',
          'amount': 'not_a_number',
          'date1':
              'Fri Sep 01 2023 00:00:00 GMT+0000 (Coordinated Universal Time)',
        };

        final creditNoteModel = CreditNoteModel.fromJson(invalidNumericJson);

        expect(creditNoteModel.quantity, equals(0));
        expect(creditNoteModel.amount, equals(0.0));
      });

      test('should handle numeric values as numbers instead of strings', () {
        final numericJson = {
          '_id': 'credit_note_123',
          'customerId': 'customer_123',
          'customerName': 'Test Customer',
          'creditNoteId': 'cn_123',
          'creditNoteNumber': 'CN-2023-001',
          'itemName': 'Test Product',
          'quantity': 5,
          'amount': 2500.50,
          'date1':
              'Fri Sep 01 2023 00:00:00 GMT+0000 (Coordinated Universal Time)',
        };

        final creditNoteModel = CreditNoteModel.fromJson(numericJson);

        expect(creditNoteModel.quantity, equals(5));
        expect(creditNoteModel.amount, equals(2500.50));
      });

      test('should handle very long string values', () {
        final longString = 'A' * 1000;
        final longStringJson = {
          '_id': longString,
          'customerId': longString,
          'customerName': longString,
          'creditNoteId': longString,
          'creditNoteNumber': longString,
          'itemName': longString,
          'brand': longString,
          'itemCategory': longString,
          'categoryType': longString,
          'tag': longString,
          'category': longString,
          'quantity': '5',
          'amount': '2500.50',
          'date1':
              'Fri Sep 01 2023 00:00:00 GMT+0000 (Coordinated Universal Time)',
        };

        final creditNoteModel = CreditNoteModel.fromJson(longStringJson);

        expect(creditNoteModel.id.length, equals(1000));
        expect(creditNoteModel.customerId.length, equals(1000));
        expect(creditNoteModel.customerName.length, equals(1000));
        expect(creditNoteModel.creditNoteId.length, equals(1000));
        expect(creditNoteModel.creditNoteNumber.length, equals(1000));
        expect(creditNoteModel.itemName.length, equals(1000));
        expect(creditNoteModel.brand!.length, equals(1000));
        expect(creditNoteModel.itemCategory!.length, equals(1000));
        expect(creditNoteModel.categoryType!.length, equals(1000));
        expect(creditNoteModel.tag!.length, equals(1000));
        expect(creditNoteModel.category!.length, equals(1000));
      });

      test('should handle unicode characters', () {
        final unicodeJson = {
          '_id': 'credit_note_123',
          'customerId': 'customer_123',
          'customerName': 'Test Customer 测试客户',
          'creditNoteId': 'cn_123',
          'creditNoteNumber': 'CN-2023-001',
          'itemName': 'Test Product 测试产品',
          'brand': 'Test Brand ブランド',
          'itemCategory': 'Feed корм',
          'categoryType': 'Aquaculture मछली पालन',
          'tag': 'refund استرداد',
          'category': 'product_return العائد',
          'quantity': '5',
          'amount': '2500.50',
          'date1':
              'Fri Sep 01 2023 00:00:00 GMT+0000 (Coordinated Universal Time)',
        };

        final creditNoteModel = CreditNoteModel.fromJson(unicodeJson);

        expect(creditNoteModel.customerName, equals('Test Customer 测试客户'));
        expect(creditNoteModel.itemName, equals('Test Product 测试产品'));
        expect(creditNoteModel.brand, equals('Test Brand ブランド'));
        expect(creditNoteModel.itemCategory, equals('Feed корм'));
        expect(creditNoteModel.categoryType, equals('Aquaculture मछली पालन'));
        expect(creditNoteModel.tag, equals('refund استرداد'));
        expect(creditNoteModel.category, equals('product_return العائد'));
      });
    });
  });
}
