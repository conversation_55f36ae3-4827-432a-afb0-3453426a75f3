import 'package:objectbox/objectbox.dart';

import '../../../objectbox.g.dart';
import '../../models/customer_scheme_model.dart';

abstract class CustomerSchemeLocalDataSource {
  Future<CustomerSchemeModel?> getCustomerScheme(String customerId);
  Future<void> cacheCustomerScheme(CustomerSchemeModel customerScheme);
  Future<void> clearCustomerScheme(String customerId);
}

class CustomerSchemeLocalDataSourceImpl
    implements CustomerSchemeLocalDataSource {
  final Box<CustomerSchemeModel> customerSchemeBox;

  CustomerSchemeLocalDataSourceImpl({required this.customerSchemeBox});

  @override
  Future<CustomerSchemeModel?> getCustomerScheme(String customerId) async {
    final query =
        customerSchemeBox
            .query(CustomerSchemeModel_.customerId.equals(customerId))
            .build();
    final result = query.findFirst();
    query.close();
    return result;
  }

  @override
  Future<void> cacheCustomerScheme(CustomerSchemeModel customerScheme) async {
    customerSchemeBox.put(customerScheme);
  }

  @override
  Future<void> clearCustomerScheme(String customerId) async {
    final query =
        customerSchemeBox
            .query(CustomerSchemeModel_.customerId.equals(customerId))
            .build();
    final entities = query.find();
    query.close();

    for (final entity in entities) {
      customerSchemeBox.remove(entity.id);
    }
  }
}
