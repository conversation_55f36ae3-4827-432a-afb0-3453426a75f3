import 'package:dartz/dartz.dart';
import '../entities/user.dart';
import '../../core/error/failures.dart';

abstract class UserRepository {
  Future<Either<Failure, User>> getUser();
  Future<Either<Failure, bool>> saveUser(User user);
  Future<Either<Failure, bool>> updateUser(User user);
  Future<Either<Failure, bool>> deleteUser();
  Future<Either<Failure, bool?>> syncUser();
  Stream<bool> getSyncStatus();
  Future<Either<Failure, bool>> hasUser();
}
