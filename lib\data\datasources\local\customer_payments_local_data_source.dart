import 'package:aquapartner/core/utils/logger.dart';

import '../../../core/error/exceptions.dart';
import '../../../objectbox.g.dart';
import '../../models/payments/customer_payment_model.dart';

abstract class CustomerPaymentsLocalDataSource {
  Future<List<CustomerPaymentModel>> getCustomerPayments(String customerId);
  Future<void> cacheCustomerPayments(List<CustomerPaymentModel> payments);
}

class CustomerPaymentsLocalDataSourceImpl
    implements CustomerPaymentsLocalDataSource {
  final Box<CustomerPaymentModel> customerPaymentsBox;
  final AppLogger logger;

  CustomerPaymentsLocalDataSourceImpl({
    required this.customerPaymentsBox,
    required this.logger,
  });

  @override
  Future<List<CustomerPaymentModel>> getCustomerPayments(
    String customerId,
  ) async {
    try {
      final query =
          customerPaymentsBox
              .query(CustomerPaymentModel_.customerId.equals(customerId))
              .build();
      final payments = query.find();
      query.close();
      return payments;
    } catch (e) {
      throw CacheException();
    }
  }

  @override
  Future<void> cacheCustomerPayments(
    List<CustomerPaymentModel> payments,
  ) async {
    try {
      // First, remove existing payments for this customer
      if (payments.isNotEmpty) {
        final customerId = payments.first.customerId;
        final query =
            customerPaymentsBox
                .query(CustomerPaymentModel_.customerId.equals(customerId))
                .build();
        final existingIds = query.find().map((p) => p.dbId).toList();
        query.close();

        if (existingIds.isNotEmpty) {
          customerPaymentsBox.removeMany(existingIds);
        }
      }

      for (var i = 0; i < payments.length; i++) {
        try {
          final payment = payments[i];
          logger.i("Inserting payment: ${payment.toString()}");
          customerPaymentsBox.put(payment);
        } catch (e) {
          logger.e("Error inserting payment at index $i: $e");
          // Skip this record or handle as needed
        }
      }
      // customerPaymentsBox.putMany(payments);
    } catch (e) {
      throw CacheException();
    }
  }
}
