import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../../lib/presentation/screens/zoho_payment_web_view.dart';

void main() {
  group('ZohoPaymentWebView', () {
    group('URL Validation', () {
      testWidgets('should handle complete HTTPS URL correctly', (tester) async {
        const validUrl = 'https://payments.zoho.com/checkout/session123';
        bool paymentCompleted = false;
        String? transactionId;

        await tester.pumpWidget(
          MaterialApp(
            home: ZohoPaymentWebView(
              paymentUrl: validUrl,
              onPaymentComplete: (success, txnId) {
                paymentCompleted = success;
                transactionId = txnId;
              },
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Should not show error view
        expect(find.text('Payment Error'), findsNothing);
        expect(find.byType(WebViewWidget), findsOneWidget);
      });

      testWidgets('should handle URL without scheme', (tester) async {
        const urlWithoutScheme = 'payments.zoho.com/checkout/session123';
        bool paymentCompleted = false;

        await tester.pumpWidget(
          MaterialApp(
            home: ZohoPaymentWebView(
              paymentUrl: urlWithoutScheme,
              onPaymentComplete: (success, txnId) {
                paymentCompleted = success;
              },
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Should not show error view (URL should be fixed automatically)
        expect(find.text('Payment Error'), findsNothing);
        expect(find.byType(WebViewWidget), findsOneWidget);
      });

      testWidgets('should handle relative URL path', (tester) async {
        const relativePath = '/api/payments/checkout/session123';
        bool paymentCompleted = false;

        await tester.pumpWidget(
          MaterialApp(
            home: ZohoPaymentWebView(
              paymentUrl: relativePath,
              onPaymentComplete: (success, txnId) {
                paymentCompleted = success;
              },
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Should not show error view (URL should be fixed with base URL)
        expect(find.text('Payment Error'), findsNothing);
        expect(find.byType(WebViewWidget), findsOneWidget);
      });

      testWidgets('should handle protocol-relative URL', (tester) async {
        const protocolRelativeUrl = '//payments.zoho.com/checkout/session123';
        bool paymentCompleted = false;

        await tester.pumpWidget(
          MaterialApp(
            home: ZohoPaymentWebView(
              paymentUrl: protocolRelativeUrl,
              onPaymentComplete: (success, txnId) {
                paymentCompleted = success;
              },
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Should not show error view (URL should be fixed with https:)
        expect(find.text('Payment Error'), findsNothing);
        expect(find.byType(WebViewWidget), findsOneWidget);
      });

      testWidgets('should show error for empty URL', (tester) async {
        const emptyUrl = '';
        bool paymentCompleted = false;

        await tester.pumpWidget(
          MaterialApp(
            home: ZohoPaymentWebView(
              paymentUrl: emptyUrl,
              onPaymentComplete: (success, txnId) {
                paymentCompleted = success;
              },
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Should show error view
        expect(find.text('Payment Error'), findsOneWidget);
        expect(find.text('Retry'), findsOneWidget);
        expect(find.text('Cancel'), findsOneWidget);
        expect(find.byType(WebViewWidget), findsNothing);
      });

      testWidgets('should handle URL with whitespace', (tester) async {
        const urlWithWhitespace = '  https://payments.zoho.com/checkout/session123  ';
        bool paymentCompleted = false;

        await tester.pumpWidget(
          MaterialApp(
            home: ZohoPaymentWebView(
              paymentUrl: urlWithWhitespace,
              onPaymentComplete: (success, txnId) {
                paymentCompleted = success;
              },
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Should not show error view (whitespace should be trimmed)
        expect(find.text('Payment Error'), findsNothing);
        expect(find.byType(WebViewWidget), findsOneWidget);
      });
    });

    group('Navigation Handling', () {
      testWidgets('should detect success URL patterns', (tester) async {
        const validUrl = 'https://payments.zoho.com/checkout/session123';
        bool paymentCompleted = false;
        String? transactionId;

        await tester.pumpWidget(
          MaterialApp(
            home: ZohoPaymentWebView(
              paymentUrl: validUrl,
              onPaymentComplete: (success, txnId) {
                paymentCompleted = success;
                transactionId = txnId;
              },
            ),
          ),
        );

        await tester.pumpAndSettle();

        // The WebView should be present
        expect(find.byType(WebViewWidget), findsOneWidget);
      });

      testWidgets('should handle close button tap', (tester) async {
        const validUrl = 'https://payments.zoho.com/checkout/session123';
        bool paymentCompleted = false;
        String? transactionId;

        await tester.pumpWidget(
          MaterialApp(
            home: ZohoPaymentWebView(
              paymentUrl: validUrl,
              onPaymentComplete: (success, txnId) {
                paymentCompleted = success;
                transactionId = txnId;
              },
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Find and tap the close button
        final closeButton = find.byIcon(Icons.close);
        expect(closeButton, findsOneWidget);

        await tester.tap(closeButton);
        await tester.pumpAndSettle();

        // Payment should be marked as failed/cancelled
        expect(paymentCompleted, false);
        expect(transactionId, null);
      });
    });

    group('Error Handling', () {
      testWidgets('should show retry button on error', (tester) async {
        const invalidUrl = '';
        bool paymentCompleted = false;

        await tester.pumpWidget(
          MaterialApp(
            home: ZohoPaymentWebView(
              paymentUrl: invalidUrl,
              onPaymentComplete: (success, txnId) {
                paymentCompleted = success;
              },
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Should show error view with retry button
        expect(find.text('Payment Error'), findsOneWidget);
        expect(find.text('Retry'), findsOneWidget);
        expect(find.byIcon(Icons.error_outline), findsOneWidget);
      });

      testWidgets('should handle cancel button in error view', (tester) async {
        const invalidUrl = '';
        bool paymentCompleted = false;
        String? transactionId;

        await tester.pumpWidget(
          MaterialApp(
            home: ZohoPaymentWebView(
              paymentUrl: invalidUrl,
              onPaymentComplete: (success, txnId) {
                paymentCompleted = success;
                transactionId = txnId;
              },
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Find and tap the cancel button
        final cancelButton = find.text('Cancel');
        expect(cancelButton, findsOneWidget);

        await tester.tap(cancelButton);
        await tester.pumpAndSettle();

        // Payment should be marked as failed/cancelled
        expect(paymentCompleted, false);
        expect(transactionId, null);
      });
    });

    group('Real-world URL Scenarios', () {
      testWidgets('should handle typical Zoho payment URL format', (tester) async {
        const zohoUrl = 'https://checkout.zoho.com/v2/payment/5619000000231070';
        bool paymentCompleted = false;

        await tester.pumpWidget(
          MaterialApp(
            home: ZohoPaymentWebView(
              paymentUrl: zohoUrl,
              onPaymentComplete: (success, txnId) {
                paymentCompleted = success;
              },
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Should load successfully
        expect(find.text('Payment Error'), findsNothing);
        expect(find.byType(WebViewWidget), findsOneWidget);
      });

      testWidgets('should handle AquaPartner API payment URL', (tester) async {
        const apiUrl = '/api/zoho/payments/checkout/session123';
        bool paymentCompleted = false;

        await tester.pumpWidget(
          MaterialApp(
            home: ZohoPaymentWebView(
              paymentUrl: apiUrl,
              onPaymentComplete: (success, txnId) {
                paymentCompleted = success;
              },
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Should load successfully with base URL prepended
        expect(find.text('Payment Error'), findsNothing);
        expect(find.byType(WebViewWidget), findsOneWidget);
      });
    });
  });
}
