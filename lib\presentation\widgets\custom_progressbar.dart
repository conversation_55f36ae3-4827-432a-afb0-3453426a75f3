import 'package:aqua_ui/aqua_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import '../../core/constants/app_constants.dart';

class CustomProgressBar extends StatelessWidget {
  final double percentage;

  const CustomProgressBar({super.key, required this.percentage});

  @override
  Widget build(BuildContext context) {
    // Clamp percentage between 0 and 100 to avoid layout issues
    final clampedPercentage = percentage.clamp(0.0, 100.0);
    final progressFactor = clampedPercentage / 100;

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Progress indicators (walking man and destination)
        LayoutBuilder(
          builder: (context, constraints) {
            final availableWidth = constraints.maxWidth * 0.87;
            return Stack(
              children: [
                // Destination icon (always at the end)
                Align(
                  alignment: Alignment.centerRight,
                  child: _buildSvgIcon(AppConstants.baliFlightIcon),
                ),
                // Walking man (moves based on progress)
                Padding(
                  padding: EdgeInsets.only(
                    left: availableWidth * progressFactor,
                  ),
                  child: _buildSvgIcon(AppConstants.walkingMan),
                ),
              ],
            );
          },
        ),
        const SizedBox(height: 4), // Add spacing between icons and progress bar
        // Progress bar
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 0),
          child: Stack(
            children: [
              // Background dashed line
              SizedBox(
                height: 2,
                width: double.infinity,
                child: CustomPaint(
                  painter: DashedLinePainter(
                    color: Colors.grey[300] ?? Colors.grey,
                  ),
                ),
              ),
              // Colored progress line
              FractionallySizedBox(
                widthFactor: 0.95 * progressFactor,
                child: Container(height: 2, color: acPrimaryBlue),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Extract SVG icon creation to a separate method
  Widget _buildSvgIcon(String assetPath) {
    return SvgPicture.asset(
      assetPath,
      height: 24,
      colorFilter: ColorFilter.mode(acPrimaryBlue, BlendMode.srcIn),
    );
  }
}

class DashedLinePainter extends CustomPainter {
  final Color color;
  final double dashWidth;
  final double dashSpace;

  const DashedLinePainter({
    required this.color,
    this.dashWidth = 5.0,
    this.dashSpace = 3.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = color
          ..strokeWidth = size.height
          ..strokeCap = StrokeCap.round;

    final dashCount = (size.width / (dashWidth + dashSpace)).ceil();

    for (int i = 0; i < dashCount; i++) {
      final startX = i * (dashWidth + dashSpace);
      final endX = startX + dashWidth;

      // Only draw if the dash is within the canvas bounds
      if (startX < size.width) {
        canvas.drawLine(
          Offset(startX, size.height / 2),
          Offset(endX.clamp(0.0, size.width), size.height / 2),
          paint,
        );
      }
    }
  }

  @override
  bool shouldRepaint(covariant DashedLinePainter oldDelegate) =>
      color != oldDelegate.color ||
      dashWidth != oldDelegate.dashWidth ||
      dashSpace != oldDelegate.dashSpace;
}
