import 'package:dartz/dartz.dart';
import '../../../core/error/failures.dart';
import '../../../core/utils/logger.dart';
import '../../entities/account_statement/account_statement.dart';
import '../../repositories/account_statement_repository.dart';

class GetAccountStatementUseCase {
  final AccountStatementRepository repository;
  final AppLogger logger;

  GetAccountStatementUseCase({
    required this.repository,
    required this.logger,
  });

  Future<Either<Failure, AccountStatement?>> call(String customerId) async {
    logger.i('GetAccountStatementUseCase called for customer: $customerId');
    return repository.getAccountStatement(customerId);
  }
}