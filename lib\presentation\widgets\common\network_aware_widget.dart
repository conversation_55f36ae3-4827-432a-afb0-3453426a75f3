import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../cubit/connectivity/connectivity_cubit.dart';
import '../../cubit/connectivity/connectivity_state.dart';

class NetworkAwareWidget extends StatelessWidget {
  final Widget child;
  final Widget Function(BuildContext context)? offlineBuilder;
  final bool showOfflineBanner;

  const NetworkAwareWidget({
    super.key,
    required this.child,
    this.offlineBuilder,
    this.showOfflineBanner = true,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ConnectivityCubit, ConnectivityState>(
      buildWhen: (previous, current) => 
          previous.isConnected != current.isConnected,
      builder: (context, state) {
        if (!state.isConnected) {
          // If we have a custom offline builder, use it
          if (offlineBuilder != null) {
            return offlineBuilder!(context);
          }
          
          // Otherwise, show the child with an offline banner if requested
          return Column(
            children: [
              if (showOfflineBanner)
                Container(
                  color: Colors.red,
                  padding: const EdgeInsets.all(8.0),
                  child: const Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.wifi_off, color: Colors.white),
                      SizedBox(width: 8.0),
                      Expanded(
                        child: Text(
                          'You are offline. Some features may be limited.',
                          style: TextStyle(color: Colors.white),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ],
                  ),
                ),
              Expanded(child: child),
            ],
          );
        }
        
        // Online - just show the child
        return child;
      },
    );
  }
}