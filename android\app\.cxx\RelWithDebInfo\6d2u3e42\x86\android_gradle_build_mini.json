{"buildFiles": ["C:\\Users\\<USER>\\dev\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\flutter projects\\aquapartner\\android\\app\\.cxx\\RelWithDebInfo\\6d2u3e42\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\flutter projects\\aquapartner\\android\\app\\.cxx\\RelWithDebInfo\\6d2u3e42\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}