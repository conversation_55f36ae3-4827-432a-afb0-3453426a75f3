import 'package:equatable/equatable.dart';

/// Base failure class for all failures in the application
abstract class Failure extends Equatable {
  @override
  List<Object> get props => [];
}

/// Failure that occurs when there is a server error
class ServerFailure extends Failure {}

/// Failure that occurs when there is a cache error
class CacheFailure extends Failure {}

/// Failure that occurs when there is a network error
class NetworkFailure extends Failure {}

/// Failure that occurs when there is an authentication error
class AuthFailure extends Failure {}

/// Failure that occurs when there is an authentication error
class UserAuthFailure extends Failure {}

class NotFoundFailure extends Failure {}

/// Failure that occurs when there is a validation error
class ValidationFailure extends Failure {
  final String message;

  ValidationFailure(this.message);

  @override
  List<Object> get props => [message];
}

/// Failure that occurs when there is an unexpected error
class UnexpectedFailure extends Failure {
  final String message;

  UnexpectedFailure(this.message);

  @override
  List<Object> get props => [message];
}
