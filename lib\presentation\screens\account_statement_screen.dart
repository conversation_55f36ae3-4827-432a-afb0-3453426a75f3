import 'package:aqua_ui/aqua_ui.dart';
import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:open_file/open_file.dart';
import '../../core/mixins/analytics_mixin.dart';
import '../../core/services/analytics_service.dart';
import '../../core/services/pdf_service.dart';
import '../../core/utils/currency_formatter.dart';
import '../../core/utils/logger.dart';
import '../../domain/entities/account_statement/account_statement_entity.dart';
import '../../injection_container.dart' as di;
import '../cubit/account_statement/account_statement_cubit.dart';
import '../cubit/account_statement/account_statement_state.dart';
import '../widgets/loading_widget.dart';

class AccountStatementScreen extends StatefulWidget {
  final AppLogger logger = AppLogger();
  AccountStatementScreen({super.key});

  @override
  State<AccountStatementScreen> createState() => _AccountStatementScreenState();
}

class _AccountStatementScreenState extends State<AccountStatementScreen>
    with AnalyticsMixin {
  @override
  void initState() {
    super.initState();
    // Load data when screen initializes
    context.read<AccountStatementCubit>().loadAccountStatement();
  }

  // Helper method to standardize and log opening balance calculation
  // Moved into State class to use widget.logger
  double _calculateOpeningBalance(List<AccountStatementEntity> statements) {
    widget.logger.i(
      '[AccountStatementScreen._calculateOpeningBalance] Calculating. Statements count: ${statements.length}',
    );

    if (statements.isEmpty) {
      widget.logger.i(
        '[AccountStatementScreen._calculateOpeningBalance] Statements empty, returning 0.0 for opening balance.',
      );
      return 0.0;
    }

    final firstStatement = statements.first;
    widget.logger.i(
      '[AccountStatementScreen._calculateOpeningBalance] First statement - Type: ${firstStatement.vchType}, Particulars: ${firstStatement.particulars}, Balance: ${firstStatement.balance}, Debit: ${firstStatement.debit}, Credit: ${firstStatement.credit}',
    );

    if (firstStatement.vchType == 'Opening Balance') {
      widget.logger.i(
        '[AccountStatementScreen._calculateOpeningBalance] Explicit Opening Balance entry found. OB: ${firstStatement.balance}',
      );
      return firstStatement.balance;
    } else {
      widget.logger.i(
        '[AccountStatementScreen._calculateOpeningBalance] No explicit OB entry. Deducing from first transaction.',
      );
      // This assumes 'firstStatement.balance' is the balance AFTER this transaction.
      if (firstStatement.debit > 0) {
        final deducedOb = firstStatement.balance + firstStatement.debit;
        widget.logger.i(
          '[AccountStatementScreen._calculateOpeningBalance] First transaction is a Debit. Deduced OB: ${firstStatement.balance} + ${firstStatement.debit} = $deducedOb',
        );
        return deducedOb;
      } else if (firstStatement.credit > 0) {
        final deducedOb = firstStatement.balance - firstStatement.credit;
        widget.logger.i(
          '[AccountStatementScreen._calculateOpeningBalance] First transaction is a Credit. Deduced OB: ${firstStatement.balance} - ${firstStatement.credit} = $deducedOb',
        );
        return deducedOb;
      } else {
        widget.logger.i(
          '[AccountStatementScreen._calculateOpeningBalance] First transaction has no debit/credit. Using its balance as OB: ${firstStatement.balance}',
        );
        return firstStatement.balance; // Fallback
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocBuilder<AccountStatementCubit, AccountStatementState>(
        builder: (context, state) {
          // Show loading indicator
          if (state is AccountStatementInitial ||
              (state is AccountStatementLoading && !(state).hasPreviousData)) {
            return LoadingWidget(message: "Fetching your account statement");
          }

          // Show data if loaded
          if (state is AccountStatementLoaded) {
            final entries = state.filteredEntries ?? [];
            final fromDate = DateFormat(
              'dd MMM yyyy',
            ).format(state.dateRange!.startDate);
            final toDate = DateFormat(
              'dd MMM yyyy',
            ).format(state.dateRange!.endDate);

            return Stack(
              children: [
                // Main content
                entries.isNotEmpty
                    ? _buildStatementContent(
                      entries,
                      fromDate,
                      toDate,
                      state.selectedFilter,
                      state.isSyncing,
                      state.customer, // Pass customer data
                    )
                    : Center(
                      child: AquaText.subheadline(
                        "You don't have any account statement for this period",
                      ),
                    ),

                // Syncing overlay
                if (state.isSyncing)
                  Positioned.fill(
                    child: Container(
                      color: Colors.black.withOpacity(0.1), // Corrected opacity
                      child: Center(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            CircularProgressIndicator(),
                            SizedBox(height: 16),
                            // Optional: Add a text like "Syncing..."
                          ],
                        ),
                      ),
                    ),
                  ),
              ],
            );
          }

          // Show error state
          if (state is AccountStatementError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, size: 48, color: Colors.red),
                  SizedBox(height: 16),
                  AquaText.subheading(state.message),
                  SizedBox(height: 24),
                  AquaButton(
                    title: "Try Again",
                    onTap: () {
                      context
                          .read<AccountStatementCubit>()
                          .loadAccountStatement();
                    },
                  ),
                ],
              ),
            );
          }

          // Default loading state
          return LoadingWidget(message: "Loading account statement");
        },
      ),
    );
  }

  Widget _buildStatementContent(
    List<AccountStatementEntity> statements,
    String fromDate,
    String toDate,
    String selectedFilter,
    bool isSyncing,
    dynamic customer, // Added customer parameter
  ) {
    // Log the received statements for debugging
    widget.logger.i(
      '[AccountStatementScreen._buildStatementContent] Building. Statements count: ${statements.length}',
    );
    if (statements.isNotEmpty) {
      final first = statements.first;
      widget.logger.i(
        '[AccountStatementScreen._buildStatementContent] First statement for OB calc - Type: ${first.vchType}, Particulars: ${first.particulars}, Balance: ${first.balance}, Debit: ${first.debit}, Credit: ${first.credit}',
      );
    }

    // Calculate opening and closing balances ONCE
    final openingBalance = _calculateOpeningBalance(statements);
    final closingBalance =
        statements.isNotEmpty ? statements.last.balance : 0.0;

    widget.logger.i(
      '[AccountStatementScreen._buildStatementContent] Opening balance calculated: $openingBalance',
    );
    widget.logger.i(
      '[AccountStatementScreen._buildStatementContent] Closing balance calculated: $closingBalance',
    );

    // Enhanced logging for opening balance (as in original, for consistency)
    final hasExplicitOpeningBalanceEntry =
        statements.isNotEmpty && statements.first.vchType == 'Opening Balance';

    // widget.logger.i('Opening balance calculated: $openingBalance'); // Already logged above
    widget.logger.i(
      '[AccountStatementScreen._buildStatementContent] Has explicit opening balance entry: $hasExplicitOpeningBalanceEntry',
    );

    if (hasExplicitOpeningBalanceEntry && statements.isNotEmpty) {
      widget.logger.i(
        '[AccountStatementScreen._buildStatementContent] Opening balance entry details:',
      );
      widget.logger.i('  Date: ${statements.first.txnDate}');
      widget.logger.i('  Type: ${statements.first.vchType}');
      widget.logger.i('  Particulars: ${statements.first.particulars}');
      widget.logger.i('  Balance: ${statements.first.balance}');
    }

    final currentState = context.read<AccountStatementCubit>().state;
    final List<String> periods =
        currentState is AccountStatementLoaded ? currentState.periods : [];
    // Ensure dateRange is available from currentState if needed directly here,
    // otherwise fromDate and toDate are already formatted.
    final dateRange =
        currentState is AccountStatementLoaded ? currentState.dateRange : null;

    return Container(
      color: acWhiteColor,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title and period selector
          Padding(
            padding: const EdgeInsets.only(
              top: 24,
              left: 16,
              bottom: 24,
              right: 16,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    AquaText.subheading(
                      'Account Statement',
                      weight: AquaFontWeight.semibold,
                      color: acTextSecondaryColor,
                    ),
                    Row(
                      children: [
                        // Download button
                        SizedBox(
                          height: 40,
                          child: TextButton(
                            child: AquaText.body(
                              "Download",
                              color: acPrimaryBlue,
                            ),
                            onPressed: () async {
                              trackUserAction('download_statement_clicked');
                              if (statements.isEmpty) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text(
                                      "No transactions to download",
                                    ),
                                  ),
                                );
                                return;
                              }

                              showDialog(
                                context: context,
                                barrierDismissible: false,
                                builder:
                                    (context) => Center(
                                      child: CircularProgressIndicator(),
                                    ),
                              );

                              try {
                                // Customer info should be available from AccountStatementLoaded state
                                // It's passed as 'customer' parameter to _buildStatementContent
                                if (customer == null || dateRange == null) {
                                  if (mounted) Navigator.of(context).pop();
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text(
                                        "Unable to access customer information or date range for PDF",
                                      ),
                                    ),
                                  );
                                  return;
                                }

                                final filteredPdfStatements =
                                    statements
                                        .where(
                                          (statement) =>
                                              !(statement.vchType ==
                                                      'Opening Balance' &&
                                                  statement.particulars
                                                      .contains(
                                                        'Opening Balance',
                                                      )),
                                        )
                                        .toList();

                                final statementItemsForPdf =
                                    filteredPdfStatements
                                        .map(
                                          (statement) => StatementItem(
                                            date: statement.txnDate,
                                            description: statement.particulars,
                                            type:
                                                statement.credit == 0
                                                    ? 'Debit'
                                                    : 'Credit',
                                            amount: statement.amount,
                                            balance: statement.balance,
                                          ),
                                        )
                                        .toList();

                                final pdfFile =
                                    await PdfService.generateAccountStatementPdf(
                                      accountStatementPeriod:
                                          dateRange, // Use dateRange from state
                                      transactions: statementItemsForPdf,
                                      openingBalance:
                                          openingBalance, // Use pre-calculated value
                                      closingBalance:
                                          closingBalance, // Use pre-calculated value
                                      customer:
                                          customer, // Use customer from state
                                    );

                                if (mounted) {
                                  Navigator.of(
                                    context,
                                  ).pop(); // Close loading dialog
                                }
                                await OpenFile.open(pdfFile.path);

                                final analyticsService =
                                    di.sl<AnalyticsService>();
                                analyticsService.logEvent(
                                  name: 'account_statement_download',
                                  parameters: {
                                    'from_date': fromDate,
                                    'to_date': toDate,
                                    'transaction_count':
                                        statementItemsForPdf
                                            .length, // Corrected count
                                    'period': selectedFilter,
                                    'opening_balance':
                                        openingBalance.toString(),
                                    'closing_balance':
                                        closingBalance.toString(),
                                  },
                                );
                              } catch (e) {
                                widget.logger.e(
                                  '[AccountStatementScreen] PDF generation error: $e',
                                );
                                if (mounted) Navigator.of(context).pop();

                                if (mounted) {
                                  String errorMessage =
                                      "Failed to generate PDF";
                                  if (e.toString().contains(
                                    "No such file or directory",
                                  )) {
                                    errorMessage =
                                        "Could not find the logo file. Please check the asset path.";
                                  } else if (e.toString().contains(
                                    "Permission denied",
                                  )) {
                                    errorMessage =
                                        "Permission denied. Please check app permissions.";
                                  } else if (e.toString().contains(
                                    "No space left on device",
                                  )) {
                                    errorMessage =
                                        "Not enough storage space to save the PDF.";
                                  }
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text(errorMessage),
                                      action: SnackBarAction(
                                        label: 'Details',
                                        onPressed: () {
                                          ScaffoldMessenger.of(
                                            context,
                                          ).showSnackBar(
                                            SnackBar(
                                              content: Text(e.toString()),
                                              duration: Duration(seconds: 10),
                                            ),
                                          );
                                        },
                                      ),
                                    ),
                                  );
                                }
                              }
                            },
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                AquaText.body(
                  '$fromDate to $toDate',
                  color: Colors.grey.shade600,
                ),
                const SizedBox(height: 8),
                Container(
                  decoration: BoxDecoration(
                    border: Border.all(color: acGrey200),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: DropdownButtonHideUnderline(
                    child: DropdownButton2<String>(
                      value: selectedFilter,
                      isExpanded: true,
                      buttonStyleData: ButtonStyleData(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        padding: EdgeInsets.only(right: 16),
                      ),
                      dropdownStyleData: DropdownStyleData(
                        padding: EdgeInsets.only(right: 0),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          color: Colors.white,
                          border: Border.all(color: acGrey200, width: 1.0),
                        ),
                        elevation: 0,
                        offset: const Offset(0, -5),
                        direction: DropdownDirection.left,
                        maxHeight: 300,
                      ),
                      items:
                          periods.map((String page) {
                            return DropdownMenuItem<String>(
                              value: page,
                              child: Container(
                                padding: const EdgeInsets.only(
                                  right: 0,
                                  left:
                                      0, // AquaText might have its own padding, ensure alignment
                                ),
                                child: Padding(
                                  // Added padding for text within item
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 16.0,
                                    vertical: 12.0,
                                  ),
                                  child: AquaText.body(page),
                                ),
                              ),
                            );
                          }).toList(),
                      onChanged: (String? newValue) {
                        if (newValue != null) {
                          context.read<AccountStatementCubit>().changePeriod(
                            newValue,
                          );
                        }
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Transactions list
          Expanded(
            child:
                statements.isEmpty
                    ? Center(
                      child: AquaText.body(
                        'No transactions found for this period',
                      ),
                    )
                    : ListView.builder(
                      shrinkWrap: false, // Good for performance with Expanded
                      padding: EdgeInsets.symmetric(horizontal: 8),
                      physics: AlwaysScrollableScrollPhysics(),
                      itemCount: statements.length,
                      itemBuilder: (context, index) {
                        final statement = statements[index];
                        return _buildTransactionCard(statement);
                      },
                    ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionCard(AccountStatementEntity transaction) {
    final dateFormat = DateFormat('dd MMM yyyy');
    final isDebit = transaction.debit > 0;
    final isCredit = transaction.credit > 0;

    return Card(
      color: acWhiteColor,
      shape: RoundedRectangleBorder(
        side: BorderSide(width: 0.5, color: acGrey200),
        borderRadius: BorderRadius.circular(12.0),
      ),
      elevation: 0,
      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: AquaText.caption(
                    '${dateFormat.format(transaction.txnDate)} ${transaction.invoiceNumber.isNotEmpty ? "- ${transaction.invoiceNumber}" : ""}',
                    color: acTextSecondaryColor,
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                  decoration: BoxDecoration(
                    color: acGrey100,
                    borderRadius: BorderRadius.circular(4.0),
                    border: Border.all(color: acGrey200),
                  ),
                  child: AquaText.caption(
                    transaction.vchType,
                    color: acTextSecondaryColor,
                  ),
                ),
              ],
            ),
            AquaDivider(), // Assuming AquaDivider is defined in aqua_ui
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    AquaText.body('Debit', color: acBlackColor),
                    AquaText.body(
                      CurrencyFormatter.formatAsINR(
                        transaction.debit,
                        decimalPlaces:
                            2, // Typically 2 decimal places for currency
                      ),
                      color: isDebit ? Colors.red : acBlackColor,
                    ),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    AquaText.body('Credit', color: acBlackColor),
                    AquaText.body(
                      CurrencyFormatter.formatAsINR(
                        transaction.credit,
                        decimalPlaces:
                            2, // Typically 2 decimal places for currency
                      ),
                      color: isCredit ? Colors.green : acBlackColor,
                    ),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    AquaText.body('Balance', color: acBlackColor),
                    AquaText.body(
                      CurrencyFormatter.formatAsINR(
                        transaction.balance,
                        decimalPlaces:
                            2, // Typically 2 decimal places for currency
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 8),
            AquaText.body(transaction.particulars, color: acTextSecondaryColor),
          ],
        ),
      ),
    );
  }

  void trackUserAction(
    String action, {
    Map<String, Object> additionalParams = const {},
  }) {
    final params = {
      'action': action,
      'timestamp': DateTime.now().toIso8601String(),
      ...additionalParams,
    };

    trackEvent('account_statement_action', params: params);
  }

  @override
  String get screenName => "AccountStatementScreen";
}
