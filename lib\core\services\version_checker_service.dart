import 'dart:io';
import 'package:aqua_ui/aqua_ui.dart';
import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import '../../domain/entities/update_info.dart';
import '../../domain/usecases/check_mandatory_update.dart';
import '../../injection_container.dart' as di;

/// @deprecated Use UpdateCheckerCubit instead
/// This service is kept for backward compatibility but will be removed in future versions.
/// Please use the UpdateCheckerCubit for checking mandatory updates.
class VersionCheckerService {
  // Remote config keys
  static const String _minAndroidVersionKey = 'min_android_version';
  static const String _minIOSVersionKey = 'min_ios_version';
  static const String _androidStoreUrlKey = 'android_store_url';
  static const String _iOSStoreUrlKey = 'ios_store_url';

  // Default values in case remote config fails
  static const String _defaultMinVersion = '1.0.0';
  static const String _defaultAndroidStoreUrl =
      'market://details?id=blue.aquaconnect.aquapartner_self_service';
  static const String _defaultAndroidFallbackUrl =
      'https://play.google.com/store/apps/details?id=blue.aquaconnect.aquapartner_self_service';
  static const String _defaultIOSStoreUrl =
      'itms-apps://itunes.apple.com/app/id1234567890';
  static const String _defaultIOSFallbackUrl =
      'https://apps.apple.com/app/id1234567890';

  // Initialize Firebase Remote Config
  static Future<FirebaseRemoteConfig> _initRemoteConfig() async {
    final FirebaseRemoteConfig remoteConfig = FirebaseRemoteConfig.instance;

    await remoteConfig.setConfigSettings(
      RemoteConfigSettings(
        fetchTimeout: const Duration(minutes: 1),
        minimumFetchInterval: const Duration(hours: 1),
      ),
    );

    await remoteConfig.setDefaults({
      _minAndroidVersionKey: _defaultMinVersion,
      _minIOSVersionKey: _defaultMinVersion,
      _androidStoreUrlKey: _defaultAndroidStoreUrl,
      _iOSStoreUrlKey: _defaultIOSStoreUrl,
    });

    try {
      await remoteConfig.fetchAndActivate();
    } catch (e) {
      // Error fetching remote config, will use default values
      print('Error fetching remote config: $e');
    }

    return remoteConfig;
  }

  // Check if update is required
  static Future<bool> isUpdateRequired() async {
    try {
      // Use the CheckMandatoryUpdate use case if available
      try {
        final checkMandatoryUpdate = di.sl<CheckMandatoryUpdate>();
        final result = await checkMandatoryUpdate.execute();
        return result.updateRequired;
      } catch (e) {
        // Fall back to the old implementation if the use case is not available
        final FirebaseRemoteConfig remoteConfig = await _initRemoteConfig();
        final PackageInfo packageInfo = await PackageInfo.fromPlatform();
        final String currentVersion = packageInfo.version;

        String minRequiredVersion;

        if (Platform.isAndroid) {
          minRequiredVersion = remoteConfig.getString(_minAndroidVersionKey);
        } else if (Platform.isIOS) {
          minRequiredVersion = remoteConfig.getString(_minIOSVersionKey);
        } else {
          return false;
        }

        return _isVersionLowerThan(currentVersion, minRequiredVersion);
      }
    } catch (e) {
      print('Error checking for updates: $e');
      return false; // In case of error, don't force update
    }
  }

  // Compare versions
  static bool _isVersionLowerThan(String currentVersion, String minVersion) {
    List<int> currentParts = currentVersion.split('.').map(int.parse).toList();
    List<int> minParts = minVersion.split('.').map(int.parse).toList();

    // Ensure both lists have the same length
    while (currentParts.length < minParts.length) {
      currentParts.add(0);
    }
    while (minParts.length < currentParts.length) {
      minParts.add(0);
    }

    // Compare each part
    for (int i = 0; i < currentParts.length; i++) {
      if (currentParts[i] < minParts[i]) {
        return true;
      } else if (currentParts[i] > minParts[i]) {
        return false;
      }
    }

    return false; // Versions are equal
  }

  // Show update dialog
  static Future<void> showUpdateDialog(BuildContext context) async {
    // Try to get update info from the use case
    UpdateInfo? updateInfo;
    try {
      final checkMandatoryUpdate = di.sl<CheckMandatoryUpdate>();
      final result = await checkMandatoryUpdate.execute();
      if (result.updateRequired) {
        updateInfo = result.updateInfo;
      }
    } catch (e) {
      // Ignore errors and use default message
    }

    await showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => PopScope(
            canPop: false, // Prevent closing with back button
            child: AlertDialog(
              title: AquaText.body('Update Required'),
              content: AquaText.body(
                updateInfo?.message ??
                    'A new version of the app is available. Please update to continue using the app.',
              ),
              actions: [
                TextButton(
                  onPressed: () => _launchAppStore(updateInfo?.storeUrl),
                  child: AquaText.body('Update Now'),
                ),
              ],
            ),
          ),
    );
  }

  // Launch app store
  static Future<void> _launchAppStore([String? storeUrl]) async {
    try {
      if (storeUrl != null) {
        final Uri uri = Uri.parse(storeUrl);
        await launchUrl(uri);
        return;
      }

      final FirebaseRemoteConfig remoteConfig = await _initRemoteConfig();
      final Uri storeUri;
      final Uri fallbackUri;

      if (Platform.isAndroid) {
        final String storeUrl = remoteConfig.getString(_androidStoreUrlKey);
        storeUri = Uri.parse(
          storeUrl.isNotEmpty ? storeUrl : _defaultAndroidStoreUrl,
        );
        fallbackUri = Uri.parse(_defaultAndroidFallbackUrl);
      } else if (Platform.isIOS) {
        final String storeUrl = remoteConfig.getString(_iOSStoreUrlKey);
        storeUri = Uri.parse(
          storeUrl.isNotEmpty ? storeUrl : _defaultIOSStoreUrl,
        );
        fallbackUri = Uri.parse(_defaultIOSFallbackUrl);
      } else {
        return;
      }

      try {
        final bool launched = await launchUrl(storeUri);
        if (!launched) {
          await launchUrl(fallbackUri);
        }
      } catch (e) {
        await launchUrl(fallbackUri);
      }
    } catch (e) {
      print('Error launching app store: $e');
    }
  }
}
