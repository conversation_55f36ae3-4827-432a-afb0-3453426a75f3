import 'package:aqua_ui/aqua_ui.dart';
import 'package:aquapartner/core/routes/app_router.dart';
import 'package:aquapartner/core/utils/currency_formatter.dart';
import 'package:aquapartner/core/utils/logger.dart';
import 'package:aquapartner/presentation/cubit/scheme/scheme_cubit.dart';
import 'package:aquapartner/presentation/widgets/loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:url_launcher/url_launcher.dart';

import '../cubit/scheme/scheme_state.dart';
import 'custom_progressbar.dart';

class SchemeQ3Bali extends StatelessWidget {
  final AppLogger logger = AppLogger();
  SchemeQ3Bali({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: BlocConsumer<SchemeCubit, SchemeState>(
        builder: (context, state) {
          logger.i("Current Scheme State $state");

          if (state is SchemeLoaded) {
            final totalPurchase = state.scheme.sales;
            final totalPayment = state.scheme.payment;

            final ticketsWon = _calculateTicketsWon(
              totalPurchase,
              totalPayment,
            );

            double purchasePending = 600000 * (ticketsWon + 1) - totalPurchase;
            purchasePending = purchasePending <= 0 ? 0 : purchasePending;

            double paymentPending = 600000 * (ticketsWon + 1) - totalPayment;
            paymentPending = paymentPending <= 0 ? 0 : paymentPending;

            final asmPerson = state.scheme.supportPersons.firstWhere(
              (person) => person.profile == 'ASM',
              orElse: () => throw Exception('No ASM found'),
            );

            return Container(
              width: double.infinity,
              padding: const EdgeInsets.all(0),

              decoration: BoxDecoration(
                border: Border.all(
                  color: Color(
                    0xFFE5E7EB,
                  ), // Change this to your desired border color
                  width: 1.0, // Optional: adjust border width
                ),
                color: acWhiteColor,
                borderRadius: BorderRadius.all(Radius.circular(8)),
              ),
              child: Padding(
                padding: const EdgeInsets.all(0.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(6),
                        topRight: Radius.circular(6),
                      ),
                      child: Image.asset(
                        'assets/images/scheme/bali_mobile_banner.jpg',
                        fit: BoxFit.cover,
                        width: double.infinity,
                      ),
                    ),
                    SizedBox(height: 16),
                    Padding(
                      padding: EdgeInsets.only(left: 16, right: 16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          RichText(
                            text: TextSpan(
                              children: [
                                TextSpan(
                                  text: 'Fly to Bali, Indonesia',
                                  style: TextStyle(
                                    color: acPrimaryBlue,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                  ),
                                ),
                                TextSpan(
                                  text: ' by completing the sales numbers.',
                                  style: TextStyle(
                                    color: acPrimaryBlue,
                                    fontSize: 16,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 8),
                          AquaText.body(
                            'Talk to your Aquaconnect Officer,',
                            color: acTextSecondaryColor,
                          ),
                          const SizedBox(height: 8),
                          GestureDetector(
                            onTap: () async {
                              final phoneUrl = Uri.parse(
                                'tel:${asmPerson.details.mobileNumber}',
                              );
                              if (await canLaunchUrl(phoneUrl)) {
                                await launchUrl(phoneUrl);
                              } else {
                                // Handle error
                                logger.e("Could not launch $phoneUrl");
                              }
                            },
                            child: Row(
                              children: [
                                Image.asset(
                                  'assets/images/scheme/phone_icon.png',
                                  color: acPrimaryBlue,
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      AquaText.subheadline(
                                        asmPerson.details.customerName
                                            .split('-')[1]
                                            .trim(),
                                        weight: AquaFontWeight.bold,
                                      ),
                                      AquaText.subheadline(
                                        asmPerson.details.mobileNumber,
                                        color: acTextSecondaryColor,
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 32),
                          AquaText.subheadline(
                            'Your Performance',
                            weight: AquaFontWeight.bold,
                          ),
                          const SizedBox(height: 8),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              AquaText.body(
                                'Ordered amount',
                                color: acTextSecondaryColor,
                              ),
                              AquaText.body(
                                CurrencyFormatter.formatAsINR(
                                  totalPurchase,
                                  decimalPlaces: 0,
                                ),
                                weight: AquaFontWeight.semibold,
                              ),
                            ],
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              AquaText.body(
                                'Payment Completed',
                                color: acTextSecondaryColor,
                              ),
                              AquaText.body(
                                CurrencyFormatter.formatAsINR(
                                  totalPayment,
                                  decimalPlaces: 0,
                                ),
                                weight: AquaFontWeight.semibold,
                              ),
                            ],
                          ),
                          const SizedBox(height: 24),
                          TextButton(
                            onPressed: () {
                              logger.i("Gotot Scheme Rules Screen");
                              AppRouter.navigateToSchemeRules();
                            },
                            style: TextButton.styleFrom(
                              padding: EdgeInsets.zero,
                              minimumSize: Size.zero,
                              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                            ),
                            child: Text(
                              'Rules',
                              style: TextStyle(
                                color: acPrimaryBlue,
                                fontWeight: FontWeight.bold,
                                fontSize: 14,
                              ),
                            ),
                          ),
                          const SizedBox(height: 32),
                          Padding(
                            padding: EdgeInsets.only(bottom: 16),
                            child: Container(
                              width: double.infinity,
                              decoration: BoxDecoration(
                                border: Border.all(
                                  color: Color(0xFFE5E7EB),
                                  width: 0.0,
                                ),
                                color: Color(0xFFF9FAFC),
                                borderRadius: BorderRadius.all(
                                  Radius.circular(8),
                                ),
                              ),
                              child: Padding(
                                padding: EdgeInsets.all(16),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'To get ${ticketsWon + 1}${ticketsWon == 0
                                          ? 'st'
                                          : ticketsWon == 1
                                          ? 'nd'
                                          : ticketsWon == 2
                                          ? 'rd'
                                          : 'th'} Ticket',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.black,
                                      ),
                                    ),
                                    SizedBox(height: 8),
                                    Row(
                                      children: [
                                        AquaText.body(
                                          'Order products worth ',
                                          color: acTextSecondaryColor,
                                        ),

                                        AquaText.body(
                                          CurrencyFormatter.formatAsINR(
                                            purchasePending,
                                            decimalPlaces: 0,
                                          ),
                                          color: acBlackColor,
                                          weight: AquaFontWeight.semibold,
                                        ),
                                      ],
                                    ),

                                    Row(
                                      children: [
                                        AquaText.body(
                                          'Pay ',
                                          color: acBlackColor,
                                        ),
                                        AquaText.body(
                                          CurrencyFormatter.formatAsINR(
                                            paymentPending,
                                            decimalPlaces: 0,
                                          ),
                                          color: acBlackColor,
                                          weight: AquaFontWeight.semibold,
                                        ),
                                      ],
                                    ),
                                    SizedBox(height: 16),
                                    CustomProgressBar(
                                      percentage:
                                          _calculatePercentageCompleted(
                                            purchasePending,
                                            paymentPending,
                                          ).toDouble(),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          }
          return LoadingWidget(message: "Fetching your Scheme Details");
        },
        listener: (context, state) {},
      ),
    );
  }

  int _calculateTicketsWon(double totalPurchase, double payment) {
    int purchaseTicketWon = 0;

    if (totalPurchase >= 1800000) {
      purchaseTicketWon = 3;
    } else if (totalPurchase >= 1200000) {
      purchaseTicketWon = 2;
    } else if (totalPurchase >= 600000) {
      purchaseTicketWon = 1;
    }

    int paymentTicketWon = 0;

    if (payment >= 1800000) {
      paymentTicketWon = 3;
    } else if (payment >= 1200000) {
      paymentTicketWon = 2;
    } else if (payment >= 600000) {
      paymentTicketWon = 1;
    }

    return [
      purchaseTicketWon,
      paymentTicketWon,
    ].reduce((min, value) => min < value ? min : value);
  }

  int _calculatePercentageCompleted(
    double purchasePending,
    double paymentPending,
  ) {
    double purchasePercentage = ((600000 - purchasePending) / 600000) * 100;
    double paymentPercentage = ((600000 - paymentPending) / 600000) * 100;

    // Ensure percentages are not negative
    purchasePercentage = purchasePercentage < 0 ? 0 : purchasePercentage;
    paymentPercentage = paymentPercentage < 0 ? 0 : paymentPercentage;

    // Calculate average and convert to string with 0 decimal places
    return (((purchasePercentage + paymentPercentage) / 2).round());
  }
}
