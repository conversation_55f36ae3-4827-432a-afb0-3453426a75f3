import 'package:firebase_app_check/firebase_app_check.dart';
import 'package:flutter/foundation.dart';
import '../utils/logger.dart';

/// A service for managing Firebase App Check integration.
///
/// App Check helps protect your backend resources from abuse by attesting that
/// incoming requests are coming from your authentic app.
class AppCheckService {
  final FirebaseAppCheck _appCheck;
  final AppLogger _logger;
  bool _isInitialized = false;

  AppCheckService(this._appCheck, this._logger);

  /// Initialize App Check with the appropriate provider based on platform
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Use debug provider in debug mode
      if (kDebugMode) {
        await _appCheck.activate(
          webProvider: ReCaptchaV3Provider('recaptcha-v3-site-key'),
          androidProvider: AndroidProvider.debug,
          appleProvider: AppleProvider.debug,
        );
        _logger.i('Firebase App Check initialized in debug mode');
      } else {
        // Use production providers in release mode
        await _appCheck.activate(
          webProvider: ReCaptchaV3Provider('recaptcha-v3-site-key'),
          androidProvider: AndroidProvider.playIntegrity,
          appleProvider: AppleProvider.deviceCheck,
        );
        _logger.i('Firebase App Check initialized in production mode');
      }

      // Set token auto refresh enabled
      await _appCheck.setTokenAutoRefreshEnabled(true);

      _isInitialized = true;
    } catch (e) {
      _logger.e('Error initializing Firebase App Check', e);
      // Don't throw - app should still function without App Check
    }
  }

  /// Get a Firebase App Check token for use with custom backends
  Future<String?> getToken() async {
    try {
      if (!_isInitialized) {
        await initialize();
      }

      // In debug mode, we might want to return a fake token to avoid issues
      if (kDebugMode) {
        try {
          final token = await _appCheck.getToken();
          return token;
        } catch (e) {
          _logger.w(
            'Using debug token because App Check failed: ${e.toString()}',
          );
          // Return a fake token in debug mode if real token fails
          return 'debug-app-check-token';
        }
      }

      // Production mode - try to get real token
      try {
        final token = await _appCheck.getToken();
        return token;
      } catch (e) {
        _logger.e('Error getting App Check token', e);
        return null;
      }
    } catch (e) {
      _logger.e('Error getting App Check token', e);
      return null;
    }
  }
}
