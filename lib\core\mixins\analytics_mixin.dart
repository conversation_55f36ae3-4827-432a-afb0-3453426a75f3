import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/analytics_service.dart';
import '../../injection_container.dart' as di;

/// A mixin that provides analytics tracking capabilities to StatefulWidget states.
///
/// This mixin handles:
/// - Screen view tracking
/// - Screen duration measurement
/// - User interaction tracking
/// - Error tracking
/// - App lifecycle events (background/foreground)
/// - Scroll position tracking
mixin AnalyticsMixin<T extends StatefulWidget> on State<T>
    implements WidgetsBindingObserver {
  final AnalyticsService _analyticsService = di.sl<AnalyticsService>();

  // Timing variables
  DateTime? _screenStartTime;
  DateTime? _pauseTime;
  DateTime? _lastInteractionTime;
  int _totalBackgroundTimeMs = 0;

  // Engagement metrics
  int _interactionCount = 0;
  double _maxScrollExtent = 0.0;
  double _currentScrollExtent = 0.0;
  final Set<int> _trackedScrollMilestones = {};

  // Session management
  static const int _sessionTimeoutMs = 30 * 60 * 1000; // 30 minutes

  /// The name of the screen for analytics tracking.
  /// Must be implemented by classes using this mixin.
  String get screenName;

  /// Optional - the parent screen name for hierarchical tracking.
  String get parentScreenName => '';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _screenStartTime = DateTime.now();
    _lastInteractionTime = DateTime.now();

    // Log screen view
    _analyticsService.logScreenView(
      screenName: screenName,
      screenClass: widget.runtimeType.toString(),
    );
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    if (_screenStartTime != null) {
      final duration = DateTime.now().difference(_screenStartTime!);
      // Subtract background time for accurate engagement measurement
      final activeTimeMs = duration.inMilliseconds - _totalBackgroundTimeMs;

      // Track final engagement metrics
      final params = {
        'screen_name': screenName,
        'duration_ms': activeTimeMs > 0 ? activeTimeMs : 0,
        'interaction_count': _interactionCount,
        'scroll_percentage': _calculateScrollPercentage(),
        'screen_class': widget.runtimeType.toString(),
      };

      _analyticsService.logScreenDuration(
        screenName: screenName,
        durationMs: activeTimeMs > 0 ? activeTimeMs : 0,
        screenClass: widget.runtimeType.toString(),
      );

      _analyticsService.logEvent(
        name: 'screen_engagement_metrics',
        parameters: params,
      );
    }
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    // No need to call super as this method is abstract in WidgetsBindingObserver

    if (state == AppLifecycleState.paused) {
      // App going to background
      _pauseTime = DateTime.now();

      // Track app background event
      trackEvent('app_backgrounded');
    } else if (state == AppLifecycleState.resumed && _pauseTime != null) {
      // App coming back to foreground
      final backgroundDuration = DateTime.now().difference(_pauseTime!);
      _totalBackgroundTimeMs += backgroundDuration.inMilliseconds;

      // Check if session timeout occurred
      if (backgroundDuration.inMilliseconds > _sessionTimeoutMs) {
        // Track session timeout
        trackEvent(
          'session_timeout',
          params: {
            'background_duration_ms': backgroundDuration.inMilliseconds,
            'timeout_threshold_ms': _sessionTimeoutMs,
          },
        );

        // Start a new session
        _analyticsService.logEvent(
          name: 'session_start',
          parameters: {
            'reason': 'timeout_after_background',
            'screen_name': screenName,
            'timestamp': DateTime.now().toIso8601String(),
          },
        );
      } else {
        // Track app foreground event with background duration
        trackEvent(
          'app_foregrounded',
          params: {'background_duration_ms': backgroundDuration.inMilliseconds},
        );
      }
    }
  }

  /// Tracks an analytics event with standardized parameters.
  ///
  /// @param eventName The name of the event to track
  /// @param params Additional parameters to include with the event
  void trackEvent(String eventName, {Map<String, Object> params = const {}}) {
    _interactionCount++;
    _lastInteractionTime = DateTime.now();

    final standardizedParams = _standardizeParameters(params);

    final eventParams = {
      'screen_name': screenName,
      'timestamp': DateTime.now().toIso8601String(),
      ...standardizedParams,
    };

    _analyticsService.logEvent(name: eventName, parameters: eventParams);
  }

  /// Standardizes parameter keys and values for consistent analytics.
  Map<String, Object> _standardizeParameters(Map<String, Object> params) {
    final result = <String, Object>{};

    params.forEach((key, value) {
      // Convert key to snake_case
      final standardKey = key.toLowerCase().replaceAll(' ', '_');

      // Standardize value format
      Object standardValue;

      if (value is DateTime) {
        standardValue = value.toIso8601String();
      } else if (value is bool) {
        standardValue = value.toString();
      } else if (value is num) {
        // Keep numeric values as is for Firebase Analytics
        standardValue = value;
      } else {
        // Convert everything else to string
        standardValue = value.toString();
      }

      result[standardKey] = standardValue;
    });

    return result;
  }

  /// Tracks a user interaction with a UI element.
  ///
  /// @param actionName The action performed (e.g., "tap", "swipe")
  /// @param elementType The type of UI element (e.g., "button", "card")
  /// @param elementId Optional identifier for the specific element
  /// @param additionalParams Additional context parameters
  void trackUserInteraction(
    String actionName,
    String elementType, {
    String? elementId,
    Map<String, Object> additionalParams = const {},
  }) {
    _interactionCount++;
    _lastInteractionTime = DateTime.now();

    _analyticsService.logUserInteraction(
      screenName: screenName,
      actionName: actionName,
      elementType: elementType,
      elementId: elementId,
      additionalParams: additionalParams,
    );
  }

  /// Tracks an error that occurred in the application.
  ///
  /// @param errorType The type or category of error
  /// @param errorMessage The error message or description
  /// @param additionalParams Additional context about the error
  void trackError(
    String errorType,
    String errorMessage, {
    Map<String, Object> additionalParams = const {},
  }) {
    final params = {
      'error_type': errorType,
      'error_message': errorMessage,
      'screen_name': screenName,
      'timestamp': DateTime.now().toIso8601String(),
      ...additionalParams,
    };

    _analyticsService.logEvent(name: 'app_error', parameters: params);
  }

  /// Tracks the user's scroll position for engagement metrics.
  ///
  /// @param currentExtent The current scroll position
  /// @param maxExtent The maximum possible scroll extent
  void trackScrollPosition(double currentExtent, double maxExtent) {
    if (maxExtent <= 0) return;

    if (maxExtent > _maxScrollExtent) {
      _maxScrollExtent = maxExtent;
    }

    if (currentExtent > _currentScrollExtent) {
      _currentScrollExtent = currentExtent;

      // Track scroll milestones (25%, 50%, 75%, 100%)
      final percentage = _calculateScrollPercentage();
      _checkAndTrackScrollMilestone(percentage);
    }
  }

  /// Checks if a scroll milestone has been reached and tracks it.
  void _checkAndTrackScrollMilestone(double percentage) {
    final milestones = [25, 50, 75, 100];

    for (final milestone in milestones) {
      if (percentage >= milestone &&
          !_trackedScrollMilestones.contains(milestone)) {
        _trackedScrollMilestones.add(milestone);

        trackEvent(
          'scroll_milestone',
          params: {'milestone_percentage': milestone},
        );

        break; // Only track one milestone at a time
      }
    }
  }

  /// Calculates the current scroll percentage.
  double _calculateScrollPercentage() {
    if (_maxScrollExtent <= 0) return 0;
    final percentage = (_currentScrollExtent / _maxScrollExtent) * 100;
    return percentage > 100 ? 100 : percentage;
  }

  /// Tracks a step in a user flow.
  ///
  /// @param flowName The name of the flow (e.g., "checkout", "registration")
  /// @param stepName The current step in the flow
  /// @param status The status of the step (e.g., "started", "completed", "failed")
  /// @param additionalParams Additional context parameters
  void trackUserFlow({
    required String flowName,
    required String stepName,
    required String status,
    Map<String, Object> additionalParams = const {},
  }) {
    final params = {
      'flow_name': flowName,
      'step_name': stepName,
      'status': status,
      'screen_name': screenName,
      'timestamp': DateTime.now().toIso8601String(),
      ...additionalParams,
    };

    _analyticsService.logEvent(name: 'user_flow', parameters: params);
  }

  // Default implementations for WidgetsBindingObserver methods
  // These can be overridden by implementing classes if needed

  @override
  void didChangeAccessibilityFeatures() {
    // Default implementation - no action needed
  }

  @override
  void didChangeLocales(List<Locale>? locales) {
    // Default implementation - no action needed
  }

  @override
  void didChangeMetrics() {
    // Default implementation - no action needed
  }

  @override
  void didChangePlatformBrightness() {
    // Default implementation - no action needed
  }

  @override
  void didChangeTextScaleFactor() {
    // Default implementation - no action needed
  }

  @override
  void didHaveMemoryPressure() {
    // Default implementation - no action needed
  }

  @override
  Future<bool> didPopRoute() async {
    // Default implementation - let the system handle it
    return false;
  }

  @override
  Future<bool> didPushRoute(String route) async {
    // Default implementation - let the system handle it
    return false;
  }

  @override
  Future<bool> didPushRouteInformation(
    RouteInformation routeInformation,
  ) async {
    // Default implementation - let the system handle it
    return false;
  }

  @override
  Future<AppExitResponse> didRequestAppExit() async {
    // Default implementation - allow exit
    return AppExitResponse.exit;
  }

  @override
  void didChangeViewFocus(ViewFocusEvent event) {
    // Default implementation - no action needed
  }

  @override
  void handleCancelBackGesture() {
    // Default implementation - no action needed
  }

  @override
  void handleCommitBackGesture() {
    // Default implementation - no action needed
  }

  @override
  bool handleStartBackGesture(PredictiveBackEvent details) {
    // Default implementation - let the system handle it
    return false;
  }

  @override
  bool handleUpdateBackGestureProgress(PredictiveBackEvent details) {
    // Default implementation - let the system handle it
    return false;
  }
}
