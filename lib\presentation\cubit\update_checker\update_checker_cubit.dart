import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../core/utils/logger.dart';
import '../../../domain/usecases/check_mandatory_update.dart';
import 'update_checker_state.dart';

class UpdateCheckerCubit extends Cubit<UpdateCheckerState> {
  final CheckMandatoryUpdate checkMandatoryUpdate;
  final AppLogger logger;

  UpdateCheckerCubit({required this.checkMandatoryUpdate, required this.logger})
    : super(UpdateCheckerInitial());

  Future<void> checkForUpdate() async {
    emit(UpdateCheckerLoading());

    try {
      final result = await checkMandatoryUpdate.execute();
      logger.i(result.updateInfo?.message ?? "Value is Empty...");

      if (result.updateRequired) {
        // Mandatory update
        emit(UpdateRequired(result.updateInfo!));
      } else {
        // No update needed
        emit(UpdateNotRequired());
      }
    } catch (e) {
      logger.e("Error checking for updates: $e");
      emit(UpdateCheckerError(e.toString()));
    }
  }
}
