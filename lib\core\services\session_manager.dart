import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'analytics_service.dart';
import '../../injection_container.dart' as di;

class SessionManager with WidgetsBindingObserver {
  final AnalyticsService _analyticsService;
  DateTime? _sessionStartTime;
  DateTime? _lastActiveTime;
  bool _isInForeground = true;
  static const String _lastSessionKey = 'last_session_timestamp';
  static const String _firstSessionKey = 'first_session_timestamp';
  static const String _sessionCountKey = 'session_count';

  SessionManager(this._analyticsService) {
    WidgetsBinding.instance.addObserver(this);
    _initSession();
  }

  Future<void> _initSession() async {
    final prefs = await SharedPreferences.getInstance();
    final now = DateTime.now();

    // Check if this is the first session ever
    if (!prefs.containsKey(_firstSessionKey)) {
      await prefs.setString(_firstSessionKey, now.toIso8601String());
    }

    // Get the last session timestamp
    final lastSessionStr = prefs.getString(_lastSessionKey);

    // Calculate days since last session if available
    int? daysSinceLastSession;
    if (lastSessionStr != null) {
      final lastSession = DateTime.parse(lastSessionStr);
      daysSinceLastSession = now.difference(lastSession).inDays;

      // Set user property for session frequency
      final analyticsService = di.sl<AnalyticsService>();
      await analyticsService.analytics.setUserProperty(
        name: 'days_since_last_session',
        value: daysSinceLastSession.toString(),
      );
    }

    // Update session count
    final sessionCount = prefs.getInt(_sessionCountKey) ?? 0;
    await prefs.setInt(_sessionCountKey, sessionCount + 1);

    // Set session count as user property
    final analyticsService = di.sl<AnalyticsService>();
    await analyticsService.analytics.setUserProperty(
      name: 'session_count',
      value: (sessionCount + 1).toString(),
    );

    // Start new session
    _sessionStartTime = now;
    _lastActiveTime = now;

    // Log session start with additional parameters
    _analyticsService.logEvent(
      name: 'start_session',
      parameters: {
        'session_count': sessionCount + 1,
        'days_since_last_session':
            daysSinceLastSession?.toString() ?? 'first_session',
        'timestamp': now.toIso8601String(),
      },
    );

    // Update last session timestamp
    await prefs.setString(_lastSessionKey, now.toIso8601String());
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    final now = DateTime.now();

    switch (state) {
      case AppLifecycleState.resumed:
        // App came to foreground
        if (!_isInForeground) {
          _isInForeground = true;
          _sessionStartTime = now;
          _lastActiveTime = now;
          _analyticsService.logSessionStart();
        }
        break;

      case AppLifecycleState.paused:
        // App went to background
        if (_isInForeground && _lastActiveTime != null) {
          _isInForeground = false;
          final duration = now.difference(_lastActiveTime!).inMilliseconds;
          _analyticsService.logUserEngagement(duration);
        }
        break;

      default:
        // Handle other lifecycle states
        break;
    }
  }

  // Call this method when user performs significant actions
  void updateUserActivity() {
    final now = DateTime.now();
    if (_lastActiveTime != null) {
      final duration = now.difference(_lastActiveTime!).inMilliseconds;
      // Only log if significant time has passed (e.g., more than 5 seconds)
      if (duration > 5000) {
        _analyticsService.logUserEngagement(duration);
      }
    }
    _lastActiveTime = now;
  }

  // Call this when the app is closing or user is logging out
  void endSession() {
    if (_sessionStartTime != null) {
      final duration =
          DateTime.now().difference(_sessionStartTime!).inMilliseconds;
      _analyticsService.logUserEngagement(duration);
    }
    WidgetsBinding.instance.removeObserver(this);
  }
}
