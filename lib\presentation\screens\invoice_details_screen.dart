import 'package:aqua_ui/aqua_ui.dart';
import 'package:aquapartner/core/utils/currency_formatter.dart';
import 'package:aquapartner/domain/entities/invoices/invoice_item.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:intl/intl.dart';

import '../../core/constants/app_constants.dart';
import '../../core/mixins/analytics_mixin.dart';
import '../../domain/entities/invoices/invoice.dart';
import '../cubit/customer/customer_cubit.dart';
import '../widgets/styled_generic_table.dart';

class InvoiceDetailsScreen extends StatefulWidget {
  final Invoice invoice;
  const InvoiceDetailsScreen({super.key, required this.invoice});

  @override
  State<InvoiceDetailsScreen> createState() => _InvoiceDetailsScreenState();
}

class _InvoiceDetailsScreenState extends State<InvoiceDetailsScreen>
    with AnalyticsMixin<InvoiceDetailsScreen> {
  late CustomerLoaded customerState;

  @override
  String get screenName => 'invoice_details';

  @override
  String get parentScreenName => 'billing_and_payments';

  @override
  void initState() {
    customerState = context.read<CustomerCubit>().state as CustomerLoaded;
    super.initState();

    // Track invoice details view
    trackEvent(
      'invoice_details_viewed',
      params: {
        'invoice_number': widget.invoice.invoiceNumber,
        'invoice_total': widget.invoice.total.toString(),
        'invoice_date': widget.invoice.invoiceDate.toIso8601String(),
        'item_count': widget.invoice.items.length.toString(),
        'customer_name': customerState.customer.companyName,
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: acWhiteColor, // Match AppBar
      appBar: AppBar(
        elevation: 0,
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.white,
          statusBarIconBrightness: Brightness.dark,
        ),
        backgroundColor: acWhiteColor,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          color: acTextSecondaryColor,
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: AquaText.headline(
          '#${widget.invoice.invoiceNumber}',
          weight: AquaFontWeight.semibold,
          color: acTextSecondaryColor,
        ),
        centerTitle: false,
      ),
      // Use a Stack to position the floating button at the bottom
      body: Container(
        color: acWhiteColor,
        width: double.infinity,
        height: double.infinity,
        padding: EdgeInsets.only(left: 16, right: 16),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SvgPicture.asset(
                AppConstants.imgAquaconnectLogo,
                height: 35,
                colorFilter: ColorFilter.mode(acPrimaryBlue, BlendMode.srcIn),
              ),
              SizedBox(height: 8),
              AquaText.body(
                "Issues on ${DateFormat('dd-MM-yyyy', Intl.defaultLocale).format(widget.invoice.invoiceDate)}",
              ),
              SizedBox(height: 16),
              AquaDivider(),
              SizedBox(height: 16),
              AquaText.subheadline("From", weight: AquaFontWeight.bold),
              SizedBox(height: 16),

              Row(
                children: [
                  Expanded(
                    flex: 3,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        AquaText.subheadline(
                          "Coastal Aquaculture Research Institute Private Limited",
                          weight: AquaFontWeight.semibold,
                        ),
                        SizedBox(height: 16),
                        AquaText.subheadline(
                          'Type II/17, Dr.VSI Estate, Thiruvanmiyur, Chennai, Tamilnadu - 600041',
                          weight: AquaFontWeight.regular,
                          color: acTextSecondaryColor,
                        ),
                      ],
                    ),
                  ),
                  Expanded(flex: 2, child: Container()),
                ],
              ),
              SizedBox(height: 16),
              AquaText.subheadline("To", weight: AquaFontWeight.bold),
              SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    flex: 3,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        AquaText.subheadline(
                          customerState.customer.companyName,
                          weight: AquaFontWeight.semibold,
                        ),
                        SizedBox(height: 16),
                        AquaText.subheadline(
                          customerState.customer.billingAddress,
                          weight: AquaFontWeight.regular,
                          color: acTextSecondaryColor,
                        ),
                        AquaText.subheadline(
                          'GST NO: ${customerState.customer.gstNo}',
                          weight: AquaFontWeight.regular,
                          color: acTextSecondaryColor,
                        ),
                      ],
                    ),
                  ),
                  Expanded(flex: 2, child: Container()),
                ],
              ),

              StyledGenericTable<InvoiceItem>(
                items: widget.invoice.items,
                showDividers: true,
                columns: [
                  ColumnConfig<InvoiceItem>(
                    title: 'Item',
                    width: 160,
                    cellBuilder:
                        (invoiceItem) => Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            AquaText.body(
                              invoiceItem.itemName,
                              weight: AquaFontWeight.semibold,
                            ),
                            SizedBox(height: 8),
                            AquaText.body(
                              CurrencyFormatter.formatAsINR(
                                invoiceItem.itemPrice,
                              ),
                              color: acTextSecondaryColor,
                            ),
                          ],
                        ),
                  ),
                  ColumnConfig<InvoiceItem>(
                    title: 'Discount',
                    width: 90,
                    cellBuilder:
                        (invoiceItem) => AquaText.body(
                          "${CurrencyFormatter.calculateOffer(subTotal: invoiceItem.subTotal, total: invoiceItem.total)}%",
                        ),
                  ),
                  ColumnConfig<InvoiceItem>(
                    title: 'Total',
                    width: 120,
                    cellBuilder:
                        (invoiceItem) => AquaText.body(
                          CurrencyFormatter.formatAsINR(invoiceItem.total),
                        ),
                  ),
                ],
              ),
              SizedBox(height: 16),
              _buildPriceRow(
                isBold: false,
                label: "SUB TOTAL",
                value: CurrencyFormatter.formatAsINR(widget.invoice.subTotal),
              ),
              SizedBox(height: 16),
              _buildPriceRow(
                isBold: false,
                label: "TAX",
                value: CurrencyFormatter.formatAsINR(
                  widget.invoice.total - widget.invoice.subTotal,
                ),
              ),
              SizedBox(height: 16),
              _buildPriceRow(
                isBold: true,
                label: "TOTAL",
                value: CurrencyFormatter.formatAsINR(widget.invoice.total),
              ),
              vSpace4,
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPriceRow({
    required String label,
    required String value,
    required bool isBold,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        AquaText.subheadline(
          label,
          weight: isBold ? AquaFontWeight.bold : AquaFontWeight.regular,
        ),
        AquaText.subheadline(
          value,
          weight: isBold ? AquaFontWeight.bold : AquaFontWeight.regular,
        ),
      ],
    );
  }
}
