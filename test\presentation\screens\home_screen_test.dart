import 'package:aquapartner/presentation/screens/home_screen.dart';
import 'package:aquapartner/presentation/cubit/auth/auth_cubit.dart';
import 'package:aquapartner/presentation/cubit/auth/auth_state.dart';
import 'package:aquapartner/presentation/cubit/connectivity/connectivity_cubit.dart';
import 'package:aquapartner/presentation/cubit/connectivity/connectivity_state.dart';
import 'package:aquapartner/presentation/cubit/home/<USER>';
import 'package:aquapartner/presentation/cubit/home/<USER>';
import 'package:aquapartner/presentation/cubit/customer/customer_cubit.dart';
import 'package:aquapartner/presentation/cubit/dashboard/dashboard_cubit.dart';
import 'package:aquapartner/presentation/cubit/dashboard/dashboard_state.dart';
import 'package:aquapartner/presentation/cubit/product_catalogue/product_catalogue_cubit.dart';
import 'package:aquapartner/presentation/cubit/product_catalogue/product_catalogue_state.dart';
import 'package:aquapartner/presentation/cubit/update_checker/update_checker_cubit.dart';
import 'package:aquapartner/presentation/cubit/update_checker/update_checker_state.dart';
import 'package:aquapartner/domain/entities/user.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mocktail/mocktail.dart';

import '../../helpers/test_helpers.dart';

// Mock Cubits
class MockAuthCubit extends Mock implements AuthCubit {}

class MockConnectivityCubit extends Mock implements ConnectivityCubit {}

class MockHomeCubit extends Mock implements HomeCubit {}

class MockCustomerCubit extends Mock implements CustomerCubit {}

class MockDashboardCubit extends Mock implements DashboardCubit {}

class MockProductCatalogueCubit extends Mock implements ProductCatalogueCubit {}

class MockUpdateCheckerCubit extends Mock implements UpdateCheckerCubit {}

void main() {
  group('HomeScreen Tests', () {
    late MockAuthCubit mockAuthCubit;
    late MockConnectivityCubit mockConnectivityCubit;
    late MockHomeCubit mockHomeCubit;
    late MockCustomerCubit mockCustomerCubit;
    late MockDashboardCubit mockDashboardCubit;
    late MockProductCatalogueCubit mockProductCatalogueCubit;
    late MockUpdateCheckerCubit mockUpdateCheckerCubit;

    setUp(() {
      // Setup GetIt services for AnalyticsMixin
      TestHelpers.setupGetItServices();

      // Initialize mocks
      mockAuthCubit = MockAuthCubit();
      mockConnectivityCubit = MockConnectivityCubit();
      mockHomeCubit = MockHomeCubit();
      mockCustomerCubit = MockCustomerCubit();
      mockDashboardCubit = MockDashboardCubit();
      mockProductCatalogueCubit = MockProductCatalogueCubit();
      mockUpdateCheckerCubit = MockUpdateCheckerCubit();

      // Setup default mock behaviors
      when(() => mockAuthCubit.state).thenReturn(
        AuthSuccess(
          user: User(
            id: 1,
            phoneNumber: '+919999999999',
            isVerified: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        ),
      );
      when(() => mockAuthCubit.stream).thenAnswer(
        (_) => Stream.value(
          AuthSuccess(
            user: User(
              id: 1,
              phoneNumber: '+919999999999',
              isVerified: true,
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
            ),
          ),
        ),
      );

      when(
        () => mockConnectivityCubit.state,
      ).thenReturn(const ConnectivityState(status: ConnectionStatus.connected));
      when(() => mockConnectivityCubit.stream).thenAnswer(
        (_) => Stream.value(
          const ConnectivityState(status: ConnectionStatus.connected),
        ),
      );

      when(
        () => mockHomeCubit.state,
      ).thenReturn(const HomeState(viewType: ViewType.dashboard));
      when(() => mockHomeCubit.stream).thenAnswer(
        (_) => Stream.value(const HomeState(viewType: ViewType.dashboard)),
      );

      when(() => mockCustomerCubit.state).thenReturn(CustomerInitial());
      when(
        () => mockCustomerCubit.stream,
      ).thenAnswer((_) => Stream.value(CustomerInitial()));

      // Setup dashboard cubit mocks
      when(() => mockDashboardCubit.state).thenReturn(DashboardInitial());
      when(
        () => mockDashboardCubit.stream,
      ).thenAnswer((_) => Stream.value(DashboardInitial()));
      when(
        () => mockDashboardCubit.loadDashboardData(),
      ).thenAnswer((_) async {});

      // Setup product catalogue cubit mocks
      when(() => mockProductCatalogueCubit.state).thenReturn(
        ProductCatalogueState(
          status: ProductCatalogueStatus.initial,
          productCatalogues: const [],
        ),
      );
      when(() => mockProductCatalogueCubit.stream).thenAnswer(
        (_) => Stream.value(
          ProductCatalogueState(
            status: ProductCatalogueStatus.initial,
            productCatalogues: const [],
          ),
        ),
      );

      // Setup update checker cubit mocks
      when(
        () => mockUpdateCheckerCubit.state,
      ).thenReturn(UpdateCheckerInitial());
      when(
        () => mockUpdateCheckerCubit.stream,
      ).thenAnswer((_) => Stream.value(UpdateCheckerInitial()));
      when(
        () => mockUpdateCheckerCubit.checkForUpdate(),
      ).thenAnswer((_) async {});
    });

    tearDown(() {
      // Cleanup GetIt services
      TestHelpers.cleanupGetItServices();
    });

    Widget createHomeScreen() {
      return TestHelpers.createTestApp(
        home: MultiBlocProvider(
          providers: [
            BlocProvider<AuthCubit>.value(value: mockAuthCubit),
            BlocProvider<ConnectivityCubit>.value(value: mockConnectivityCubit),
            BlocProvider<HomeCubit>.value(value: mockHomeCubit),
            BlocProvider<CustomerCubit>.value(value: mockCustomerCubit),
            BlocProvider<DashboardCubit>.value(value: mockDashboardCubit),
            BlocProvider<ProductCatalogueCubit>.value(
              value: mockProductCatalogueCubit,
            ),
            BlocProvider<UpdateCheckerCubit>.value(
              value: mockUpdateCheckerCubit,
            ),
          ],
          child: const HomeScreen(),
        ),
      );
    }

    group('Widget Rendering', () {
      testWidgets('should render home screen with basic structure', (
        tester,
      ) async {
        await tester.pumpWidget(createHomeScreen());
        await tester.pump();

        // Verify main structure exists
        expect(find.byType(HomeScreen), findsOneWidget);
        expect(find.byType(Scaffold), findsAtLeastNWidgets(1));
        expect(find.byType(AppBar), findsOneWidget);
      });

      testWidgets('should handle widget creation without errors', (
        tester,
      ) async {
        // Create widget without errors
        final widget = createHomeScreen();
        expect(widget, isNotNull);

        // Pump widget
        await tester.pumpWidget(widget);
        await tester.pump();

        // Verify no exceptions thrown
        expect(find.byType(HomeScreen), findsOneWidget);
      });
    });

    group('Basic Functionality', () {
      testWidgets('should display drawer menu', (tester) async {
        await tester.pumpWidget(createHomeScreen());
        await tester.pump();

        // Verify drawer menu is accessible (DrawerMenu is a custom widget)
        // We can check if the drawer property is set by looking for the drawer icon
        expect(find.byIcon(Icons.menu), findsOneWidget);
      });

      testWidgets('should handle different view types', (tester) async {
        await tester.pumpWidget(createHomeScreen());
        await tester.pump();

        // Verify the screen renders without errors for dashboard view
        expect(find.byType(HomeScreen), findsOneWidget);

        // Verify that the AnimatedSwitcher is present (for view switching)
        expect(find.byType(AnimatedSwitcher), findsOneWidget);
      });

      testWidgets('should handle analytics mixin integration', (tester) async {
        await tester.pumpWidget(createHomeScreen());
        await tester.pump();

        // Verify screen renders without analytics errors
        expect(find.byType(HomeScreen), findsOneWidget);

        // The screen should not crash when analytics methods are called
        // This is implicitly tested by the successful rendering
      });
    });
  });
}
