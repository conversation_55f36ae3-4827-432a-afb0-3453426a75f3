import 'package:aqua_ui/aqua_ui.dart';
import 'package:flutter/material.dart';

class CustomBadge extends StatelessWidget {
  final String title;
  final BorderRadius borderRadius;
  final Color titleColor;

  const CustomBadge({
    super.key,
    required this.title,
    this.titleColor = acPrimaryBlue,
    this.borderRadius = const BorderRadius.only(topRight: Radius.circular(4)),
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
      decoration: BoxDecoration(
        color: titleColor.withValues(alpha: 0.2),
        borderRadius: borderRadius, // Use the provided borderRadius
      ),
      child: AquaText.caption(title, color: titleColor),
    );
  }
}
