import 'package:equatable/equatable.dart';

/// Represents a Visit in the domain layer.
/// This is a pure Dart object, free from database or network annotations.
class Visit extends Equatable {
  final int? id; // Optional ID from the data layer (e.g., ObjectBox ID)
  final String pondId;
  final String farmerId;
  final String farmerName;
  final String mobileNumber;
  final DateTime createdDateTime;
  final int doc;
  final String productUsed;

  const Visit({
    this.id,
    required this.createdDateTime,
    required this.doc,
    required this.pondId,
    required this.mobileNumber,
    required this.productUsed,
    required this.farmerId,
    required this.farmerName,
  });

  // Use Equatable to easily compare Visit objects
  @override
  List<Object?> get props => [
    id,
    createdDateTime,
    doc,
    pondId,
    mobileNumber,
    productUsed,
    farmerId,
    farmerName,
  ];
}
