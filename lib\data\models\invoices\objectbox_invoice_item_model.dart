import 'package:objectbox/objectbox.dart';

import '../../../domain/entities/invoices/invoice_item.dart';
import 'objectbox_invoice_model.dart';

@Entity()
class ObjectBoxInvoiceItemModel {
  @Id()
  int id = 0;

  @Unique()
  final String itemId;
  final String productId;
  final String itemName;
  final double quantity;

  // Renamed from invoiceId to invoiceIdValue to avoid conflict with relation
  final String invoiceIdValue;

  @Property(type: PropertyType.dateNano)
  final DateTime createdTime;

  final double discountAmount;
  final String hsnSac;
  final double itemPrice;

  @Property(type: PropertyType.dateNano)
  final DateTime lastModifiedTime;

  final String placeOfSupply;
  final String productCategory;
  final String source;
  final double subTotal;
  final double total;

  final invoice = ToOne<ObjectBoxInvoiceModel>();

  ObjectBoxInvoiceItemModel({
    this.id = 0,
    required this.itemId,
    required this.productId,
    required this.itemName,
    required this.quantity,
    required this.invoiceIdValue,
    required this.createdTime,
    required this.discountAmount,
    required this.hsnSac,
    required this.itemPrice,
    required this.lastModifiedTime,
    required this.placeOfSupply,
    required this.productCategory,
    required this.source,
    required this.subTotal,
    required this.total,
  });

  InvoiceItem toEntity() {
    return InvoiceItem(
      id: itemId,
      productId: productId,
      itemName: itemName,
      quantity: quantity,
      invoiceId: invoiceIdValue,
      createdTime: createdTime,
      discountAmount: discountAmount,
      hsnSac: hsnSac,
      itemPrice: itemPrice,
      lastModifiedTime: lastModifiedTime,
      placeOfSupply: placeOfSupply,
      productCategory: productCategory,
      source: source,
      subTotal: subTotal,
      total: total,
    );
  }

  static ObjectBoxInvoiceItemModel fromEntity(InvoiceItem item) {
    return ObjectBoxInvoiceItemModel(
      itemId: item.id,
      productId: item.productId,
      itemName: item.itemName,
      quantity: item.quantity,
      invoiceIdValue: item.invoiceId,
      createdTime: item.createdTime,
      discountAmount: item.discountAmount,
      hsnSac: item.hsnSac,
      itemPrice: item.itemPrice,
      lastModifiedTime: item.lastModifiedTime,
      placeOfSupply: item.placeOfSupply,
      productCategory: item.productCategory,
      source: item.source,
      subTotal: item.subTotal,
      total: item.total,
    );
  }
}
