import 'package:equatable/equatable.dart';

class AccountStatementEntity extends Equatable {
  final DateTime txnDate;
  final String vchType;
  final String invoiceNumber;
  final String particulars;
  final double debit;
  final double credit;
  final double balance;
  final double amount;

  const AccountStatementEntity({
    required this.txnDate,
    required this.vchType,
    required this.invoiceNumber,
    required this.particulars,
    required this.debit,
    required this.credit,
    required this.balance,
    required this.amount,
  });

  @override
  List<Object?> get props => [
    txnDate,
    vchType,
    invoiceNumber,
    particulars,
    debit,
    credit,
    balance,
    amount,
  ];
}
