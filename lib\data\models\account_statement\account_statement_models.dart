import 'package:objectbox/objectbox.dart';
import '../../../domain/entities/account_statement/account_statement.dart';
import '../../../domain/entities/account_statement/account_statement_entity.dart';

// This file contains both models to avoid circular dependencies

@Entity()
class AccountStatementModel {
  @Id()
  int id = 0;

  String customerId;

  @Property(type: PropertyType.date)
  DateTime lastSyncTime;

  @Backlink('accountStatement')
  final entries = ToMany<AccountStatementEntryModel>();

  AccountStatementModel({
    this.id = 0,
    required this.customerId,
    required this.lastSyncTime,
  });

  // Convert from domain entity to model
  factory AccountStatementModel.fromEntity(
    AccountStatement entity,
    String customerId,
  ) {
    final model = AccountStatementModel(
      customerId: customerId,
      lastSyncTime: entity.lastSyncTime,
    );

    // Add entries
    for (var entry in entity.entries) {
      final entryModel = AccountStatementEntryModel.fromEntity(entry);
      entryModel.accountStatement.target = model;
      model.entries.add(entryModel);
    }

    return model;
  }

  // Convert from model to domain entity
  AccountStatement toEntity() {
    return AccountStatement(
      entries: entries.map((entryModel) => entryModel.toEntity()).toList(),
      lastSyncTime: lastSyncTime,
    );
  }

  // Convert from JSON to model
  factory AccountStatementModel.fromJson(
    Map<String, dynamic> json,
    String customerId,
  ) {
    final model = AccountStatementModel(
      customerId: customerId,
      lastSyncTime: DateTime.now(),
    );

    // Process entries
    if (json['results'] != null) {
      final entriesList = json['results'] as List;
      for (var entryJson in entriesList) {
        final entry = AccountStatementEntryModel.fromJson(entryJson);
        entry.accountStatement.target = model;
        model.entries.add(entry);
      }
    }

    return model;
  }

  // Convert from model to JSON
  Map<String, dynamic> toJson() {
    return {
      'customerId': customerId,
      'lastSyncTime': lastSyncTime.toIso8601String(),
      'results': entries.map((entry) => entry.toJson()).toList(),
    };
  }
}

@Entity()
class AccountStatementEntryModel {
  @Id()
  int id = 0;

  @Property(type: PropertyType.date)
  DateTime txnDate;

  String vchType;
  String invoiceNumber;
  String particulars;
  double debit;
  double credit;
  double balance;
  double amount;

  // Relation to parent AccountStatementModel
  final accountStatement = ToOne<AccountStatementModel>();

  AccountStatementEntryModel({
    this.id = 0,
    required this.txnDate,
    required this.vchType,
    required this.invoiceNumber,
    required this.particulars,
    required this.debit,
    required this.credit,
    required this.balance,
    required this.amount,
  });

  // Convert from domain entity to model
  factory AccountStatementEntryModel.fromEntity(AccountStatementEntity entity) {
    return AccountStatementEntryModel(
      txnDate: entity.txnDate,
      vchType: entity.vchType,
      invoiceNumber: entity.invoiceNumber,
      particulars: entity.particulars,
      debit: entity.debit,
      credit: entity.credit,
      balance: entity.balance,
      amount: entity.amount,
    );
  }

  // Convert from model to domain entity
  AccountStatementEntity toEntity() {
    return AccountStatementEntity(
      txnDate: txnDate,
      vchType: vchType,
      invoiceNumber: invoiceNumber,
      particulars: particulars,
      debit: debit,
      credit: credit,
      balance: balance,
      amount: amount,
    );
  }

  // Convert from JSON to model
  factory AccountStatementEntryModel.fromJson(Map<String, dynamic> json) {
    // Handle both 'amount' and 'total' fields for backward compatibility
    // The API returns 'total' but our model uses 'amount'
    final amountValue =
        json.containsKey('amount')
            ? json['amount']
            : (json.containsKey('total') ? json['total'] : 0.0);

    return AccountStatementEntryModel(
      txnDate: DateTime.parse(json['txnDate']),
      vchType: json['vchType'] ?? '',
      invoiceNumber: json['invoiceNumber'] ?? '',
      particulars: json['particulars'] ?? '',
      debit: (json['debit'] ?? 0.0).toDouble(),
      credit: (json['credit'] ?? 0.0).toDouble(),
      balance: (json['balance'] ?? 0.0).toDouble(),
      amount: (amountValue ?? 0.0).toDouble(),
    );
  }

  // Convert from model to JSON
  Map<String, dynamic> toJson() {
    return {
      'txnDate': txnDate.toIso8601String(),
      'vchType': vchType,
      'invoiceNumber': invoiceNumber,
      'particulars': particulars,
      'debit': debit,
      'credit': credit,
      'balance': balance,
      'amount': amount,
    };
  }
}
